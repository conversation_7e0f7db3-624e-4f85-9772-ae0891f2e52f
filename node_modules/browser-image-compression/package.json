{"name": "browser-image-compression", "version": "2.0.2", "description": "Compress images in the browser", "main": "dist/browser-image-compression.js", "module": "dist/browser-image-compression.mjs", "jsnext:main": "dist/browser-image-compression.mjs", "types": "dist/browser-image-compression.d.ts", "scripts": {"eslint": "eslint lib test --fix", "build": "rollup -c --environment BUILD:production --bundleConfigAsCjs", "watch": "rollup -c -w --environment BUILD:development --bundleConfigAsCjs", "dev": "npm run watch", "test": "cross-env NODE_ENV=test nyc mocha", "posttest": "npm run coverage-badges", "test:watch": "cross-env NODE_ENV=test nyc mocha -w", "prepublishOnly": "npm test && npm run build", "coverage-badges": "make-coverage-badge", "commit": "cz"}, "homepage": "https://github.com/Donaldcwl/browser-image-compression#readme", "repository": {"type": "git", "url": "https://github.com/Donaldcwl/browser-image-compression"}, "bugs": {"url": "https://github.com/Donaldcwl/browser-image-compression/issues"}, "keywords": ["image compression", "browser", "image processing", "reduce resolution", "reduce size"], "author": "<PERSON> <donaldc<PERSON>@gmail.com>", "license": "MIT", "files": ["dist"], "dependencies": {"uzip": "0.20201231.0"}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "@babel/register": "^7.0.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.2", "babel-plugin-istanbul": "^6.0.0", "canvas": "2.6.1", "chai": "^4.1.0", "chai-as-promised": "^7.1.1", "cross-env": "^7.0.2", "cz-conventional-changelog": "3.3.0", "eslint": "^8.34.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.22.1", "istanbul": "^0.4.5", "jsdom": "^21.1.0", "make-coverage-badge": "^1.0.1", "mocha": "^10.2.0", "nyc": "^15.0.1", "rollup": "^3.15.0", "rollup-plugin-copy": "^3.3.0", "rollup-plugin-license": "^3.0.1", "rollup-plugin-nodent": "^0.2.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.6.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}