{"version": 3, "sources": ["../../src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport {\n  type FallbackRouteParams,\n  getFallbackRouteParams,\n} from './request/fallback-params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport {\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ServerComponentsHmrCache,\n  type ResponseCacheBase,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n  CachedRouteKind,\n  type CachedRedirectValue,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PreviewData } from '../types'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from './route-modules/app-page/module'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport { getRedirectStatus } from '../lib/redirect-status'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { getCacheControlHeader, type CacheControl } from './lib/cache-control'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { getBotType, isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getPreviouslyRevalidatedTags, getServerUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport { getTracer, isBubbledError, SpanKind } from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport { normalizeNextQueryParam } from './web/utils'\nimport {\n  CACHE_ONE_YEAR,\n  MATCHED_PATH_HEADER,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { decodePathParams } from './lib/router-utils/decode-path-params'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n  isPagesRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsPossibleServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { ENCODED_TAGS } from './stream-utils/encoded-tags'\nimport { NextRequestHint } from './web/adapter'\nimport { getRevalidateReason } from './instrumentation/utils'\nimport { RouteKind } from './route-kind'\nimport type { RouteModule } from './route-modules/route-module'\nimport { FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { toResponseCacheEntry } from './response-cache/utils'\nimport { SegmentPrefixRSCPathnameNormalizer } from './normalizers/request/segment-prefix-rsc'\nimport {\n  shouldServeStreamingMetadata,\n  isHtmlBotRequest,\n} from './lib/streaming-metadata'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport { NoFallbackError } from '../shared/lib/no-fallback-error.external'\nimport { getCacheHandlers } from './use-cache/handlers'\nimport { fixMojibake } from './lib/fix-mojibake'\nimport { computeCacheBustingSearchParam } from '../shared/lib/router/utils/cache-busting-search-param'\nimport { RedirectStatusCode } from '../client/components/redirect-status-code'\nimport { setCacheBustingSearchParamWithHash } from '../client/components/router-reducer/set-cache-busting-search-param'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  type: 'html' | 'json' | 'rsc'\n  body: RenderResult\n  cacheControl?: CacheControl\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly segmentPrefetchRSC: SegmentPrefixRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n  private readonly isAppSegmentPrefetchEnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for dynamic IO\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? dir\n        : (require('path') as typeof import('path')).resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? this.nextConfig.distDir\n        : (require('path') as typeof import('path')).join(\n            this.dir,\n            this.nextConfig.distDir\n          )\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.isAppSegmentPrefetchEnabled =\n      this.enabledDirectories.app &&\n      this.nextConfig.experimental.clientSegmentCache === true\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      segmentPrefetchRSC:\n        this.isAppSegmentPrefetchEnabled && this.minimalMode\n          ? new SegmentPrefixRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n    }\n\n    this.renderOpts = {\n      dir: this.dir,\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      strictNextHead: this.nextConfig.experimental.strictNextHead ?? true,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      htmlLimitedBots: this.nextConfig.htmlLimitedBots,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        staleTimes: this.nextConfig.experimental.staleTimes,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        dynamicIO: this.nextConfig.experimental.dynamicIO ?? false,\n        clientSegmentCache:\n          this.nextConfig.experimental.clientSegmentCache === 'client-only'\n            ? 'client-only'\n            : Boolean(this.nextConfig.experimental.clientSegmentCache),\n        dynamicOnHover: this.nextConfig.experimental.dynamicOnHover ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n      devtoolSegmentExplorer:\n        this.nextConfig.experimental.devtoolSegmentExplorer,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  protected reloadMatchers() {\n    return this.matchers.reload()\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.segmentPrefetchRSC?.match(parsedUrl.pathname)) {\n      const result = this.normalizers.segmentPrefetchRSC.extract(\n        parsedUrl.pathname\n      )\n      if (!result) return false\n\n      const { originalPathname, segmentPath } = result\n      parsedUrl.pathname = originalPathname\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n        segmentPath\n\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPath)\n    } else if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n\n        const segmentPrefetchRSCRequest =\n          req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()]\n        if (typeof segmentPrefetchRSCRequest === 'string') {\n          addRequestMeta(\n            req,\n            'segmentPrefetchRSCRequest',\n            segmentPrefetchRSCRequest\n          )\n        }\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = await this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          getRequestMeta(req, 'middlewareInvoke')\n        ) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        addRequestMeta(req, 'locale', localePathResult.detectedLocale)\n        addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          removeRequestMeta(req, 'localeInferredFromDefault')\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      addRequestMeta(req, 'isNextDataReq', true)\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            fixMojibake(req.headers[MATCHED_PATH_HEADER] as string),\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          matchedPath = denormalizePagePath(matchedPath)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            addRequestMeta(req, 'locale', localeAnalysisResult.detectedLocale)\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              addRequestMeta(req, 'localeInferredFromDefault', true)\n            } else {\n              removeRequestMeta(req, 'localeInferredFromDefault')\n            }\n          }\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n          let paramsResult: {\n            params: ParsedUrlQuery | false\n            hasValidParams: boolean\n          } = {\n            params: false,\n            hasValidParams: false,\n          }\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n\n              // The page is dynamic if the params are defined. We know at this\n              // stage that the matched path is not a static page if the params\n              // were parsed from the matched path header.\n              if (typeof match.params !== 'undefined') {\n                pageIsDynamic = true\n                paramsResult.params = match.params\n                paramsResult.hasValidParams = true\n              }\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getServerUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          // Store a copy of `parsedUrl.query` before calling handleRewrites.\n          // Since `handleRewrites` might add new queries to `parsedUrl.query`.\n          const originQueryParams = { ...parsedUrl.query }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParamKeys = Object.keys(\n            utils.handleRewrites(req, parsedUrl)\n          )\n\n          // Create a copy of the query params to avoid mutating the original\n          // object. This prevents any overlapping query params that have the\n          // same normalized key from causing issues.\n          const queryParams = { ...parsedUrl.query }\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n\n          const routeParamKeys = new Set<string>()\n          for (const [key, value] of Object.entries(parsedUrl.query)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            // Remove the prefixed key from the query params because we want\n            // to consume it for the dynamic route matcher.\n            delete parsedUrl.query[key]\n            routeParamKeys.add(normalizedKey)\n\n            if (typeof value === 'undefined') continue\n\n            queryParams[normalizedKey] = Array.isArray(value)\n              ? value.map((v) => decodeQueryPathParameter(v))\n              : decodeQueryPathParameter(value)\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            // If we don't already have valid params, try to parse them from\n            // the query params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                false\n              )\n            }\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams, false)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult = utils.normalizeDynamicRouteParams(\n                  matcherParams,\n                  false\n                )\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            const routeMatchesHeader = req.headers['x-now-route-matches']\n            if (\n              typeof routeMatchesHeader === 'string' &&\n              routeMatchesHeader &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const routeMatches =\n                utils.getParamsFromRouteMatches(routeMatchesHeader)\n\n              if (routeMatches) {\n                paramsResult = utils.normalizeDynamicRouteParams(\n                  routeMatches,\n                  true\n                )\n\n                if (paramsResult.hasValidParams) {\n                  params = paramsResult.params\n                }\n              }\n            }\n\n            // Try to parse the params from the query if we couldn't parse them\n            // from the route matches but ignore missing optional params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // If the pathname being requested is the same as the source\n            // pathname, and we don't have valid params, we want to use the\n            // default route matches.\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // If the route matches header is an empty string, we want to\n              // render a fallback shell. This is because we know this came from\n              // a prerender (it has the header) but it's values were filtered\n              // out (because the allowQuery was empty). If it was undefined\n              // then we know that the request is hitting the lambda directly.\n              if (routeMatchesHeader === '') {\n                addRequestMeta(req, 'renderFallbackShell', true)\n              }\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n\n              // If the request is for a segment prefetch, we need to update the\n              // segment prefetch request path to include the interpolated\n              // params.\n              let segmentPrefetchRSCRequest = getRequestMeta(\n                req,\n                'segmentPrefetchRSCRequest'\n              )\n              if (\n                segmentPrefetchRSCRequest &&\n                isDynamicRoute(segmentPrefetchRSCRequest, false)\n              ) {\n                segmentPrefetchRSCRequest = utils.interpolateDynamicPath(\n                  segmentPrefetchRSCRequest,\n                  params\n                )\n\n                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n                  segmentPrefetchRSCRequest\n                addRequestMeta(\n                  req,\n                  'segmentPrefetchRSCRequest',\n                  segmentPrefetchRSCRequest\n                )\n              }\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeCdnUrl(req, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          // Remove the route `params` keys from `parsedUrl.query` if they are\n          // not in the original query params.\n          // If it's used in both route `params` and query `searchParams`, it should be kept.\n          for (const key of routeParamKeys) {\n            if (!(key in originQueryParams)) {\n              delete parsedUrl.query[key]\n            }\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !getRequestMeta(req, 'locale')) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          addRequestMeta(req, 'locale', pathnameInfo.locale)\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          addRequestMeta(req, 'localeInferredFromDefault', true)\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n        })\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        // This is needed for pages router to leverage unstable_cache\n        // TODO: re-work this handling to not use global and use a AsyncStore\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      const cacheHandlers = getCacheHandlers()\n\n      if (cacheHandlers) {\n        await Promise.all(\n          [...cacheHandlers].map(async (cacheHandler) => {\n            if ('refreshTags' in cacheHandler) {\n              // Note: cacheHandler.refreshTags() is called lazily before the\n              // first cache entry is retrieved. It allows us to skip the\n              // refresh request if no caches are read at all.\n            } else {\n              const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n                req.headers,\n                this.getPrerenderManifest().preview.previewModeId\n              )\n\n              await cacheHandler.receiveExpiredTags(\n                ...previouslyRevalidatedTags\n              )\n            }\n          })\n        )\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath =\n        !useMatchedPathHeader &&\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          addRequestMeta(req, 'locale', invokePathnameInfo.locale)\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales\n        )\n\n        if (normalizeResult.detectedLocale) {\n          addRequestMeta(req, 'locale', normalizeResult.detectedLocale)\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          delete parsedUrl.query[key]\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        getRequestMeta(req, 'middlewareInvoke')\n      ) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the segment prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.segmentPrefetchRSC) {\n      normalizers.push(this.normalizers.segmentPrefetchRSC)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.nextConfig.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n    this.renderOpts.assetPrefix = this.nextConfig.assetPrefix\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    // Get instrumentation module\n    if (!this.instrumentation) {\n      this.instrumentation = await this.loadInstrumentationModule()\n    }\n    if (this.preparedPromise === null) {\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const ua = partialContext.req.headers['user-agent'] || ''\n\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        // `renderOpts.botType` is accumulated in `this.renderImpl()`\n        supportsDynamicResponse: !this.renderOpts.botType,\n        serveStreamingMetadata: shouldServeStreamingMetadata(\n          ua,\n          this.nextConfig.htmlLimitedBots\n        ),\n      },\n    }\n\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body, type } = payload\n    let { cacheControl } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        cacheControl = undefined\n      }\n\n      if (cacheControl && cacheControl.expire === undefined) {\n        cacheControl.expire = this.nextConfig.expireTime\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        type,\n        generateEtags,\n        poweredByHeader,\n        cacheControl,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.serverOptions.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    const ua = req.headers['user-agent'] || ''\n    this.renderOpts.botType = getBotType(ua)\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !getRequestMeta(req, 'isNextDataReq') &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.appendHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.appendHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    let hasGetStaticPaths = !!components.getStaticPaths\n    const isPossibleServerAction = getIsPossibleServerAction(req)\n    const hasGetInitialProps = !!components.Component?.getInitialProps\n    let isSSG = !!components.getStaticProps\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // Not all CDNs respect the Vary header when caching. We must assume that\n    // only the URL is used to vary the responses. The Next client computes a\n    // hash of the header values and sends it as a search param. Before\n    // responding to a request, we must verify that the hash matches the\n    // expected value. Neglecting to do this properly can lead to cache\n    // poisoning attacks on certain CDNs.\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders &&\n      isRSCRequest\n    ) {\n      const headers = req.headers\n      const expectedHash = computeCacheBustingSearchParam(\n        headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()],\n        headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()],\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()],\n        headers[NEXT_URL.toLowerCase()]\n      )\n      const actualHash =\n        getRequestMeta(req, 'cacheBustingSearchParam') ??\n        new URL(req.url || '', 'http://localhost').searchParams.get(\n          NEXT_RSC_UNION_QUERY\n        )\n\n      if (expectedHash !== actualHash) {\n        // The hash sent by the client does not match the expected value.\n        // Redirect to the URL with the correct cache-busting search param.\n        // This prevents cache poisoning attacks on CDNs that don't respect Vary headers.\n        // Note: When no headers are present, expectedHash is empty string and client\n        // must send `_rsc` param, otherwise actualHash is null and hash check fails.\n        const url = new URL(req.url || '', 'http://localhost')\n        setCacheBustingSearchParamWithHash(url, expectedHash)\n        res.statusCode = 307\n        res.setHeader('location', `${url.pathname}${url.search}`)\n        res.body('').send()\n        return null\n      }\n    }\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let fallbackMode: FallbackMode | undefined\n    let hasFallback = false\n\n    const isDynamic = isDynamicRoute(components.page)\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (isAppPath && isDynamic) {\n      const pathsResult = await this.getStaticPaths({\n        pathname,\n        page: components.page,\n        isAppPath,\n        requestHeaders: req.headers,\n      })\n\n      staticPaths = pathsResult.staticPaths\n      fallbackMode = pathsResult.fallbackMode\n      hasFallback = typeof fallbackMode !== 'undefined'\n\n      if (this.nextConfig.output === 'export') {\n        const page = components.page\n        if (!staticPaths) {\n          throw new Error(\n            `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n\n        const resolvedWithoutSlash = removeTrailingSlash(resolvedUrlPathname)\n        if (!staticPaths.includes(resolvedWithoutSlash)) {\n          throw new Error(\n            `Page \"${page}\" is missing param \"${resolvedWithoutSlash}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n      }\n\n      if (hasFallback) {\n        hasGetStaticPaths = true\n      }\n    }\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        getRequestMeta(req, 'isNextDataReq') ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    /**\n     * If true, this indicates that the request being made is for an app\n     * prefetch request.\n     */\n    const isPrefetchRSCRequest =\n      getRequestMeta(req, 'isPrefetchRSCRequest') ?? false\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    const locale = getRequestMeta(req, 'locale')\n    const defaultLocale = isSSG\n      ? this.nextConfig.i18n?.defaultLocale\n      : getRequestMeta(req, 'defaultLocale')\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery =\n      hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    const isDebugStaticShell: boolean =\n      hasDebugStaticShellQuery && isRoutePPREnabled\n\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses =\n      isDebugStaticShell && this.renderOpts.dev === true\n\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest =\n      isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = getRequestMeta(\n      req,\n      'segmentPrefetchRSCRequest'\n    )\n\n    const isHtmlBot = isHtmlBotRequest(req)\n    if (isHtmlBot && isRoutePPREnabled) {\n      isSSG = false\n      this.renderOpts.serveStreamingMetadata = false\n    }\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isPossibleServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      res.body('Method Not Allowed').send()\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        type: 'html',\n        // TODO: Static pages should be serialized as RenderResult\n        body: RenderResult.fromStatic(components.Component),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const ua = req.headers['user-agent'] || ''\n      const isBotRequest = isBot(ua)\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    const locales = this.nextConfig.i18n?.locales\n\n    let previewData: PreviewData\n    let isPreviewMode = false\n\n    if (hasServerProps || isSSG || isAppPath) {\n      // For the edge runtime, we don't support preview mode in SSG.\n      if (process.env.NEXT_RUNTIME !== 'edge') {\n        const { tryGetPreviewData } =\n          require('./api-utils/node/try-get-preview-data') as typeof import('./api-utils/node/try-get-preview-data')\n        previewData = tryGetPreviewData(\n          req,\n          res,\n          this.renderOpts.previewProps,\n          !!this.nextConfig.experimental.multiZoneDraftMode\n        )\n        isPreviewMode = previewData !== false\n      }\n    }\n\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (\n      isAppPath &&\n      !opts.dev &&\n      !isPreviewMode &&\n      isSSG &&\n      isRSCRequest &&\n      !isDynamicRSCRequest &&\n      (!isEdgeRuntime(opts.runtime) ||\n        (this.serverOptions as any).webServerConfig)\n    ) {\n      stripFlightHeaders(req.headers)\n    }\n\n    let { isOnDemandRevalidate, revalidateOnlyGenerated } =\n      checkIsOnDemandRevalidate(req, this.renderOpts.previewProps)\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    const handleRedirect = (pageData: any) => {\n      const redirect = {\n        destination: pageData.pageProps.__N_REDIRECT,\n        statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n        basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n      }\n      const statusCode = getRedirectStatus(redirect)\n      const { basePath } = this.nextConfig\n\n      if (\n        basePath &&\n        redirect.basePath !== false &&\n        redirect.destination.startsWith('/')\n      ) {\n        redirect.destination = `${basePath}${redirect.destination}`\n      }\n\n      if (redirect.destination.startsWith('/')) {\n        redirect.destination = normalizeRepeatedSlashes(redirect.destination)\n      }\n\n      res\n        .redirect(redirect.destination, statusCode)\n        .body(redirect.destination)\n        .send()\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    let ssgCacheKey: string | null = null\n    if (\n      !isPreviewMode &&\n      isSSG &&\n      !opts.supportsDynamicResponse &&\n      !isPossibleServerAction &&\n      !minimalPostponed &&\n      !isDynamicRSCRequest\n    ) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${\n        (pathname === '/' || resolvedUrlPathname === '/') && locale\n          ? ''\n          : resolvedUrlPathname\n      }${query.amp ? '.amp' : ''}`\n    }\n\n    if ((is404Page || is500Page) && isSSG) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${\n        query.amp ? '.amp' : ''\n      }`\n    }\n\n    if (ssgCacheKey) {\n      ssgCacheKey = decodePathParams(ssgCacheKey)\n\n      // ensure /index and / is normalized to one key\n      ssgCacheKey =\n        ssgCacheKey === '/index' && pathname === '/' ? '/' : ssgCacheKey\n    }\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      process.env.NEXT_RUNTIME === 'edge' &&\n      (globalThis as any).__incrementalCache\n        ? (globalThis as any).__incrementalCache\n        : await this.getIncrementalCache({\n            requestHeaders: Object.assign({}, req.headers),\n          })\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    type RendererContext = {\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      pagesFallback: boolean | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }\n    type Renderer = (\n      context: RendererContext\n    ) => Promise<ResponseCacheEntry | null>\n\n    const doRender: Renderer = async ({\n      postponed,\n      pagesFallback = false,\n      fallbackRouteParams,\n    }) => {\n      // In development, we always want to generate dynamic HTML.\n      let supportsDynamicResponse: boolean =\n        // If we're in development, we always support dynamic HTML, unless it's\n        // a data request, in which case we only produce static HTML.\n        (!isNextDataRequest && opts.dev === true) ||\n        // If this is not SSG or does not have static paths, then it supports\n        // dynamic HTML.\n        (!isSSG && !hasGetStaticPaths) ||\n        // If this request has provided postponed data, it supports dynamic\n        // HTML.\n        typeof postponed === 'string' ||\n        // If this is a dynamic RSC request, then this render supports dynamic\n        // HTML (it's dynamic).\n        isDynamicRSCRequest\n\n      const origQuery = parseUrl(req.url || '', true).query\n\n      // clear any dynamic route params so they aren't in\n      // the resolvedUrl\n      if (opts.params) {\n        Object.keys(opts.params).forEach((key) => {\n          delete origQuery[key]\n        })\n      }\n      const hadTrailingSlash =\n        urlPathname !== '/' && this.nextConfig.trailingSlash\n\n      const resolvedUrl = formatUrl({\n        pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,\n        // make sure to only add query values from original URL\n        query: origQuery,\n      })\n\n      // When html bots request PPR page, perform the full dynamic rendering.\n      const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n      const renderOpts: LoadedRenderOpts = {\n        ...components,\n        ...opts,\n        ...(isAppPath\n          ? {\n              incrementalCache,\n              // This is a revalidation request if the request is for a static\n              // page and it is not being resumed from a postponed render and\n              // it is not a dynamic RSC request then it is a revalidation\n              // request.\n              isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n              serverActions: this.nextConfig.experimental.serverActions,\n            }\n          : {}),\n        isNextDataRequest,\n        resolvedUrl,\n        locale,\n        locales,\n        defaultLocale,\n        multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n        // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on\n        // asPath\n        resolvedAsPath:\n          hasServerProps || hasGetInitialProps\n            ? formatUrl({\n                // we use the original URL pathname less the _next/data prefix if\n                // present\n                pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,\n                query: origQuery,\n              })\n            : resolvedUrl,\n        experimental: {\n          ...opts.experimental,\n          isRoutePPREnabled,\n        },\n        supportsDynamicResponse,\n        shouldWaitOnAllReady,\n        isOnDemandRevalidate,\n        isDraftMode: isPreviewMode,\n        isPossibleServerAction,\n        postponed,\n        waitUntil: this.getWaitUntil(),\n        onClose: res.onClose.bind(res),\n        onAfterTaskError: undefined,\n        // only available in dev\n        setIsrStatus: (this as any).setIsrStatus,\n      }\n\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        supportsDynamicResponse = false\n        renderOpts.nextExport = true\n        renderOpts.supportsDynamicResponse = false\n        renderOpts.isStaticGeneration = true\n        renderOpts.isRevalidate = true\n        renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses\n      }\n\n      // Legacy render methods will return a render result that needs to be\n      // served by the server.\n      let result: RenderResult\n\n      if (routeModule) {\n        if (\n          isAppRouteRouteModule(routeModule) ||\n          isPagesRouteModule(routeModule) ||\n          isAppPageRouteModule(routeModule)\n        ) {\n          // An OPTIONS request to a page handler is invalid.\n          if (\n            req.method === 'OPTIONS' &&\n            !is404Page &&\n            !isAppRouteRouteModule(routeModule)\n          ) {\n            await sendResponse(req, res, new Response(null, { status: 400 }))\n            return null\n          }\n\n          const request = isNodeNextRequest(req) ? req.originalRequest : req\n          const response = isNodeNextResponse(res) ? res.originalResponse : res\n\n          if (\n            components.ComponentMod.handler &&\n            process.env.NEXT_RUNTIME !== 'edge'\n          ) {\n            const parsedInitUrl = parseUrl(\n              getRequestMeta(req, 'initURL') || req.url\n            )\n            let initPathname = parsedInitUrl.pathname || '/'\n\n            for (const normalizer of [\n              this.normalizers.segmentPrefetchRSC,\n              this.normalizers.prefetchRSC,\n              this.normalizers.rsc,\n            ]) {\n              if (normalizer?.match(initPathname)) {\n                initPathname = normalizer.normalize(initPathname)\n              }\n            }\n\n            // On minimal mode, the request url of dynamic route can be a\n            // literal dynamic route ('/[slug]') instead of actual URL, so overwriting to initPathname\n            // will transform back the resolved url to the dynamic route pathname.\n            if (!(this.minimalMode && isErrorPathname)) {\n              request.url = `${initPathname}${parsedInitUrl.search || ''}`\n            }\n\n            // propagate the request context for dev\n            setRequestMeta(request, getRequestMeta(req))\n            addRequestMeta(request, 'projectDir', this.dir)\n            addRequestMeta(request, 'distDir', this.distDir)\n            addRequestMeta(request, 'isIsrFallback', pagesFallback)\n            addRequestMeta(request, 'query', query)\n            addRequestMeta(request, 'params', opts.params)\n            addRequestMeta(\n              request,\n              'ampValidator',\n              this.renderOpts.ampValidator\n            )\n            addRequestMeta(request, 'minimalMode', this.minimalMode)\n\n            if (renderOpts.err) {\n              addRequestMeta(request, 'invokeError', renderOpts.err)\n            }\n\n            const handler: (\n              req: ServerRequest | IncomingMessage,\n              res: ServerResponse | HTTPServerResponse,\n              ctx: {\n                waitUntil: ReturnType<Server['getWaitUntil']>\n              }\n            ) => Promise<RenderResult> = components.ComponentMod.handler\n\n            const maybeDevRequest =\n              // we need to capture fetch metrics when they are set\n              // and can't wait for handler to resolve as the fetch\n              // metrics are logged on response close which happens\n              // before handler resolves\n              process.env.NODE_ENV === 'development'\n                ? new Proxy(request, {\n                    get(target: any, prop) {\n                      if (typeof target[prop] === 'function') {\n                        return target[prop].bind(target)\n                      }\n                      return target[prop]\n                    },\n                    set(target: any, prop, value) {\n                      if (prop === 'fetchMetrics') {\n                        ;(req as any).fetchMetrics = value\n                      }\n                      target[prop] = value\n                      return true\n                    },\n                  })\n                : request\n\n            result = await handler(maybeDevRequest, response, {\n              waitUntil: this.getWaitUntil(),\n            })\n\n            // response is handled fully in handler\n            return null\n          } else {\n            if (isPagesRouteModule(routeModule)) {\n              // Due to the way we pass data by mutating `renderOpts`, we can't extend\n              // the object here but only updating its `clientReferenceManifest` and\n              // `nextFontManifest` properties.\n              // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n              renderOpts.nextFontManifest = this.nextFontManifest\n              renderOpts.clientReferenceManifest =\n                components.clientReferenceManifest\n\n              // Call the built-in render method on the module.\n              try {\n                result = await routeModule.render(\n                  request as any,\n                  response as any,\n                  {\n                    page: pathname,\n                    params: opts.params,\n                    query,\n                    renderOpts,\n                    sharedContext: {\n                      buildId: this.buildId,\n                      deploymentId: this.nextConfig.deploymentId,\n                      customServer:\n                        this.serverOptions.customServer || undefined,\n                    },\n                    renderContext: {\n                      isFallback: pagesFallback,\n                      isDraftMode: renderOpts.isDraftMode,\n                      developmentNotFoundSourcePage: getRequestMeta(\n                        req,\n                        'developmentNotFoundSourcePage'\n                      ),\n                    },\n                  }\n                )\n              } catch (err) {\n                await this.instrumentationOnRequestError(err, req, {\n                  routerKind: 'Pages Router',\n                  routePath: pathname,\n                  routeType: 'render',\n                  revalidateReason: getRevalidateReason({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n                  }),\n                })\n                throw err\n              }\n            } else {\n              const module = components.routeModule as AppPageRouteModule\n\n              // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n              // object here but only updating its `nextFontManifest` field.\n              // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n              renderOpts.nextFontManifest = this.nextFontManifest\n\n              const context: AppPageRouteHandlerContext = {\n                page: is404Page ? '/404' : pathname,\n                params: opts.params,\n                query,\n                fallbackRouteParams,\n                renderOpts,\n                serverComponentsHmrCache: this.getServerComponentsHmrCache(),\n                sharedContext: {\n                  buildId: this.buildId,\n                },\n              }\n\n              // TODO: adapt for putting the RDC inside the postponed data\n              // If we're in dev, and this isn't a prefetch or a server action,\n              // we should seed the resume data cache.\n              if (\n                this.nextConfig.experimental.dynamicIO &&\n                this.renderOpts.dev &&\n                !isPrefetchRSCRequest &&\n                !isPossibleServerAction\n              ) {\n                const warmup = await module.warmup(req, res, context)\n\n                // If the warmup is successful, we should use the resume data\n                // cache from the warmup.\n                if (warmup.metadata.renderResumeDataCache) {\n                  renderOpts.renderResumeDataCache =\n                    warmup.metadata.renderResumeDataCache\n                }\n              }\n\n              // Call the built-in render method on the module.\n              result = await module.render(req, res, context)\n            }\n          }\n        } else {\n          throw new Error('Invariant: Unknown route module type')\n        }\n      } else {\n        // If we didn't match a page, we should fallback to using the legacy\n        // render method.\n        result = await this.renderHTML(req, res, pathname, query, renderOpts)\n      }\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isAppPath &&\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !this.renderOpts.dev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${urlPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      // Based on the metadata, we can determine what kind of cache result we\n      // should return.\n\n      // Handle `isNotFound`.\n      if ('isNotFound' in metadata && metadata.isNotFound) {\n        return {\n          value: null,\n          cacheControl,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isRedirect`.\n      if (metadata.isRedirect) {\n        return {\n          value: {\n            kind: CachedRouteKind.REDIRECT,\n            props: metadata.pageData ?? metadata.flightData,\n          } satisfies CachedRedirectValue,\n          cacheControl,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isNull`.\n      if (result.isNull) {\n        return null\n      }\n\n      // We now have a valid HTML result that we can return to the user.\n      if (isAppPath) {\n        return {\n          value: {\n            kind: CachedRouteKind.APP_PAGE,\n            html: result,\n            headers,\n            rscData: metadata.flightData,\n            postponed: metadata.postponed,\n            status: metadata.statusCode,\n            segmentData: metadata.segmentData,\n          } satisfies CachedAppPageValue,\n          cacheControl,\n        } satisfies ResponseCacheEntry\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.PAGES,\n          html: result,\n          pageData: metadata.pageData ?? metadata.flightData,\n          headers,\n          status: isAppPath ? res.statusCode : undefined,\n        } satisfies CachedPageValue,\n        cacheControl,\n      }\n    }\n\n    let responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n    }): Promise<ResponseCacheEntry | null> => {\n      const isProduction = !this.renderOpts.dev\n      const didRespond = hasResolved || res.sent\n\n      // If we haven't found the static paths for the route, then do it now.\n      if (!staticPaths && isDynamic) {\n        if (hasGetStaticPaths) {\n          const pathsResult = await this.getStaticPaths({\n            pathname,\n            requestHeaders: req.headers,\n            isAppPath,\n            page: components.page,\n          })\n\n          staticPaths = pathsResult.staticPaths\n          fallbackMode = pathsResult.fallbackMode\n        } else {\n          staticPaths = undefined\n          fallbackMode = FallbackMode.NOT_FOUND\n        }\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (\n        fallbackMode === FallbackMode.PRERENDER &&\n        isBot(req.headers['user-agent'] || '')\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !this.minimalMode\n      ) {\n        await this.render404(req, res)\n        return null\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // We use `ssgCacheKey` here as it is normalized to match the encoding\n      // from getStaticPaths along with including the locale.\n      //\n      // We use the `resolvedUrlPathname` for the development case when this\n      // is an app path since it doesn't include locale information.\n      //\n      // We decode the `resolvedUrlPathname` to correctly match the app path\n      // with prerendered paths.\n      let staticPathKey = ssgCacheKey\n      if (!staticPathKey && opts.dev && isAppPath) {\n        staticPathKey = decodePathParams(resolvedUrlPathname)\n      }\n      if (staticPathKey && query.amp) {\n        staticPathKey = staticPathKey.replace(/\\.amp$/, '')\n      }\n\n      const isPageIncludedInStaticPaths =\n        staticPathKey && staticPaths?.includes(staticPathKey)\n\n      // When experimental compile is used, no pages have been prerendered,\n      // so they should all be blocking.\n\n      if (this.nextConfig.experimental.isExperimentalCompile) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // When we did not respond from cache, we need to choose to block on\n      // rendering or return a skeleton.\n      //\n      // - Data requests always block.\n      // - Blocking mode fallback always blocks.\n      // - Preview mode toggles all pages to be resolved in a blocking manner.\n      // - Non-dynamic pages should block (though this is an impossible\n      //   case in production).\n      // - Dynamic pages should return their skeleton if not defined in\n      //   getStaticPaths, then finish the data request on the client-side.\n      //\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        !this.minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isPreviewMode &&\n        isDynamic &&\n        (isProduction || !staticPaths || !isPageIncludedInStaticPaths)\n      ) {\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || (staticPaths && staticPaths?.length > 0)) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        // If this is a pages router page.\n        if (isPagesRouteModule(components.routeModule) && !isNextDataRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? (locale ? `/${locale}${pathname}` : pathname) : null,\n            // This is the response generator for the fallback shell.\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              // For the pages router, fallbacks cannot be revalidated or\n              // generated in production. In the case of a missing fallback,\n              // we return null, but if it's being revalidated, we just return\n              // the previous fallback cache entry. This preserves the previous\n              // behavior.\n              if (isProduction) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n\n              // We pass `undefined` and `null` as it doesn't apply to the pages\n              // router.\n              return doRender({\n                postponed: undefined,\n                // For the pages router, fallbacks can only be generated on\n                // demand in development, so if we're not in production, and we\n                // aren't a app path.\n                pagesFallback: true,\n                fallbackRouteParams: null,\n              })\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n        // If this is a app router page, PPR is enabled, and PPR is also\n        // enabled, then we should use the fallback renderer.\n        else if (\n          isRoutePPREnabled &&\n          isAppPageRouteModule(components.routeModule) &&\n          !isRSCRequest\n        ) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? pathname : null,\n            // This is the response generator for the fallback shell.\n            async () =>\n              doRender({\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                pagesFallback: undefined,\n                fallbackRouteParams:\n                  // If we're in production or we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(pathname)\n                    : null,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n\n        // If the fallback response was set to null, then we should return null.\n        if (fallbackResponse === null) return null\n\n        // Otherwise, if we did get a fallback response, we should return it.\n        if (fallbackResponse) {\n          // Remove the cache control from the response to prevent it from being\n          // used in the surrounding cache.\n          delete fallbackResponse.cacheControl\n\n          return fallbackResponse\n        }\n      }\n\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        isDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'renderFallbackShell') || isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        postponed,\n        pagesFallback: undefined,\n        fallbackRouteParams,\n      })\n    }\n\n    if (\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // default _error module in dev doesn't have handler yet\n      components.ComponentMod.handler &&\n      (isPagesRouteModule(components.routeModule) ||\n        isAppRouteRouteModule(components.routeModule) ||\n        isAppPageRouteModule(components.routeModule))\n    ) {\n      if (\n        routeModule?.isDev &&\n        isDynamicRoute(pathname) &&\n        (components.getStaticPaths || isAppPath)\n      ) {\n        await this.getStaticPaths({\n          pathname,\n          requestHeaders: req.headers,\n          page: components.page,\n          isAppPath,\n        })\n      }\n      await doRender({\n        postponed: undefined,\n        pagesFallback: false,\n        fallbackRouteParams: null,\n      })\n      return null\n    }\n\n    const cacheEntry = await this.responseCache.get(\n      ssgCacheKey,\n      responseGenerator,\n      {\n        routeKind:\n          // If the route module is not defined, we can assume it's a page being\n          // rendered and thus check isAppPath.\n          routeModule?.definition.kind ??\n          (isAppPath ? RouteKind.APP_PAGE : RouteKind.PAGES),\n        incrementalCache,\n        isOnDemandRevalidate,\n        isPrefetch: req.headers.purpose === 'prefetch',\n        isRoutePPREnabled,\n      }\n    )\n\n    if (isPreviewMode) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    if (!cacheEntry) {\n      if (\n        ssgCacheKey &&\n        !(isOnDemandRevalidate && revalidateOnlyGenerated) &&\n        !isPagesRouteModule(components.routeModule) &&\n        !isAppRouteRouteModule(components.routeModule) &&\n        !isAppPageRouteModule(components.routeModule)\n      ) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n      return null\n    }\n\n    const didPostpone =\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      typeof cacheEntry.value.postponed === 'string'\n\n    if (\n      isSSG &&\n      // We don't want to send a cache header for requests that contain dynamic\n      // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n      // request, then we should set the cache header.\n      !isDynamicRSCRequest &&\n      (!didPostpone || isPrefetchRSCRequest)\n    ) {\n      if (!this.minimalMode) {\n        // set x-nextjs-cache header to match the header\n        // we set for the image-optimizer\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n      // Set a header used by the client router to signal the response is static\n      // and should respect the `static` cache staleTime value.\n      res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n    }\n\n    const { value: cachedData } = cacheEntry\n\n    // If the cache value is an image, we should error early.\n    if (cachedData?.kind === CachedRouteKind.IMAGE) {\n      throw new InvariantError('SSG should not return an image cache value')\n    }\n\n    // Coerce the cache control parameter from the render.\n    let cacheControl: CacheControl | undefined\n\n    // If this is a resume request in minimal mode it is streamed with dynamic\n    // content and should not be cached.\n    if (minimalPostponed) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    }\n\n    // If this is in minimal mode and this is a flight request that isn't a\n    // prefetch request while PPR is enabled, it cannot be cached as it contains\n    // dynamic content.\n    else if (\n      this.minimalMode &&\n      isRSCRequest &&\n      !isPrefetchRSCRequest &&\n      isRoutePPREnabled\n    ) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    } else if (!this.renderOpts.dev || (hasServerProps && !isNextDataRequest)) {\n      // If this is a preview mode request, we shouldn't cache it\n      if (isPreviewMode) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this isn't SSG, then we should set change the header only if it is\n      // not set already.\n      else if (!isSSG) {\n        if (!res.getHeader('Cache-Control')) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n      }\n\n      // If we are rendering the 404 page we derive the cache-control\n      // revalidate period from the value that trigged the not found\n      // to be rendered. So if `getStaticProps` returns\n      // { notFound: true, revalidate 60 } the revalidate period should\n      // be 60 but if a static asset 404s directly it should have a revalidate\n      // period of 0 so that it doesn't get cached unexpectedly by a CDN\n      else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n\n        cacheControl = {\n          revalidate:\n            typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n          expire: undefined,\n        }\n      } else if (is500Page) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (cacheEntry.cacheControl) {\n        // If the cache entry has a cache control with a revalidate value that's\n        // a number, use it.\n        if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n          if (cacheEntry.cacheControl.revalidate < 1) {\n            throw new Error(\n              `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n            )\n          }\n\n          cacheControl = {\n            revalidate: cacheEntry.cacheControl.revalidate,\n            expire:\n              cacheEntry.cacheControl?.expire ?? this.nextConfig.expireTime,\n          }\n        }\n        // Otherwise if the revalidate value is false, then we should use the\n        // cache time of one year.\n        else {\n          cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n        }\n      }\n    }\n\n    cacheEntry.cacheControl = cacheControl\n\n    if (\n      typeof segmentPrefetchHeader === 'string' &&\n      cachedData?.kind === CachedRouteKind.APP_PAGE &&\n      cachedData.segmentData\n    ) {\n      // This is a prefetch request issued by the client Segment Cache. These\n      // should never reach the application layer (lambda). We should either\n      // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n      // Set a header to indicate that PPR is enabled for this route. This\n      // lets the client distinguish between a regular cache miss and a cache\n      // miss due to PPR being disabled. In other contexts this header is used\n      // to indicate that the response contains dynamic data, but here we're\n      // only using it to indicate that the feature is enabled — the segment\n      // response itself contains whether the data is dynamic.\n      res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (this.minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n      if (matchedSegment !== undefined) {\n        // Cache hit\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(matchedSegment),\n          // TODO: Eventually this should use cache control of the individual\n          // segment, not the whole page.\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // Cache miss. Either a cache entry for this route has not been generated\n      // (which technically should not be possible when PPR is enabled, because\n      // at a minimum there should always be a fallback entry) or there's no\n      // match for the requested segment. Respond with a 204 No Content. We\n      // don't bother to respond with 404, because these requests are only\n      // issued as part of a prefetch.\n      res.statusCode = 204\n      return {\n        type: 'rsc',\n        body: RenderResult.fromStatic(''),\n        cacheControl: cacheEntry?.cacheControl,\n      }\n    }\n\n    // If there's a callback for `onCacheEntry`, call it with the cache entry\n    // and the revalidate options.\n    const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n    if (onCacheEntry) {\n      const finished = await onCacheEntry(\n        {\n          ...cacheEntry,\n          // TODO: remove this when upstream doesn't\n          // always expect this value to be \"PAGE\"\n          value: {\n            ...cacheEntry.value,\n            kind:\n              cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n                ? 'PAGE'\n                : cacheEntry.value?.kind,\n          },\n        },\n        {\n          url: getRequestMeta(req, 'initURL'),\n        }\n      )\n      if (finished) {\n        // TODO: maybe we have to end the request?\n        return null\n      }\n    }\n\n    if (!cachedData) {\n      // add revalidate metadata before rendering 404 page\n      // so that we can use this as source of truth for the\n      // cache-control header instead of what the 404 page returns\n      // for the revalidate value\n      addRequestMeta(\n        req,\n        'notFoundRevalidate',\n        cacheEntry.cacheControl?.revalidate\n      )\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n      if (isNextDataRequest) {\n        res.statusCode = 404\n        res.body('{\"notFound\":true}').send()\n        return null\n      }\n\n      if (this.renderOpts.dev) {\n        addRequestMeta(req, 'developmentNotFoundSourcePage', pathname)\n      }\n      await this.render404(req, res, { pathname, query }, false)\n      return null\n    } else if (cachedData.kind === CachedRouteKind.REDIRECT) {\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      if (isNextDataRequest) {\n        return {\n          type: 'json',\n          body: RenderResult.fromStatic(\n            // @TODO: Handle flight data.\n            JSON.stringify(cachedData.props)\n          ),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      } else {\n        await handleRedirect(cachedData.props)\n        return null\n      }\n    } else if (cachedData.kind === CachedRouteKind.APP_ROUTE) {\n      // this is handled inside the app_route handler fully\n      throw new Error(`Invariant: unexpected APP_ROUTE cache data`)\n    } else if (cachedData.kind === CachedRouteKind.APP_PAGE) {\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!this.minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (this.minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n      if (\n        !this.minimalMode &&\n        cachedData.status &&\n        RedirectStatusCode[cachedData.status] &&\n        isRSCRequest\n      ) {\n        res.statusCode = 200\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isPreviewMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return {\n            type: 'rsc',\n            body: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          }\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(cachedData.rscData),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || this.minimalMode) {\n        return {\n          type: 'html',\n          body,\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return {\n          type: 'html',\n          body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        }\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        postponed: cachedData.postponed,\n        pagesFallback: undefined,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return {\n        type: 'html',\n        body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      }\n    } else if (isNextDataRequest) {\n      return {\n        type: 'json',\n        body: RenderResult.fromStatic(JSON.stringify(cachedData.pageData)),\n        cacheControl: cacheEntry.cacheControl,\n      }\n    } else {\n      return {\n        type: 'html',\n        body: cachedData.html,\n        cacheControl: cacheEntry.cacheControl,\n      }\n    }\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      locale: getRequestMeta(ctx.req, 'locale'),\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): Promise<MiddlewareRoutingItem | undefined>\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { req, res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback =\n      getRequestMeta(ctx.req, 'bubbleNoFallback') ?? false\n\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders\n    ) {\n      addRequestMeta(\n        ctx.req,\n        'cacheBustingSearchParam',\n        query[NEXT_RSC_UNION_QUERY]\n      )\n    }\n    delete query[NEXT_RSC_UNION_QUERY]\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromRequest(req, pathname),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        addRequestMeta(ctx.req, 'customErrorRender', true)\n        await this.renderErrorToResponse(ctx, err)\n        removeRequestMeta(ctx.req, 'customErrorRender')\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (\n          (this.minimalMode && process.env.NEXT_RUNTIME !== 'edge') ||\n          this.renderOpts.dev\n        ) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    const middleware = await this.getMiddleware()\n    if (\n      middleware &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      const locale = getRequestMeta(req, 'locale')\n\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('content-type', 'application/json')\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic(''),\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !getRequestMeta(ctx.req, 'customErrorRender') &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          locale: getRequestMeta(ctx.req, 'locale'),\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            type: 'html',\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic('Internal Server Error'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    // Ensure the locales are provided on the request meta.\n    if (this.nextConfig.i18n) {\n      if (!getRequestMeta(req, 'locale')) {\n        addRequestMeta(req, 'locale', this.nextConfig.i18n.defaultLocale)\n      }\n      addRequestMeta(req, 'defaultLocale', this.nextConfig.i18n.defaultLocale)\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["getFallbackRouteParams", "CachedRouteKind", "NormalizeError", "DecodeError", "normalizeRepeatedSlashes", "MissingStaticPage", "format", "formatUrl", "parse", "parseUrl", "formatHostname", "getRedirectStatus", "isEdgeRuntime", "APP_PATHS_MANIFEST", "NEXT_BUILTIN_DOCUMENT", "PAGES_MANIFEST", "STATIC_STATUS_PAGES", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "isDynamicRoute", "checkIsOnDemandRevalidate", "setConfig", "getCacheControlHeader", "execOnce", "isBlockedPage", "getBotType", "isBot", "RenderResult", "removeTrailingSlash", "denormalizePagePath", "Log", "getPreviouslyRevalidatedTags", "getServerUtils", "isError", "getProperError", "addRequestMeta", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "removePathPrefix", "normalizeAppPath", "getHostname", "parseUrlUtil", "getNextPathnameInfo", "RSC_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_URL", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_IS_PRERENDER_HEADER", "LocaleRouteNormalizer", "DefaultRouteMatcherManager", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "PagesRouteMatcherProvider", "ServerManifestLoader", "getTracer", "isBubbledError", "SpanKind", "BaseServerSpan", "I18NProvider", "sendResponse", "normalizeNextQueryParam", "CACHE_ONE_YEAR", "MATCHED_PATH_HEADER", "NEXT_CACHE_TAGS_HEADER", "NEXT_RESUME_HEADER", "normalizeLocalePath", "matchNextDataPathname", "getRouteFromAssetPath", "decodePathParams", "RSCPathnameNormalizer", "stripFlightHeaders", "isAppPageRouteModule", "isAppRouteRouteModule", "isPagesRouteModule", "PrefetchRSCPathnameNormalizer", "NextDataPathnameNormalizer", "getIsPossibleServerAction", "isInterceptionRouteAppPath", "toRoute", "isNodeNextRequest", "isNodeNextResponse", "patchSetHeaderWithCookieSupport", "checkIsAppPPREnabled", "getBuiltinRequestContext", "ENCODED_TAGS", "NextRequestHint", "getRevalidateReason", "RouteKind", "FallbackMode", "parseFallbackField", "toResponseCacheEntry", "SegmentPrefixRSCPathnameNormalizer", "shouldServeStreamingMetadata", "isHtmlBotRequest", "InvariantError", "decodeQueryPathParameter", "NoFallbackError", "getCacheHandlers", "fixMojibake", "computeCacheBustingSearchParam", "RedirectStatusCode", "setCacheBustingSearchParamWithHash", "WrappedBuildError", "Error", "constructor", "innerError", "Server", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "segmentPrefetchRSC", "match", "result", "extract", "originalPathname", "segmentPath", "headers", "toLowerCase", "prefetchRSC", "normalize", "rsc", "segmentPrefetchRSCRequest", "url", "parsed", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "path", "buildId", "process", "env", "NEXT_RUNTIME", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "localeNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "ppr", "isAppSegmentPrefetchEnabled", "clientSegmentCache", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "strictNextHead", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "htmlLimitedBots", "expireTime", "staleTimes", "clientTraceMetadata", "dynamicIO", "Boolean", "dynamicOnHover", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "devtoolSegmentExplorer", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "reloadMatchers", "manifest<PERSON><PERSON>der", "name", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "withPropagatedContext", "trace", "spanName", "kind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "redirect", "body", "send", "query", "URLSearchParams", "xForwardedProto", "isHttps", "socket", "encrypted", "toString", "remoteAddress", "attachRequestMeta", "replace", "pathnameInfo", "useMatchedPathHeader", "<PERSON><PERSON><PERSON>", "URL", "urlPathname", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "localeAnalysisResult", "inferredFromDefault", "srcPathname", "pageIsDynamic", "paramsResult", "hasValidParams", "definition", "utils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "originQueryParams", "pathnameBeforeRewrite", "rewriteParamKeys", "handleRewrites", "queryParams", "didRewrite", "routeParamKeys", "Set", "key", "value", "normalizedKey", "add", "Array", "isArray", "map", "v", "normalizeDynamicRouteParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "routeMatchesHeader", "routeMatches", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeCdnUrl", "defaultRouteRegex", "groups", "renderError", "webServerConfig", "incrementalCache", "getIncrementalCache", "requestHeaders", "resetRequestCache", "__incrementalCache", "cacheHandlers", "Promise", "all", "cache<PERSON><PERSON><PERSON>", "previouslyRevalidatedTags", "previewModeId", "receiveExpiredTags", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "response", "Response", "bubble", "run", "code", "getRequestHandlerWithMetadata", "meta", "handler", "getRequestHandler", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "ua", "botType", "serveStreamingMetadata", "payload", "originalStatus", "type", "cacheControl", "sent", "<PERSON><PERSON><PERSON><PERSON>", "expire", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "waitUntil", "getInternalWaitUntil", "startsWith", "customServer", "hasPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "addedNextUrlToVary", "append<PERSON><PERSON>er", "opts", "components", "prerenderManifest", "cacheEntry", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "hasGetStaticPaths", "isPossibleServerAction", "hasGetInitialProps", "Component", "getInitialProps", "isSSG", "getStaticProps", "validateRSCRequestHeaders", "expectedHash", "actualHash", "searchParams", "search", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "isDynamic", "pathsResult", "resolvedWithoutSlash", "includes", "routes", "isNextDataRequest", "isPrefetchRSCRequest", "routeModule", "couldSupportPPR", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "isDynamicRSCRequest", "segmentPrefetchHeader", "isHtmlBot", "parseInt", "slice", "fromStatic", "isBotRequest", "isSupportedDocument", "Document", "previewData", "isPreviewMode", "tryGetPreviewData", "multiZoneDraftMode", "runtime", "isOnDemandRevalidate", "revalidateOnlyGenerated", "handleRedirect", "pageData", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "ssgCacheKey", "doR<PERSON>", "pagesFallback", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hadTrailingSlash", "resolvedUrl", "shouldWaitOnAllReady", "isRevalidate", "serverActions", "resolvedAsPath", "isDraftMode", "onClose", "onAfterTaskError", "setIsrStatus", "nextExport", "isStaticGeneration", "status", "request", "ComponentMod", "parsedInitUrl", "initPathname", "ampValidator", "maybeDevRequest", "NODE_ENV", "Proxy", "target", "prop", "set", "fetchMetrics", "clientReferenceManifest", "sharedContext", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "developmentNotFoundSourcePage", "routerKind", "routePath", "routeType", "revalidateReason", "module", "context", "warmup", "metadata", "renderResumeDataCache", "renderHTML", "fetchTags", "cacheTags", "revalidate", "staticBailoutInfo", "description", "stack", "message", "indexOf", "isNotFound", "isRedirect", "REDIRECT", "props", "flightData", "isNull", "APP_PAGE", "html", "rscData", "segmentData", "PAGES", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "NOT_FOUND", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "static<PERSON><PERSON><PERSON><PERSON>", "isPageIncludedInStaticPaths", "fallbackResponse", "previousFallbackCacheEntry", "routeKind", "isDev", "isPrefetch", "purpose", "didPostpone", "isMiss", "cachedData", "IMAGE", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "tags", "matchedSegment", "onCacheEntry", "JSON", "stringify", "APP_ROUTE", "chain", "ReadableStream", "start", "controller", "enqueue", "CLOSED", "BODY_AND_HTML", "transformer", "TransformStream", "readable", "pipeTo", "writable", "catch", "abort", "e", "stripLocale", "splitPath", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "fromRequest", "matchAll", "invokeOutput", "initUrl", "rewroteUrl", "renderErrorToResponse", "isWrappedError", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "is404", "using404Page", "statusPage", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": "AAIA,SAEEA,sBAAsB,QACjB,4BAA2B;AAalC,SAOEC,eAAe,QAEV,mBAAkB;AAEzB,SACEC,cAAc,EACdC,WAAW,EACXC,wBAAwB,EACxBC,iBAAiB,QACZ,sBAAqB;AA0B5B,SAASC,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SACEC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,mBAAmB,EACnBC,0BAA0B,EAC1BC,gCAAgC,QAC3B,0BAAyB;AAChC,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,yBAAyB,QAAQ,cAAa;AACvD,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,qBAAqB,QAA2B,sBAAqB;AAC9E,SAASC,QAAQ,QAAQ,sBAAqB;AAC9C,SAASC,aAAa,QAAQ,UAAS;AACvC,SAASC,UAAU,EAAEC,KAAK,QAAQ,oCAAmC;AACrE,OAAOC,kBAAkB,kBAAiB;AAC1C,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,4BAA4B,EAAEC,cAAc,QAAQ,iBAAgB;AAC7E,OAAOC,WAAWC,cAAc,QAAQ,kBAAiB;AACzD,SACEC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,cAAc,QACT,iBAAgB;AACvB,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,WAAW,QAAQ,6BAA4B;AACxD,SAAShC,YAAYiC,YAAY,QAAQ,uCAAsC;AAC/E,SAASC,mBAAmB,QAAQ,oDAAmD;AACvF,SACEC,UAAU,EACVC,oBAAoB,EACpBC,2BAA2B,EAC3BC,mCAAmC,EACnCC,wBAAwB,EACxBC,QAAQ,EACRC,6BAA6B,EAC7BC,wBAAwB,QACnB,0CAAyC;AAKhD,SAASC,qBAAqB,QAAQ,wCAAuC;AAC7E,SAASC,0BAA0B,QAAQ,yDAAwD;AACnG,SAASC,2BAA2B,QAAQ,4DAA2D;AACvG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,yBAAyB,QAAQ,yDAAwD;AAClG,SAASC,oBAAoB,QAAQ,4EAA2E;AAChH,SAASC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,qBAAoB;AACxE,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,YAAY,QAAQ,sBAAqB;AAClD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,uBAAuB,QAAQ,cAAa;AACrD,SACEC,cAAc,EACdC,mBAAmB,EACnBC,sBAAsB,EACtBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,OAAOC,2BAA2B,uDAAsD;AACxF,SAASC,gBAAgB,QAAQ,wCAAuC;AACxE,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,kBAAkB,QAAQ,oCAAmC;AACtE,SACEC,oBAAoB,EACpBC,qBAAqB,EACrBC,kBAAkB,QACb,yBAAwB;AAC/B,SAASC,6BAA6B,QAAQ,qCAAoC;AAClF,SAASC,0BAA0B,QAAQ,kCAAiC;AAC5E,SAASC,yBAAyB,QAAQ,mCAAkC;AAC5E,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,OAAO,QAAQ,iBAAgB;AAExC,SAASC,iBAAiB,EAAEC,kBAAkB,QAAQ,sBAAqB;AAC3E,SAASC,+BAA+B,QAAQ,yBAAwB;AACxE,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SACEC,wBAAwB,QAEnB,kCAAiC;AACxC,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,eAAe,QAAQ,gBAAe;AAC/C,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,SAAS,QAAQ,eAAc;AAExC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;AAClE,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,kCAAkC,QAAQ,2CAA0C;AAC7F,SACEC,4BAA4B,EAC5BC,gBAAgB,QACX,2BAA0B;AACjC,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,wBAAwB,QAAQ,oCAAmC;AAC5E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,WAAW,QAAQ,qBAAoB;AAChD,SAASC,8BAA8B,QAAQ,wDAAuD;AACtG,SAASC,kBAAkB,QAAQ,4CAA2C;AAC9E,SAASC,kCAAkC,QAAQ,qEAAoE;AAmIvH,sDAAsD;AACtD,uDAAuD;AACvD,OAAO,MAAMC,0BAA0BC;IAGrCC,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAaA,eAAe,MAAeC;IAiGlBC,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACxD,AAACC,WAAmBC,0BAA0B,GAC9CC;IACN;IAsBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YA0CrB,uBAwEE,mCAQL;aAkEXC,mBAAgE,CACtEC,KACAC,MACAC;gBAII,sCAkBO,+BAWA;YA/BX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,KAAI,uCAAA,IAAI,CAACC,WAAW,CAACC,kBAAkB,qBAAnC,qCAAqCC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClE,MAAMI,SAAS,IAAI,CAACH,WAAW,CAACC,kBAAkB,CAACG,OAAO,CACxDN,UAAUC,QAAQ;gBAEpB,IAAI,CAACI,QAAQ,OAAO;gBAEpB,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGH;gBAC1CL,UAAUC,QAAQ,GAAGM;gBAErB,iDAAiD;gBACjDT,IAAIW,OAAO,CAACxF,WAAWyF,WAAW,GAAG,GAAG;gBACxCZ,IAAIW,OAAO,CAACtF,4BAA4BuF,WAAW,GAAG,GAAG;gBACzDZ,IAAIW,OAAO,CAACrF,oCAAoCsF,WAAW,GAAG,GAC5DF;gBAEFhG,eAAesF,KAAK,gBAAgB;gBACpCtF,eAAesF,KAAK,wBAAwB;gBAC5CtF,eAAesF,KAAK,6BAA6BU;YACnD,OAAO,KAAI,gCAAA,IAAI,CAACN,WAAW,CAACS,WAAW,qBAA5B,8BAA8BP,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClED,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACS,WAAW,CAACC,SAAS,CACzDZ,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIW,OAAO,CAACxF,WAAWyF,WAAW,GAAG,GAAG;gBACxCZ,IAAIW,OAAO,CAACtF,4BAA4BuF,WAAW,GAAG,GAAG;gBACzDlG,eAAesF,KAAK,gBAAgB;gBACpCtF,eAAesF,KAAK,wBAAwB;YAC9C,OAAO,KAAI,wBAAA,IAAI,CAACI,WAAW,CAACW,GAAG,qBAApB,sBAAsBT,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACW,GAAG,CAACD,SAAS,CACjDZ,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIW,OAAO,CAACxF,WAAWyF,WAAW,GAAG,GAAG;gBACxClG,eAAesF,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIW,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;gBACvCzD,mBAAmB8C,IAAIW,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIX,IAAIW,OAAO,CAACxF,WAAWyF,WAAW,GAAG,KAAK,KAAK;gBACxDlG,eAAesF,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIW,OAAO,CAACtF,4BAA4BuF,WAAW,GAAG,KAAK,KAAK;oBAClElG,eAAesF,KAAK,wBAAwB;oBAE5C,MAAMgB,4BACJhB,IAAIW,OAAO,CAACrF,oCAAoCsF,WAAW,GAAG;oBAChE,IAAI,OAAOI,8BAA8B,UAAU;wBACjDtG,eACEsF,KACA,6BACAgB;oBAEJ;gBACF;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIhB,IAAIiB,GAAG,EAAE;gBACX,MAAMC,SAASlI,SAASgH,IAAIiB,GAAG;gBAC/BC,OAAOf,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIiB,GAAG,GAAGnI,UAAUoI;YACtB;YAEA,OAAO;QACT;aAEQC,wBACN,OAAOnB,KAAKoB,KAAKlB;YACf,MAAMmB,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,MAAMC,SAASzE,sBAAsBoD,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAACoB,UAAU,CAACA,OAAOC,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAID,OAAOC,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IACEC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BjH,eAAeqF,KAAK,qBACpB;oBACA,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAAC6B,SAAS,CAAC7B,KAAKoB,KAAKlB;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1BqB,OAAOC,IAAI,CAACM,KAAK;YAEjB,MAAMC,YAAYR,OAAOC,IAAI,CAACD,OAAOC,IAAI,CAACQ,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAAC7B,KAAKoB,KAAKlB;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAEoB,OAAOC,IAAI,CAACU,IAAI,CAAC,MAAM;YAC1C/B,WAAWpD,sBAAsBoD,UAAU;YAE3C,iDAAiD;YACjD,IAAIkB,YAAY;gBACd,IAAI,IAAI,CAAC7B,UAAU,CAAC2C,aAAa,IAAI,CAAChC,SAAS8B,QAAQ,CAAC,MAAM;oBAC5D9B,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAAC2C,aAAa,IAC9BhC,SAAS6B,MAAM,GAAG,KAClB7B,SAAS8B,QAAQ,CAAC,MAClB;oBACA9B,WAAWA,SAASiC,SAAS,CAAC,GAAGjC,SAAS6B,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACK,YAAY,EAAE;oBAEJrC;gBADjB,gDAAgD;gBAChD,MAAMsC,WAAWtC,wBAAAA,oBAAAA,IAAKW,OAAO,CAAC4B,IAAI,qBAAjBvC,kBAAmBwC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC5B,WAAW;gBAEhE,MAAM6B,eAAe,IAAI,CAACJ,YAAY,CAACK,kBAAkB,CAACJ;gBAC1D,MAAMK,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,KAAI,IAAI,CAACN,YAAY,CAACO,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACR,YAAY,CAACS,OAAO,CAAC3C;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAI0C,iBAAiBE,cAAc,EAAE;oBACnC5C,WAAW0C,iBAAiB1C,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChEzF,eAAesF,KAAK,UAAU6C,iBAAiBE,cAAc;gBAC7DrI,eAAesF,KAAK,iBAAiB2C;gBAErC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpCnI,kBAAkBoF,KAAK;gBACzB;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAAC6C,iBAAiBE,cAAc,IAAI,CAAC1B,YAAY;oBACnD3G,eAAesF,KAAK,UAAU2C;oBAC9B,MAAM,IAAI,CAACd,SAAS,CAAC7B,KAAKoB,KAAKlB;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrBzF,eAAesF,KAAK,iBAAiB;YAErC,OAAO;QACT;aAEQgD,yBAGN,IAAM;aAEAC,8BAGN,IAAM;aAEAC,kCAGN,IAAM;QAwxBV;;;;;;GAMC,QACOpC,YAAY,CAACX;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAAC+C,IAAI,EAAE;gBACzB/C,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAAC+C,IAAI;YACxC;YAEA,2EAA2E;YAC3E,qEAAqE;YACrE,IAAI,IAAI,CAAC/C,WAAW,CAACC,kBAAkB,EAAE;gBACvCD,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAACC,kBAAkB;YACtD;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAACD,WAAW,CAACS,WAAW,EAAE;gBAChCT,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAACS,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACT,WAAW,CAACW,GAAG,EAAE;gBACxBX,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAACW,GAAG;YACvC;YAEA,KAAK,MAAMsC,cAAcjD,YAAa;gBACpC,IAAI,CAACiD,WAAW/C,KAAK,CAACH,WAAW;gBAEjC,OAAOkD,WAAWvC,SAAS,CAACX,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQmD,6BAGJ,OAAOtD,KAAKoB,KAAKH;YACnB,IAAIsC,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAAChD,KAAKoB,KAAKH;YAC3D,IAAIsC,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAACpC,qBAAqB,CAACnB,KAAKoB,KAAKH;gBACtD,IAAIsC,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAiCUG,WAAoB;aACpBC,kBAAwC;aAqtE1CC,uBAAuB9J,SAAS;YACtCO,IAAIwJ,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QAj8GE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnB5B,QAAQ,EACR6B,IAAI,EACJC,qBAAqB,EACtB,GAAGtE;QAEJ,IAAI,CAACsE,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGvE;QAErB,IAAI,CAACgE,GAAG,GACNpC,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzBkC,MACA,AAACQ,QAAQ,QAAkCC,OAAO,CAACT;QAEzD,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACS,aAAa,CAAC;YAAEP;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACzE,UAAU,GAAGwE;QAClB,IAAI,CAAC1B,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAACmC,aAAa,GAAGxL,eAAe,IAAI,CAACqJ,QAAQ;QACnD;QACA,IAAI,CAAC6B,IAAI,GAAGA;QACZ,IAAI,CAACO,OAAO,GACVhD,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,IAAI,CAACpC,UAAU,CAACkF,OAAO,GACvB,AAACJ,QAAQ,QAAkCpC,IAAI,CAC7C,IAAI,CAAC4B,GAAG,EACR,IAAI,CAACtE,UAAU,CAACkF,OAAO;QAE/B,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACX,eAAe,IAAI,CAACY,eAAe;QAExD,IAAI,CAACzC,YAAY,GAAG,EAAA,wBAAA,IAAI,CAAC7C,UAAU,CAACuF,IAAI,qBAApB,sBAAsBC,OAAO,IAC7C,IAAI1I,aAAa,IAAI,CAACkD,UAAU,CAACuF,IAAI,IACrClF;QAEJ,yEAAyE;QACzE,IAAI,CAACoF,gBAAgB,GAAG,IAAI,CAAC5C,YAAY,GACrC,IAAI1G,sBAAsB,IAAI,CAAC0G,YAAY,IAC3CxC;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJqF,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC7F,UAAU;QAEnB,IAAI,CAACiC,OAAO,GAAG,IAAI,CAAC6D,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBrB,eAAe,CAAC,CAACxC,QAAQC,GAAG,CAAC6D,yBAAyB;QAExD,IAAI,CAAChC,kBAAkB,GAAG,IAAI,CAACiC,qBAAqB,CAACxB;QAErD,IAAI,CAACyB,eAAe,GAClB,IAAI,CAAClC,kBAAkB,CAACmC,GAAG,IAC3B7H,qBAAqB,IAAI,CAAC0B,UAAU,CAACC,YAAY,CAACmG,GAAG;QAEvD,IAAI,CAACC,2BAA2B,GAC9B,IAAI,CAACrC,kBAAkB,CAACmC,GAAG,IAC3B,IAAI,CAACnG,UAAU,CAACC,YAAY,CAACqG,kBAAkB,KAAK;QAEtD,IAAI,CAAC1F,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCW,KACE,IAAI,CAACyC,kBAAkB,CAACmC,GAAG,IAAI,IAAI,CAACzB,WAAW,GAC3C,IAAIjH,0BACJ4C;YACNgB,aACE,IAAI,CAAC6E,eAAe,IAAI,IAAI,CAACxB,WAAW,GACpC,IAAI5G,kCACJuC;YACNQ,oBACE,IAAI,CAACwF,2BAA2B,IAAI,IAAI,CAAC3B,WAAW,GAChD,IAAI3F,uCACJsB;YACNsD,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,IAAIlG,2BAA2B,IAAI,CAACkE,OAAO,IAC3C5B;QACN;QAEA,IAAI,CAACkG,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAEhD,IAAItE,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvCF,QAAQC,GAAG,CAACsE,kBAAkB,GAAG,IAAI,CAACzG,UAAU,CAAC0G,YAAY,IAAI;QACnE;QAEA,IAAI,CAACC,UAAU,GAAG;YAChBrC,KAAK,IAAI,CAACA,GAAG;YACbsC,yBAAyB;YACzBjE,eAAe,IAAI,CAAC3C,UAAU,CAAC2C,aAAa;YAC5C+D,cAAc,IAAI,CAAC1G,UAAU,CAAC0G,YAAY;YAC1CG,gBAAgB,IAAI,CAAC7G,UAAU,CAACC,YAAY,CAAC4G,cAAc,IAAI;YAC/DC,iBAAiB,IAAI,CAAC9G,UAAU,CAAC8G,eAAe;YAChDC,eAAe,IAAI,CAAC/G,UAAU,CAACgH,GAAG,CAACD,aAAa,IAAI;YACpDlB;YACAoB,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjDC,kBAAkB,GAAE,oCAAA,IAAI,CAACpH,UAAU,CAACC,YAAY,CAAC+G,GAAG,qBAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAACtH,UAAU,CAACsH,QAAQ;YAClCC,QAAQ,IAAI,CAACvH,UAAU,CAACuH,MAAM;YAC9BC,aAAa,IAAI,CAACxH,UAAU,CAACC,YAAY,CAACuH,WAAW;YACrDC,kBAAkB,IAAI,CAACzH,UAAU,CAAC0H,MAAM;YACxCC,mBAAmB,IAAI,CAAC3H,UAAU,CAACC,YAAY,CAAC0H,iBAAiB;YACjEC,yBACE,IAAI,CAAC5H,UAAU,CAACC,YAAY,CAAC2H,uBAAuB;YACtDC,aAAa,GAAE,yBAAA,IAAI,CAAC7H,UAAU,CAACuF,IAAI,qBAApB,uBAAsBuC,OAAO;YAC5C5C,SAAS,IAAI,CAACA,OAAO;YACrB6C,kBAAkB,IAAI,CAAC/D,kBAAkB,CAACmC,GAAG;YAC7C6B,mBAAmB,IAAI,CAAChI,UAAU,CAACC,YAAY,CAACgI,SAAS;YACzDC,gBAAgB,IAAI,CAAClI,UAAU,CAACC,YAAY,CAACkI,KAAK;YAClDC,aAAa,IAAI,CAACpI,UAAU,CAACoI,WAAW,GACpC,IAAI,CAACpI,UAAU,CAACoI,WAAW,GAC3B/H;YACJgI,oBAAoB,IAAI,CAACrI,UAAU,CAACC,YAAY,CAACoI,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAC7C,qBAAqBnD,MAAM,GAAG,IACtCmD,sBACAtF;YAENoI,uBAAuB,IAAI,CAACzI,UAAU,CAACC,YAAY,CAACwI,qBAAqB;YACzE,8EAA8E;YAC9EC,iBAAiB,IAAI,CAAC1I,UAAU,CAAC0I,eAAe;YAChDzI,cAAc;gBACZ0I,YAAY,IAAI,CAAC3I,UAAU,CAAC2I,UAAU;gBACtCC,YAAY,IAAI,CAAC5I,UAAU,CAACC,YAAY,CAAC2I,UAAU;gBACnDC,qBAAqB,IAAI,CAAC7I,UAAU,CAACC,YAAY,CAAC4I,mBAAmB;gBACrEC,WAAW,IAAI,CAAC9I,UAAU,CAACC,YAAY,CAAC6I,SAAS,IAAI;gBACrDxC,oBACE,IAAI,CAACtG,UAAU,CAACC,YAAY,CAACqG,kBAAkB,KAAK,gBAChD,gBACAyC,QAAQ,IAAI,CAAC/I,UAAU,CAACC,YAAY,CAACqG,kBAAkB;gBAC7D0C,gBAAgB,IAAI,CAAChJ,UAAU,CAACC,YAAY,CAAC+I,cAAc,IAAI;gBAC/DC,WAAW,IAAI,CAACjJ,UAAU,CAACC,YAAY,CAACgJ,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAAClJ,UAAU,CAACC,YAAY,CAACiJ,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAACtJ,UAAU,CAACsJ,qBAAqB;YAC5DC,wBACE,IAAI,CAACvJ,UAAU,CAACC,YAAY,CAACsJ,sBAAsB;QACvD;QAEA,4DAA4D;QAC5DnP,UAAU;YACRsL;YACAC;QACF;QAEA,IAAI,CAAC6D,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAACvE;QACpB,IAAI,CAACwE,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAE5F;QAAI;IACnD;IAEU6F,iBAAiB;QACzB,OAAO,IAAI,CAACN,QAAQ,CAACE,MAAM;IAC7B;IAmMUD,mBAAwC;QAChD,yEAAyE;QACzE,MAAMM,iBAAiB,IAAI9N,qBAAqB,CAAC+N;YAC/C,OAAQA;gBACN,KAAK1Q;oBACH,OAAO,IAAI,CAAC2P,gBAAgB,MAAM;gBACpC,KAAK7P;oBACH,OAAO,IAAI,CAAC+P,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,IAAI5N;QAE1C,8BAA8B;QAC9B4N,SAASpG,IAAI,CACX,IAAIpH,0BACF,IAAI,CAAC0I,OAAO,EACZqF,gBACA,IAAI,CAAC1H,YAAY;QAIrB,uCAAuC;QACvCmH,SAASpG,IAAI,CACX,IAAIrH,6BACF,IAAI,CAAC2I,OAAO,EACZqF,gBACA,IAAI,CAAC1H,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACmB,kBAAkB,CAACmC,GAAG,EAAE;YAC/B,gCAAgC;YAChC6D,SAASpG,IAAI,CACX,IAAIvH,4BAA4B,IAAI,CAAC6I,OAAO,EAAEqF;YAEhDP,SAASpG,IAAI,CACX,IAAItH,6BAA6B,IAAI,CAAC4I,OAAO,EAAEqF;QAEnD;QAEA,OAAOP;IACT;IAEA,MAAgBZ,8BACd,GAAGqB,IAAqD,EACxD;QACA,MAAM,CAACC,KAAKlK,KAAKmK,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,OAAM,IAAI,CAACA,eAAe,CAACC,cAAc,oBAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,MAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACE1I,MAAMxB,IAAIiB,GAAG,IAAI;oBACjBqJ,QAAQtK,IAAIsK,MAAM,IAAI;oBACtB,gEAAgE;oBAChE3J,SACEX,eAAe/B,kBACX8J,OAAOwC,WAAW,CAACvK,IAAIW,OAAO,CAAC6J,OAAO,MACtCxK,IAAIW,OAAO;gBACnB,GACAwJ;YAEJ,EAAE,OAAOM,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASV,GAAU,EAAQ;QAChC,IAAI,IAAI,CAACnG,KAAK,EAAE;QAChB1J,IAAIsQ,KAAK,CAACT;IACZ;IAEA,MAAaW,cACX7K,GAAkB,EAClBoB,GAAmB,EACnBlB,SAAkC,EACnB;QACf,MAAM,IAAI,CAAC4K,OAAO;QAClB,MAAMR,SAAStK,IAAIsK,MAAM,CAACS,WAAW;QAErC,MAAMC,SAAS9O;QACf,OAAO8O,OAAOC,qBAAqB,CAACjL,IAAIW,OAAO,EAAE;YAC/C,OAAOqK,OAAOE,KAAK,CACjB7O,eAAewO,aAAa,EAC5B;gBACEM,UAAU,GAAGb,OAAO,CAAC,EAAEtK,IAAIiB,GAAG,EAAE;gBAChCmK,MAAMhP,SAASiP,MAAM;gBACrBC,YAAY;oBACV,eAAehB;oBACf,eAAetK,IAAIiB,GAAG;gBACxB;YACF,GACA,OAAOsK,OACL,IAAI,CAACC,iBAAiB,CAACxL,KAAKoB,KAAKlB,WAAWuL,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,eAAe/Q,eAAeqF,KAAK,mBAAmB;oBAC5DuL,KAAKI,aAAa,CAAC;wBACjB,oBAAoBvK,IAAIwK,UAAU;wBAClC,YAAYF;oBACd;oBAEA,MAAMG,qBAAqBb,OAAOc,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvB1P,eAAewO,aAAa,EAC5B;wBACAH,QAAQ7G,IAAI,CACV,CAAC,2BAA2B,EAAEgI,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAMhC,OAAO0B,eACT,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAE0B,OAAO,GACxB,GAAG1B,OAAO,CAAC,EAAE0B,OAAO;wBAExBT,KAAKI,aAAa,CAAC;4BACjB,cAAcK;4BACd,cAAcA;4BACd,kBAAkBhC;wBACpB;wBACAuB,KAAKU,UAAU,CAACjC;oBAClB,OAAO;wBACLuB,KAAKU,UAAU,CACbP,eACI,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAEtK,IAAIiB,GAAG,EAAE,GAC1B,GAAGqJ,OAAO,CAAC,EAAEtK,IAAIiB,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAcuK,kBACZxL,GAAkB,EAClBoB,GAAmB,EACnBlB,SAAkC,EACnB;QACf,IAAI;gBAiDKgM,yBAS4BA,0BASd,oBAKY;YAvEjC,qCAAqC;YACrC,MAAM,IAAI,CAAC1C,QAAQ,CAAC2C,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;YAClDtO,gCACEmC,KACApC,mBAAmBwD,OAAOA,IAAIgL,gBAAgB,GAAGhL;YAGnD,MAAMiL,WAAW,AAACrM,CAAAA,IAAIiB,GAAG,IAAI,EAAC,EAAGuB,KAAK,CAAC,KAAK;YAC5C,MAAM8J,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,8BAAAA,WAAYhM,KAAK,CAAC,cAAc;gBAClC,MAAMiM,WAAW5T,yBAAyBqH,IAAIiB,GAAG;gBACjDG,IAAIoL,QAAQ,CAACD,UAAU,KAAKE,IAAI,CAACF,UAAUG,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACxM,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIiB,GAAG,EAAE;oBACZ,MAAM,qBAAgD,CAAhD,IAAI9B,MAAM,wCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+C;gBACvD;gBAEAe,YAAYlH,SAASgH,IAAIiB,GAAG,EAAG;YACjC;YAEA,IAAI,CAACf,UAAUC,QAAQ,EAAE;gBACvB,MAAM,qBAA+C,CAA/C,IAAIhB,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEA,iFAAiF;YACjF,IAAI,OAAOe,UAAUyM,KAAK,KAAK,UAAU;gBACvCzM,UAAUyM,KAAK,GAAG5E,OAAOwC,WAAW,CAClC,IAAIqC,gBAAgB1M,UAAUyM,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAET,kBAAkB,IAAI,EAAE,GAAGvO,kBAAkBqC,OAAOA,MAAM,CAAC;YACnE,MAAM6M,kBAAkBX,mCAAAA,gBAAiBvL,OAAO,CAAC,oBAAoB;YACrE,MAAMmM,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,EAAEX,oCAAAA,0BAAAA,gBAAiBa,MAAM,qBAAxB,AAACb,wBAAuCc,SAAS;YAEvDhN,IAAIW,OAAO,CAAC,mBAAmB,KAAKX,IAAIW,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC2B,QAAQ;YACxEtC,IAAIW,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAACwD,IAAI,GACzC,IAAI,CAACA,IAAI,CAAC8I,QAAQ,KAClBH,UACE,QACA;YACN9M,IAAIW,OAAO,CAAC,oBAAoB,KAAKmM,UAAU,UAAU;YACzD9M,IAAIW,OAAO,CAAC,kBAAkB,KAAKuL,oCAAAA,2BAAAA,gBAAiBa,MAAM,qBAAvBb,yBAAyBgB,aAAa;YAEzE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAACC,iBAAiB,CAACnN,KAAKE;YAE5B,IAAIqD,WAAW,MAAM,IAAI,CAACxD,gBAAgB,CAACC,KAAKoB,KAAKlB;YACrD,IAAIqD,UAAU;YAEd,MAAMd,gBAAe,qBAAA,IAAI,CAACJ,YAAY,qBAAjB,mBAAmBK,kBAAkB,CACxD1H,YAAYkF,WAAWF,IAAIW,OAAO;YAGpC,MAAMgC,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,OAAI,wBAAA,IAAI,CAACnD,UAAU,CAACuF,IAAI,qBAApB,sBAAsBpC,aAAa;YACpEjI,eAAesF,KAAK,iBAAiB2C;YAErC,MAAM1B,MAAMhG,aAAa+E,IAAIiB,GAAG,CAACmM,OAAO,CAAC,QAAQ;YACjD,MAAMC,eAAenS,oBAAoB+F,IAAId,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3B6C,cAAc,IAAI,CAACA,YAAY;YACjC;YACApB,IAAId,QAAQ,GAAGkN,aAAalN,QAAQ;YAEpC,IAAIkN,aAAavG,QAAQ,EAAE;gBACzB9G,IAAIiB,GAAG,GAAGnG,iBAAiBkF,IAAIiB,GAAG,EAAG,IAAI,CAACzB,UAAU,CAACsH,QAAQ;YAC/D;YAEA,MAAMwG,uBACJ,IAAI,CAACpJ,WAAW,IAAI,OAAOlE,IAAIW,OAAO,CAACjE,oBAAoB,KAAK;YAElE,uCAAuC;YACvC,IAAI4Q,sBAAsB;gBACxB,IAAI;wBAuBE,wBA6ByB,qBA6DjB;oBAhHZ,IAAI,IAAI,CAAC9J,kBAAkB,CAACmC,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAI3F,IAAIiB,GAAG,CAACX,KAAK,CAAC,mBAAmB;4BACnCN,IAAIiB,GAAG,GAAGjB,IAAIiB,GAAG,CAACmM,OAAO,CAAC,YAAY;wBACxC;wBACAlN,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAUoN,WAAW,EAAE,GAAG,IAAIC,IAClC1O,YAAYkB,IAAIW,OAAO,CAACjE,oBAAoB,GAC5C;oBAGF,IAAI,EAAEyD,UAAUsN,WAAW,EAAE,GAAG,IAAID,IAAIxN,IAAIiB,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,KAAI,yBAAA,IAAI,CAACb,WAAW,CAAC+C,IAAI,qBAArB,uBAAuB7C,KAAK,CAACmN,cAAc;wBAC7C/S,eAAesF,KAAK,iBAAiB;oBACvC,OAGK,IACH,IAAI,CAAC0F,eAAe,IACpB,IAAI,CAACxB,WAAW,IAChBlE,IAAIW,OAAO,CAAC/D,mBAAmB,KAAK,OACpCoD,IAAIsK,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAMmC,OAAsB,EAAE;wBAC9B,WAAW,MAAMiB,SAAS1N,IAAIyM,IAAI,CAAE;4BAClCA,KAAKrJ,IAAI,CAACsK;wBACZ;wBACA,MAAMC,YAAYC,OAAOC,MAAM,CAACpB,MAAMQ,QAAQ,CAAC;wBAE/CvS,eAAesF,KAAK,aAAa2N;oBACnC;oBAEAJ,cAAc,IAAI,CAACzM,SAAS,CAACyM;oBAC7B,MAAMO,oBAAoB,IAAI,CAACC,iBAAiB,CAACN;oBAEjDF,cAAcnT,oBAAoBmT;oBAElC,8CAA8C;oBAC9C,MAAMS,wBAAuB,sBAAA,IAAI,CAAC3L,YAAY,qBAAjB,oBAAmBS,OAAO,CAACyK,aAAa;wBACnE5K;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAIqL,sBAAsB;wBACxBtT,eAAesF,KAAK,UAAUgO,qBAAqBjL,cAAc;wBAEjE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAIiL,qBAAqBC,mBAAmB,EAAE;4BAC5CvT,eAAesF,KAAK,6BAA6B;wBACnD,OAAO;4BACLpF,kBAAkBoF,KAAK;wBACzB;oBACF;oBAEA,IAAIkO,cAAcX;oBAClB,IAAIY,gBAAgBzU,eAAewU;oBACnC,IAAIE,eAGA;wBACF7M,QAAQ;wBACR8M,gBAAgB;oBAClB;oBAEA,IAAI,CAACF,eAAe;wBAClB,MAAM7N,QAAQ,MAAM,IAAI,CAACkJ,QAAQ,CAAClJ,KAAK,CAAC4N,aAAa;4BACnDnJ,MAAMiJ;wBACR;wBAEA,6DAA6D;wBAC7D,IAAI1N,OAAO;4BACT4N,cAAc5N,MAAMgO,UAAU,CAACnO,QAAQ;4BAEvC,iEAAiE;4BACjE,iEAAiE;4BACjE,4CAA4C;4BAC5C,IAAI,OAAOG,MAAMiB,MAAM,KAAK,aAAa;gCACvC4M,gBAAgB;gCAChBC,aAAa7M,MAAM,GAAGjB,MAAMiB,MAAM;gCAClC6M,aAAaC,cAAc,GAAG;4BAChC;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAIL,sBAAsB;wBACxBT,cAAcS,qBAAqB7N,QAAQ;oBAC7C;oBAEA,MAAMoO,QAAQhU,eAAe;wBAC3B4T;wBACAK,MAAMN;wBACNnJ,MAAM,IAAI,CAACvF,UAAU,CAACuF,IAAI;wBAC1B+B,UAAU,IAAI,CAACtH,UAAU,CAACsH,QAAQ;wBAClC2H,UAAU,EAAA,0BAAA,IAAI,CAACC,iBAAiB,uBAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAACtP,UAAU,CAACC,YAAY,CAACsP,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIpM,iBAAiB,CAAC0K,aAAa2B,MAAM,EAAE;wBACzC9O,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAEwC,gBAAgBzC,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,mEAAmE;oBACnE,qEAAqE;oBACrE,MAAM8O,oBAAoB;wBAAE,GAAG/O,UAAUyM,KAAK;oBAAC;oBAE/C,MAAMuC,wBAAwBhP,UAAUC,QAAQ;oBAChD,MAAMgP,mBAAmBpH,OAAOC,IAAI,CAClCuG,MAAMa,cAAc,CAACpP,KAAKE;oBAG5B,mEAAmE;oBACnE,mEAAmE;oBACnE,2CAA2C;oBAC3C,MAAMmP,cAAc;wBAAE,GAAGnP,UAAUyM,KAAK;oBAAC;oBACzC,MAAM2C,aAAaJ,0BAA0BhP,UAAUC,QAAQ;oBAE/D,IAAImP,cAAcpP,UAAUC,QAAQ,EAAE;wBACpCzF,eAAesF,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBAEA,MAAMoP,iBAAiB,IAAIC;oBAC3B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAI3H,OAAOyC,OAAO,CAACtK,UAAUyM,KAAK,EAAG;wBAC1D,MAAMgD,gBAAgBnT,wBAAwBiT;wBAC9C,IAAI,CAACE,eAAe;wBAEpB,gEAAgE;wBAChE,+CAA+C;wBAC/C,OAAOzP,UAAUyM,KAAK,CAAC8C,IAAI;wBAC3BF,eAAeK,GAAG,CAACD;wBAEnB,IAAI,OAAOD,UAAU,aAAa;wBAElCL,WAAW,CAACM,cAAc,GAAGE,MAAMC,OAAO,CAACJ,SACvCA,MAAMK,GAAG,CAAC,CAACC,IAAMrR,yBAAyBqR,MAC1CrR,yBAAyB+Q;oBAC/B;oBAEA,yDAAyD;oBACzD,IAAIvB,eAAe;wBACjB,IAAI5M,SAAiC,CAAC;wBAEtC,gEAAgE;wBAChE,oBAAoB;wBACpB,IAAI,CAAC6M,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM0B,2BAA2B,CAC9CZ,aACA;wBAEJ;wBAEA,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAACjB,aAAaC,cAAc,IAC5B,CAAC3U,eAAeoU,oBAChB;4BACA,IAAIoC,gBAAgB3B,MAAM4B,mBAAmB,oBAAzB5B,MAAM4B,mBAAmB,MAAzB5B,OAA4BT;4BAEhD,IAAIoC,eAAe;gCACjB3B,MAAM0B,2BAA2B,CAACC,eAAe;gCACjDnI,OAAOqI,MAAM,CAAChC,aAAa7M,MAAM,EAAE2O;gCACnC9B,aAAaC,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IACE,8DAA8D;wBAC9Dd,gBAAgB,YAChB,CAACa,aAAaC,cAAc,IAC5B,CAAC3U,eAAe6T,cAChB;4BACA,IAAI2C,gBAAgB3B,MAAM4B,mBAAmB,oBAAzB5B,MAAM4B,mBAAmB,MAAzB5B,OAA4BhB;4BAEhD,IAAI2C,eAAe;gCACjB,MAAMG,kBAAkB9B,MAAM0B,2BAA2B,CACvDC,eACA;gCAGF,IAAIG,gBAAgBhC,cAAc,EAAE;oCAClCtG,OAAOqI,MAAM,CAAC7O,QAAQ2O;oCACtB9B,eAAeiC;gCACjB;4BACF;wBACF;wBAEA,IAAIjC,aAAaC,cAAc,EAAE;4BAC/B9M,SAAS6M,aAAa7M,MAAM;wBAC9B;wBAEA,MAAM+O,qBAAqBtQ,IAAIW,OAAO,CAAC,sBAAsB;wBAC7D,IACE,OAAO2P,uBAAuB,YAC9BA,sBACA5W,eAAe6T,gBACf,CAACa,aAAaC,cAAc,EAC5B;4BACA,MAAMkC,eACJhC,MAAMiC,yBAAyB,CAACF;4BAElC,IAAIC,cAAc;gCAChBnC,eAAeG,MAAM0B,2BAA2B,CAC9CM,cACA;gCAGF,IAAInC,aAAaC,cAAc,EAAE;oCAC/B9M,SAAS6M,aAAa7M,MAAM;gCAC9B;4BACF;wBACF;wBAEA,mEAAmE;wBACnE,6DAA6D;wBAC7D,IAAI,CAAC6M,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM0B,2BAA2B,CAC9CZ,aACA;4BAGF,IAAIjB,aAAaC,cAAc,EAAE;gCAC/B9M,SAAS6M,aAAa7M,MAAM;4BAC9B;wBACF;wBAEA,4DAA4D;wBAC5D,+DAA+D;wBAC/D,yBAAyB;wBACzB,IACEgN,MAAMkC,mBAAmB,IACzB3C,sBAAsBI,eACtB,CAACE,aAAaC,cAAc,EAC5B;4BACA9M,SAASgN,MAAMkC,mBAAmB;4BAElC,6DAA6D;4BAC7D,kEAAkE;4BAClE,gEAAgE;4BAChE,8DAA8D;4BAC9D,gEAAgE;4BAChE,IAAIH,uBAAuB,IAAI;gCAC7B5V,eAAesF,KAAK,uBAAuB;4BAC7C;wBACF;wBAEA,IAAIuB,QAAQ;4BACVgM,cAAcgB,MAAMmC,sBAAsB,CAACxC,aAAa3M;4BACxDvB,IAAIiB,GAAG,GAAGsN,MAAMmC,sBAAsB,CAAC1Q,IAAIiB,GAAG,EAAGM;4BAEjD,kEAAkE;4BAClE,4DAA4D;4BAC5D,UAAU;4BACV,IAAIP,4BAA4BrG,eAC9BqF,KACA;4BAEF,IACEgB,6BACAtH,eAAesH,2BAA2B,QAC1C;gCACAA,4BAA4BuN,MAAMmC,sBAAsB,CACtD1P,2BACAO;gCAGFvB,IAAIW,OAAO,CAACrF,oCAAoCsF,WAAW,GAAG,GAC5DI;gCACFtG,eACEsF,KACA,6BACAgB;4BAEJ;wBACF;oBACF;oBAEA,IAAImN,iBAAiBmB,YAAY;4BAGdf;wBAFjBA,MAAMoC,eAAe,CAAC3Q,KAAK;+BACtBmP;+BACApH,OAAOC,IAAI,CAACuG,EAAAA,2BAAAA,MAAMqC,iBAAiB,qBAAvBrC,yBAAyBsC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACA,oEAAoE;oBACpE,oCAAoC;oBACpC,mFAAmF;oBACnF,KAAK,MAAMpB,OAAOF,eAAgB;wBAChC,IAAI,CAAEE,CAAAA,OAAOR,iBAAgB,GAAI;4BAC/B,OAAO/O,UAAUyM,KAAK,CAAC8C,IAAI;wBAC7B;oBACF;oBACAvP,UAAUC,QAAQ,GAAGoN;oBACrBtM,IAAId,QAAQ,GAAGD,UAAUC,QAAQ;oBACjCoD,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACtD,KAAKoB,KAAKlB;oBAC3D,IAAIqD,UAAU;gBAChB,EAAE,OAAO2G,KAAK;oBACZ,IAAIA,eAAexR,eAAewR,eAAezR,gBAAgB;wBAC/D2I,IAAIwK,UAAU,GAAG;wBACjB,OAAO,IAAI,CAACkF,WAAW,CAAC,MAAM9Q,KAAKoB,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAM8I;gBACR;YACF;YAEAxP,eAAesF,KAAK,kBAAkBuI,QAAQ9F;YAE9C,IAAI4K,aAAa2B,MAAM,EAAE;gBACvBhP,IAAIiB,GAAG,GAAGnI,UAAUmI;gBACpBvG,eAAesF,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAACkE,WAAW,IAAI,CAACvJ,eAAeqF,KAAK,WAAW;gBACvD,gEAAgE;gBAChE,IAAIqN,aAAa2B,MAAM,EAAE;oBACvBtU,eAAesF,KAAK,UAAUqN,aAAa2B,MAAM;gBACnD,OAGK,IAAIrM,eAAe;oBACtBjI,eAAesF,KAAK,UAAU2C;oBAC9BjI,eAAesF,KAAK,6BAA6B;gBACnD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAC,AAAC,IAAI,CAACqE,aAAa,CAAS0M,eAAe,IAC5C,CAACpW,eAAeqF,KAAK,qBACrB;gBACA,MAAMgR,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgBnJ,OAAOqI,MAAM,CAAC,CAAC,GAAGpQ,IAAIW,OAAO;gBAC/C;gBAEAqQ,iBAAiBG,iBAAiB;gBAClCzW,eAAesF,KAAK,oBAAoBgR;gBAGtCrR,WAAmByR,kBAAkB,GAAGJ;YAC5C;YAEA,MAAMK,gBAAgBxS;YAEtB,IAAIwS,eAAe;gBACjB,MAAMC,QAAQC,GAAG,CACf;uBAAIF;iBAAc,CAACtB,GAAG,CAAC,OAAOyB;oBAC5B,IAAI,iBAAiBA,cAAc;oBACjC,+DAA+D;oBAC/D,2DAA2D;oBAC3D,gDAAgD;oBAClD,OAAO;wBACL,MAAMC,4BAA4BnX,6BAChC0F,IAAIW,OAAO,EACX,IAAI,CAAC+F,oBAAoB,GAAGC,OAAO,CAAC+K,aAAa;wBAGnD,MAAMF,aAAaG,kBAAkB,IAChCF;oBAEP;gBACF;YAEJ;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAAC9W,eAAeqF,KAAK,6BAA6B;gBACpDtF,eACEsF,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAMqS,aAAajX,eAAeqF,KAAK;YACvC,MAAM6R,gBACJ,CAACvE,wBACD5L,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BgQ;YAEF,IAAIC,eAAe;oBAkCf;gBAjCF,MAAMC,eAAenX,eAAeqF,KAAK;gBACzC,IAAI8R,cAAc;oBAChB,MAAMC,cAAcpX,eAAeqF,KAAK;oBAExC,IAAI+R,aAAa;wBACfhK,OAAOqI,MAAM,CAAClQ,UAAUyM,KAAK,EAAEoF;oBACjC;oBAEA3Q,IAAIwK,UAAU,GAAGkG;oBACjB,IAAI5H,MAAoBvP,eAAeqF,KAAK,kBAAkB;oBAE9D,OAAO,IAAI,CAAC8Q,WAAW,CAAC5G,KAAKlK,KAAKoB,KAAK,WAAWlB,UAAUyM,KAAK;gBACnE;gBAEA,MAAMqF,oBAAoB,IAAIxE,IAAIoE,cAAc,KAAK;gBACrD,MAAMK,qBAAqB/W,oBACzB8W,kBAAkB7R,QAAQ,EAC1B;oBACEX,YAAY,IAAI,CAACA,UAAU;oBAC3B0S,WAAW;gBACb;gBAGF,IAAID,mBAAmBjD,MAAM,EAAE;oBAC7BtU,eAAesF,KAAK,UAAUiS,mBAAmBjD,MAAM;gBACzD;gBAEA,IAAI9O,UAAUC,QAAQ,KAAK6R,kBAAkB7R,QAAQ,EAAE;oBACrDD,UAAUC,QAAQ,GAAG6R,kBAAkB7R,QAAQ;oBAC/CzF,eAAesF,KAAK,cAAciS,mBAAmB9R,QAAQ;gBAC/D;gBACA,MAAMgS,kBAAkBtV,oBACtB/B,iBAAiBoF,UAAUC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAACsH,QAAQ,IAAI,MACjE,yBAAA,IAAI,CAACtH,UAAU,CAACuF,IAAI,qBAApB,uBAAsBC,OAAO;gBAG/B,IAAImN,gBAAgBpP,cAAc,EAAE;oBAClCrI,eAAesF,KAAK,UAAUmS,gBAAgBpP,cAAc;gBAC9D;gBACA7C,UAAUC,QAAQ,GAAGgS,gBAAgBhS,QAAQ;gBAE7C,KAAK,MAAMsP,OAAO1H,OAAOC,IAAI,CAAC9H,UAAUyM,KAAK,EAAG;oBAC9C,OAAOzM,UAAUyM,KAAK,CAAC8C,IAAI;gBAC7B;gBACA,MAAMsC,cAAcpX,eAAeqF,KAAK;gBAExC,IAAI+R,aAAa;oBACfhK,OAAOqI,MAAM,CAAClQ,UAAUyM,KAAK,EAAEoF;gBACjC;gBAEAxO,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACtD,KAAKoB,KAAKlB;gBAC3D,IAAIqD,UAAU;gBAEd,MAAM,IAAI,CAACN,2BAA2B,CAACjD,KAAKoB,KAAKlB;gBACjD;YACF;YAEA,IACEwB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BjH,eAAeqF,KAAK,qBACpB;gBACAuD,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACtD,KAAKoB,KAAKlB;gBAC3D,IAAIqD,UAAU;gBAEdA,WAAW,MAAM,IAAI,CAACL,+BAA+B,CACnDlD,KACAoB,KACAlB;gBAEF,IAAIqD,UAAU;gBAEd,MAAM2G,MAAM,IAAI/K;gBACd+K,IAAY3J,MAAM,GAAG;oBACrB6R,UAAU,IAAIC,SAAS,MAAM;wBAC3B1R,SAAS;4BACP,qBAAqB;wBACvB;oBACF;gBACF;gBACEuJ,IAAYoI,MAAM,GAAG;gBACvB,MAAMpI;YACR;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAACoD,wBAAwBD,aAAavG,QAAQ,EAAE;gBAClD5G,UAAUC,QAAQ,GAAGrF,iBACnBoF,UAAUC,QAAQ,EAClBkN,aAAavG,QAAQ;YAEzB;YAEA1F,IAAIwK,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAAC2G,GAAG,CAACvS,KAAKoB,KAAKlB;QAClC,EAAE,OAAOgK,KAAU;YACjB,IAAIA,eAAetL,iBAAiB;gBAClC,MAAMsL;YACR;YAEA,IACE,AAACA,OAAO,OAAOA,QAAQ,YAAYA,IAAIsI,IAAI,KAAK,qBAChDtI,eAAexR,eACfwR,eAAezR,gBACf;gBACA2I,IAAIwK,UAAU,GAAG;gBACjB,OAAO,IAAI,CAACkF,WAAW,CAAC,MAAM9Q,KAAKoB,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAAC8C,WAAW,IAChB,IAAI,CAACiC,UAAU,CAAClC,GAAG,IAClB9H,eAAe+N,QAAQA,IAAIoI,MAAM,EAClC;gBACA,MAAMpI;YACR;YACA,IAAI,CAACU,QAAQ,CAACnQ,eAAeyP;YAC7B9I,IAAIwK,UAAU,GAAG;YACjBxK,IAAIqL,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAwDA;;GAEC,GACD,AAAO+F,8BACLC,IAAiB,EACkC;QACnD,MAAMC,UAAU,IAAI,CAACC,iBAAiB;QACtC,OAAO,CAAC5S,KAAKoB,KAAKlB;YAChBrF,eAAemF,KAAK0S;YACpB,OAAOC,QAAQ3S,KAAKoB,KAAKlB;QAC3B;IACF;IAEO0S,oBAGL;QACA,OAAO,IAAI,CAAC/H,aAAa,CAAChC,IAAI,CAAC,IAAI;IACrC;IAQOc,eAAekJ,MAAe,EAAQ;QAC3C,IAAI,CAACrT,UAAU,CAAC4F,WAAW,GAAGyN,SAASA,OAAOzF,OAAO,CAAC,OAAO,MAAM;QACnE,IAAI,CAACjH,UAAU,CAACf,WAAW,GAAG,IAAI,CAAC5F,UAAU,CAAC4F,WAAW;IAC3D;IAIA;;;GAGC,GACD,MAAa0F,UAAyB;QACpC,IAAI,IAAI,CAACpH,QAAQ,EAAE;QAEnB,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC0G,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAG,MAAM,IAAI,CAAC0I,yBAAyB;QAC7D;QACA,IAAI,IAAI,CAACnP,eAAe,KAAK,MAAM;YACjC,IAAI,CAACA,eAAe,GAAG,IAAI,CAACoP,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAACtP,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgBoP,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3B5J,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDrB,OAAOC,IAAI,CAAC,IAAI,CAACkB,gBAAgB,IAAI,CAAC,GAAGgK,OAAO,CAAC,CAACC;YAChD,MAAMC,iBAAiBrY,iBAAiBoY;YACxC,IAAI,CAAC/J,aAAa,CAACgK,eAAe,EAAE;gBAClChK,aAAa,CAACgK,eAAe,GAAG,EAAE;YACpC;YACAhK,aAAa,CAACgK,eAAe,CAAChQ,IAAI,CAAC+P;QACrC;QACA,OAAO/J;IACT;IAEA,MAAgBmJ,IACdvS,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA6B,EACd;QACf,OAAOhE,YAAYgP,KAAK,CAAC7O,eAAekW,GAAG,EAAE,UAC3C,IAAI,CAACc,OAAO,CAACrT,KAAKoB,KAAKlB;IAE3B;IAEA,MAAcmT,QACZrT,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA6B,EACd;QACf,MAAM,IAAI,CAAC+C,2BAA2B,CAACjD,KAAKoB,KAAKlB;IACnD;IAEA,MAAcoT,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAOtX,YAAYgP,KAAK,CAAC7O,eAAeiX,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,KAAKF,eAAexT,GAAG,CAACW,OAAO,CAAC,aAAa,IAAI;QAEvD,MAAMwJ,MAAqD;YACzD,GAAGqJ,cAAc;YACjBrN,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClB,6DAA6D;gBAC7DC,yBAAyB,CAAC,IAAI,CAACD,UAAU,CAACwN,OAAO;gBACjDC,wBAAwBpV,6BACtBkV,IACA,IAAI,CAAClU,UAAU,CAAC0I,eAAe;YAEnC;QACF;QAEA,MAAM2L,UAAU,MAAMN,GAAGpJ;QACzB,IAAI0J,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAE7T,GAAG,EAAEoB,GAAG,EAAE,GAAG+I;QACrB,MAAM2J,iBAAiB1S,IAAIwK,UAAU;QACrC,MAAM,EAAEa,IAAI,EAAEsH,IAAI,EAAE,GAAGF;QACvB,IAAI,EAAEG,YAAY,EAAE,GAAGH;QACvB,IAAI,CAACzS,IAAI6S,IAAI,EAAE;YACb,MAAM,EAAE5O,aAAa,EAAEiB,eAAe,EAAErC,GAAG,EAAE,GAAG,IAAI,CAACkC,UAAU;YAE/D,oDAAoD;YACpD,IAAIlC,KAAK;gBACP7C,IAAI8S,SAAS,CAAC,iBAAiB;gBAC/BF,eAAenU;YACjB;YAEA,IAAImU,gBAAgBA,aAAaG,MAAM,KAAKtU,WAAW;gBACrDmU,aAAaG,MAAM,GAAG,IAAI,CAAC3U,UAAU,CAAC2I,UAAU;YAClD;YAEA,MAAM,IAAI,CAACiM,gBAAgB,CAACpU,KAAKoB,KAAK;gBACpCb,QAAQkM;gBACRsH;gBACA1O;gBACAiB;gBACA0N;YACF;YACA5S,IAAIwK,UAAU,GAAGkI;QACnB;IACF;IAEA,MAAcO,cACZd,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAMrJ,MAAqD;YACzD,GAAGqJ,cAAc;YACjBrN,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAMyN,UAAU,MAAMN,GAAGpJ;QACzB,IAAI0J,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQpH,IAAI,CAAC6H,iBAAiB;IACvC;IAEA,MAAaC,OACXvU,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBwM,QAA4B,CAAC,CAAC,EAC9BzM,SAAkC,EAClCsU,iBAAiB,KAAK,EACP;QACf,OAAOtY,YAAYgP,KAAK,CAAC7O,eAAekY,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAACzU,KAAKoB,KAAKjB,UAAUwM,OAAOzM,WAAWsU;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,wBAAwB5W;QAC9B,IAAI4W,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBC,SAAS;QACxC;QAEA,IAAI,IAAI,CAAC1Q,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAOrE;QACT;QAEA,OAAO,IAAI,CAACgV,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAOhV;IACT;IAEA,MAAc4U,WACZzU,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBwM,QAA4B,CAAC,CAAC,EAC9BzM,SAAkC,EAClCsU,iBAAiB,KAAK,EACP;YA4BZxU;QA3BH,IAAI,CAACG,SAAS2U,UAAU,CAAC,MAAM;YAC7BpK,QAAQ7G,IAAI,CACV,CAAC,8BAA8B,EAAE1D,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAACkE,aAAa,CAAC0Q,YAAY,IAC/B5U,aAAa,YACb,CAAE,MAAM,IAAI,CAAC6U,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxC7U,WAAW;QACb;QAEA,MAAMuT,KAAK1T,IAAIW,OAAO,CAAC,aAAa,IAAI;QACxC,IAAI,CAACwF,UAAU,CAACwN,OAAO,GAAG3Z,WAAW0Z;QAErC,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAACc,kBACD,CAAC,IAAI,CAACtQ,WAAW,IACjB,CAACvJ,eAAeqF,KAAK,oBACpBA,CAAAA,EAAAA,WAAAA,IAAIiB,GAAG,qBAAPjB,SAASM,KAAK,CAAC,kBACb,IAAI,CAACuE,YAAY,IAAI7E,IAAIiB,GAAG,CAAEX,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAACuK,aAAa,CAAC7K,KAAKoB,KAAKlB;QACtC;QAEA,IAAInG,cAAcoG,WAAW;YAC3B,OAAO,IAAI,CAAC0B,SAAS,CAAC7B,KAAKoB,KAAKlB;QAClC;QAEA,OAAO,IAAI,CAACoT,IAAI,CAAC,CAACnJ,MAAQ,IAAI,CAAC8K,gBAAgB,CAAC9K,MAAM;YACpDnK;YACAoB;YACAjB;YACAwM;QACF;IACF;IAEA,MAAgBuI,eAAe,EAC7B/U,QAAQ,EAMT,EAGE;YAGC;QAFF,+DAA+D;QAC/D,MAAMgV,iBACJ,oDAAA,IAAI,CAACzO,oBAAoB,GAAG0O,aAAa,CAACjV,SAAS,qBAAnD,kDAAqD0O,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvCwG,aAAaxV;YACbyV,cAAcjX,mBAAmB8W;QACnC;IACF;IAEA,MAAcI,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,OAAOvZ,YAAYgP,KAAK,CACtB7O,eAAekZ,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,OACEnY,2BAA2BmY,qBAC3B,IAAI,CAACtM,yBAAyB,CAACuM,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACH;QACrB;IAEJ;IAEUI,cACRhW,GAAkB,EAClBoB,GAAmB,EACnB6U,SAAkB,EAClBL,gBAAwB,EAClB;QACN,MAAMM,iBAAiB,GAAG/a,WAAW,EAAE,EAAEM,8BAA8B,EAAE,EAAEJ,4BAA4B,EAAE,EAAEC,qCAAqC;QAChJ,MAAMoQ,eAAe/Q,eAAeqF,KAAK,mBAAmB;QAE5D,IAAImW,qBAAqB;QAEzB,IAAIF,aAAa,IAAI,CAACN,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FxU,IAAIgV,YAAY,CAAC,QAAQ,GAAGF,eAAe,EAAE,EAAE1a,UAAU;YACzD2a,qBAAqB;QACvB,OAAO,IAAIF,aAAavK,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnGtK,IAAIgV,YAAY,CAAC,QAAQF;QAC3B;QAEA,IAAI,CAACC,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAOnW,IAAIW,OAAO,CAACnF,SAAS;QAC9B;IACF;IAEA,MAAcka,mCACZ,EACE1V,GAAG,EACHoB,GAAG,EACHjB,QAAQ,EACRgG,YAAYkQ,IAAI,EAC8B,EAChD,EAAEC,UAAU,EAAE3J,KAAK,EAAwB,EACV;YAcJ2J,uBAyJzB,uBA4CAC,OAiHY,wBAm2BdC;QAtqCF,IAAIrW,aAAa3G,4BAA4B;YAC3C2G,WAAW;QACb;QACA,MAAMsW,kBAAkBtW,aAAa;QACrC,MAAMuW,YACJvW,aAAa,UAAWsW,mBAAmBrV,IAAIwK,UAAU,KAAK;QAChE,MAAM+K,YACJxW,aAAa,UAAWsW,mBAAmBrV,IAAIwK,UAAU,KAAK;QAChE,MAAMqK,YAAYK,WAAWL,SAAS,KAAK;QAE3C,MAAMW,iBAAiB,CAAC,CAACN,WAAWO,kBAAkB;QACtD,IAAIC,oBAAoB,CAAC,CAACR,WAAWpB,cAAc;QACnD,MAAM6B,yBAAyBvZ,0BAA0BwC;QACzD,MAAMgX,qBAAqB,CAAC,GAACV,wBAAAA,WAAWW,SAAS,qBAApBX,sBAAsBY,eAAe;QAClE,IAAIC,QAAQ,CAAC,CAACb,WAAWc,cAAc;QACvC,uFAAuF;QACvF,MAAM1L,eAAe/Q,eAAeqF,KAAK,mBAAmB;QAE5D,yEAAyE;QACzE,yEAAyE;QACzE,mEAAmE;QACnE,oEAAoE;QACpE,mEAAmE;QACnE,qCAAqC;QACrC,IACE,CAAC,IAAI,CAACkE,WAAW,IACjB,IAAI,CAAC1E,UAAU,CAACC,YAAY,CAAC4X,yBAAyB,IACtD3L,cACA;YACA,MAAM/K,UAAUX,IAAIW,OAAO;YAC3B,MAAM2W,eAAevY,+BACnB4B,OAAO,CAACtF,4BAA4BuF,WAAW,GAAG,EAClDD,OAAO,CAACrF,oCAAoCsF,WAAW,GAAG,EAC1DD,OAAO,CAAClF,8BAA8BmF,WAAW,GAAG,EACpDD,OAAO,CAACnF,SAASoF,WAAW,GAAG;YAEjC,MAAM2W,aACJ5c,eAAeqF,KAAK,8BACpB,IAAIwN,IAAIxN,IAAIiB,GAAG,IAAI,IAAI,oBAAoBuW,YAAY,CAACzL,GAAG,CACzD3Q;YAGJ,IAAIkc,iBAAiBC,YAAY;gBAC/B,iEAAiE;gBACjE,mEAAmE;gBACnE,iFAAiF;gBACjF,6EAA6E;gBAC7E,6EAA6E;gBAC7E,MAAMtW,MAAM,IAAIuM,IAAIxN,IAAIiB,GAAG,IAAI,IAAI;gBACnChC,mCAAmCgC,KAAKqW;gBACxClW,IAAIwK,UAAU,GAAG;gBACjBxK,IAAI8S,SAAS,CAAC,YAAY,GAAGjT,IAAId,QAAQ,GAAGc,IAAIwW,MAAM,EAAE;gBACxDrW,IAAIqL,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;QACF;QAEA,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIe,cAAczU,SAASgH,IAAIiB,GAAG,IAAI,IAAId,QAAQ,IAAI;QAEtD,IAAIuX,sBAAsB/c,eAAeqF,KAAK,iBAAiByN;QAE/D,IAAI,CAACuI,aAAa,CAAChW,KAAKoB,KAAK6U,WAAWyB;QAExC,IAAIrC;QACJ,IAAIC;QACJ,IAAIqC,cAAc;QAElB,MAAMC,YAAYle,eAAe4c,WAAW9H,IAAI;QAEhD,MAAM+H,oBAAoB,IAAI,CAAC7P,oBAAoB;QAEnD,IAAIuP,aAAa2B,WAAW;YAC1B,MAAMC,cAAc,MAAM,IAAI,CAAC3C,cAAc,CAAC;gBAC5C/U;gBACAqO,MAAM8H,WAAW9H,IAAI;gBACrByH;gBACA/E,gBAAgBlR,IAAIW,OAAO;YAC7B;YAEA0U,cAAcwC,YAAYxC,WAAW;YACrCC,eAAeuC,YAAYvC,YAAY;YACvCqC,cAAc,OAAOrC,iBAAiB;YAEtC,IAAI,IAAI,CAAC9V,UAAU,CAAC0H,MAAM,KAAK,UAAU;gBACvC,MAAMsH,OAAO8H,WAAW9H,IAAI;gBAC5B,IAAI,CAAC6G,aAAa;oBAChB,MAAM,qBAEL,CAFK,IAAIlW,MACR,CAAC,MAAM,EAAEqP,KAAK,wGAAwG,CAAC,GADnH,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMsJ,uBAAuB3d,oBAAoBud;gBACjD,IAAI,CAACrC,YAAY0C,QAAQ,CAACD,uBAAuB;oBAC/C,MAAM,qBAEL,CAFK,IAAI3Y,MACR,CAAC,MAAM,EAAEqP,KAAK,oBAAoB,EAAEsJ,qBAAqB,8EAA8E,CAAC,GADpI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAIH,aAAa;gBACfb,oBAAoB;YACtB;QACF;QAEA,IACEa,gBACAtC,+BAAAA,YAAa0C,QAAQ,CAACL,yBACtB,mDAAmD;QACnD,+BAA+B;QAC/B1X,IAAIW,OAAO,CAAC,sBAAsB,EAClC;YACAwW,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAAChR,UAAU,CAAClC,GAAG,EAAE;YAC/BkT,UAAU,CAAC,CAACZ,kBAAkByB,MAAM,CAACta,QAAQyC,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAM8X,oBACJ,CAAC,CACCtd,CAAAA,eAAeqF,KAAK,oBACnBA,IAAIW,OAAO,CAAC,gBAAgB,IAC3B,AAAC,IAAI,CAAC0D,aAAa,CAAS0M,eAAe,KAE9CoG,CAAAA,SAASP,cAAa;QAEzB;;;KAGC,GACD,MAAMsB,uBACJvd,eAAeqF,KAAK,2BAA2B;QAEjD,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACmX,SACDnX,IAAIW,OAAO,CAAC,wBAAwB,IACpC,CAAE+V,CAAAA,aAAavW,aAAa,SAAQ,GACpC;YACAiB,IAAI8S,SAAS,CAACxX,qBAAqByD;YACnCiB,IAAI8S,SAAS,CAAC,qBAAqB;YACnC9S,IAAI8S,SAAS,CACX,iBACA;YAEF9S,IAAIqL,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,iEAAiE;QACjE,IACEyK,SACA,IAAI,CAACjT,WAAW,IAChBlE,IAAIW,OAAO,CAACjE,oBAAoB,IAChCsD,IAAIiB,GAAG,CAAC6T,UAAU,CAAC,gBACnB;YACA9U,IAAIiB,GAAG,GAAG,IAAI,CAAC8M,iBAAiB,CAAC/N,IAAIiB,GAAG;QAC1C;QAEA,MAAM+N,SAASrU,eAAeqF,KAAK;QACnC,MAAM2C,gBAAgBwU,SAClB,wBAAA,IAAI,CAAC3X,UAAU,CAACuF,IAAI,qBAApB,sBAAsBpC,aAAa,GACnChI,eAAeqF,KAAK;QAExB,IACE,CAAC,CAACA,IAAIW,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACS,IAAIwK,UAAU,IAAIxK,IAAIwK,UAAU,KAAK,GAAE,GACzC;YACAxK,IAAI8S,SAAS,CACX,yBACA,GAAGlF,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK7O,UAAU;QAE9C;QAEA,IAAIgY;QACJ,IAAI7B,WAAW6B,WAAW,EAAE;YAC1BA,cAAc7B,WAAW6B,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAAC1S,eAAe,IACpB,OAAOyS,gBAAgB,eACvBhb,qBAAqBgb;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAME,2BACJ3W,QAAQC,GAAG,CAAC2W,0CAA0C,KAAK,OAC3D,OAAO3L,MAAM4L,aAAa,KAAK,eAC/BH;QAEF,sEAAsE;QACtE,6CAA6C;QAC7C,MAAMI,6BACJH,4BAA4B1L,MAAM4L,aAAa,KAAK;QAEtD,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAME,oBACJL,mBACC,CAAA,EACC7B,QAAAA,kBAAkByB,MAAM,CAAC7X,SAAS,IAClCoW,kBAAkBnB,aAAa,CAACjV,SAAS,qBAF1C,AACCoW,MAECmC,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BL,4BACE,CAAA,IAAI,CAAClS,UAAU,CAAClC,GAAG,KAAK,QACvB,IAAI,CAACG,qBAAqB,KAAK,IAAG,CAAE;QAE5C,MAAMuU,qBACJN,4BAA4BI;QAE9B,oEAAoE;QACpE,iEAAiE;QACjE,MAAMG,yBACJD,sBAAsB,IAAI,CAACxS,UAAU,CAAClC,GAAG,KAAK;QAEhD,MAAM4U,uBAAuBL,8BAA8BC;QAE3D,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMK,mBAAmBL,oBACrB9d,eAAeqF,KAAK,eACpBH;QAEJ,0EAA0E;QAC1E,wEAAwE;QACxE,0DAA0D;QAC1D,MAAMkZ,sBACJN,qBAAqB/M,gBAAgB,CAACwM;QAExC,yEAAyE;QACzE,iEAAiE;QACjE,yEAAyE;QACzE,yEAAyE;QACzE,MAAMc,wBAAwBre,eAC5BqF,KACA;QAGF,MAAMiZ,YAAYxa,iBAAiBuB;QACnC,IAAIiZ,aAAaR,mBAAmB;YAClCtB,QAAQ;YACR,IAAI,CAAChR,UAAU,CAACyN,sBAAsB,GAAG;QAC3C;QAEA,gEAAgE;QAChE,IAAI8C,aAAa,CAACuB,qBAAqB,CAACvM,cAAc;YACpDtK,IAAIwK,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAIrS,oBAAoBwe,QAAQ,CAAC5X,WAAW;YAC1CiB,IAAIwK,UAAU,GAAGsN,SAAS/Y,SAASgZ,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,+CAA+C;QAC/C,CAACpC,0BACD,uCAAuC;QACvC,CAAC+B,oBACD,CAACpC,aACD,CAACC,aACDxW,aAAa,aACbH,IAAIsK,MAAM,KAAK,UACftK,IAAIsK,MAAM,KAAK,SACd,CAAA,OAAOgM,WAAWW,SAAS,KAAK,YAAYE,KAAI,GACjD;YACA/V,IAAIwK,UAAU,GAAG;YACjBxK,IAAI8S,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtC9S,IAAIqL,IAAI,CAAC,sBAAsBC,IAAI;YACnC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAO4J,WAAWW,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACLlD,MAAM;gBACN,0DAA0D;gBAC1DtH,MAAMvS,aAAakf,UAAU,CAAC9C,WAAWW,SAAS;YACpD;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAAStK,SAAS,CAACA,MAAMnG,GAAG,EAAE,OAAOmG,MAAMnG,GAAG;QAElD,IAAI6P,KAAKjQ,uBAAuB,KAAK,MAAM;gBAIhCkQ;YAHT,MAAM5C,KAAK1T,IAAIW,OAAO,CAAC,aAAa,IAAI;YACxC,MAAM0Y,eAAepf,MAAMyZ;YAC3B,MAAM4F,sBACJ,SAAOhD,uBAAAA,WAAWiD,QAAQ,qBAAnBjD,qBAAqBY,eAAe,MAAK,cAChD,oFAAoF;YACpF7d,yBAAyBid,WAAWiD,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClDlD,KAAKjQ,uBAAuB,GAC1B,CAAC+Q,SAAS,CAACkC,gBAAgB,CAAC1M,MAAMnG,GAAG,IAAI8S;QAC7C;QAEA,2DAA2D;QAC3D,IAAI,CAACrB,qBAAqBhC,aAAaI,KAAKpS,GAAG,EAAE;YAC/CoS,KAAKjQ,uBAAuB,GAAG;QACjC;QAEA,MAAMpB,WAAU,yBAAA,IAAI,CAACxF,UAAU,CAACuF,IAAI,qBAApB,uBAAsBC,OAAO;QAE7C,IAAIwU;QACJ,IAAIC,gBAAgB;QAEpB,IAAI7C,kBAAkBO,SAASlB,WAAW;YACxC,8DAA8D;YAC9D,IAAIvU,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,MAAM,EAAE8X,iBAAiB,EAAE,GACzBpV,QAAQ;gBACVkV,cAAcE,kBACZ1Z,KACAoB,KACA,IAAI,CAAC+E,UAAU,CAACM,YAAY,EAC5B,CAAC,CAAC,IAAI,CAACjH,UAAU,CAACC,YAAY,CAACka,kBAAkB;gBAEnDF,gBAAgBD,gBAAgB;YAClC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,gCAAgC;QAChC,IACEvD,aACA,CAACI,KAAKpS,GAAG,IACT,CAACwV,iBACDtC,SACAzL,gBACA,CAACqN,uBACA,CAAA,CAAC5f,cAAckd,KAAKuD,OAAO,KAC1B,AAAC,IAAI,CAACvV,aAAa,CAAS0M,eAAe,AAAD,GAC5C;YACA7T,mBAAmB8C,IAAIW,OAAO;QAChC;QAEA,IAAI,EAAEkZ,oBAAoB,EAAEC,uBAAuB,EAAE,GACnDngB,0BAA0BqG,KAAK,IAAI,CAACmG,UAAU,CAACM,YAAY;QAE7D,IAAI0Q,SAAS,IAAI,CAACjT,WAAW,IAAIlE,IAAIW,OAAO,CAACjE,oBAAoB,EAAE;YACjE,uEAAuE;YACvEgb,sBAAsBjK;QACxB;QAEAA,cAActT,oBAAoBsT;QAClCiK,sBAAsBvd,oBAAoBud;QAC1C,IAAI,IAAI,CAACzS,gBAAgB,EAAE;YACzByS,sBAAsB,IAAI,CAACzS,gBAAgB,CAACnE,SAAS,CAAC4W;QACxD;QAEA,MAAMqC,iBAAiB,CAACC;YACtB,MAAMxN,WAAW;gBACfyN,aAAaD,SAASE,SAAS,CAACC,YAAY;gBAC5CvO,YAAYoO,SAASE,SAAS,CAACE,mBAAmB;gBAClDtT,UAAUkT,SAASE,SAAS,CAACG,sBAAsB;YACrD;YACA,MAAMzO,aAAa1S,kBAAkBsT;YACrC,MAAM,EAAE1F,QAAQ,EAAE,GAAG,IAAI,CAACtH,UAAU;YAEpC,IACEsH,YACA0F,SAAS1F,QAAQ,KAAK,SACtB0F,SAASyN,WAAW,CAACnF,UAAU,CAAC,MAChC;gBACAtI,SAASyN,WAAW,GAAG,GAAGnT,WAAW0F,SAASyN,WAAW,EAAE;YAC7D;YAEA,IAAIzN,SAASyN,WAAW,CAACnF,UAAU,CAAC,MAAM;gBACxCtI,SAASyN,WAAW,GAAGthB,yBAAyB6T,SAASyN,WAAW;YACtE;YAEA7Y,IACGoL,QAAQ,CAACA,SAASyN,WAAW,EAAErO,YAC/Ba,IAAI,CAACD,SAASyN,WAAW,EACzBvN,IAAI;QACT;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAIuL,mBAAmB;YACrBP,sBAAsB,IAAI,CAAC3J,iBAAiB,CAAC2J;YAC7CjK,cAAc,IAAI,CAACM,iBAAiB,CAACN;QACvC;QAEA,IAAI6M,cAA6B;QACjC,IACE,CAACb,iBACDtC,SACA,CAACd,KAAKjQ,uBAAuB,IAC7B,CAAC2Q,0BACD,CAAC+B,oBACD,CAACC,qBACD;YACAuB,cAAc,GAAGtL,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACvC,AAAC7O,CAAAA,aAAa,OAAOuX,wBAAwB,GAAE,KAAM1I,SACjD,KACA0I,sBACH/K,MAAMnG,GAAG,GAAG,SAAS,IAAI;QAC9B;QAEA,IAAI,AAACkQ,CAAAA,aAAaC,SAAQ,KAAMQ,OAAO;YACrCmD,cAAc,GAAGtL,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK7O,WAC5CwM,MAAMnG,GAAG,GAAG,SAAS,IACrB;QACJ;QAEA,IAAI8T,aAAa;YACfA,cAActd,iBAAiBsd;YAE/B,+CAA+C;YAC/CA,cACEA,gBAAgB,YAAYna,aAAa,MAAM,MAAMma;QACzD;QAEA,sDAAsD;QACtD,MAAMtJ,mBACJtP,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,AAACjC,WAAmByR,kBAAkB,GAClC,AAACzR,WAAmByR,kBAAkB,GACtC,MAAM,IAAI,CAACH,mBAAmB,CAAC;YAC7BC,gBAAgBnJ,OAAOqI,MAAM,CAAC,CAAC,GAAGpQ,IAAIW,OAAO;QAC/C;QAEN,0EAA0E;QAC1EqQ,iBAAiBG,iBAAiB;QAoBlC,MAAMoJ,WAAqB,OAAO,EAChC5M,SAAS,EACT6M,gBAAgB,KAAK,EACrBC,mBAAmB,EACpB;YACC,2DAA2D;YAC3D,IAAIrU,0BAGF,AAFA,uEAAuE;YACvE,6DAA6D;YAC5D,CAAC6R,qBAAqB5B,KAAKpS,GAAG,KAAK,QACpC,qEAAqE;YACrE,gBAAgB;YACf,CAACkT,SAAS,CAACL,qBACZ,mEAAmE;YACnE,QAAQ;YACR,OAAOnJ,cAAc,YACrB,sEAAsE;YACtE,uBAAuB;YACvBoL;YAEF,MAAM2B,YAAY1hB,SAASgH,IAAIiB,GAAG,IAAI,IAAI,MAAM0L,KAAK;YAErD,mDAAmD;YACnD,kBAAkB;YAClB,IAAI0J,KAAK9U,MAAM,EAAE;gBACfwG,OAAOC,IAAI,CAACqO,KAAK9U,MAAM,EAAE2R,OAAO,CAAC,CAACzD;oBAChC,OAAOiL,SAAS,CAACjL,IAAI;gBACvB;YACF;YACA,MAAMkL,mBACJlN,gBAAgB,OAAO,IAAI,CAACjO,UAAU,CAAC2C,aAAa;YAEtD,MAAMyY,cAAc9hB,UAAU;gBAC5BqH,UAAU,GAAGuX,sBAAsBiD,mBAAmB,MAAM,IAAI;gBAChE,uDAAuD;gBACvDhO,OAAO+N;YACT;YAEA,uEAAuE;YACvE,MAAMG,uBAAuB5B,aAAaR;YAE1C,MAAMtS,aAA+B;gBACnC,GAAGmQ,UAAU;gBACb,GAAGD,IAAI;gBACP,GAAIJ,YACA;oBACEjF;oBACA,gEAAgE;oBAChE,+DAA+D;oBAC/D,4DAA4D;oBAC5D,WAAW;oBACX8J,cAAc3D,SAAS,CAACxJ,aAAa,CAACoL;oBACtCgC,eAAe,IAAI,CAACvb,UAAU,CAACC,YAAY,CAACsb,aAAa;gBAC3D,IACA,CAAC,CAAC;gBACN9C;gBACA2C;gBACA5L;gBACAhK;gBACArC;gBACAgX,oBAAoB,IAAI,CAACna,UAAU,CAACC,YAAY,CAACka,kBAAkB;gBACnE,uFAAuF;gBACvF,8DAA8D;gBAC9D,SAAS;gBACTqB,gBACEpE,kBAAkBI,qBACdle,UAAU;oBACR,iEAAiE;oBACjE,UAAU;oBACVqH,UAAU,GAAGsN,cAAckN,mBAAmB,MAAM,IAAI;oBACxDhO,OAAO+N;gBACT,KACAE;gBACNnb,cAAc;oBACZ,GAAG4W,KAAK5W,YAAY;oBACpBgZ;gBACF;gBACArS;gBACAyU;gBACAhB;gBACAoB,aAAaxB;gBACb1C;gBACApJ;gBACAiH,WAAW,IAAI,CAACF,YAAY;gBAC5BwG,SAAS9Z,IAAI8Z,OAAO,CAACrS,IAAI,CAACzH;gBAC1B+Z,kBAAkBtb;gBAClB,wBAAwB;gBACxBub,cAAc,AAAC,IAAI,CAASA,YAAY;YAC1C;YAEA,IAAIzC,sBAAsBC,wBAAwB;gBAChDxS,0BAA0B;gBAC1BD,WAAWkV,UAAU,GAAG;gBACxBlV,WAAWC,uBAAuB,GAAG;gBACrCD,WAAWmV,kBAAkB,GAAG;gBAChCnV,WAAW2U,YAAY,GAAG;gBAC1B3U,WAAWyS,sBAAsB,GAAGA;YACtC;YAEA,qEAAqE;YACrE,wBAAwB;YACxB,IAAIrY;YAEJ,IAAI4X,aAAa;gBACf,IACE/a,sBAAsB+a,gBACtB9a,mBAAmB8a,gBACnBhb,qBAAqBgb,cACrB;oBACA,mDAAmD;oBACnD,IACEnY,IAAIsK,MAAM,KAAK,aACf,CAACoM,aACD,CAACtZ,sBAAsB+a,cACvB;wBACA,MAAM5b,aAAayD,KAAKoB,KAAK,IAAIiR,SAAS,MAAM;4BAAEkJ,QAAQ;wBAAI;wBAC9D,OAAO;oBACT;oBAEA,MAAMC,UAAU7d,kBAAkBqC,OAAOA,IAAIkM,eAAe,GAAGlM;oBAC/D,MAAMoS,WAAWxU,mBAAmBwD,OAAOA,IAAIgL,gBAAgB,GAAGhL;oBAElE,IACEkV,WAAWmF,YAAY,CAAC9I,OAAO,IAC/BjR,QAAQC,GAAG,CAACC,YAAY,KAAK,QAC7B;wBACA,MAAM8Z,gBAAgB1iB,SACpB2B,eAAeqF,KAAK,cAAcA,IAAIiB,GAAG;wBAE3C,IAAI0a,eAAeD,cAAcvb,QAAQ,IAAI;wBAE7C,KAAK,MAAMkD,cAAc;4BACvB,IAAI,CAACjD,WAAW,CAACC,kBAAkB;4BACnC,IAAI,CAACD,WAAW,CAACS,WAAW;4BAC5B,IAAI,CAACT,WAAW,CAACW,GAAG;yBACrB,CAAE;4BACD,IAAIsC,8BAAAA,WAAY/C,KAAK,CAACqb,eAAe;gCACnCA,eAAetY,WAAWvC,SAAS,CAAC6a;4BACtC;wBACF;wBAEA,6DAA6D;wBAC7D,0FAA0F;wBAC1F,sEAAsE;wBACtE,IAAI,CAAE,CAAA,IAAI,CAACzX,WAAW,IAAIuS,eAAc,GAAI;4BAC1C+E,QAAQva,GAAG,GAAG,GAAG0a,eAAeD,cAAcjE,MAAM,IAAI,IAAI;wBAC9D;wBAEA,wCAAwC;wBACxC5c,eAAe2gB,SAAS7gB,eAAeqF;wBACvCtF,eAAe8gB,SAAS,cAAc,IAAI,CAAC1X,GAAG;wBAC9CpJ,eAAe8gB,SAAS,WAAW,IAAI,CAAC9W,OAAO;wBAC/ChK,eAAe8gB,SAAS,iBAAiBhB;wBACzC9f,eAAe8gB,SAAS,SAAS7O;wBACjCjS,eAAe8gB,SAAS,UAAUnF,KAAK9U,MAAM;wBAC7C7G,eACE8gB,SACA,gBACA,IAAI,CAACrV,UAAU,CAACyV,YAAY;wBAE9BlhB,eAAe8gB,SAAS,eAAe,IAAI,CAACtX,WAAW;wBAEvD,IAAIiC,WAAW+D,GAAG,EAAE;4BAClBxP,eAAe8gB,SAAS,eAAerV,WAAW+D,GAAG;wBACvD;wBAEA,MAAMyI,UAMuB2D,WAAWmF,YAAY,CAAC9I,OAAO;wBAE5D,MAAMkJ,kBACJ,qDAAqD;wBACrD,qDAAqD;wBACrD,qDAAqD;wBACrD,0BAA0B;wBAC1Bna,QAAQC,GAAG,CAACma,QAAQ,KAAK,gBACrB,IAAIC,MAAMP,SAAS;4BACjBzP,KAAIiQ,MAAW,EAAEC,IAAI;gCACnB,IAAI,OAAOD,MAAM,CAACC,KAAK,KAAK,YAAY;oCACtC,OAAOD,MAAM,CAACC,KAAK,CAACpT,IAAI,CAACmT;gCAC3B;gCACA,OAAOA,MAAM,CAACC,KAAK;4BACrB;4BACAC,KAAIF,MAAW,EAAEC,IAAI,EAAEvM,KAAK;gCAC1B,IAAIuM,SAAS,gBAAgB;;oCACzBjc,IAAYmc,YAAY,GAAGzM;gCAC/B;gCACAsM,MAAM,CAACC,KAAK,GAAGvM;gCACf,OAAO;4BACT;wBACF,KACA8L;wBAENjb,SAAS,MAAMoS,QAAQkJ,iBAAiBzJ,UAAU;4BAChDwC,WAAW,IAAI,CAACF,YAAY;wBAC9B;wBAEA,uCAAuC;wBACvC,OAAO;oBACT,OAAO;wBACL,IAAIrX,mBAAmB8a,cAAc;4BACnC,wEAAwE;4BACxE,sEAAsE;4BACtE,iCAAiC;4BACjC,4HAA4H;4BAC5HhS,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;4BACnDI,WAAWiW,uBAAuB,GAChC9F,WAAW8F,uBAAuB;4BAEpC,iDAAiD;4BACjD,IAAI;gCACF7b,SAAS,MAAM4X,YAAY5D,MAAM,CAC/BiH,SACApJ,UACA;oCACE5D,MAAMrO;oCACNoB,QAAQ8U,KAAK9U,MAAM;oCACnBoL;oCACAxG;oCACAkW,eAAe;wCACb5a,SAAS,IAAI,CAACA,OAAO;wCACrByE,cAAc,IAAI,CAAC1G,UAAU,CAAC0G,YAAY;wCAC1C6O,cACE,IAAI,CAAC1Q,aAAa,CAAC0Q,YAAY,IAAIlV;oCACvC;oCACAyc,eAAe;wCACbC,YAAY/B;wCACZS,aAAa9U,WAAW8U,WAAW;wCACnCuB,+BAA+B7hB,eAC7BqF,KACA;oCAEJ;gCACF;4BAEJ,EAAE,OAAOkK,KAAK;gCACZ,MAAM,IAAI,CAACtB,6BAA6B,CAACsB,KAAKlK,KAAK;oCACjDyc,YAAY;oCACZC,WAAWvc;oCACXwc,WAAW;oCACXC,kBAAkB1e,oBAAoB;wCACpC4c,cAAc3D;wCACd0C,sBAAsB1T,WAAW0T,oBAAoB;oCACvD;gCACF;gCACA,MAAM3P;4BACR;wBACF,OAAO;4BACL,MAAM2S,SAASvG,WAAW6B,WAAW;4BAErC,4EAA4E;4BAC5E,8DAA8D;4BAC9D,4HAA4H;4BAC5HhS,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;4BAEnD,MAAM+W,UAAsC;gCAC1CtO,MAAMkI,YAAY,SAASvW;gCAC3BoB,QAAQ8U,KAAK9U,MAAM;gCACnBoL;gCACA8N;gCACAtU;gCACAzG,0BAA0B,IAAI,CAACH,2BAA2B;gCAC1D8c,eAAe;oCACb5a,SAAS,IAAI,CAACA,OAAO;gCACvB;4BACF;4BAEA,4DAA4D;4BAC5D,iEAAiE;4BACjE,wCAAwC;4BACxC,IACE,IAAI,CAACjC,UAAU,CAACC,YAAY,CAAC6I,SAAS,IACtC,IAAI,CAACnC,UAAU,CAAClC,GAAG,IACnB,CAACiU,wBACD,CAACnB,wBACD;gCACA,MAAMgG,SAAS,MAAMF,OAAOE,MAAM,CAAC/c,KAAKoB,KAAK0b;gCAE7C,6DAA6D;gCAC7D,yBAAyB;gCACzB,IAAIC,OAAOC,QAAQ,CAACC,qBAAqB,EAAE;oCACzC9W,WAAW8W,qBAAqB,GAC9BF,OAAOC,QAAQ,CAACC,qBAAqB;gCACzC;4BACF;4BAEA,iDAAiD;4BACjD1c,SAAS,MAAMsc,OAAOtI,MAAM,CAACvU,KAAKoB,KAAK0b;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAM,qBAAiD,CAAjD,IAAI3d,MAAM,yCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAgD;gBACxD;YACF,OAAO;gBACL,oEAAoE;gBACpE,iBAAiB;gBACjBoB,SAAS,MAAM,IAAI,CAAC2c,UAAU,CAACld,KAAKoB,KAAKjB,UAAUwM,OAAOxG;YAC5D;YAEA,MAAM,EAAE6W,QAAQ,EAAE,GAAGzc;YAErB,MAAM,EACJyT,YAAY,EACZrT,UAAU,CAAC,CAAC,EACZ,oEAAoE;YACpEwc,WAAWC,SAAS,EACrB,GAAGJ;YAEJ,IAAII,WAAW;gBACbzc,OAAO,CAAChE,uBAAuB,GAAGygB;YACpC;YAEA,2DAA2D;;YACzDpd,IAAYmc,YAAY,GAAGa,SAASb,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACElG,aACAkB,SACAnD,CAAAA,gCAAAA,aAAcqJ,UAAU,MAAK,KAC7B,CAAC,IAAI,CAAClX,UAAU,CAAClC,GAAG,IACpB,CAACwU,mBACD;gBACA,MAAM6E,oBAAoBN,SAASM,iBAAiB;gBAEpD,MAAMpT,MAAM,qBAOX,CAPW,IAAI/K,MACd,CAAC,+CAA+C,EAAEsO,cAChD6P,CAAAA,qCAAAA,kBAAmBC,WAAW,IAC1B,CAAC,UAAU,EAAED,kBAAkBC,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAOZ;gBAEA,IAAID,qCAAAA,kBAAmBE,KAAK,EAAE;oBAC5B,MAAMA,QAAQF,kBAAkBE,KAAK;oBACrCtT,IAAIsT,KAAK,GAAGtT,IAAIuT,OAAO,GAAGD,MAAMpb,SAAS,CAACob,MAAME,OAAO,CAAC;gBAC1D;gBAEA,MAAMxT;YACR;YAEA,uEAAuE;YACvE,iBAAiB;YAEjB,uBAAuB;YACvB,IAAI,gBAAgB8S,YAAYA,SAASW,UAAU,EAAE;gBACnD,OAAO;oBACLjO,OAAO;oBACPsE;gBACF;YACF;YAEA,uBAAuB;YACvB,IAAIgJ,SAASY,UAAU,EAAE;gBACvB,OAAO;oBACLlO,OAAO;wBACLtE,MAAM5S,gBAAgBqlB,QAAQ;wBAC9BC,OAAOd,SAAShD,QAAQ,IAAIgD,SAASe,UAAU;oBACjD;oBACA/J;gBACF;YACF;YAEA,mBAAmB;YACnB,IAAIzT,OAAOyd,MAAM,EAAE;gBACjB,OAAO;YACT;YAEA,kEAAkE;YAClE,IAAI/H,WAAW;gBACb,OAAO;oBACLvG,OAAO;wBACLtE,MAAM5S,gBAAgBylB,QAAQ;wBAC9BC,MAAM3d;wBACNI;wBACAwd,SAASnB,SAASe,UAAU;wBAC5BpQ,WAAWqP,SAASrP,SAAS;wBAC7B4N,QAAQyB,SAASpR,UAAU;wBAC3BwS,aAAapB,SAASoB,WAAW;oBACnC;oBACApK;gBACF;YACF;YAEA,OAAO;gBACLtE,OAAO;oBACLtE,MAAM5S,gBAAgB6lB,KAAK;oBAC3BH,MAAM3d;oBACNyZ,UAAUgD,SAAShD,QAAQ,IAAIgD,SAASe,UAAU;oBAClDpd;oBACA4a,QAAQtF,YAAY7U,IAAIwK,UAAU,GAAG/L;gBACvC;gBACAmU;YACF;QACF;QAEA,IAAIsK,oBAAuC,OAAO,EAChDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACf;YACC,MAAMC,eAAe,CAAC,IAAI,CAACvY,UAAU,CAAClC,GAAG;YACzC,MAAM0a,aAAaJ,eAAend,IAAI6S,IAAI;YAE1C,sEAAsE;YACtE,IAAI,CAACoB,eAAeuC,WAAW;gBAC7B,IAAId,mBAAmB;oBACrB,MAAMe,cAAc,MAAM,IAAI,CAAC3C,cAAc,CAAC;wBAC5C/U;wBACA+Q,gBAAgBlR,IAAIW,OAAO;wBAC3BsV;wBACAzH,MAAM8H,WAAW9H,IAAI;oBACvB;oBAEA6G,cAAcwC,YAAYxC,WAAW;oBACrCC,eAAeuC,YAAYvC,YAAY;gBACzC,OAAO;oBACLD,cAAcxV;oBACdyV,eAAelX,aAAawgB,SAAS;gBACvC;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,IACEtJ,iBAAiBlX,aAAaygB,SAAS,IACvC5kB,MAAM+F,IAAIW,OAAO,CAAC,aAAa,IAAI,KACnC;gBACA2U,eAAelX,aAAa0gB,sBAAsB;YACpD;YAEA,wDAAwD;YACxD,iCAAiC;YACjC,IACEjF,wBACAC,2BACA,CAAC0E,sBACD,CAAC,IAAI,CAACta,WAAW,EACjB;gBACA,MAAM,IAAI,CAACrC,SAAS,CAAC7B,KAAKoB;gBAC1B,OAAO;YACT;YAEA,IAAIod,CAAAA,sCAAAA,mBAAoBO,OAAO,MAAK,CAAC,GAAG;gBACtClF,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACCvE,CAAAA,iBAAiBlX,aAAawgB,SAAS,IAAIJ,kBAAiB,GAC7D;gBACAlJ,eAAelX,aAAa0gB,sBAAsB;YACpD;YAEA,sEAAsE;YACtE,uDAAuD;YACvD,EAAE;YACF,sEAAsE;YACtE,8DAA8D;YAC9D,EAAE;YACF,sEAAsE;YACtE,0BAA0B;YAC1B,IAAIE,gBAAgB1E;YACpB,IAAI,CAAC0E,iBAAiB3I,KAAKpS,GAAG,IAAIgS,WAAW;gBAC3C+I,gBAAgBhiB,iBAAiB0a;YACnC;YACA,IAAIsH,iBAAiBrS,MAAMnG,GAAG,EAAE;gBAC9BwY,gBAAgBA,cAAc5R,OAAO,CAAC,UAAU;YAClD;YAEA,MAAM6R,8BACJD,kBAAiB3J,+BAAAA,YAAa0C,QAAQ,CAACiH;YAEzC,qEAAqE;YACrE,kCAAkC;YAElC,IAAI,IAAI,CAACxf,UAAU,CAACC,YAAY,CAACwI,qBAAqB,EAAE;gBACtDqN,eAAelX,aAAa0gB,sBAAsB;YACpD;YAEA,oEAAoE;YACpE,kCAAkC;YAClC,EAAE;YACF,gCAAgC;YAChC,0CAA0C;YAC1C,wEAAwE;YACxE,iEAAiE;YACjE,yBAAyB;YACzB,iEAAiE;YACjE,qEAAqE;YACrE,EAAE;YACF,IACEpd,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC,IAAI,CAACsC,WAAW,IACjBoR,iBAAiBlX,aAAa0gB,sBAAsB,IACpDE,iBACA,CAACL,cACD,CAAClF,iBACD7B,aACC8G,CAAAA,gBAAgB,CAACrJ,eAAe,CAAC4J,2BAA0B,GAC5D;gBACA,IAGE,AAFA,2DAA2D;gBAC3D,kBAAkB;gBACjBP,CAAAA,gBAAiBrJ,eAAeA,CAAAA,+BAAAA,YAAarT,MAAM,IAAG,CAAC,KACxD,2DAA2D;gBAC3DsT,iBAAiBlX,aAAawgB,SAAS,EACvC;oBACA,MAAM,IAAIhgB;gBACZ;gBAEA,IAAIsgB;gBAEJ,kCAAkC;gBAClC,IAAI7hB,mBAAmBiZ,WAAW6B,WAAW,KAAK,CAACF,mBAAmB;oBACpE,gEAAgE;oBAChE,oCAAoC;oBACpCiH,mBAAmB,MAAM,IAAI,CAACtV,aAAa,CAACmC,GAAG,CAC7C2S,eAAgB1P,SAAS,CAAC,CAAC,EAAEA,SAAS7O,UAAU,GAAGA,WAAY,MAC/D,yDAAyD;oBACzD,OAAO,EACLqe,oBAAoBW,6BAA6B,IAAI,EACtD;wBACC,2DAA2D;wBAC3D,8DAA8D;wBAC9D,gEAAgE;wBAChE,iEAAiE;wBACjE,YAAY;wBACZ,IAAIT,cAAc;4BAChB,OAAOpgB,qBAAqB6gB;wBAC9B;wBAEA,kEAAkE;wBAClE,UAAU;wBACV,OAAO5E,SAAS;4BACd5M,WAAW9N;4BACX,2DAA2D;4BAC3D,+DAA+D;4BAC/D,qBAAqB;4BACrB2a,eAAe;4BACfC,qBAAqB;wBACvB;oBACF,GACA;wBACE2E,WAAWjhB,UAAUkgB,KAAK;wBAC1BrN;wBACAyH;wBACA8D,YAAY;oBACd;gBAEJ,OAGK,IACH9D,qBACAtb,qBAAqBmZ,WAAW6B,WAAW,KAC3C,CAACzM,cACD;oBACA,gEAAgE;oBAChE,oCAAoC;oBACpCwT,mBAAmB,MAAM,IAAI,CAACtV,aAAa,CAACmC,GAAG,CAC7C2S,eAAeve,WAAW,MAC1B,yDAAyD;oBACzD,UACEoa,SAAS;4BACP,4DAA4D;4BAC5D,QAAQ;4BACR5M,WAAW9N;4BACX2a,eAAe3a;4BACf4a,qBACE,yDAAyD;4BACzD,wDAAwD;4BACxD,YAAY;4BACZiE,gBAAgB7F,uBACZtgB,uBAAuB4H,YACvB;wBACR,IACF;wBACEif,WAAWjhB,UAAU8f,QAAQ;wBAC7BjN;wBACAyH;wBACA8D,YAAY;oBACd;gBAEJ;gBAEA,wEAAwE;gBACxE,IAAI2C,qBAAqB,MAAM,OAAO;gBAEtC,qEAAqE;gBACrE,IAAIA,kBAAkB;oBACpB,sEAAsE;oBACtE,iCAAiC;oBACjC,OAAOA,iBAAiBlL,YAAY;oBAEpC,OAAOkL;gBACT;YACF;YAEA,wEAAwE;YACxE,oEAAoE;YACpE,MAAMvR,YACJ,CAACkM,wBAAwB,CAAC4E,kBAAkB3F,mBACxCA,mBACAjZ;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACE,AAAC8Y,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAOjL,cAAc,aACrB;gBACA,OAAO;oBACLqG,cAAc;wBAAEqJ,YAAY;wBAAGlJ,QAAQtU;oBAAU;oBACjD6P,OAAO;wBACLtE,MAAM5S,gBAAgB6lB,KAAK;wBAC3BH,MAAMhkB,aAAakf,UAAU,CAAC;wBAC9BY,UAAU,CAAC;wBACXrZ,SAASd;wBACT0b,QAAQ1b;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAM4a,sBACJ7C,aACAa,qBACC9d,CAAAA,eAAeqF,KAAK,0BAA0B6Y,oBAAmB,IAC9DtgB,uBAAuB4H,YACvB;YAEN,sBAAsB;YACtB,OAAOoa,SAAS;gBACd5M;gBACA6M,eAAe3a;gBACf4a;YACF;QACF;QAEA,IACE/Y,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,wDAAwD;QACxD0U,WAAWmF,YAAY,CAAC9I,OAAO,IAC9BtV,CAAAA,mBAAmBiZ,WAAW6B,WAAW,KACxC/a,sBAAsBkZ,WAAW6B,WAAW,KAC5Chb,qBAAqBmZ,WAAW6B,WAAW,CAAA,GAC7C;YACA,IACEA,CAAAA,+BAAAA,YAAakH,KAAK,KAClB3lB,eAAeyG,aACdmW,CAAAA,WAAWpB,cAAc,IAAIe,SAAQ,GACtC;gBACA,MAAM,IAAI,CAACf,cAAc,CAAC;oBACxB/U;oBACA+Q,gBAAgBlR,IAAIW,OAAO;oBAC3B6N,MAAM8H,WAAW9H,IAAI;oBACrByH;gBACF;YACF;YACA,MAAMsE,SAAS;gBACb5M,WAAW9N;gBACX2a,eAAe;gBACfC,qBAAqB;YACvB;YACA,OAAO;QACT;QAEA,MAAMjE,aAAa,MAAM,IAAI,CAAC5M,aAAa,CAACmC,GAAG,CAC7CuO,aACAgE,mBACA;YACEc,WACE,sEAAsE;YACtE,qCAAqC;YACrCjH,CAAAA,+BAAAA,YAAa7J,UAAU,CAAClD,IAAI,KAC3B6K,CAAAA,YAAY9X,UAAU8f,QAAQ,GAAG9f,UAAUkgB,KAAK,AAAD;YAClDrN;YACA6I;YACAyF,YAAYtf,IAAIW,OAAO,CAAC4e,OAAO,KAAK;YACpC9G;QACF;QAGF,IAAIgB,eAAe;YACjBrY,IAAI8S,SAAS,CACX,iBACA;QAEJ;QAEA,IAAI,CAACsC,YAAY;YACf,IACE8D,eACA,CAAET,CAAAA,wBAAwBC,uBAAsB,KAChD,CAACzc,mBAAmBiZ,WAAW6B,WAAW,KAC1C,CAAC/a,sBAAsBkZ,WAAW6B,WAAW,KAC7C,CAAChb,qBAAqBmZ,WAAW6B,WAAW,GAC5C;gBACA,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,qBAA8D,CAA9D,IAAIhZ,MAAM,sDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;YACA,OAAO;QACT;QAEA,MAAMqgB,cACJhJ,EAAAA,oBAAAA,WAAW9G,KAAK,qBAAhB8G,kBAAkBpL,IAAI,MAAK5S,gBAAgBylB,QAAQ,IACnD,OAAOzH,WAAW9G,KAAK,CAAC/B,SAAS,KAAK;QAExC,IACEwJ,SACA,yEAAyE;QACzE,kEAAkE;QAClE,gDAAgD;QAChD,CAAC4B,uBACA,CAAA,CAACyG,eAAetH,oBAAmB,GACpC;YACA,IAAI,CAAC,IAAI,CAAChU,WAAW,EAAE;gBACrB,gDAAgD;gBAChD,iCAAiC;gBACjC9C,IAAI8S,SAAS,CACX,kBACA2F,uBACI,gBACArD,WAAWiJ,MAAM,GACf,SACAjJ,WAAWuI,OAAO,GAChB,UACA;YAEZ;YACA,0EAA0E;YAC1E,yDAAyD;YACzD3d,IAAI8S,SAAS,CAACxY,0BAA0B;QAC1C;QAEA,MAAM,EAAEgU,OAAOgQ,UAAU,EAAE,GAAGlJ;QAE9B,yDAAyD;QACzD,IAAIkJ,CAAAA,8BAAAA,WAAYtU,IAAI,MAAK5S,gBAAgBmnB,KAAK,EAAE;YAC9C,MAAM,qBAAgE,CAAhE,IAAIjhB,eAAe,+CAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,sDAAsD;QACtD,IAAIsV;QAEJ,0EAA0E;QAC1E,oCAAoC;QACpC,IAAI8E,kBAAkB;YACpB9E,eAAe;gBAAEqJ,YAAY;gBAAGlJ,QAAQtU;YAAU;QACpD,OAKK,IACH,IAAI,CAACqE,WAAW,IAChBwH,gBACA,CAACwM,wBACDO,mBACA;YACAzE,eAAe;gBAAEqJ,YAAY;gBAAGlJ,QAAQtU;YAAU;QACpD,OAAO,IAAI,CAAC,IAAI,CAACsG,UAAU,CAAClC,GAAG,IAAK2S,kBAAkB,CAACqB,mBAAoB;YACzE,2DAA2D;YAC3D,IAAIwB,eAAe;gBACjBzF,eAAe;oBAAEqJ,YAAY;oBAAGlJ,QAAQtU;gBAAU;YACpD,OAIK,IAAI,CAACsX,OAAO;gBACf,IAAI,CAAC/V,IAAIwe,SAAS,CAAC,kBAAkB;oBACnC5L,eAAe;wBAAEqJ,YAAY;wBAAGlJ,QAAQtU;oBAAU;gBACpD;YACF,OAQK,IAAI6W,WAAW;gBAClB,MAAMmJ,qBAAqBllB,eAAeqF,KAAK;gBAE/CgU,eAAe;oBACbqJ,YACE,OAAOwC,uBAAuB,cAAc,IAAIA;oBAClD1L,QAAQtU;gBACV;YACF,OAAO,IAAI8W,WAAW;gBACpB3C,eAAe;oBAAEqJ,YAAY;oBAAGlJ,QAAQtU;gBAAU;YACpD,OAAO,IAAI2W,WAAWxC,YAAY,EAAE;gBAClC,wEAAwE;gBACxE,oBAAoB;gBACpB,IAAI,OAAOwC,WAAWxC,YAAY,CAACqJ,UAAU,KAAK,UAAU;wBAUtD7G;oBATJ,IAAIA,WAAWxC,YAAY,CAACqJ,UAAU,GAAG,GAAG;wBAC1C,MAAM,qBAEL,CAFK,IAAIle,MACR,CAAC,2CAA2C,EAAEqX,WAAWxC,YAAY,CAACqJ,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEArJ,eAAe;wBACbqJ,YAAY7G,WAAWxC,YAAY,CAACqJ,UAAU;wBAC9ClJ,QACEqC,EAAAA,2BAAAA,WAAWxC,YAAY,qBAAvBwC,yBAAyBrC,MAAM,KAAI,IAAI,CAAC3U,UAAU,CAAC2I,UAAU;oBACjE;gBACF,OAGK;oBACH6L,eAAe;wBAAEqJ,YAAY5gB;wBAAgB0X,QAAQtU;oBAAU;gBACjE;YACF;QACF;QAEA2W,WAAWxC,YAAY,GAAGA;QAE1B,IACE,OAAOgF,0BAA0B,YACjC0G,CAAAA,8BAAAA,WAAYtU,IAAI,MAAK5S,gBAAgBylB,QAAQ,IAC7CyB,WAAWtB,WAAW,EACtB;gBAeasB;YAdb,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YAEtE,oEAAoE;YACpE,uEAAuE;YACvE,wEAAwE;YACxE,sEAAsE;YACtE,sEAAsE;YACtE,wDAAwD;YACxDte,IAAI8S,SAAS,CAAC3Y,0BAA0B;YAExC,sEAAsE;YACtE,8CAA8C;YAC9C,MAAMukB,QAAOJ,sBAAAA,WAAW/e,OAAO,qBAAlB+e,mBAAoB,CAAC/iB,uBAAuB;YACzD,IAAI,IAAI,CAACuH,WAAW,IAAIiT,SAAS2I,QAAQ,OAAOA,SAAS,UAAU;gBACjE1e,IAAI8S,SAAS,CAACvX,wBAAwBmjB;YACxC;YAEA,MAAMC,iBAAiBL,WAAWtB,WAAW,CAACrS,GAAG,CAACiN;YAClD,IAAI+G,mBAAmBlgB,WAAW;gBAChC,YAAY;gBACZ,OAAO;oBACLkU,MAAM;oBACNtH,MAAMvS,aAAakf,UAAU,CAAC2G;oBAC9B,mEAAmE;oBACnE,+BAA+B;oBAC/B/L,cAAcwC,WAAWxC,YAAY;gBACvC;YACF;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,sEAAsE;YACtE,qEAAqE;YACrE,oEAAoE;YACpE,gCAAgC;YAChC5S,IAAIwK,UAAU,GAAG;YACjB,OAAO;gBACLmI,MAAM;gBACNtH,MAAMvS,aAAakf,UAAU,CAAC;gBAC9BpF,YAAY,EAAEwC,8BAAAA,WAAYxC,YAAY;YACxC;QACF;QAEA,yEAAyE;QACzE,8BAA8B;QAC9B,MAAMgM,eAAerlB,eAAeqF,KAAK;QACzC,IAAIggB,cAAc;gBASRxJ,oBAEIA;YAVZ,MAAMjT,WAAW,MAAMyc,aACrB;gBACE,GAAGxJ,UAAU;gBACb,0CAA0C;gBAC1C,wCAAwC;gBACxC9G,OAAO;oBACL,GAAG8G,WAAW9G,KAAK;oBACnBtE,MACEoL,EAAAA,qBAAAA,WAAW9G,KAAK,qBAAhB8G,mBAAkBpL,IAAI,MAAK5S,gBAAgBylB,QAAQ,GAC/C,UACAzH,qBAAAA,WAAW9G,KAAK,qBAAhB8G,mBAAkBpL,IAAI;gBAC9B;YACF,GACA;gBACEnK,KAAKtG,eAAeqF,KAAK;YAC3B;YAEF,IAAIuD,UAAU;gBACZ,0CAA0C;gBAC1C,OAAO;YACT;QACF;QAEA,IAAI,CAACmc,YAAY;gBAQblJ;YAPF,oDAAoD;YACpD,qDAAqD;YACrD,4DAA4D;YAC5D,2BAA2B;YAC3B9b,eACEsF,KACA,uBACAwW,4BAAAA,WAAWxC,YAAY,qBAAvBwC,0BAAyB6G,UAAU;YAGrC,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAI7G,WAAWxC,YAAY,IAAI,CAAC5S,IAAIwe,SAAS,CAAC,kBAAkB;gBAC9Dxe,IAAI8S,SAAS,CACX,iBACAra,sBAAsB2c,WAAWxC,YAAY;YAEjD;YACA,IAAIiE,mBAAmB;gBACrB7W,IAAIwK,UAAU,GAAG;gBACjBxK,IAAIqL,IAAI,CAAC,qBAAqBC,IAAI;gBAClC,OAAO;YACT;YAEA,IAAI,IAAI,CAACvG,UAAU,CAAClC,GAAG,EAAE;gBACvBvJ,eAAesF,KAAK,iCAAiCG;YACvD;YACA,MAAM,IAAI,CAAC0B,SAAS,CAAC7B,KAAKoB,KAAK;gBAAEjB;gBAAUwM;YAAM,GAAG;YACpD,OAAO;QACT,OAAO,IAAI+S,WAAWtU,IAAI,KAAK5S,gBAAgBqlB,QAAQ,EAAE;YACvD,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAIrH,WAAWxC,YAAY,IAAI,CAAC5S,IAAIwe,SAAS,CAAC,kBAAkB;gBAC9Dxe,IAAI8S,SAAS,CACX,iBACAra,sBAAsB2c,WAAWxC,YAAY;YAEjD;YAEA,IAAIiE,mBAAmB;gBACrB,OAAO;oBACLlE,MAAM;oBACNtH,MAAMvS,aAAakf,UAAU,CAC3B,6BAA6B;oBAC7B6G,KAAKC,SAAS,CAACR,WAAW5B,KAAK;oBAEjC9J,cAAcwC,WAAWxC,YAAY;gBACvC;YACF,OAAO;gBACL,MAAM+F,eAAe2F,WAAW5B,KAAK;gBACrC,OAAO;YACT;QACF,OAAO,IAAI4B,WAAWtU,IAAI,KAAK5S,gBAAgB2nB,SAAS,EAAE;YACxD,qDAAqD;YACrD,MAAM,qBAAuD,CAAvD,IAAIhhB,MAAM,CAAC,0CAA0C,CAAC,GAAtD,qBAAA;uBAAA;4BAAA;8BAAA;YAAsD;QAC9D,OAAO,IAAIugB,WAAWtU,IAAI,KAAK5S,gBAAgBylB,QAAQ,EAAE;gBAkC1CyB;YAjCb,oEAAoE;YACpE,gBAAgB;YAChB,IAAIF,eAAe1G,kBAAkB;gBACnC,MAAM,qBAEL,CAFK,IAAI3Z,MACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIugB,WAAW/e,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAG+e,WAAW/e,OAAO;gBAAC;gBAExC,IAAI,CAAC,IAAI,CAACuD,WAAW,IAAI,CAACiT,OAAO;oBAC/B,OAAOxW,OAAO,CAAChE,uBAAuB;gBACxC;gBAEA,KAAK,IAAI,CAAC8S,KAAKC,MAAM,IAAI3H,OAAOyC,OAAO,CAAC7J,SAAU;oBAChD,IAAI,OAAO+O,UAAU,aAAa;oBAElC,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;wBACxB,KAAK,MAAMM,KAAKN,MAAO;4BACrBtO,IAAIgV,YAAY,CAAC3G,KAAKO;wBACxB;oBACF,OAAO,IAAI,OAAON,UAAU,UAAU;wBACpCA,QAAQA,MAAMzC,QAAQ;wBACtB7L,IAAIgV,YAAY,CAAC3G,KAAKC;oBACxB,OAAO;wBACLtO,IAAIgV,YAAY,CAAC3G,KAAKC;oBACxB;gBACF;YACF;YAEA,sEAAsE;YACtE,8CAA8C;YAC9C,MAAMoQ,QAAOJ,uBAAAA,WAAW/e,OAAO,qBAAlB+e,oBAAoB,CAAC/iB,uBAAuB;YACzD,IAAI,IAAI,CAACuH,WAAW,IAAIiT,SAAS2I,QAAQ,OAAOA,SAAS,UAAU;gBACjE1e,IAAI8S,SAAS,CAACvX,wBAAwBmjB;YACxC;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAIJ,WAAWnE,MAAM,IAAK,CAAA,CAAC7P,gBAAgB,CAAC+M,iBAAgB,GAAI;gBAC9DrX,IAAIwK,UAAU,GAAG8T,WAAWnE,MAAM;YACpC;YAEA,gGAAgG;YAChG,IACE,CAAC,IAAI,CAACrX,WAAW,IACjBwb,WAAWnE,MAAM,IACjBvc,kBAAkB,CAAC0gB,WAAWnE,MAAM,CAAC,IACrC7P,cACA;gBACAtK,IAAIwK,UAAU,GAAG;YACnB;YAEA,sCAAsC;YACtC,IAAI4T,aAAa;gBACfpe,IAAI8S,SAAS,CAAC3Y,0BAA0B;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAImQ,gBAAgB,CAAC+N,eAAe;gBAClC,8DAA8D;gBAC9D,IAAI,OAAOiG,WAAWvB,OAAO,KAAK,aAAa;oBAC7C,IAAIuB,WAAW/R,SAAS,EAAE;wBACxB,MAAM,qBAA0D,CAA1D,IAAIxO,MAAM,kDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyD;oBACjE;oBAEA,OAAO;wBACL4U,MAAM;wBACNtH,MAAMiT,WAAWxB,IAAI;wBACrB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/ElK,cAAc+E,sBACV;4BAAEsE,YAAY;4BAAGlJ,QAAQtU;wBAAU,IACnC2W,WAAWxC,YAAY;oBAC7B;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO;oBACLD,MAAM;oBACNtH,MAAMvS,aAAakf,UAAU,CAACsG,WAAWvB,OAAO;oBAChDnK,cAAcwC,WAAWxC,YAAY;gBACvC;YACF;YAEA,mCAAmC;YACnC,IAAIvH,OAAOiT,WAAWxB,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAACsB,eAAe,IAAI,CAACtb,WAAW,EAAE;gBACpC,OAAO;oBACL6P,MAAM;oBACNtH;oBACAuH,cAAcwC,WAAWxC,YAAY;gBACvC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAI2E,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnDnM,KAAK2T,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,CAACxiB,aAAayiB,MAAM,CAACC,aAAa;wBACpDH,WAAWtN,KAAK;oBAClB;gBACF;gBAGF,OAAO;oBACLc,MAAM;oBACNtH;oBACAuH,cAAc;wBAAEqJ,YAAY;wBAAGlJ,QAAQtU;oBAAU;gBACnD;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAM8gB,cAAc,IAAIC;YACxBnU,KAAK2T,KAAK,CAACO,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzEtG,SAAS;gBACP5M,WAAW+R,WAAW/R,SAAS;gBAC/B6M,eAAe3a;gBACf,sEAAsE;gBACtE,YAAY;gBACZ4a,qBAAqB;YACvB,GACGzH,IAAI,CAAC,OAAOzS;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,qBAAwD,CAAxD,IAAIpB,MAAM,gDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuD;gBAC/D;gBAEA,IAAIoB,EAAAA,gBAAAA,OAAOmP,KAAK,qBAAZnP,cAAc6K,IAAI,MAAK5S,gBAAgBylB,QAAQ,EAAE;wBAEL1d;oBAD9C,MAAM,qBAEL,CAFK,IAAIpB,MACR,CAAC,yCAAyC,GAAEoB,iBAAAA,OAAOmP,KAAK,qBAAZnP,eAAc6K,IAAI,EAAE,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,6CAA6C;gBAC7C,MAAM7K,OAAOmP,KAAK,CAACwO,IAAI,CAAC4C,MAAM,CAACH,YAAYI,QAAQ;YACrD,GACCC,KAAK,CAAC,CAAC9W;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1DyW,YAAYI,QAAQ,CAACE,KAAK,CAAC/W,KAAK8W,KAAK,CAAC,CAACE;oBACrCxW,QAAQC,KAAK,CAAC,8BAA8BuW;gBAC9C;YACF;YAEF,OAAO;gBACLnN,MAAM;gBACNtH;gBACA,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrCuH,cAAc;oBAAEqJ,YAAY;oBAAGlJ,QAAQtU;gBAAU;YACnD;QACF,OAAO,IAAIoY,mBAAmB;YAC5B,OAAO;gBACLlE,MAAM;gBACNtH,MAAMvS,aAAakf,UAAU,CAAC6G,KAAKC,SAAS,CAACR,WAAW1F,QAAQ;gBAChEhG,cAAcwC,WAAWxC,YAAY;YACvC;QACF,OAAO;YACL,OAAO;gBACLD,MAAM;gBACNtH,MAAMiT,WAAWxB,IAAI;gBACrBlK,cAAcwC,WAAWxC,YAAY;YACvC;QACF;IACF;IAEQjG,kBAAkBvM,IAAY,EAAE2f,cAAc,IAAI,EAAE;QAC1D,IAAI3f,KAAKuW,QAAQ,CAAC,IAAI,CAACtW,OAAO,GAAG;YAC/B,MAAM2f,YAAY5f,KAAKY,SAAS,CAC9BZ,KAAKkc,OAAO,CAAC,IAAI,CAACjc,OAAO,IAAI,IAAI,CAACA,OAAO,CAACO,MAAM;YAGlDR,OAAOpH,oBAAoBgnB,UAAUhU,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAACnI,gBAAgB,IAAIkc,aAAa;YACxC,OAAO,IAAI,CAAClc,gBAAgB,CAACnE,SAAS,CAACU;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChC6f,oBAAoBrV,KAAa,EAAE;QAC3C,IAAI,IAAI,CAACxI,kBAAkB,CAACmC,GAAG,EAAE;gBACP;YAAxB,MAAM2b,mBAAkB,sBAAA,IAAI,CAAClY,aAAa,qBAAlB,mBAAoB,CAAC4C,MAAM;YAEnD,IAAI,CAACsV,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACdpX,GAAkD,EAClDqX,gBAAyB,EACzB;YAkBgB;QAjBhB,MAAM,EAAE7U,KAAK,EAAExM,QAAQ,EAAE,GAAGgK;QAE5B,MAAMsX,WAAW,IAAI,CAACJ,mBAAmB,CAAClhB;QAC1C,MAAM8V,YAAYpG,MAAMC,OAAO,CAAC2R;QAEhC,IAAIjT,OAAOrO;QACX,IAAI8V,WAAW;YACb,4EAA4E;YAC5EzH,OAAOiT,QAAQ,CAACA,SAASzf,MAAM,GAAG,EAAE;QACtC;QAEA,MAAMzB,SAAS,MAAM,IAAI,CAACmhB,kBAAkB,CAAC;YAC3C1S,QAAQrU,eAAewP,IAAInK,GAAG,EAAE;YAChCwO;YACA7B;YACApL,QAAQ4I,IAAIhE,UAAU,CAAC5E,MAAM,IAAI,CAAC;YAClC0U;YACA0L,YAAY,CAAC,GAAC,oCAAA,IAAI,CAACniB,UAAU,CAACC,YAAY,CAACmiB,GAAG,qBAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAIvhB,QAAQ;YACVrE,YAAY6lB,oBAAoB,CAAC,cAAc5hB;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAACoV,8BAA8B,CAACpL,KAAK5J;YACxD,EAAE,OAAO2J,KAAK;gBACZ,MAAM8X,oBAAoB9X,eAAetL;gBAEzC,IAAI,CAACojB,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAMtX;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAc+K,iBACZ9K,GAAkD,EACjB;QACjC,OAAOjO,YAAYgP,KAAK,CACtB7O,eAAe4Y,gBAAgB,EAC/B;YACE9J,UAAU,CAAC,cAAc,CAAC;YAC1BG,YAAY;gBACV,cAAcnB,IAAIhK,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAAC8hB,oBAAoB,CAAC9X;QACnC;IAEJ;IAQA,MAAc8X,qBACZ9X,GAAkD,EACjB;YAmBzB;QAlBR,MAAM,EAAEnK,GAAG,EAAEoB,GAAG,EAAEuL,KAAK,EAAExM,QAAQ,EAAE,GAAGgK;QACtC,IAAIqE,OAAOrO;QACX,MAAMqhB,mBACJ7mB,eAAewP,IAAInK,GAAG,EAAE,uBAAuB;QAEjD,IACE,CAAC,IAAI,CAACkE,WAAW,IACjB,IAAI,CAAC1E,UAAU,CAACC,YAAY,CAAC4X,yBAAyB,EACtD;YACA3c,eACEyP,IAAInK,GAAG,EACP,2BACA2M,KAAK,CAACvR,qBAAqB;QAE/B;QACA,OAAOuR,KAAK,CAACvR,qBAAqB;QAElC,MAAM0E,UAAwB;YAC5BiF,IAAI,GAAE,qBAAA,IAAI,CAAC1C,YAAY,qBAAjB,mBAAmB6f,WAAW,CAACliB,KAAKG;QAC5C;QAEA,IAAI;YACF,WAAW,MAAMG,SAAS,IAAI,CAACkJ,QAAQ,CAAC2Y,QAAQ,CAAChiB,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAMsiB,eAAeznB,eAAewP,IAAInK,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAACkE,WAAW,IACjB,OAAOke,iBAAiB,YACxB1oB,eAAe0oB,gBAAgB,OAC/BA,iBAAiB9hB,MAAMgO,UAAU,CAACnO,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAMI,SAAS,MAAM,IAAI,CAACghB,mBAAmB,CAC3C;oBACE,GAAGpX,GAAG;oBACNhK,UAAUG,MAAMgO,UAAU,CAACnO,QAAQ;oBACnCgG,YAAY;wBACV,GAAGgE,IAAIhE,UAAU;wBACjB5E,QAAQjB,MAAMiB,MAAM;oBACtB;gBACF,GACAigB;gBAEF,IAAIjhB,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAAC8D,aAAa,CAAC0M,eAAe,EAAE;gBACtC,sDAAsD;gBACtD5G,IAAIhK,QAAQ,GAAG,IAAI,CAACkE,aAAa,CAAC0M,eAAe,CAACvC,IAAI;gBACtD,MAAMjO,SAAS,MAAM,IAAI,CAACghB,mBAAmB,CAACpX,KAAKqX;gBACnD,IAAIjhB,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAOoK,OAAO;YACd,MAAMT,MAAMzP,eAAekQ;YAE3B,IAAIA,iBAAiB/R,mBAAmB;gBACtC8R,QAAQC,KAAK,CACX,yCACAsV,KAAKC,SAAS,CACZ;oBACE1R;oBACAvN,KAAKkJ,IAAInK,GAAG,CAACiB,GAAG;oBAChBsM,aAAapD,IAAInK,GAAG,CAACW,OAAO,CAACjE,oBAAoB;oBACjD2lB,SAAS1nB,eAAewP,IAAInK,GAAG,EAAE;oBACjCsP,YAAY,CAAC,CAAC3U,eAAewP,IAAInK,GAAG,EAAE;oBACtCsiB,YAAY3nB,eAAewP,IAAInK,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAMkK;YACR;YAEA,IAAIA,eAAetL,mBAAmB4iB,kBAAkB;gBACtD,MAAMtX;YACR;YACA,IAAIA,eAAexR,eAAewR,eAAezR,gBAAgB;gBAC/D2I,IAAIwK,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAAC2W,qBAAqB,CAACpY,KAAKD;YAC/C;YAEA9I,IAAIwK,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAACoJ,OAAO,CAAC,SAAS;gBAC9Bta,eAAeyP,IAAInK,GAAG,EAAE,qBAAqB;gBAC7C,MAAM,IAAI,CAACuiB,qBAAqB,CAACpY,KAAKD;gBACtCtP,kBAAkBuP,IAAInK,GAAG,EAAE;YAC7B;YAEA,MAAMwiB,iBAAiBtY,eAAehL;YAEtC,IAAI,CAACsjB,gBAAgB;gBACnB,IACE,AAAC,IAAI,CAACte,WAAW,IAAIxC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAClD,IAAI,CAACuE,UAAU,CAAClC,GAAG,EACnB;oBACA,IAAIzJ,QAAQ0P,MAAMA,IAAIsE,IAAI,GAAGA;oBAC7B,MAAMtE;gBACR;gBACA,IAAI,CAACU,QAAQ,CAACnQ,eAAeyP;YAC/B;YACA,MAAMkI,WAAW,MAAM,IAAI,CAACmQ,qBAAqB,CAC/CpY,KACAqY,iBAAiB,AAACtY,IAA0B7K,UAAU,GAAG6K;YAE3D,OAAOkI;QACT;QAEA,MAAM/Q,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IACED,cACA,CAAC,CAAC8I,IAAInK,GAAG,CAACW,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACS,IAAIwK,UAAU,IAAIxK,IAAIwK,UAAU,KAAK,OAAOxK,IAAIwK,UAAU,KAAK,GAAE,GACnE;YACA,MAAMoD,SAASrU,eAAeqF,KAAK;YAEnCoB,IAAI8S,SAAS,CACX,yBACA,GAAGlF,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK7O,UAAU;YAE5CiB,IAAIwK,UAAU,GAAG;YACjBxK,IAAI8S,SAAS,CAAC,gBAAgB;YAC9B9S,IAAIqL,IAAI,CAAC;YACTrL,IAAIsL,IAAI;YACR,OAAO;QACT;QAEAtL,IAAIwK,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC2W,qBAAqB,CAACpY,KAAK;IACzC;IAEA,MAAasY,aACXziB,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBwM,QAAwB,CAAC,CAAC,EACF;QACxB,OAAOzQ,YAAYgP,KAAK,CAAC7O,eAAeomB,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAAC1iB,KAAKoB,KAAKjB,UAAUwM;QACnD;IACF;IAEA,MAAc+V,iBACZ1iB,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBwM,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC0H,aAAa,CAAC,CAAClK,MAAQ,IAAI,CAAC8K,gBAAgB,CAAC9K,MAAM;YAC7DnK;YACAoB;YACAjB;YACAwM;QACF;IACF;IAEA,MAAamE,YACX5G,GAAiB,EACjBlK,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBwM,QAA4B,CAAC,CAAC,EAC9BgW,aAAa,IAAI,EACF;QACf,OAAOzmB,YAAYgP,KAAK,CAAC7O,eAAeyU,WAAW,EAAE;YACnD,OAAO,IAAI,CAAC8R,eAAe,CAAC1Y,KAAKlK,KAAKoB,KAAKjB,UAAUwM,OAAOgW;QAC9D;IACF;IAEA,MAAcC,gBACZ1Y,GAAiB,EACjBlK,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBwM,QAA4B,CAAC,CAAC,EAC9BgW,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACdvhB,IAAI8S,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAACZ,IAAI,CACd,OAAOnJ;YACL,MAAMiI,WAAW,MAAM,IAAI,CAACmQ,qBAAqB,CAACpY,KAAKD;YACvD,IAAI,IAAI,CAAChG,WAAW,IAAI9C,IAAIwK,UAAU,KAAK,KAAK;gBAC9C,MAAM1B;YACR;YACA,OAAOkI;QACT,GACA;YAAEpS;YAAKoB;YAAKjB;YAAUwM;QAAM;IAEhC;IAQA,MAAc4V,sBACZpY,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOhO,YAAYgP,KAAK,CAAC7O,eAAekmB,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACM,yBAAyB,CAAC1Y,KAAKD;QAC7C;IACF;IAEA,MAAgB2Y,0BACd1Y,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAAC/D,UAAU,CAAClC,GAAG,IAAIkG,IAAIhK,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACL4T,MAAM;gBACNtH,MAAMvS,aAAakf,UAAU,CAAC;YAChC;QACF;QACA,MAAM,EAAEhY,GAAG,EAAEuL,KAAK,EAAE,GAAGxC;QAEvB,IAAI;YACF,IAAI5J,SAAsC;YAE1C,MAAMuiB,QAAQ1hB,IAAIwK,UAAU,KAAK;YACjC,IAAImX,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAACtf,kBAAkB,CAACmC,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CpF,SAAS,MAAM,IAAI,CAACmhB,kBAAkB,CAAC;wBACrC1S,QAAQrU,eAAewP,IAAInK,GAAG,EAAE;wBAChCwO,MAAM/U;wBACNkT;wBACApL,QAAQ,CAAC;wBACT0U,WAAW;wBACX6L,cAAc;wBACd7gB,KAAKkJ,IAAInK,GAAG,CAACiB,GAAG;oBAClB;oBACA8hB,eAAexiB,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAACyU,OAAO,CAAC,SAAU;oBAC3CzU,SAAS,MAAM,IAAI,CAACmhB,kBAAkB,CAAC;wBACrC1S,QAAQrU,eAAewP,IAAInK,GAAG,EAAE;wBAChCwO,MAAM;wBACN7B;wBACApL,QAAQ,CAAC;wBACT0U,WAAW;wBACX,qEAAqE;wBACrE6L,cAAc;wBACd7gB,KAAKkJ,IAAInK,GAAG,CAACiB,GAAG;oBAClB;oBACA8hB,eAAexiB,WAAW;gBAC5B;YACF;YACA,IAAIyiB,aAAa,CAAC,CAAC,EAAE5hB,IAAIwK,UAAU,EAAE;YAErC,IACE,CAACjR,eAAewP,IAAInK,GAAG,EAAE,wBACzB,CAACO,UACDhH,oBAAoBwe,QAAQ,CAACiL,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAAC7c,UAAU,CAAClC,GAAG,EAAE;oBACjD1D,SAAS,MAAM,IAAI,CAACmhB,kBAAkB,CAAC;wBACrC1S,QAAQrU,eAAewP,IAAInK,GAAG,EAAE;wBAChCwO,MAAMwU;wBACNrW;wBACApL,QAAQ,CAAC;wBACT0U,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACT6L,cAAc;wBACd7gB,KAAKkJ,IAAInK,GAAG,CAACiB,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAACV,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAACmhB,kBAAkB,CAAC;oBACrC1S,QAAQrU,eAAewP,IAAInK,GAAG,EAAE;oBAChCwO,MAAM;oBACN7B;oBACApL,QAAQ,CAAC;oBACT0U,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACT6L,cAAc;oBACd7gB,KAAKkJ,IAAInK,GAAG,CAACiB,GAAG;gBAClB;gBACA+hB,aAAa;YACf;YAEA,IACEthB,QAAQC,GAAG,CAACma,QAAQ,KAAK,gBACzB,CAACiH,gBACA,MAAM,IAAI,CAAC/N,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAACpR,oBAAoB;YAC3B;YAEA,IAAI,CAACrD,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAAC4F,UAAU,CAAClC,GAAG,EAAE;oBACvB,OAAO;wBACL8P,MAAM;wBACN,mDAAmD;wBACnDtH,MAAMvS,aAAakf,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC;oBAEd;gBACF;gBAEA,MAAM,IAAIla,kBACR,qBAA8C,CAA9C,IAAIC,MAAM,sCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YAEjD;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAIoB,OAAO+V,UAAU,CAAC6B,WAAW,EAAE;gBACjCzd,eAAeyP,IAAInK,GAAG,EAAE,SAAS;oBAC/BsO,YAAY/N,OAAO+V,UAAU,CAAC6B,WAAW,CAAC7J,UAAU;oBACpD/M,QAAQ1B;gBACV;YACF,OAAO;gBACLjF,kBAAkBuP,IAAInK,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAACuV,8BAA8B,CAC9C;oBACE,GAAGpL,GAAG;oBACNhK,UAAU6iB;oBACV7c,YAAY;wBACV,GAAGgE,IAAIhE,UAAU;wBACjB+D;oBACF;gBACF,GACA3J;YAEJ,EAAE,OAAO0iB,oBAAoB;gBAC3B,IAAIA,8BAA8BrkB,iBAAiB;oBACjD,MAAM,qBAAmD,CAAnD,IAAIO,MAAM,2CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAkD;gBAC1D;gBACA,MAAM8jB;YACR;QACF,EAAE,OAAOtY,OAAO;YACd,MAAMuY,oBAAoBzoB,eAAekQ;YACzC,MAAM6X,iBAAiBU,6BAA6BhkB;YACpD,IAAI,CAACsjB,gBAAgB;gBACnB,IAAI,CAAC5X,QAAQ,CAACsY;YAChB;YACA9hB,IAAIwK,UAAU,GAAG;YACjB,MAAMuX,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9DjZ,IAAInK,GAAG,CAACiB,GAAG;YAGb,IAAIkiB,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;gBACnCzoB,eAAeyP,IAAInK,GAAG,EAAE,SAAS;oBAC/BsO,YAAY6U,mBAAmBhL,WAAW,CAAE7J,UAAU;oBACtD/M,QAAQ1B;gBACV;gBAEA,OAAO,IAAI,CAAC0V,8BAA8B,CACxC;oBACE,GAAGpL,GAAG;oBACNhK,UAAU;oBACVgG,YAAY;wBACV,GAAGgE,IAAIhE,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtC+D,KAAKsY,iBACDU,kBAAkB7jB,UAAU,GAC5B6jB;oBACN;gBACF,GACA;oBACEvW;oBACA2J,YAAY6M;gBACd;YAEJ;YACA,OAAO;gBACLpP,MAAM;gBACNtH,MAAMvS,aAAakf,UAAU,CAAC;YAChC;QACF;IACF;IAEA,MAAaiK,kBACXnZ,GAAiB,EACjBlK,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBwM,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC0H,aAAa,CAAC,CAAClK,MAAQ,IAAI,CAACoY,qBAAqB,CAACpY,KAAKD,MAAM;YACvElK;YACAoB;YACAjB;YACAwM;QACF;IACF;IAEA,MAAa9K,UACX7B,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA8D,EAC9DyiB,aAAa,IAAI,EACF;QACf,MAAM,EAAExiB,QAAQ,EAAEwM,KAAK,EAAE,GAAGzM,YAAYA,YAAYlH,SAASgH,IAAIiB,GAAG,EAAG;QAEvE,uDAAuD;QACvD,IAAI,IAAI,CAACzB,UAAU,CAACuF,IAAI,EAAE;YACxB,IAAI,CAACpK,eAAeqF,KAAK,WAAW;gBAClCtF,eAAesF,KAAK,UAAU,IAAI,CAACR,UAAU,CAACuF,IAAI,CAACpC,aAAa;YAClE;YACAjI,eAAesF,KAAK,iBAAiB,IAAI,CAACR,UAAU,CAACuF,IAAI,CAACpC,aAAa;QACzE;QAEAvB,IAAIwK,UAAU,GAAG;QACjB,OAAO,IAAI,CAACkF,WAAW,CAAC,MAAM9Q,KAAKoB,KAAKjB,UAAWwM,OAAOgW;IAC5D;AACF", "ignoreList": [0]}