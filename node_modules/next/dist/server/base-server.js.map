{"version": 3, "sources": ["../../src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport {\n  type FallbackRouteParams,\n  getFallbackRouteParams,\n} from './request/fallback-params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport {\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ServerComponentsHmrCache,\n  type ResponseCacheBase,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n  CachedRouteKind,\n  type CachedRedirectValue,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PreviewData } from '../types'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from './route-modules/app-page/module'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport { getRedirectStatus } from '../lib/redirect-status'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { getCacheControlHeader, type CacheControl } from './lib/cache-control'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { getBotType, isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getPreviouslyRevalidatedTags, getServerUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport { getTracer, isBubbledError, SpanKind } from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport { normalizeNextQueryParam } from './web/utils'\nimport {\n  CACHE_ONE_YEAR,\n  MATCHED_PATH_HEADER,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { decodePathParams } from './lib/router-utils/decode-path-params'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n  isPagesRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsPossibleServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { ENCODED_TAGS } from './stream-utils/encoded-tags'\nimport { NextRequestHint } from './web/adapter'\nimport { getRevalidateReason } from './instrumentation/utils'\nimport { RouteKind } from './route-kind'\nimport type { RouteModule } from './route-modules/route-module'\nimport { FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { toResponseCacheEntry } from './response-cache/utils'\nimport { SegmentPrefixRSCPathnameNormalizer } from './normalizers/request/segment-prefix-rsc'\nimport {\n  shouldServeStreamingMetadata,\n  isHtmlBotRequest,\n} from './lib/streaming-metadata'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport { NoFallbackError } from '../shared/lib/no-fallback-error.external'\nimport { getCacheHandlers } from './use-cache/handlers'\nimport { fixMojibake } from './lib/fix-mojibake'\nimport { computeCacheBustingSearchParam } from '../shared/lib/router/utils/cache-busting-search-param'\nimport { RedirectStatusCode } from '../client/components/redirect-status-code'\nimport { setCacheBustingSearchParamWithHash } from '../client/components/router-reducer/set-cache-busting-search-param'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  type: 'html' | 'json' | 'rsc'\n  body: RenderResult\n  cacheControl?: CacheControl\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly segmentPrefetchRSC: SegmentPrefixRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n  private readonly isAppSegmentPrefetchEnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for dynamic IO\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? dir\n        : (require('path') as typeof import('path')).resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? this.nextConfig.distDir\n        : (require('path') as typeof import('path')).join(\n            this.dir,\n            this.nextConfig.distDir\n          )\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.isAppSegmentPrefetchEnabled =\n      this.enabledDirectories.app &&\n      this.nextConfig.experimental.clientSegmentCache === true\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      segmentPrefetchRSC:\n        this.isAppSegmentPrefetchEnabled && this.minimalMode\n          ? new SegmentPrefixRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n    }\n\n    this.renderOpts = {\n      dir: this.dir,\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      strictNextHead: this.nextConfig.experimental.strictNextHead ?? true,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      htmlLimitedBots: this.nextConfig.htmlLimitedBots,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        staleTimes: this.nextConfig.experimental.staleTimes,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        dynamicIO: this.nextConfig.experimental.dynamicIO ?? false,\n        clientSegmentCache:\n          this.nextConfig.experimental.clientSegmentCache === 'client-only'\n            ? 'client-only'\n            : Boolean(this.nextConfig.experimental.clientSegmentCache),\n        dynamicOnHover: this.nextConfig.experimental.dynamicOnHover ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n      devtoolSegmentExplorer:\n        this.nextConfig.experimental.devtoolSegmentExplorer,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  protected reloadMatchers() {\n    return this.matchers.reload()\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.segmentPrefetchRSC?.match(parsedUrl.pathname)) {\n      const result = this.normalizers.segmentPrefetchRSC.extract(\n        parsedUrl.pathname\n      )\n      if (!result) return false\n\n      const { originalPathname, segmentPath } = result\n      parsedUrl.pathname = originalPathname\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n        segmentPath\n\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPath)\n    } else if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n\n        const segmentPrefetchRSCRequest =\n          req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()]\n        if (typeof segmentPrefetchRSCRequest === 'string') {\n          addRequestMeta(\n            req,\n            'segmentPrefetchRSCRequest',\n            segmentPrefetchRSCRequest\n          )\n        }\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = await this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          getRequestMeta(req, 'middlewareInvoke')\n        ) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        addRequestMeta(req, 'locale', localePathResult.detectedLocale)\n        addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          removeRequestMeta(req, 'localeInferredFromDefault')\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      addRequestMeta(req, 'isNextDataReq', true)\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            fixMojibake(req.headers[MATCHED_PATH_HEADER] as string),\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          matchedPath = denormalizePagePath(matchedPath)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            addRequestMeta(req, 'locale', localeAnalysisResult.detectedLocale)\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              addRequestMeta(req, 'localeInferredFromDefault', true)\n            } else {\n              removeRequestMeta(req, 'localeInferredFromDefault')\n            }\n          }\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n          let paramsResult: {\n            params: ParsedUrlQuery | false\n            hasValidParams: boolean\n          } = {\n            params: false,\n            hasValidParams: false,\n          }\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n\n              // The page is dynamic if the params are defined. We know at this\n              // stage that the matched path is not a static page if the params\n              // were parsed from the matched path header.\n              if (typeof match.params !== 'undefined') {\n                pageIsDynamic = true\n                paramsResult.params = match.params\n                paramsResult.hasValidParams = true\n              }\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getServerUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          // Store a copy of `parsedUrl.query` before calling handleRewrites.\n          // Since `handleRewrites` might add new queries to `parsedUrl.query`.\n          const originQueryParams = { ...parsedUrl.query }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParamKeys = Object.keys(\n            utils.handleRewrites(req, parsedUrl)\n          )\n\n          // Create a copy of the query params to avoid mutating the original\n          // object. This prevents any overlapping query params that have the\n          // same normalized key from causing issues.\n          const queryParams = { ...parsedUrl.query }\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n\n          const routeParamKeys = new Set<string>()\n          for (const [key, value] of Object.entries(parsedUrl.query)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            // Remove the prefixed key from the query params because we want\n            // to consume it for the dynamic route matcher.\n            delete parsedUrl.query[key]\n            routeParamKeys.add(normalizedKey)\n\n            if (typeof value === 'undefined') continue\n\n            queryParams[normalizedKey] = Array.isArray(value)\n              ? value.map((v) => decodeQueryPathParameter(v))\n              : decodeQueryPathParameter(value)\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            // If we don't already have valid params, try to parse them from\n            // the query params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                false\n              )\n            }\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams, false)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult = utils.normalizeDynamicRouteParams(\n                  matcherParams,\n                  false\n                )\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            const routeMatchesHeader = req.headers['x-now-route-matches']\n            if (\n              typeof routeMatchesHeader === 'string' &&\n              routeMatchesHeader &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const routeMatches =\n                utils.getParamsFromRouteMatches(routeMatchesHeader)\n\n              if (routeMatches) {\n                paramsResult = utils.normalizeDynamicRouteParams(\n                  routeMatches,\n                  true\n                )\n\n                if (paramsResult.hasValidParams) {\n                  params = paramsResult.params\n                }\n              }\n            }\n\n            // Try to parse the params from the query if we couldn't parse them\n            // from the route matches but ignore missing optional params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // If the pathname being requested is the same as the source\n            // pathname, and we don't have valid params, we want to use the\n            // default route matches.\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // If the route matches header is an empty string, we want to\n              // render a fallback shell. This is because we know this came from\n              // a prerender (it has the header) but it's values were filtered\n              // out (because the allowQuery was empty). If it was undefined\n              // then we know that the request is hitting the lambda directly.\n              if (routeMatchesHeader === '') {\n                addRequestMeta(req, 'renderFallbackShell', true)\n              }\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n\n              // If the request is for a segment prefetch, we need to update the\n              // segment prefetch request path to include the interpolated\n              // params.\n              let segmentPrefetchRSCRequest = getRequestMeta(\n                req,\n                'segmentPrefetchRSCRequest'\n              )\n              if (\n                segmentPrefetchRSCRequest &&\n                isDynamicRoute(segmentPrefetchRSCRequest, false)\n              ) {\n                segmentPrefetchRSCRequest = utils.interpolateDynamicPath(\n                  segmentPrefetchRSCRequest,\n                  params\n                )\n\n                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n                  segmentPrefetchRSCRequest\n                addRequestMeta(\n                  req,\n                  'segmentPrefetchRSCRequest',\n                  segmentPrefetchRSCRequest\n                )\n              }\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeCdnUrl(req, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          // Remove the route `params` keys from `parsedUrl.query` if they are\n          // not in the original query params.\n          // If it's used in both route `params` and query `searchParams`, it should be kept.\n          for (const key of routeParamKeys) {\n            if (!(key in originQueryParams)) {\n              delete parsedUrl.query[key]\n            }\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !getRequestMeta(req, 'locale')) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          addRequestMeta(req, 'locale', pathnameInfo.locale)\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          addRequestMeta(req, 'localeInferredFromDefault', true)\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n        })\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        // This is needed for pages router to leverage unstable_cache\n        // TODO: re-work this handling to not use global and use a AsyncStore\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      const cacheHandlers = getCacheHandlers()\n\n      if (cacheHandlers) {\n        await Promise.all(\n          [...cacheHandlers].map(async (cacheHandler) => {\n            if ('refreshTags' in cacheHandler) {\n              // Note: cacheHandler.refreshTags() is called lazily before the\n              // first cache entry is retrieved. It allows us to skip the\n              // refresh request if no caches are read at all.\n            } else {\n              const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n                req.headers,\n                this.getPrerenderManifest().preview.previewModeId\n              )\n\n              await cacheHandler.receiveExpiredTags(\n                ...previouslyRevalidatedTags\n              )\n            }\n          })\n        )\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath =\n        !useMatchedPathHeader &&\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          addRequestMeta(req, 'locale', invokePathnameInfo.locale)\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales\n        )\n\n        if (normalizeResult.detectedLocale) {\n          addRequestMeta(req, 'locale', normalizeResult.detectedLocale)\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          delete parsedUrl.query[key]\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        getRequestMeta(req, 'middlewareInvoke')\n      ) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the segment prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.segmentPrefetchRSC) {\n      normalizers.push(this.normalizers.segmentPrefetchRSC)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.nextConfig.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n    this.renderOpts.assetPrefix = this.nextConfig.assetPrefix\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    // Get instrumentation module\n    if (!this.instrumentation) {\n      this.instrumentation = await this.loadInstrumentationModule()\n    }\n    if (this.preparedPromise === null) {\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const ua = partialContext.req.headers['user-agent'] || ''\n\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        // `renderOpts.botType` is accumulated in `this.renderImpl()`\n        supportsDynamicResponse: !this.renderOpts.botType,\n        serveStreamingMetadata: shouldServeStreamingMetadata(\n          ua,\n          this.nextConfig.htmlLimitedBots\n        ),\n      },\n    }\n\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body, type } = payload\n    let { cacheControl } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        cacheControl = undefined\n      }\n\n      if (cacheControl && cacheControl.expire === undefined) {\n        cacheControl.expire = this.nextConfig.expireTime\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        type,\n        generateEtags,\n        poweredByHeader,\n        cacheControl,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.serverOptions.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    const ua = req.headers['user-agent'] || ''\n    this.renderOpts.botType = getBotType(ua)\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !getRequestMeta(req, 'isNextDataReq') &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.appendHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.appendHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    let hasGetStaticPaths = !!components.getStaticPaths\n    const isPossibleServerAction = getIsPossibleServerAction(req)\n    const hasGetInitialProps = !!components.Component?.getInitialProps\n    let isSSG = !!components.getStaticProps\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // Not all CDNs respect the Vary header when caching. We must assume that\n    // only the URL is used to vary the responses. The Next client computes a\n    // hash of the header values and sends it as a search param. Before\n    // responding to a request, we must verify that the hash matches the\n    // expected value. Neglecting to do this properly can lead to cache\n    // poisoning attacks on certain CDNs.\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders &&\n      isRSCRequest\n    ) {\n      const headers = req.headers\n      const expectedHash = computeCacheBustingSearchParam(\n        headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()],\n        headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()],\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()],\n        headers[NEXT_URL.toLowerCase()]\n      )\n      const actualHash =\n        getRequestMeta(req, 'cacheBustingSearchParam') ??\n        new URL(req.url || '', 'http://localhost').searchParams.get(\n          NEXT_RSC_UNION_QUERY\n        )\n\n      if (expectedHash !== actualHash) {\n        // The hash sent by the client does not match the expected value.\n        // Redirect to the URL with the correct cache-busting search param.\n        // This prevents cache poisoning attacks on CDNs that don't respect Vary headers.\n        // Note: When no headers are present, expectedHash is empty string and client\n        // must send `_rsc` param, otherwise actualHash is null and hash check fails.\n        const url = new URL(req.url || '', 'http://localhost')\n        setCacheBustingSearchParamWithHash(url, expectedHash)\n        res.statusCode = 307\n        res.setHeader('location', `${url.pathname}${url.search}`)\n        res.body('').send()\n        return null\n      }\n    }\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let fallbackMode: FallbackMode | undefined\n    let hasFallback = false\n\n    const isDynamic = isDynamicRoute(components.page)\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (isAppPath && isDynamic) {\n      const pathsResult = await this.getStaticPaths({\n        pathname,\n        page: components.page,\n        isAppPath,\n        requestHeaders: req.headers,\n      })\n\n      staticPaths = pathsResult.staticPaths\n      fallbackMode = pathsResult.fallbackMode\n      hasFallback = typeof fallbackMode !== 'undefined'\n\n      if (this.nextConfig.output === 'export') {\n        const page = components.page\n        if (!staticPaths) {\n          throw new Error(\n            `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n\n        const resolvedWithoutSlash = removeTrailingSlash(resolvedUrlPathname)\n        if (!staticPaths.includes(resolvedWithoutSlash)) {\n          throw new Error(\n            `Page \"${page}\" is missing param \"${resolvedWithoutSlash}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n      }\n\n      if (hasFallback) {\n        hasGetStaticPaths = true\n      }\n    }\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        getRequestMeta(req, 'isNextDataReq') ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    /**\n     * If true, this indicates that the request being made is for an app\n     * prefetch request.\n     */\n    const isPrefetchRSCRequest =\n      getRequestMeta(req, 'isPrefetchRSCRequest') ?? false\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    const locale = getRequestMeta(req, 'locale')\n    const defaultLocale = isSSG\n      ? this.nextConfig.i18n?.defaultLocale\n      : getRequestMeta(req, 'defaultLocale')\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery =\n      hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    const isDebugStaticShell: boolean =\n      hasDebugStaticShellQuery && isRoutePPREnabled\n\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses =\n      isDebugStaticShell && this.renderOpts.dev === true\n\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest =\n      isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = getRequestMeta(\n      req,\n      'segmentPrefetchRSCRequest'\n    )\n\n    const isHtmlBot = isHtmlBotRequest(req)\n    if (isHtmlBot && isRoutePPREnabled) {\n      isSSG = false\n      this.renderOpts.serveStreamingMetadata = false\n    }\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isPossibleServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      res.body('Method Not Allowed').send()\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        type: 'html',\n        // TODO: Static pages should be serialized as RenderResult\n        body: RenderResult.fromStatic(components.Component),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const ua = req.headers['user-agent'] || ''\n      const isBotRequest = isBot(ua)\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    const locales = this.nextConfig.i18n?.locales\n\n    let previewData: PreviewData\n    let isPreviewMode = false\n\n    if (hasServerProps || isSSG || isAppPath) {\n      // For the edge runtime, we don't support preview mode in SSG.\n      if (process.env.NEXT_RUNTIME !== 'edge') {\n        const { tryGetPreviewData } =\n          require('./api-utils/node/try-get-preview-data') as typeof import('./api-utils/node/try-get-preview-data')\n        previewData = tryGetPreviewData(\n          req,\n          res,\n          this.renderOpts.previewProps,\n          !!this.nextConfig.experimental.multiZoneDraftMode\n        )\n        isPreviewMode = previewData !== false\n      }\n    }\n\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (\n      isAppPath &&\n      !opts.dev &&\n      !isPreviewMode &&\n      isSSG &&\n      isRSCRequest &&\n      !isDynamicRSCRequest &&\n      (!isEdgeRuntime(opts.runtime) ||\n        (this.serverOptions as any).webServerConfig)\n    ) {\n      stripFlightHeaders(req.headers)\n    }\n\n    let { isOnDemandRevalidate, revalidateOnlyGenerated } =\n      checkIsOnDemandRevalidate(req, this.renderOpts.previewProps)\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    const handleRedirect = (pageData: any) => {\n      const redirect = {\n        destination: pageData.pageProps.__N_REDIRECT,\n        statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n        basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n      }\n      const statusCode = getRedirectStatus(redirect)\n      const { basePath } = this.nextConfig\n\n      if (\n        basePath &&\n        redirect.basePath !== false &&\n        redirect.destination.startsWith('/')\n      ) {\n        redirect.destination = `${basePath}${redirect.destination}`\n      }\n\n      if (redirect.destination.startsWith('/')) {\n        redirect.destination = normalizeRepeatedSlashes(redirect.destination)\n      }\n\n      res\n        .redirect(redirect.destination, statusCode)\n        .body(redirect.destination)\n        .send()\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    let ssgCacheKey: string | null = null\n    if (\n      !isPreviewMode &&\n      isSSG &&\n      !opts.supportsDynamicResponse &&\n      !isPossibleServerAction &&\n      !minimalPostponed &&\n      !isDynamicRSCRequest\n    ) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${\n        (pathname === '/' || resolvedUrlPathname === '/') && locale\n          ? ''\n          : resolvedUrlPathname\n      }${query.amp ? '.amp' : ''}`\n    }\n\n    if ((is404Page || is500Page) && isSSG) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${\n        query.amp ? '.amp' : ''\n      }`\n    }\n\n    if (ssgCacheKey) {\n      ssgCacheKey = decodePathParams(ssgCacheKey)\n\n      // ensure /index and / is normalized to one key\n      ssgCacheKey =\n        ssgCacheKey === '/index' && pathname === '/' ? '/' : ssgCacheKey\n    }\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      process.env.NEXT_RUNTIME === 'edge' &&\n      (globalThis as any).__incrementalCache\n        ? (globalThis as any).__incrementalCache\n        : await this.getIncrementalCache({\n            requestHeaders: Object.assign({}, req.headers),\n          })\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    type RendererContext = {\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      pagesFallback: boolean | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }\n    type Renderer = (\n      context: RendererContext\n    ) => Promise<ResponseCacheEntry | null>\n\n    const doRender: Renderer = async ({\n      postponed,\n      pagesFallback = false,\n      fallbackRouteParams,\n    }) => {\n      // In development, we always want to generate dynamic HTML.\n      let supportsDynamicResponse: boolean =\n        // If we're in development, we always support dynamic HTML, unless it's\n        // a data request, in which case we only produce static HTML.\n        (!isNextDataRequest && opts.dev === true) ||\n        // If this is not SSG or does not have static paths, then it supports\n        // dynamic HTML.\n        (!isSSG && !hasGetStaticPaths) ||\n        // If this request has provided postponed data, it supports dynamic\n        // HTML.\n        typeof postponed === 'string' ||\n        // If this is a dynamic RSC request, then this render supports dynamic\n        // HTML (it's dynamic).\n        isDynamicRSCRequest\n\n      const origQuery = parseUrl(req.url || '', true).query\n\n      // clear any dynamic route params so they aren't in\n      // the resolvedUrl\n      if (opts.params) {\n        Object.keys(opts.params).forEach((key) => {\n          delete origQuery[key]\n        })\n      }\n      const hadTrailingSlash =\n        urlPathname !== '/' && this.nextConfig.trailingSlash\n\n      const resolvedUrl = formatUrl({\n        pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,\n        // make sure to only add query values from original URL\n        query: origQuery,\n      })\n\n      // When html bots request PPR page, perform the full dynamic rendering.\n      const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n      const renderOpts: LoadedRenderOpts = {\n        ...components,\n        ...opts,\n        ...(isAppPath\n          ? {\n              incrementalCache,\n              // This is a revalidation request if the request is for a static\n              // page and it is not being resumed from a postponed render and\n              // it is not a dynamic RSC request then it is a revalidation\n              // request.\n              isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n              serverActions: this.nextConfig.experimental.serverActions,\n            }\n          : {}),\n        isNextDataRequest,\n        resolvedUrl,\n        locale,\n        locales,\n        defaultLocale,\n        multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n        // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on\n        // asPath\n        resolvedAsPath:\n          hasServerProps || hasGetInitialProps\n            ? formatUrl({\n                // we use the original URL pathname less the _next/data prefix if\n                // present\n                pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,\n                query: origQuery,\n              })\n            : resolvedUrl,\n        experimental: {\n          ...opts.experimental,\n          isRoutePPREnabled,\n        },\n        supportsDynamicResponse,\n        shouldWaitOnAllReady,\n        isOnDemandRevalidate,\n        isDraftMode: isPreviewMode,\n        isPossibleServerAction,\n        postponed,\n        waitUntil: this.getWaitUntil(),\n        onClose: res.onClose.bind(res),\n        onAfterTaskError: undefined,\n        // only available in dev\n        setIsrStatus: (this as any).setIsrStatus,\n      }\n\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        supportsDynamicResponse = false\n        renderOpts.nextExport = true\n        renderOpts.supportsDynamicResponse = false\n        renderOpts.isStaticGeneration = true\n        renderOpts.isRevalidate = true\n        renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses\n      }\n\n      // Legacy render methods will return a render result that needs to be\n      // served by the server.\n      let result: RenderResult\n\n      if (routeModule) {\n        if (\n          isAppRouteRouteModule(routeModule) ||\n          isPagesRouteModule(routeModule) ||\n          isAppPageRouteModule(routeModule)\n        ) {\n          // An OPTIONS request to a page handler is invalid.\n          if (\n            req.method === 'OPTIONS' &&\n            !is404Page &&\n            !isAppRouteRouteModule(routeModule)\n          ) {\n            await sendResponse(req, res, new Response(null, { status: 400 }))\n            return null\n          }\n\n          const request = isNodeNextRequest(req) ? req.originalRequest : req\n          const response = isNodeNextResponse(res) ? res.originalResponse : res\n\n          if (\n            components.ComponentMod.handler &&\n            process.env.NEXT_RUNTIME !== 'edge'\n          ) {\n            const parsedInitUrl = parseUrl(\n              getRequestMeta(req, 'initURL') || req.url\n            )\n            let initPathname = parsedInitUrl.pathname || '/'\n\n            for (const normalizer of [\n              this.normalizers.segmentPrefetchRSC,\n              this.normalizers.prefetchRSC,\n              this.normalizers.rsc,\n            ]) {\n              if (normalizer?.match(initPathname)) {\n                initPathname = normalizer.normalize(initPathname)\n              }\n            }\n\n            // On minimal mode, the request url of dynamic route can be a\n            // literal dynamic route ('/[slug]') instead of actual URL, so overwriting to initPathname\n            // will transform back the resolved url to the dynamic route pathname.\n            if (!(this.minimalMode && isErrorPathname)) {\n              request.url = `${initPathname}${parsedInitUrl.search || ''}`\n            }\n\n            // propagate the request context for dev\n            setRequestMeta(request, getRequestMeta(req))\n            addRequestMeta(request, 'projectDir', this.dir)\n            addRequestMeta(request, 'distDir', this.distDir)\n            addRequestMeta(request, 'isIsrFallback', pagesFallback)\n            addRequestMeta(request, 'query', query)\n            addRequestMeta(request, 'params', opts.params)\n            addRequestMeta(\n              request,\n              'ampValidator',\n              this.renderOpts.ampValidator\n            )\n            addRequestMeta(request, 'minimalMode', this.minimalMode)\n\n            if (renderOpts.err) {\n              addRequestMeta(request, 'invokeError', renderOpts.err)\n            }\n\n            const handler: (\n              req: ServerRequest | IncomingMessage,\n              res: ServerResponse | HTTPServerResponse,\n              ctx: {\n                waitUntil: ReturnType<Server['getWaitUntil']>\n              }\n            ) => Promise<RenderResult> = components.ComponentMod.handler\n\n            const maybeDevRequest =\n              // we need to capture fetch metrics when they are set\n              // and can't wait for handler to resolve as the fetch\n              // metrics are logged on response close which happens\n              // before handler resolves\n              process.env.NODE_ENV === 'development'\n                ? new Proxy(request, {\n                    get(target: any, prop) {\n                      if (typeof target[prop] === 'function') {\n                        return target[prop].bind(target)\n                      }\n                      return target[prop]\n                    },\n                    set(target: any, prop, value) {\n                      if (prop === 'fetchMetrics') {\n                        ;(req as any).fetchMetrics = value\n                      }\n                      target[prop] = value\n                      return true\n                    },\n                  })\n                : request\n\n            result = await handler(maybeDevRequest, response, {\n              waitUntil: this.getWaitUntil(),\n            })\n\n            // response is handled fully in handler\n            return null\n          } else {\n            if (isPagesRouteModule(routeModule)) {\n              // Due to the way we pass data by mutating `renderOpts`, we can't extend\n              // the object here but only updating its `clientReferenceManifest` and\n              // `nextFontManifest` properties.\n              // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n              renderOpts.nextFontManifest = this.nextFontManifest\n              renderOpts.clientReferenceManifest =\n                components.clientReferenceManifest\n\n              // Call the built-in render method on the module.\n              try {\n                result = await routeModule.render(\n                  request as any,\n                  response as any,\n                  {\n                    page: pathname,\n                    params: opts.params,\n                    query,\n                    renderOpts,\n                    sharedContext: {\n                      buildId: this.buildId,\n                      deploymentId: this.nextConfig.deploymentId,\n                      customServer:\n                        this.serverOptions.customServer || undefined,\n                    },\n                    renderContext: {\n                      isFallback: pagesFallback,\n                      isDraftMode: renderOpts.isDraftMode,\n                      developmentNotFoundSourcePage: getRequestMeta(\n                        req,\n                        'developmentNotFoundSourcePage'\n                      ),\n                    },\n                  }\n                )\n              } catch (err) {\n                await this.instrumentationOnRequestError(err, req, {\n                  routerKind: 'Pages Router',\n                  routePath: pathname,\n                  routeType: 'render',\n                  revalidateReason: getRevalidateReason({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n                  }),\n                })\n                throw err\n              }\n            } else {\n              const module = components.routeModule as AppPageRouteModule\n\n              // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n              // object here but only updating its `nextFontManifest` field.\n              // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n              renderOpts.nextFontManifest = this.nextFontManifest\n\n              const context: AppPageRouteHandlerContext = {\n                page: is404Page ? '/404' : pathname,\n                params: opts.params,\n                query,\n                fallbackRouteParams,\n                renderOpts,\n                serverComponentsHmrCache: this.getServerComponentsHmrCache(),\n                sharedContext: {\n                  buildId: this.buildId,\n                },\n              }\n\n              // TODO: adapt for putting the RDC inside the postponed data\n              // If we're in dev, and this isn't a prefetch or a server action,\n              // we should seed the resume data cache.\n              if (\n                this.nextConfig.experimental.dynamicIO &&\n                this.renderOpts.dev &&\n                !isPrefetchRSCRequest &&\n                !isPossibleServerAction\n              ) {\n                const warmup = await module.warmup(req, res, context)\n\n                // If the warmup is successful, we should use the resume data\n                // cache from the warmup.\n                if (warmup.metadata.renderResumeDataCache) {\n                  renderOpts.renderResumeDataCache =\n                    warmup.metadata.renderResumeDataCache\n                }\n              }\n\n              // Call the built-in render method on the module.\n              result = await module.render(req, res, context)\n            }\n          }\n        } else {\n          throw new Error('Invariant: Unknown route module type')\n        }\n      } else {\n        // If we didn't match a page, we should fallback to using the legacy\n        // render method.\n        result = await this.renderHTML(req, res, pathname, query, renderOpts)\n      }\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isAppPath &&\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !this.renderOpts.dev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${urlPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      // Based on the metadata, we can determine what kind of cache result we\n      // should return.\n\n      // Handle `isNotFound`.\n      if ('isNotFound' in metadata && metadata.isNotFound) {\n        return {\n          value: null,\n          cacheControl,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isRedirect`.\n      if (metadata.isRedirect) {\n        return {\n          value: {\n            kind: CachedRouteKind.REDIRECT,\n            props: metadata.pageData ?? metadata.flightData,\n          } satisfies CachedRedirectValue,\n          cacheControl,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isNull`.\n      if (result.isNull) {\n        return null\n      }\n\n      // We now have a valid HTML result that we can return to the user.\n      if (isAppPath) {\n        return {\n          value: {\n            kind: CachedRouteKind.APP_PAGE,\n            html: result,\n            headers,\n            rscData: metadata.flightData,\n            postponed: metadata.postponed,\n            status: metadata.statusCode,\n            segmentData: metadata.segmentData,\n          } satisfies CachedAppPageValue,\n          cacheControl,\n        } satisfies ResponseCacheEntry\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.PAGES,\n          html: result,\n          pageData: metadata.pageData ?? metadata.flightData,\n          headers,\n          status: isAppPath ? res.statusCode : undefined,\n        } satisfies CachedPageValue,\n        cacheControl,\n      }\n    }\n\n    let responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n    }): Promise<ResponseCacheEntry | null> => {\n      const isProduction = !this.renderOpts.dev\n      const didRespond = hasResolved || res.sent\n\n      // If we haven't found the static paths for the route, then do it now.\n      if (!staticPaths && isDynamic) {\n        if (hasGetStaticPaths) {\n          const pathsResult = await this.getStaticPaths({\n            pathname,\n            requestHeaders: req.headers,\n            isAppPath,\n            page: components.page,\n          })\n\n          staticPaths = pathsResult.staticPaths\n          fallbackMode = pathsResult.fallbackMode\n        } else {\n          staticPaths = undefined\n          fallbackMode = FallbackMode.NOT_FOUND\n        }\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (\n        fallbackMode === FallbackMode.PRERENDER &&\n        isBot(req.headers['user-agent'] || '')\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !this.minimalMode\n      ) {\n        await this.render404(req, res)\n        return null\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // We use `ssgCacheKey` here as it is normalized to match the encoding\n      // from getStaticPaths along with including the locale.\n      //\n      // We use the `resolvedUrlPathname` for the development case when this\n      // is an app path since it doesn't include locale information.\n      //\n      // We decode the `resolvedUrlPathname` to correctly match the app path\n      // with prerendered paths.\n      let staticPathKey = ssgCacheKey\n      if (!staticPathKey && opts.dev && isAppPath) {\n        staticPathKey = decodePathParams(resolvedUrlPathname)\n      }\n      if (staticPathKey && query.amp) {\n        staticPathKey = staticPathKey.replace(/\\.amp$/, '')\n      }\n\n      const isPageIncludedInStaticPaths =\n        staticPathKey && staticPaths?.includes(staticPathKey)\n\n      // When experimental compile is used, no pages have been prerendered,\n      // so they should all be blocking.\n\n      if (this.nextConfig.experimental.isExperimentalCompile) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // When we did not respond from cache, we need to choose to block on\n      // rendering or return a skeleton.\n      //\n      // - Data requests always block.\n      // - Blocking mode fallback always blocks.\n      // - Preview mode toggles all pages to be resolved in a blocking manner.\n      // - Non-dynamic pages should block (though this is an impossible\n      //   case in production).\n      // - Dynamic pages should return their skeleton if not defined in\n      //   getStaticPaths, then finish the data request on the client-side.\n      //\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        !this.minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isPreviewMode &&\n        isDynamic &&\n        (isProduction || !staticPaths || !isPageIncludedInStaticPaths)\n      ) {\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || (staticPaths && staticPaths?.length > 0)) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        // If this is a pages router page.\n        if (isPagesRouteModule(components.routeModule) && !isNextDataRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? (locale ? `/${locale}${pathname}` : pathname) : null,\n            // This is the response generator for the fallback shell.\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              // For the pages router, fallbacks cannot be revalidated or\n              // generated in production. In the case of a missing fallback,\n              // we return null, but if it's being revalidated, we just return\n              // the previous fallback cache entry. This preserves the previous\n              // behavior.\n              if (isProduction) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n\n              // We pass `undefined` and `null` as it doesn't apply to the pages\n              // router.\n              return doRender({\n                postponed: undefined,\n                // For the pages router, fallbacks can only be generated on\n                // demand in development, so if we're not in production, and we\n                // aren't a app path.\n                pagesFallback: true,\n                fallbackRouteParams: null,\n              })\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n        // If this is a app router page, PPR is enabled, and PPR is also\n        // enabled, then we should use the fallback renderer.\n        else if (\n          isRoutePPREnabled &&\n          isAppPageRouteModule(components.routeModule) &&\n          !isRSCRequest\n        ) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? pathname : null,\n            // This is the response generator for the fallback shell.\n            async () =>\n              doRender({\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                pagesFallback: undefined,\n                fallbackRouteParams:\n                  // If we're in production or we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(pathname)\n                    : null,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n\n        // If the fallback response was set to null, then we should return null.\n        if (fallbackResponse === null) return null\n\n        // Otherwise, if we did get a fallback response, we should return it.\n        if (fallbackResponse) {\n          // Remove the cache control from the response to prevent it from being\n          // used in the surrounding cache.\n          delete fallbackResponse.cacheControl\n\n          return fallbackResponse\n        }\n      }\n\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        isDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'renderFallbackShell') || isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        postponed,\n        pagesFallback: undefined,\n        fallbackRouteParams,\n      })\n    }\n\n    if (\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // default _error module in dev doesn't have handler yet\n      components.ComponentMod.handler &&\n      (isPagesRouteModule(components.routeModule) ||\n        isAppRouteRouteModule(components.routeModule) ||\n        isAppPageRouteModule(components.routeModule))\n    ) {\n      if (\n        routeModule?.isDev &&\n        isDynamicRoute(pathname) &&\n        (components.getStaticPaths || isAppPath)\n      ) {\n        await this.getStaticPaths({\n          pathname,\n          requestHeaders: req.headers,\n          page: components.page,\n          isAppPath,\n        })\n      }\n      await doRender({\n        postponed: undefined,\n        pagesFallback: false,\n        fallbackRouteParams: null,\n      })\n      return null\n    }\n\n    const cacheEntry = await this.responseCache.get(\n      ssgCacheKey,\n      responseGenerator,\n      {\n        routeKind:\n          // If the route module is not defined, we can assume it's a page being\n          // rendered and thus check isAppPath.\n          routeModule?.definition.kind ??\n          (isAppPath ? RouteKind.APP_PAGE : RouteKind.PAGES),\n        incrementalCache,\n        isOnDemandRevalidate,\n        isPrefetch: req.headers.purpose === 'prefetch',\n        isRoutePPREnabled,\n      }\n    )\n\n    if (isPreviewMode) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    if (!cacheEntry) {\n      if (\n        ssgCacheKey &&\n        !(isOnDemandRevalidate && revalidateOnlyGenerated) &&\n        !isPagesRouteModule(components.routeModule) &&\n        !isAppRouteRouteModule(components.routeModule) &&\n        !isAppPageRouteModule(components.routeModule)\n      ) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n      return null\n    }\n\n    const didPostpone =\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      typeof cacheEntry.value.postponed === 'string'\n\n    if (\n      isSSG &&\n      // We don't want to send a cache header for requests that contain dynamic\n      // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n      // request, then we should set the cache header.\n      !isDynamicRSCRequest &&\n      (!didPostpone || isPrefetchRSCRequest)\n    ) {\n      if (!this.minimalMode) {\n        // set x-nextjs-cache header to match the header\n        // we set for the image-optimizer\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n      // Set a header used by the client router to signal the response is static\n      // and should respect the `static` cache staleTime value.\n      res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n    }\n\n    const { value: cachedData } = cacheEntry\n\n    // If the cache value is an image, we should error early.\n    if (cachedData?.kind === CachedRouteKind.IMAGE) {\n      throw new InvariantError('SSG should not return an image cache value')\n    }\n\n    // Coerce the cache control parameter from the render.\n    let cacheControl: CacheControl | undefined\n\n    // If this is a resume request in minimal mode it is streamed with dynamic\n    // content and should not be cached.\n    if (minimalPostponed) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    }\n\n    // If this is in minimal mode and this is a flight request that isn't a\n    // prefetch request while PPR is enabled, it cannot be cached as it contains\n    // dynamic content.\n    else if (\n      this.minimalMode &&\n      isRSCRequest &&\n      !isPrefetchRSCRequest &&\n      isRoutePPREnabled\n    ) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    } else if (!this.renderOpts.dev || (hasServerProps && !isNextDataRequest)) {\n      // If this is a preview mode request, we shouldn't cache it\n      if (isPreviewMode) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this isn't SSG, then we should set change the header only if it is\n      // not set already.\n      else if (!isSSG) {\n        if (!res.getHeader('Cache-Control')) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n      }\n\n      // If we are rendering the 404 page we derive the cache-control\n      // revalidate period from the value that trigged the not found\n      // to be rendered. So if `getStaticProps` returns\n      // { notFound: true, revalidate 60 } the revalidate period should\n      // be 60 but if a static asset 404s directly it should have a revalidate\n      // period of 0 so that it doesn't get cached unexpectedly by a CDN\n      else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n\n        cacheControl = {\n          revalidate:\n            typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n          expire: undefined,\n        }\n      } else if (is500Page) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (cacheEntry.cacheControl) {\n        // If the cache entry has a cache control with a revalidate value that's\n        // a number, use it.\n        if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n          if (cacheEntry.cacheControl.revalidate < 1) {\n            throw new Error(\n              `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n            )\n          }\n\n          cacheControl = {\n            revalidate: cacheEntry.cacheControl.revalidate,\n            expire:\n              cacheEntry.cacheControl?.expire ?? this.nextConfig.expireTime,\n          }\n        }\n        // Otherwise if the revalidate value is false, then we should use the\n        // cache time of one year.\n        else {\n          cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n        }\n      }\n    }\n\n    cacheEntry.cacheControl = cacheControl\n\n    if (\n      typeof segmentPrefetchHeader === 'string' &&\n      cachedData?.kind === CachedRouteKind.APP_PAGE &&\n      cachedData.segmentData\n    ) {\n      // This is a prefetch request issued by the client Segment Cache. These\n      // should never reach the application layer (lambda). We should either\n      // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n      // Set a header to indicate that PPR is enabled for this route. This\n      // lets the client distinguish between a regular cache miss and a cache\n      // miss due to PPR being disabled. In other contexts this header is used\n      // to indicate that the response contains dynamic data, but here we're\n      // only using it to indicate that the feature is enabled — the segment\n      // response itself contains whether the data is dynamic.\n      res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (this.minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n      if (matchedSegment !== undefined) {\n        // Cache hit\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(matchedSegment),\n          // TODO: Eventually this should use cache control of the individual\n          // segment, not the whole page.\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // Cache miss. Either a cache entry for this route has not been generated\n      // (which technically should not be possible when PPR is enabled, because\n      // at a minimum there should always be a fallback entry) or there's no\n      // match for the requested segment. Respond with a 204 No Content. We\n      // don't bother to respond with 404, because these requests are only\n      // issued as part of a prefetch.\n      res.statusCode = 204\n      return {\n        type: 'rsc',\n        body: RenderResult.fromStatic(''),\n        cacheControl: cacheEntry?.cacheControl,\n      }\n    }\n\n    // If there's a callback for `onCacheEntry`, call it with the cache entry\n    // and the revalidate options.\n    const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n    if (onCacheEntry) {\n      const finished = await onCacheEntry(\n        {\n          ...cacheEntry,\n          // TODO: remove this when upstream doesn't\n          // always expect this value to be \"PAGE\"\n          value: {\n            ...cacheEntry.value,\n            kind:\n              cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n                ? 'PAGE'\n                : cacheEntry.value?.kind,\n          },\n        },\n        {\n          url: getRequestMeta(req, 'initURL'),\n        }\n      )\n      if (finished) {\n        // TODO: maybe we have to end the request?\n        return null\n      }\n    }\n\n    if (!cachedData) {\n      // add revalidate metadata before rendering 404 page\n      // so that we can use this as source of truth for the\n      // cache-control header instead of what the 404 page returns\n      // for the revalidate value\n      addRequestMeta(\n        req,\n        'notFoundRevalidate',\n        cacheEntry.cacheControl?.revalidate\n      )\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n      if (isNextDataRequest) {\n        res.statusCode = 404\n        res.body('{\"notFound\":true}').send()\n        return null\n      }\n\n      if (this.renderOpts.dev) {\n        addRequestMeta(req, 'developmentNotFoundSourcePage', pathname)\n      }\n      await this.render404(req, res, { pathname, query }, false)\n      return null\n    } else if (cachedData.kind === CachedRouteKind.REDIRECT) {\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      if (isNextDataRequest) {\n        return {\n          type: 'json',\n          body: RenderResult.fromStatic(\n            // @TODO: Handle flight data.\n            JSON.stringify(cachedData.props)\n          ),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      } else {\n        await handleRedirect(cachedData.props)\n        return null\n      }\n    } else if (cachedData.kind === CachedRouteKind.APP_ROUTE) {\n      // this is handled inside the app_route handler fully\n      throw new Error(`Invariant: unexpected APP_ROUTE cache data`)\n    } else if (cachedData.kind === CachedRouteKind.APP_PAGE) {\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!this.minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (this.minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n      if (\n        !this.minimalMode &&\n        cachedData.status &&\n        RedirectStatusCode[cachedData.status] &&\n        isRSCRequest\n      ) {\n        res.statusCode = 200\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isPreviewMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return {\n            type: 'rsc',\n            body: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          }\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(cachedData.rscData),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || this.minimalMode) {\n        return {\n          type: 'html',\n          body,\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return {\n          type: 'html',\n          body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        }\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        postponed: cachedData.postponed,\n        pagesFallback: undefined,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return {\n        type: 'html',\n        body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      }\n    } else if (isNextDataRequest) {\n      return {\n        type: 'json',\n        body: RenderResult.fromStatic(JSON.stringify(cachedData.pageData)),\n        cacheControl: cacheEntry.cacheControl,\n      }\n    } else {\n      return {\n        type: 'html',\n        body: cachedData.html,\n        cacheControl: cacheEntry.cacheControl,\n      }\n    }\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      locale: getRequestMeta(ctx.req, 'locale'),\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): Promise<MiddlewareRoutingItem | undefined>\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { req, res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback =\n      getRequestMeta(ctx.req, 'bubbleNoFallback') ?? false\n\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders\n    ) {\n      addRequestMeta(\n        ctx.req,\n        'cacheBustingSearchParam',\n        query[NEXT_RSC_UNION_QUERY]\n      )\n    }\n    delete query[NEXT_RSC_UNION_QUERY]\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromRequest(req, pathname),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        addRequestMeta(ctx.req, 'customErrorRender', true)\n        await this.renderErrorToResponse(ctx, err)\n        removeRequestMeta(ctx.req, 'customErrorRender')\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (\n          (this.minimalMode && process.env.NEXT_RUNTIME !== 'edge') ||\n          this.renderOpts.dev\n        ) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    const middleware = await this.getMiddleware()\n    if (\n      middleware &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      const locale = getRequestMeta(req, 'locale')\n\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('content-type', 'application/json')\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic(''),\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !getRequestMeta(ctx.req, 'customErrorRender') &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          locale: getRequestMeta(ctx.req, 'locale'),\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            type: 'html',\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic('Internal Server Error'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    // Ensure the locales are provided on the request meta.\n    if (this.nextConfig.i18n) {\n      if (!getRequestMeta(req, 'locale')) {\n        addRequestMeta(req, 'locale', this.nextConfig.i18n.defaultLocale)\n      }\n      addRequestMeta(req, 'defaultLocale', this.nextConfig.i18n.defaultLocale)\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["WrappedBuildError", "Server", "Error", "constructor", "innerError", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "segmentPrefetchRSC", "match", "result", "extract", "originalPathname", "segmentPath", "headers", "RSC_HEADER", "toLowerCase", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "addRequestMeta", "prefetchRSC", "normalize", "rsc", "stripFlightHeaders", "segmentPrefetchRSCRequest", "url", "parsed", "parseUrl", "formatUrl", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "matchNextDataPathname", "path", "buildId", "process", "env", "NEXT_RUNTIME", "getRequestMeta", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "getRouteFromAssetPath", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "removeRequestMeta", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "execOnce", "Log", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "formatHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "I18NProvider", "localeNormalizer", "LocaleRouteNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "checkIsAppPPREnabled", "ppr", "isAppSegmentPrefetchEnabled", "clientSegmentCache", "RSCPathnameNormalizer", "PrefetchRSCPathnameNormalizer", "SegmentPrefixRSCPathnameNormalizer", "NextDataPathnameNormalizer", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "strictNextHead", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "htmlLimitedBots", "expireTime", "staleTimes", "clientTraceMetadata", "dynamicIO", "Boolean", "dynamicOnHover", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "devtoolSegmentExplorer", "setConfig", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "reloadMatchers", "manifest<PERSON><PERSON>der", "ServerManifestLoader", "name", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "DefaultRouteMatcherManager", "PagesRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "NextRequestHint", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "getTracer", "withPropagatedContext", "trace", "BaseServerSpan", "spanName", "kind", "SpanKind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "patchSetHeaderWithCookieSupport", "isNodeNextResponse", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "normalizeRepeatedSlashes", "redirect", "body", "send", "query", "URLSearchParams", "isNodeNextRequest", "xForwardedProto", "isHttps", "socket", "encrypted", "toString", "remoteAddress", "attachRequestMeta", "getHostname", "parseUrlUtil", "replace", "pathnameInfo", "getNextPathnameInfo", "removePathPrefix", "useMatchedPathHeader", "MATCHED_PATH_HEADER", "<PERSON><PERSON><PERSON>", "URL", "fixMojibake", "urlPathname", "NEXT_RESUME_HEADER", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "denormalizePagePath", "localeAnalysisResult", "inferredFromDefault", "srcPathname", "pageIsDynamic", "isDynamicRoute", "paramsResult", "hasValidParams", "definition", "utils", "getServerUtils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "originQueryParams", "pathnameBeforeRewrite", "rewriteParamKeys", "handleRewrites", "queryParams", "didRewrite", "routeParamKeys", "Set", "key", "value", "normalizedKey", "normalizeNextQueryParam", "add", "Array", "isArray", "map", "v", "decodeQueryPathParameter", "normalizeDynamicRouteParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "routeMatchesHeader", "routeMatches", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeCdnUrl", "defaultRouteRegex", "groups", "DecodeError", "NormalizeError", "renderError", "webServerConfig", "incrementalCache", "getIncrementalCache", "requestHeaders", "resetRequestCache", "__incrementalCache", "cacheHandlers", "getCacheHandlers", "Promise", "all", "cache<PERSON><PERSON><PERSON>", "previouslyRevalidatedTags", "getPreviouslyRevalidatedTags", "previewModeId", "receiveExpiredTags", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "normalizeLocalePath", "response", "Response", "bubble", "run", "NoFallbackError", "code", "isBubbledError", "getProperError", "getRequestHandlerWithMetadata", "meta", "handler", "getRequestHandler", "setRequestMeta", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "normalizeAppPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "ua", "botType", "serveStreamingMetadata", "shouldServeStreamingMetadata", "payload", "originalStatus", "type", "cacheControl", "sent", "<PERSON><PERSON><PERSON><PERSON>", "expire", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "getBuiltinRequestContext", "waitUntil", "getInternalWaitUntil", "startsWith", "customServer", "hasPage", "getBotType", "isBlockedPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "parseFallbackField", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "isInterceptionRouteAppPath", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "NEXT_ROUTER_STATE_TREE_HEADER", "addedNextUrlToVary", "append<PERSON><PERSON>er", "NEXT_URL", "opts", "components", "prerenderManifest", "cacheEntry", "UNDERSCORE_NOT_FOUND_ROUTE", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "hasGetStaticPaths", "isPossibleServerAction", "getIsPossibleServerAction", "hasGetInitialProps", "Component", "getInitialProps", "isSSG", "getStaticProps", "validateRSCRequestHeaders", "expectedHash", "computeCacheBustingSearchParam", "actualHash", "searchParams", "NEXT_RSC_UNION_QUERY", "setCacheBustingSearchParamWithHash", "search", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "isDynamic", "pathsResult", "resolvedWithoutSlash", "removeTrailingSlash", "includes", "routes", "toRoute", "isNextDataRequest", "isPrefetchRSCRequest", "routeModule", "couldSupportPPR", "isAppPageRouteModule", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "isDynamicRSCRequest", "segmentPrefetchHeader", "isHtmlBot", "isHtmlBotRequest", "STATIC_STATUS_PAGES", "parseInt", "slice", "RenderResult", "fromStatic", "isBotRequest", "isBot", "isSupportedDocument", "Document", "NEXT_BUILTIN_DOCUMENT", "previewData", "isPreviewMode", "tryGetPreviewData", "multiZoneDraftMode", "isEdgeRuntime", "runtime", "isOnDemandRevalidate", "revalidateOnlyGenerated", "checkIsOnDemandRevalidate", "handleRedirect", "pageData", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "getRedirectStatus", "ssgCacheKey", "decodePathParams", "doR<PERSON>", "pagesFallback", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hadTrailingSlash", "resolvedUrl", "shouldWaitOnAllReady", "isRevalidate", "serverActions", "resolvedAsPath", "isDraftMode", "onClose", "onAfterTaskError", "setIsrStatus", "nextExport", "isStaticGeneration", "isAppRouteRouteModule", "isPagesRouteModule", "sendResponse", "status", "request", "ComponentMod", "parsedInitUrl", "initPathname", "ampValidator", "maybeDevRequest", "NODE_ENV", "Proxy", "target", "prop", "set", "fetchMetrics", "clientReferenceManifest", "sharedContext", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "developmentNotFoundSourcePage", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "module", "context", "warmup", "metadata", "renderResumeDataCache", "renderHTML", "fetchTags", "cacheTags", "NEXT_CACHE_TAGS_HEADER", "revalidate", "staticBailoutInfo", "description", "stack", "message", "indexOf", "isNotFound", "isRedirect", "CachedRouteKind", "REDIRECT", "props", "flightData", "isNull", "APP_PAGE", "html", "rscData", "segmentData", "PAGES", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "FallbackMode", "NOT_FOUND", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "static<PERSON><PERSON><PERSON><PERSON>", "isPageIncludedInStaticPaths", "fallbackResponse", "previousFallbackCacheEntry", "toResponseCacheEntry", "routeKind", "RouteKind", "getFallbackRouteParams", "isDev", "isPrefetch", "purpose", "didPostpone", "isMiss", "NEXT_IS_PRERENDER_HEADER", "cachedData", "IMAGE", "InvariantError", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "CACHE_ONE_YEAR", "NEXT_DID_POSTPONE_HEADER", "tags", "matchedSegment", "onCacheEntry", "getCacheControlHeader", "JSON", "stringify", "APP_ROUTE", "RedirectStatusCode", "chain", "ReadableStream", "start", "controller", "enqueue", "ENCODED_TAGS", "CLOSED", "BODY_AND_HTML", "transformer", "TransformStream", "readable", "pipeTo", "writable", "catch", "abort", "e", "stripLocale", "splitPath", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "fromRequest", "matchAll", "invokeOutput", "MissingStaticPage", "initUrl", "rewroteUrl", "renderErrorToResponse", "isWrappedError", "isError", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "is404", "using404Page", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "statusPage", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": ";;;;;;;;;;;;;;;IAkTaA,iBAAiB;eAAjBA;;IAoBb,OA4zHC;eA5zH6BC;;;gCA/TvB;+BAsBA;uBAOA;qBA0BgD;gCACxB;gCACG;+BACJ;2BAQvB;wBACwB;0BACW;uCAChB;8BAC+B;wBAE3B;uBACI;qEACT;qCACW;qCACA;6DACf;6BACwC;iEACrB;6BAMjC;kCAC0B;0BACA;6BACL;0BACa;qCACL;kCAU7B;uCAK+B;4CACK;6CACC;8CACC;8CACA;2CACH;sCACL;wBACe;4BACrB;8BACF;8BACA;wBACW;4BAMjC;qCAC6B;uCACE;8EACJ;kCACD;qBACK;oCACH;wBAK5B;6BACuC;0BACH;yCACD;oCACC;yBACnB;yBAE8B;gCACN;qBACX;uCAI9B;6BACsB;yBACG;wBACI;2BACV;0BAEuB;wBACZ;kCACc;mCAI5C;gCACwB;0CACU;yCACT;0BACC;6BACL;yCACmB;oCACZ;4CACgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqI5C,MAAMD,0BAA0BE;IAGrCC,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAae,MAAeH;IAiGlBI,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACxD,AAACC,WAAmBC,0BAA0B,GAC9CC;IACN;IAsBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YA0CrB,uBAwEE,mCAQL;aAkEXC,mBAAgE,CACtEC,KACAC,MACAC;gBAII,sCAkBO,+BAWA;YA/BX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,KAAI,uCAAA,IAAI,CAACC,WAAW,CAACC,kBAAkB,qBAAnC,qCAAqCC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClE,MAAMI,SAAS,IAAI,CAACH,WAAW,CAACC,kBAAkB,CAACG,OAAO,CACxDN,UAAUC,QAAQ;gBAEpB,IAAI,CAACI,QAAQ,OAAO;gBAEpB,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGH;gBAC1CL,UAAUC,QAAQ,GAAGM;gBAErB,iDAAiD;gBACjDT,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCb,IAAIW,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,GAAG;gBACzDb,IAAIW,OAAO,CAACI,qDAAmC,CAACF,WAAW,GAAG,GAC5DH;gBAEFM,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;gBACpCgB,IAAAA,2BAAc,EAAChB,KAAK,wBAAwB;gBAC5CgB,IAAAA,2BAAc,EAAChB,KAAK,6BAA6BU;YACnD,OAAO,KAAI,gCAAA,IAAI,CAACN,WAAW,CAACa,WAAW,qBAA5B,8BAA8BX,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClED,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACa,WAAW,CAACC,SAAS,CACzDhB,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCb,IAAIW,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,GAAG;gBACzDG,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;gBACpCgB,IAAAA,2BAAc,EAAChB,KAAK,wBAAwB;YAC9C,OAAO,KAAI,wBAAA,IAAI,CAACI,WAAW,CAACe,GAAG,qBAApB,sBAAsBb,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACe,GAAG,CAACD,SAAS,CACjDhB,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCG,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIW,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;gBACvCS,IAAAA,sCAAkB,EAACpB,IAAIW,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIX,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,KAAK,KAAK;gBACxDG,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIW,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,KAAK,KAAK;oBAClEG,IAAAA,2BAAc,EAAChB,KAAK,wBAAwB;oBAE5C,MAAMqB,4BACJrB,IAAIW,OAAO,CAACI,qDAAmC,CAACF,WAAW,GAAG;oBAChE,IAAI,OAAOQ,8BAA8B,UAAU;wBACjDL,IAAAA,2BAAc,EACZhB,KACA,6BACAqB;oBAEJ;gBACF;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIrB,IAAIsB,GAAG,EAAE;gBACX,MAAMC,SAASC,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG;gBAC/BC,OAAOpB,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIsB,GAAG,GAAGG,IAAAA,WAAS,EAACF;YACtB;YAEA,OAAO;QACT;aAEQG,wBACN,OAAO1B,KAAK2B,KAAKzB;YACf,MAAM0B,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,MAAMC,SAASC,IAAAA,4CAAqB,EAAC7B,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAAC2B,UAAU,CAACA,OAAOE,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAIF,OAAOE,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IACEC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,2BAAc,EAACrC,KAAK,qBACpB;oBACA,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAACsC,SAAS,CAACtC,KAAK2B,KAAKzB;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1B4B,OAAOE,IAAI,CAACO,KAAK;YAEjB,MAAMC,YAAYV,OAAOE,IAAI,CAACF,OAAOE,IAAI,CAACS,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAACtC,KAAK2B,KAAKzB;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAE2B,OAAOE,IAAI,CAACW,IAAI,CAAC,MAAM;YAC1CxC,WAAWyC,IAAAA,8BAAqB,EAACzC,UAAU;YAE3C,iDAAiD;YACjD,IAAIyB,YAAY;gBACd,IAAI,IAAI,CAACpC,UAAU,CAACqD,aAAa,IAAI,CAAC1C,SAASuC,QAAQ,CAAC,MAAM;oBAC5DvC,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAACqD,aAAa,IAC9B1C,SAASsC,MAAM,GAAG,KAClBtC,SAASuC,QAAQ,CAAC,MAClB;oBACAvC,WAAWA,SAAS2C,SAAS,CAAC,GAAG3C,SAASsC,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACM,YAAY,EAAE;oBAEJ/C;gBADjB,gDAAgD;gBAChD,MAAMgD,WAAWhD,wBAAAA,oBAAAA,IAAKW,OAAO,CAACsC,IAAI,qBAAjBjD,kBAAmBkD,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACrC,WAAW;gBAEhE,MAAMsC,eAAe,IAAI,CAACJ,YAAY,CAACK,kBAAkB,CAACJ;gBAC1D,MAAMK,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,KAAI,IAAI,CAACN,YAAY,CAACO,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACR,YAAY,CAACS,OAAO,CAACrD;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAIoD,iBAAiBE,cAAc,EAAE;oBACnCtD,WAAWoD,iBAAiBpD,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChEa,IAAAA,2BAAc,EAAChB,KAAK,UAAUuD,iBAAiBE,cAAc;gBAC7DzC,IAAAA,2BAAc,EAAChB,KAAK,iBAAiBqD;gBAErC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpCC,IAAAA,8BAAiB,EAAC1D,KAAK;gBACzB;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAACuD,iBAAiBE,cAAc,IAAI,CAAC7B,YAAY;oBACnDZ,IAAAA,2BAAc,EAAChB,KAAK,UAAUqD;oBAC9B,MAAM,IAAI,CAACf,SAAS,CAACtC,KAAK2B,KAAKzB;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrBa,IAAAA,2BAAc,EAAChB,KAAK,iBAAiB;YAErC,OAAO;QACT;aAEQ2D,yBAGN,IAAM;aAEAC,8BAGN,IAAM;aAEAC,kCAGN,IAAM;QAwxBV;;;;;;GAMC,QACO3C,YAAY,CAACf;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAAC0D,IAAI,EAAE;gBACzB1D,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAAC0D,IAAI;YACxC;YAEA,2EAA2E;YAC3E,qEAAqE;YACrE,IAAI,IAAI,CAAC1D,WAAW,CAACC,kBAAkB,EAAE;gBACvCD,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACC,kBAAkB;YACtD;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAACD,WAAW,CAACa,WAAW,EAAE;gBAChCb,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACa,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACb,WAAW,CAACe,GAAG,EAAE;gBACxBf,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACe,GAAG;YACvC;YAEA,KAAK,MAAM6C,cAAc5D,YAAa;gBACpC,IAAI,CAAC4D,WAAW1D,KAAK,CAACH,WAAW;gBAEjC,OAAO6D,WAAW9C,SAAS,CAACf,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQ8D,6BAGJ,OAAOjE,KAAK2B,KAAKL;YACnB,IAAI4C,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAAC3D,KAAK2B,KAAKL;YAC3D,IAAI4C,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAACxC,qBAAqB,CAAC1B,KAAK2B,KAAKL;gBACtD,IAAI4C,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAiCUG,WAAoB;aACpBC,kBAAwC;aAqtE1CC,uBAAuBC,IAAAA,eAAQ,EAAC;YACtCC,KAAIC,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QAj8GE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnB/B,QAAQ,EACRgC,IAAI,EACJC,qBAAqB,EACtB,GAAGnF;QAEJ,IAAI,CAACmF,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGpF;QAErB,IAAI,CAAC6E,GAAG,GACNzC,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzBuC,MACA,AAACQ,QAAQ,QAAkCC,OAAO,CAACT;QAEzD,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACS,aAAa,CAAC;YAAEP;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACtF,UAAU,GAAGqF;QAClB,IAAI,CAAC7B,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAACsC,aAAa,GAAGC,IAAAA,8BAAc,EAAC,IAAI,CAACvC,QAAQ;QACnD;QACA,IAAI,CAACgC,IAAI,GAAGA;QACZ,IAAI,CAACQ,OAAO,GACVtD,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,IAAI,CAAC5C,UAAU,CAACgG,OAAO,GACvB,AAACL,QAAQ,QAAkCxC,IAAI,CAC7C,IAAI,CAACgC,GAAG,EACR,IAAI,CAACnF,UAAU,CAACgG,OAAO;QAE/B,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACZ,eAAe,IAAI,CAACa,eAAe;QAExD,IAAI,CAAC7C,YAAY,GAAG,EAAA,wBAAA,IAAI,CAACvD,UAAU,CAACqG,IAAI,qBAApB,sBAAsBC,OAAO,IAC7C,IAAIC,0BAAY,CAAC,IAAI,CAACvG,UAAU,CAACqG,IAAI,IACrChG;QAEJ,yEAAyE;QACzE,IAAI,CAACmG,gBAAgB,GAAG,IAAI,CAACjD,YAAY,GACrC,IAAIkD,4CAAqB,CAAC,IAAI,CAAClD,YAAY,IAC3ClD;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJqG,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC7G,UAAU;QAEnB,IAAI,CAACyC,OAAO,GAAG,IAAI,CAACqE,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBxB,eAAe,CAAC,CAAC7C,QAAQC,GAAG,CAACqE,yBAAyB;QAExD,IAAI,CAACrC,kBAAkB,GAAG,IAAI,CAACsC,qBAAqB,CAAC3B;QAErD,IAAI,CAAC4B,eAAe,GAClB,IAAI,CAACvC,kBAAkB,CAACwC,GAAG,IAC3BC,IAAAA,yBAAoB,EAAC,IAAI,CAACpH,UAAU,CAACC,YAAY,CAACoH,GAAG;QAEvD,IAAI,CAACC,2BAA2B,GAC9B,IAAI,CAAC3C,kBAAkB,CAACwC,GAAG,IAC3B,IAAI,CAACnH,UAAU,CAACC,YAAY,CAACsH,kBAAkB,KAAK;QAEtD,IAAI,CAAC3G,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCe,KACE,IAAI,CAACgD,kBAAkB,CAACwC,GAAG,IAAI,IAAI,CAAC5B,WAAW,GAC3C,IAAIiC,0BAAqB,KACzBnH;YACNoB,aACE,IAAI,CAACyF,eAAe,IAAI,IAAI,CAAC3B,WAAW,GACpC,IAAIkC,0CAA6B,KACjCpH;YACNQ,oBACE,IAAI,CAACyG,2BAA2B,IAAI,IAAI,CAAC/B,WAAW,GAChD,IAAImC,oDAAkC,KACtCrH;YACNiE,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,IAAI+C,oCAA0B,CAAC,IAAI,CAAClF,OAAO,IAC3CpC;QACN;QAEA,IAAI,CAACuH,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAEhD,IAAInF,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvCF,QAAQC,GAAG,CAACmF,kBAAkB,GAAG,IAAI,CAAC9H,UAAU,CAAC+H,YAAY,IAAI;QACnE;QAEA,IAAI,CAACC,UAAU,GAAG;YAChB7C,KAAK,IAAI,CAACA,GAAG;YACb8C,yBAAyB;YACzB5E,eAAe,IAAI,CAACrD,UAAU,CAACqD,aAAa;YAC5C0E,cAAc,IAAI,CAAC/H,UAAU,CAAC+H,YAAY;YAC1CG,gBAAgB,IAAI,CAAClI,UAAU,CAACC,YAAY,CAACiI,cAAc,IAAI;YAC/DC,iBAAiB,IAAI,CAACnI,UAAU,CAACmI,eAAe;YAChDC,eAAe,IAAI,CAACpI,UAAU,CAACqI,GAAG,CAACD,aAAa,IAAI;YACpDvB;YACAyB,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjDC,kBAAkB,GAAE,oCAAA,IAAI,CAACzI,UAAU,CAACC,YAAY,CAACoI,GAAG,qBAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAAC3I,UAAU,CAAC2I,QAAQ;YAClCC,QAAQ,IAAI,CAAC5I,UAAU,CAAC4I,MAAM;YAC9BC,aAAa,IAAI,CAAC7I,UAAU,CAACC,YAAY,CAAC4I,WAAW;YACrDC,kBAAkB,IAAI,CAAC9I,UAAU,CAAC+I,MAAM;YACxCC,mBAAmB,IAAI,CAAChJ,UAAU,CAACC,YAAY,CAAC+I,iBAAiB;YACjEC,yBACE,IAAI,CAACjJ,UAAU,CAACC,YAAY,CAACgJ,uBAAuB;YACtDC,aAAa,GAAE,yBAAA,IAAI,CAAClJ,UAAU,CAACqG,IAAI,qBAApB,uBAAsB8C,OAAO;YAC5CnD,SAAS,IAAI,CAACA,OAAO;YACrBoD,kBAAkB,IAAI,CAACzE,kBAAkB,CAACwC,GAAG;YAC7CkC,mBAAmB,IAAI,CAACrJ,UAAU,CAACC,YAAY,CAACqJ,SAAS;YACzDC,gBAAgB,IAAI,CAACvJ,UAAU,CAACC,YAAY,CAACuJ,KAAK;YAClDC,aAAa,IAAI,CAACzJ,UAAU,CAACyJ,WAAW,GACpC,IAAI,CAACzJ,UAAU,CAACyJ,WAAW,GAC3BpJ;YACJqJ,oBAAoB,IAAI,CAAC1J,UAAU,CAACC,YAAY,CAACyJ,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAClD,qBAAqB1D,MAAM,GAAG,IACtC0D,sBACAtG;YAENyJ,uBAAuB,IAAI,CAAC9J,UAAU,CAACC,YAAY,CAAC6J,qBAAqB;YACzE,8EAA8E;YAC9EC,iBAAiB,IAAI,CAAC/J,UAAU,CAAC+J,eAAe;YAChD9J,cAAc;gBACZ+J,YAAY,IAAI,CAAChK,UAAU,CAACgK,UAAU;gBACtCC,YAAY,IAAI,CAACjK,UAAU,CAACC,YAAY,CAACgK,UAAU;gBACnDC,qBAAqB,IAAI,CAAClK,UAAU,CAACC,YAAY,CAACiK,mBAAmB;gBACrEC,WAAW,IAAI,CAACnK,UAAU,CAACC,YAAY,CAACkK,SAAS,IAAI;gBACrD5C,oBACE,IAAI,CAACvH,UAAU,CAACC,YAAY,CAACsH,kBAAkB,KAAK,gBAChD,gBACA6C,QAAQ,IAAI,CAACpK,UAAU,CAACC,YAAY,CAACsH,kBAAkB;gBAC7D8C,gBAAgB,IAAI,CAACrK,UAAU,CAACC,YAAY,CAACoK,cAAc,IAAI;gBAC/DC,WAAW,IAAI,CAACtK,UAAU,CAACC,YAAY,CAACqK,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAACvK,UAAU,CAACC,YAAY,CAACsK,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAAC3K,UAAU,CAAC2K,qBAAqB;YAC5DC,wBACE,IAAI,CAAC5K,UAAU,CAACC,YAAY,CAAC2K,sBAAsB;QACvD;QAEA,4DAA4D;QAC5DC,IAAAA,gCAAS,EAAC;YACRnE;YACAC;QACF;QAEA,IAAI,CAACmE,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAAC7E;QACpB,IAAI,CAAC8E,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAErG;QAAI;IACnD;IAEUsG,iBAAiB;QACzB,OAAO,IAAI,CAACN,QAAQ,CAACE,MAAM;IAC7B;IAmMUD,mBAAwC;QAChD,yEAAyE;QACzE,MAAMM,iBAAiB,IAAIC,0CAAoB,CAAC,CAACC;YAC/C,OAAQA;gBACN,KAAKC,yBAAc;oBACjB,OAAO,IAAI,CAACjB,gBAAgB,MAAM;gBACpC,KAAKkB,6BAAkB;oBACrB,OAAO,IAAI,CAAChB,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,IAAIY,sDAA0B;QAEpE,8BAA8B;QAC9BZ,SAAS/G,IAAI,CACX,IAAI4H,oDAAyB,CAC3B,IAAI,CAACnG,OAAO,EACZ6F,gBACA,IAAI,CAACtI,YAAY;QAIrB,uCAAuC;QACvC+H,SAAS/G,IAAI,CACX,IAAI6H,0DAA4B,CAC9B,IAAI,CAACpG,OAAO,EACZ6F,gBACA,IAAI,CAACtI,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACoB,kBAAkB,CAACwC,GAAG,EAAE;YAC/B,gCAAgC;YAChCmE,SAAS/G,IAAI,CACX,IAAI8H,wDAA2B,CAAC,IAAI,CAACrG,OAAO,EAAE6F;YAEhDP,SAAS/G,IAAI,CACX,IAAI+H,0DAA4B,CAAC,IAAI,CAACtG,OAAO,EAAE6F;QAEnD;QAEA,OAAOP;IACT;IAEA,MAAgBb,8BACd,GAAG8B,IAAqD,EACxD;QACA,MAAM,CAACC,KAAKhM,KAAKiM,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,OAAM,IAAI,CAACA,eAAe,CAACC,cAAc,oBAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,MAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACEhK,MAAMhC,IAAIsB,GAAG,IAAI;oBACjB8K,QAAQpM,IAAIoM,MAAM,IAAI;oBACtB,gEAAgE;oBAChEzL,SACEX,eAAeqM,wBAAe,GAC1BjD,OAAOkD,WAAW,CAACtM,IAAIW,OAAO,CAAC4L,OAAO,MACtCvM,IAAIW,OAAO;gBACnB,GACAsL;YAEJ,EAAE,OAAOO,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASX,GAAU,EAAQ;QAChC,IAAI,IAAI,CAACpH,KAAK,EAAE;QAChBH,KAAIiI,KAAK,CAACV;IACZ;IAEA,MAAaY,cACX5M,GAAkB,EAClB2B,GAAmB,EACnBzB,SAAkC,EACnB;QACf,MAAM,IAAI,CAAC2M,OAAO;QAClB,MAAMT,SAASpM,IAAIoM,MAAM,CAACU,WAAW;QAErC,MAAMC,SAASC,IAAAA,iBAAS;QACxB,OAAOD,OAAOE,qBAAqB,CAACjN,IAAIW,OAAO,EAAE;YAC/C,OAAOoM,OAAOG,KAAK,CACjBC,0BAAc,CAACP,aAAa,EAC5B;gBACEQ,UAAU,GAAGhB,OAAO,CAAC,EAAEpM,IAAIsB,GAAG,EAAE;gBAChC+L,MAAMC,gBAAQ,CAACC,MAAM;gBACrBC,YAAY;oBACV,eAAepB;oBACf,eAAepM,IAAIsB,GAAG;gBACxB;YACF,GACA,OAAOmM,OACL,IAAI,CAACC,iBAAiB,CAAC1N,KAAK2B,KAAKzB,WAAWyN,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,eAAevL,IAAAA,2BAAc,EAACrC,KAAK,mBAAmB;oBAC5DyN,KAAKI,aAAa,CAAC;wBACjB,oBAAoBlM,IAAImM,UAAU;wBAClC,YAAYF;oBACd;oBAEA,MAAMG,qBAAqBhB,OAAOiB,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvBd,0BAAc,CAACP,aAAa,EAC5B;wBACAH,QAAQ/H,IAAI,CACV,CAAC,2BAA2B,EAAEqJ,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAM3C,OAAOqC,eACT,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAE8B,OAAO,GACxB,GAAG9B,OAAO,CAAC,EAAE8B,OAAO;wBAExBT,KAAKI,aAAa,CAAC;4BACjB,cAAcK;4BACd,cAAcA;4BACd,kBAAkB3C;wBACpB;wBACAkC,KAAKU,UAAU,CAAC5C;oBAClB,OAAO;wBACLkC,KAAKU,UAAU,CACbP,eACI,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAEpM,IAAIsB,GAAG,EAAE,GAC1B,GAAG8K,OAAO,CAAC,EAAEpM,IAAIsB,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAcoM,kBACZ1N,GAAkB,EAClB2B,GAAmB,EACnBzB,SAAkC,EACnB;QACf,IAAI;gBAiDKkO,yBAS4BA,0BASd,oBAKY;YAvEjC,qCAAqC;YACrC,MAAM,IAAI,CAACtD,QAAQ,CAACuD,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;YAClDC,IAAAA,+CAA+B,EAC7BtO,KACAuO,IAAAA,2BAAkB,EAAC5M,OAAOA,IAAI6M,gBAAgB,GAAG7M;YAGnD,MAAM8M,WAAW,AAACzO,CAAAA,IAAIsB,GAAG,IAAI,EAAC,EAAG4B,KAAK,CAAC,KAAK;YAC5C,MAAMwL,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,8BAAAA,WAAYpO,KAAK,CAAC,cAAc;gBAClC,MAAMqO,WAAWC,IAAAA,+BAAwB,EAAC5O,IAAIsB,GAAG;gBACjDK,IAAIkN,QAAQ,CAACF,UAAU,KAAKG,IAAI,CAACH,UAAUI,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAAC7O,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIsB,GAAG,EAAE;oBACZ,MAAM,qBAAgD,CAAhD,IAAIlC,MAAM,wCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+C;gBACvD;gBAEAc,YAAYsB,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,EAAG;YACjC;YAEA,IAAI,CAACpB,UAAUC,QAAQ,EAAE;gBACvB,MAAM,qBAA+C,CAA/C,IAAIf,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEA,iFAAiF;YACjF,IAAI,OAAOc,UAAU8O,KAAK,KAAK,UAAU;gBACvC9O,UAAU8O,KAAK,GAAG5F,OAAOkD,WAAW,CAClC,IAAI2C,gBAAgB/O,UAAU8O,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAEZ,kBAAkB,IAAI,EAAE,GAAGc,IAAAA,0BAAiB,EAAClP,OAAOA,MAAM,CAAC;YACnE,MAAMmP,kBAAkBf,mCAAAA,gBAAiBzN,OAAO,CAAC,oBAAoB;YACrE,MAAMyO,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,EAAEf,oCAAAA,0BAAAA,gBAAiBiB,MAAM,qBAAxB,AAACjB,wBAAuCkB,SAAS;YAEvDtP,IAAIW,OAAO,CAAC,mBAAmB,KAAKX,IAAIW,OAAO,CAAC,OAAO,IAAI,IAAI,CAACqC,QAAQ;YACxEhD,IAAIW,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAACqE,IAAI,GACzC,IAAI,CAACA,IAAI,CAACuK,QAAQ,KAClBH,UACE,QACA;YACNpP,IAAIW,OAAO,CAAC,oBAAoB,KAAKyO,UAAU,UAAU;YACzDpP,IAAIW,OAAO,CAAC,kBAAkB,KAAKyN,oCAAAA,2BAAAA,gBAAiBiB,MAAM,qBAAvBjB,yBAAyBoB,aAAa;YAEzE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAACC,iBAAiB,CAACzP,KAAKE;YAE5B,IAAIgE,WAAW,MAAM,IAAI,CAACnE,gBAAgB,CAACC,KAAK2B,KAAKzB;YACrD,IAAIgE,UAAU;YAEd,MAAMf,gBAAe,qBAAA,IAAI,CAACJ,YAAY,qBAAjB,mBAAmBK,kBAAkB,CACxDsM,IAAAA,wBAAW,EAACxP,WAAWF,IAAIW,OAAO;YAGpC,MAAM0C,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,OAAI,wBAAA,IAAI,CAAC7D,UAAU,CAACqG,IAAI,qBAApB,sBAAsBxC,aAAa;YACpErC,IAAAA,2BAAc,EAAChB,KAAK,iBAAiBqD;YAErC,MAAM/B,MAAMqO,IAAAA,kBAAY,EAAC3P,IAAIsB,GAAG,CAACsO,OAAO,CAAC,QAAQ;YACjD,MAAMC,eAAeC,IAAAA,wCAAmB,EAACxO,IAAInB,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3BuD,cAAc,IAAI,CAACA,YAAY;YACjC;YACAzB,IAAInB,QAAQ,GAAG0P,aAAa1P,QAAQ;YAEpC,IAAI0P,aAAa1H,QAAQ,EAAE;gBACzBnI,IAAIsB,GAAG,GAAGyO,IAAAA,kCAAgB,EAAC/P,IAAIsB,GAAG,EAAG,IAAI,CAAC9B,UAAU,CAAC2I,QAAQ;YAC/D;YAEA,MAAM6H,uBACJ,IAAI,CAACjL,WAAW,IAAI,OAAO/E,IAAIW,OAAO,CAACsP,+BAAmB,CAAC,KAAK;YAElE,uCAAuC;YACvC,IAAID,sBAAsB;gBACxB,IAAI;wBAuBE,wBA6ByB,qBA6DjB;oBAhHZ,IAAI,IAAI,CAAC7L,kBAAkB,CAACwC,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAI3G,IAAIsB,GAAG,CAAChB,KAAK,CAAC,mBAAmB;4BACnCN,IAAIsB,GAAG,GAAGtB,IAAIsB,GAAG,CAACsO,OAAO,CAAC,YAAY;wBACxC;wBACA1P,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAU+P,WAAW,EAAE,GAAG,IAAIC,IAClCC,IAAAA,wBAAW,EAACpQ,IAAIW,OAAO,CAACsP,+BAAmB,CAAC,GAC5C;oBAGF,IAAI,EAAE9P,UAAUkQ,WAAW,EAAE,GAAG,IAAIF,IAAInQ,IAAIsB,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,KAAI,yBAAA,IAAI,CAAClB,WAAW,CAAC0D,IAAI,qBAArB,uBAAuBxD,KAAK,CAAC+P,cAAc;wBAC7CrP,IAAAA,2BAAc,EAAChB,KAAK,iBAAiB;oBACvC,OAGK,IACH,IAAI,CAAC0G,eAAe,IACpB,IAAI,CAAC3B,WAAW,IAChB/E,IAAIW,OAAO,CAAC2P,8BAAkB,CAAC,KAAK,OACpCtQ,IAAIoM,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAM0C,OAAsB,EAAE;wBAC9B,WAAW,MAAMyB,SAASvQ,IAAI8O,IAAI,CAAE;4BAClCA,KAAK/K,IAAI,CAACwM;wBACZ;wBACA,MAAMC,YAAYC,OAAOC,MAAM,CAAC5B,MAAMS,QAAQ,CAAC;wBAE/CvO,IAAAA,2BAAc,EAAChB,KAAK,aAAawQ;oBACnC;oBAEAN,cAAc,IAAI,CAAChP,SAAS,CAACgP;oBAC7B,MAAMS,oBAAoB,IAAI,CAACC,iBAAiB,CAACP;oBAEjDH,cAAcW,IAAAA,wCAAmB,EAACX;oBAElC,8CAA8C;oBAC9C,MAAMY,wBAAuB,sBAAA,IAAI,CAAC/N,YAAY,qBAAjB,oBAAmBS,OAAO,CAAC0M,aAAa;wBACnE7M;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAIyN,sBAAsB;wBACxB9P,IAAAA,2BAAc,EAAChB,KAAK,UAAU8Q,qBAAqBrN,cAAc;wBAEjE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAIqN,qBAAqBC,mBAAmB,EAAE;4BAC5C/P,IAAAA,2BAAc,EAAChB,KAAK,6BAA6B;wBACnD,OAAO;4BACL0D,IAAAA,8BAAiB,EAAC1D,KAAK;wBACzB;oBACF;oBAEA,IAAIgR,cAAcd;oBAClB,IAAIe,gBAAgBC,IAAAA,sBAAc,EAACF;oBACnC,IAAIG,eAGA;wBACFrP,QAAQ;wBACRsP,gBAAgB;oBAClB;oBAEA,IAAI,CAACH,eAAe;wBAClB,MAAM3Q,QAAQ,MAAM,IAAI,CAACwK,QAAQ,CAACxK,KAAK,CAAC0Q,aAAa;4BACnDnL,MAAMiL;wBACR;wBAEA,6DAA6D;wBAC7D,IAAIxQ,OAAO;4BACT0Q,cAAc1Q,MAAM+Q,UAAU,CAAClR,QAAQ;4BAEvC,iEAAiE;4BACjE,iEAAiE;4BACjE,4CAA4C;4BAC5C,IAAI,OAAOG,MAAMwB,MAAM,KAAK,aAAa;gCACvCmP,gBAAgB;gCAChBE,aAAarP,MAAM,GAAGxB,MAAMwB,MAAM;gCAClCqP,aAAaC,cAAc,GAAG;4BAChC;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAIN,sBAAsB;wBACxBZ,cAAcY,qBAAqB3Q,QAAQ;oBAC7C;oBAEA,MAAMmR,QAAQC,IAAAA,2BAAc,EAAC;wBAC3BN;wBACAO,MAAMR;wBACNnL,MAAM,IAAI,CAACrG,UAAU,CAACqG,IAAI;wBAC1BsC,UAAU,IAAI,CAAC3I,UAAU,CAAC2I,QAAQ;wBAClCsJ,UAAU,EAAA,0BAAA,IAAI,CAACC,iBAAiB,uBAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAACtS,UAAU,CAACC,YAAY,CAACsS,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAI1O,iBAAiB,CAACwM,aAAamC,MAAM,EAAE;wBACzC9R,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAEkD,gBAAgBnD,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,mEAAmE;oBACnE,qEAAqE;oBACrE,MAAM8R,oBAAoB;wBAAE,GAAG/R,UAAU8O,KAAK;oBAAC;oBAE/C,MAAMkD,wBAAwBhS,UAAUC,QAAQ;oBAChD,MAAMgS,mBAAmB/I,OAAOC,IAAI,CAClCiI,MAAMc,cAAc,CAACpS,KAAKE;oBAG5B,mEAAmE;oBACnE,mEAAmE;oBACnE,2CAA2C;oBAC3C,MAAMmS,cAAc;wBAAE,GAAGnS,UAAU8O,KAAK;oBAAC;oBACzC,MAAMsD,aAAaJ,0BAA0BhS,UAAUC,QAAQ;oBAE/D,IAAImS,cAAcpS,UAAUC,QAAQ,EAAE;wBACpCa,IAAAA,2BAAc,EAAChB,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBAEA,MAAMoS,iBAAiB,IAAIC;oBAC3B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAItJ,OAAOmD,OAAO,CAACrM,UAAU8O,KAAK,EAAG;wBAC1D,MAAM2D,gBAAgBC,IAAAA,+BAAuB,EAACH;wBAC9C,IAAI,CAACE,eAAe;wBAEpB,gEAAgE;wBAChE,+CAA+C;wBAC/C,OAAOzS,UAAU8O,KAAK,CAACyD,IAAI;wBAC3BF,eAAeM,GAAG,CAACF;wBAEnB,IAAI,OAAOD,UAAU,aAAa;wBAElCL,WAAW,CAACM,cAAc,GAAGG,MAAMC,OAAO,CAACL,SACvCA,MAAMM,GAAG,CAAC,CAACC,IAAMC,IAAAA,kDAAwB,EAACD,MAC1CC,IAAAA,kDAAwB,EAACR;oBAC/B;oBAEA,yDAAyD;oBACzD,IAAIzB,eAAe;wBACjB,IAAInP,SAAiC,CAAC;wBAEtC,gEAAgE;wBAChE,oBAAoB;wBACpB,IAAI,CAACqP,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM6B,2BAA2B,CAC9Cd,aACA;wBAEJ;wBAEA,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAAClB,aAAaC,cAAc,IAC5B,CAACF,IAAAA,sBAAc,EAACP,oBAChB;4BACA,IAAIyC,gBAAgB9B,MAAM+B,mBAAmB,oBAAzB/B,MAAM+B,mBAAmB,MAAzB/B,OAA4BX;4BAEhD,IAAIyC,eAAe;gCACjB9B,MAAM6B,2BAA2B,CAACC,eAAe;gCACjDhK,OAAOkK,MAAM,CAACnC,aAAarP,MAAM,EAAEsR;gCACnCjC,aAAaC,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IACE,8DAA8D;wBAC9DlB,gBAAgB,YAChB,CAACiB,aAAaC,cAAc,IAC5B,CAACF,IAAAA,sBAAc,EAAChB,cAChB;4BACA,IAAIkD,gBAAgB9B,MAAM+B,mBAAmB,oBAAzB/B,MAAM+B,mBAAmB,MAAzB/B,OAA4BpB;4BAEhD,IAAIkD,eAAe;gCACjB,MAAMG,kBAAkBjC,MAAM6B,2BAA2B,CACvDC,eACA;gCAGF,IAAIG,gBAAgBnC,cAAc,EAAE;oCAClChI,OAAOkK,MAAM,CAACxR,QAAQsR;oCACtBjC,eAAeoC;gCACjB;4BACF;wBACF;wBAEA,IAAIpC,aAAaC,cAAc,EAAE;4BAC/BtP,SAASqP,aAAarP,MAAM;wBAC9B;wBAEA,MAAM0R,qBAAqBxT,IAAIW,OAAO,CAAC,sBAAsB;wBAC7D,IACE,OAAO6S,uBAAuB,YAC9BA,sBACAtC,IAAAA,sBAAc,EAAChB,gBACf,CAACiB,aAAaC,cAAc,EAC5B;4BACA,MAAMqC,eACJnC,MAAMoC,yBAAyB,CAACF;4BAElC,IAAIC,cAAc;gCAChBtC,eAAeG,MAAM6B,2BAA2B,CAC9CM,cACA;gCAGF,IAAItC,aAAaC,cAAc,EAAE;oCAC/BtP,SAASqP,aAAarP,MAAM;gCAC9B;4BACF;wBACF;wBAEA,mEAAmE;wBACnE,6DAA6D;wBAC7D,IAAI,CAACqP,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM6B,2BAA2B,CAC9Cd,aACA;4BAGF,IAAIlB,aAAaC,cAAc,EAAE;gCAC/BtP,SAASqP,aAAarP,MAAM;4BAC9B;wBACF;wBAEA,4DAA4D;wBAC5D,+DAA+D;wBAC/D,yBAAyB;wBACzB,IACEwP,MAAMqC,mBAAmB,IACzBhD,sBAAsBK,eACtB,CAACG,aAAaC,cAAc,EAC5B;4BACAtP,SAASwP,MAAMqC,mBAAmB;4BAElC,6DAA6D;4BAC7D,kEAAkE;4BAClE,gEAAgE;4BAChE,8DAA8D;4BAC9D,gEAAgE;4BAChE,IAAIH,uBAAuB,IAAI;gCAC7BxS,IAAAA,2BAAc,EAAChB,KAAK,uBAAuB;4BAC7C;wBACF;wBAEA,IAAI8B,QAAQ;4BACVoO,cAAcoB,MAAMsC,sBAAsB,CAAC5C,aAAalP;4BACxD9B,IAAIsB,GAAG,GAAGgQ,MAAMsC,sBAAsB,CAAC5T,IAAIsB,GAAG,EAAGQ;4BAEjD,kEAAkE;4BAClE,4DAA4D;4BAC5D,UAAU;4BACV,IAAIT,4BAA4BgB,IAAAA,2BAAc,EAC5CrC,KACA;4BAEF,IACEqB,6BACA6P,IAAAA,sBAAc,EAAC7P,2BAA2B,QAC1C;gCACAA,4BAA4BiQ,MAAMsC,sBAAsB,CACtDvS,2BACAS;gCAGF9B,IAAIW,OAAO,CAACI,qDAAmC,CAACF,WAAW,GAAG,GAC5DQ;gCACFL,IAAAA,2BAAc,EACZhB,KACA,6BACAqB;4BAEJ;wBACF;oBACF;oBAEA,IAAI4P,iBAAiBqB,YAAY;4BAGdhB;wBAFjBA,MAAMuC,eAAe,CAAC7T,KAAK;+BACtBmS;+BACA/I,OAAOC,IAAI,CAACiI,EAAAA,2BAAAA,MAAMwC,iBAAiB,qBAAvBxC,yBAAyByC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACA,oEAAoE;oBACpE,oCAAoC;oBACpC,mFAAmF;oBACnF,KAAK,MAAMtB,OAAOF,eAAgB;wBAChC,IAAI,CAAEE,CAAAA,OAAOR,iBAAgB,GAAI;4BAC/B,OAAO/R,UAAU8O,KAAK,CAACyD,IAAI;wBAC7B;oBACF;oBACAvS,UAAUC,QAAQ,GAAG+P;oBACrB5O,IAAInB,QAAQ,GAAGD,UAAUC,QAAQ;oBACjC+D,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACjE,KAAK2B,KAAKzB;oBAC3D,IAAIgE,UAAU;gBAChB,EAAE,OAAO8H,KAAK;oBACZ,IAAIA,eAAegI,kBAAW,IAAIhI,eAAeiI,qBAAc,EAAE;wBAC/DtS,IAAImM,UAAU,GAAG;wBACjB,OAAO,IAAI,CAACoG,WAAW,CAAC,MAAMlU,KAAK2B,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAMqK;gBACR;YACF;YAEAhL,IAAAA,2BAAc,EAAChB,KAAK,kBAAkB4J,QAAQzG;YAE9C,IAAI0M,aAAamC,MAAM,EAAE;gBACvBhS,IAAIsB,GAAG,GAAGG,IAAAA,WAAS,EAACH;gBACpBN,IAAAA,2BAAc,EAAChB,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAAC+E,WAAW,IAAI,CAAC1C,IAAAA,2BAAc,EAACrC,KAAK,WAAW;gBACvD,gEAAgE;gBAChE,IAAI6P,aAAamC,MAAM,EAAE;oBACvBhR,IAAAA,2BAAc,EAAChB,KAAK,UAAU6P,aAAamC,MAAM;gBACnD,OAGK,IAAI3O,eAAe;oBACtBrC,IAAAA,2BAAc,EAAChB,KAAK,UAAUqD;oBAC9BrC,IAAAA,2BAAc,EAAChB,KAAK,6BAA6B;gBACnD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAC,AAAC,IAAI,CAACkF,aAAa,CAASiP,eAAe,IAC5C,CAAC9R,IAAAA,2BAAc,EAACrC,KAAK,qBACrB;gBACA,MAAMoU,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgBlL,OAAOkK,MAAM,CAAC,CAAC,GAAGtT,IAAIW,OAAO;gBAC/C;gBAEAyT,iBAAiBG,iBAAiB;gBAClCvT,IAAAA,2BAAc,EAAChB,KAAK,oBAAoBoU;gBAGtCzU,WAAmB6U,kBAAkB,GAAGJ;YAC5C;YAEA,MAAMK,gBAAgBC,IAAAA,0BAAgB;YAEtC,IAAID,eAAe;gBACjB,MAAME,QAAQC,GAAG,CACf;uBAAIH;iBAAc,CAACzB,GAAG,CAAC,OAAO6B;oBAC5B,IAAI,iBAAiBA,cAAc;oBACjC,+DAA+D;oBAC/D,2DAA2D;oBAC3D,gDAAgD;oBAClD,OAAO;wBACL,MAAMC,4BAA4BC,IAAAA,yCAA4B,EAC5D/U,IAAIW,OAAO,EACX,IAAI,CAACoH,oBAAoB,GAAGC,OAAO,CAACgN,aAAa;wBAGnD,MAAMH,aAAaI,kBAAkB,IAChCH;oBAEP;gBACF;YAEJ;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAACzS,IAAAA,2BAAc,EAACrC,KAAK,6BAA6B;gBACpDgB,IAAAA,2BAAc,EACZhB,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAM2V,aAAa7S,IAAAA,2BAAc,EAACrC,KAAK;YACvC,MAAMmV,gBACJ,CAACnF,wBACD9N,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B8S;YAEF,IAAIC,eAAe;oBAkCf;gBAjCF,MAAMC,eAAe/S,IAAAA,2BAAc,EAACrC,KAAK;gBACzC,IAAIoV,cAAc;oBAChB,MAAMC,cAAchT,IAAAA,2BAAc,EAACrC,KAAK;oBAExC,IAAIqV,aAAa;wBACfjM,OAAOkK,MAAM,CAACpT,UAAU8O,KAAK,EAAEqG;oBACjC;oBAEA1T,IAAImM,UAAU,GAAGsH;oBACjB,IAAIpJ,MAAoB3J,IAAAA,2BAAc,EAACrC,KAAK,kBAAkB;oBAE9D,OAAO,IAAI,CAACkU,WAAW,CAAClI,KAAKhM,KAAK2B,KAAK,WAAWzB,UAAU8O,KAAK;gBACnE;gBAEA,MAAMsG,oBAAoB,IAAInF,IAAI+E,cAAc,KAAK;gBACrD,MAAMK,qBAAqBzF,IAAAA,wCAAmB,EAC5CwF,kBAAkBnV,QAAQ,EAC1B;oBACEX,YAAY,IAAI,CAACA,UAAU;oBAC3BgW,WAAW;gBACb;gBAGF,IAAID,mBAAmBvD,MAAM,EAAE;oBAC7BhR,IAAAA,2BAAc,EAAChB,KAAK,UAAUuV,mBAAmBvD,MAAM;gBACzD;gBAEA,IAAI9R,UAAUC,QAAQ,KAAKmV,kBAAkBnV,QAAQ,EAAE;oBACrDD,UAAUC,QAAQ,GAAGmV,kBAAkBnV,QAAQ;oBAC/Ca,IAAAA,2BAAc,EAAChB,KAAK,cAAcuV,mBAAmBpV,QAAQ;gBAC/D;gBACA,MAAMsV,kBAAkBC,IAAAA,wCAAmB,EACzC3F,IAAAA,kCAAgB,EAAC7P,UAAUC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAAC2I,QAAQ,IAAI,MACjE,yBAAA,IAAI,CAAC3I,UAAU,CAACqG,IAAI,qBAApB,uBAAsBC,OAAO;gBAG/B,IAAI2P,gBAAgBhS,cAAc,EAAE;oBAClCzC,IAAAA,2BAAc,EAAChB,KAAK,UAAUyV,gBAAgBhS,cAAc;gBAC9D;gBACAvD,UAAUC,QAAQ,GAAGsV,gBAAgBtV,QAAQ;gBAE7C,KAAK,MAAMsS,OAAOrJ,OAAOC,IAAI,CAACnJ,UAAU8O,KAAK,EAAG;oBAC9C,OAAO9O,UAAU8O,KAAK,CAACyD,IAAI;gBAC7B;gBACA,MAAM4C,cAAchT,IAAAA,2BAAc,EAACrC,KAAK;gBAExC,IAAIqV,aAAa;oBACfjM,OAAOkK,MAAM,CAACpT,UAAU8O,KAAK,EAAEqG;gBACjC;gBAEAnR,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACjE,KAAK2B,KAAKzB;gBAC3D,IAAIgE,UAAU;gBAEd,MAAM,IAAI,CAACN,2BAA2B,CAAC5D,KAAK2B,KAAKzB;gBACjD;YACF;YAEA,IACEgC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,2BAAc,EAACrC,KAAK,qBACpB;gBACAkE,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACjE,KAAK2B,KAAKzB;gBAC3D,IAAIgE,UAAU;gBAEdA,WAAW,MAAM,IAAI,CAACL,+BAA+B,CACnD7D,KACA2B,KACAzB;gBAEF,IAAIgE,UAAU;gBAEd,MAAM8H,MAAM,IAAI5M;gBACd4M,IAAYzL,MAAM,GAAG;oBACrBoV,UAAU,IAAIC,SAAS,MAAM;wBAC3BjV,SAAS;4BACP,qBAAqB;wBACvB;oBACF;gBACF;gBACEqL,IAAY6J,MAAM,GAAG;gBACvB,MAAM7J;YACR;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAACgE,wBAAwBH,aAAa1H,QAAQ,EAAE;gBAClDjI,UAAUC,QAAQ,GAAG4P,IAAAA,kCAAgB,EACnC7P,UAAUC,QAAQ,EAClB0P,aAAa1H,QAAQ;YAEzB;YAEAxG,IAAImM,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAACgI,GAAG,CAAC9V,KAAK2B,KAAKzB;QAClC,EAAE,OAAO8L,KAAU;YACjB,IAAIA,eAAe+J,wCAAe,EAAE;gBAClC,MAAM/J;YACR;YAEA,IACE,AAACA,OAAO,OAAOA,QAAQ,YAAYA,IAAIgK,IAAI,KAAK,qBAChDhK,eAAegI,kBAAW,IAC1BhI,eAAeiI,qBAAc,EAC7B;gBACAtS,IAAImM,UAAU,GAAG;gBACjB,OAAO,IAAI,CAACoG,WAAW,CAAC,MAAMlU,KAAK2B,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAACoD,WAAW,IAChB,IAAI,CAACyC,UAAU,CAAC1C,GAAG,IAClBmR,IAAAA,sBAAc,EAACjK,QAAQA,IAAI6J,MAAM,EAClC;gBACA,MAAM7J;YACR;YACA,IAAI,CAACW,QAAQ,CAACuJ,IAAAA,uBAAc,EAAClK;YAC7BrK,IAAImM,UAAU,GAAG;YACjBnM,IAAImN,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAwDA;;GAEC,GACD,AAAOoH,8BACLC,IAAiB,EACkC;QACnD,MAAMC,UAAU,IAAI,CAACC,iBAAiB;QACtC,OAAO,CAACtW,KAAK2B,KAAKzB;YAChBqW,IAAAA,2BAAc,EAACvW,KAAKoW;YACpB,OAAOC,QAAQrW,KAAK2B,KAAKzB;QAC3B;IACF;IAEOoW,oBAGL;QACA,OAAO,IAAI,CAAC1J,aAAa,CAAC1C,IAAI,CAAC,IAAI;IACrC;IAQOe,eAAeuL,MAAe,EAAQ;QAC3C,IAAI,CAAChX,UAAU,CAAC4G,WAAW,GAAGoQ,SAASA,OAAO5G,OAAO,CAAC,OAAO,MAAM;QACnE,IAAI,CAACpI,UAAU,CAACpB,WAAW,GAAG,IAAI,CAAC5G,UAAU,CAAC4G,WAAW;IAC3D;IAIA;;;GAGC,GACD,MAAayG,UAAyB;QACpC,IAAI,IAAI,CAACxI,QAAQ,EAAE;QAEnB,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC6H,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAG,MAAM,IAAI,CAACuK,yBAAyB;QAC7D;QACA,IAAI,IAAI,CAACnS,eAAe,KAAK,MAAM;YACjC,IAAI,CAACA,eAAe,GAAG,IAAI,CAACoS,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAACtS,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgBoS,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3BjM,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDtB,OAAOC,IAAI,CAAC,IAAI,CAACmB,gBAAgB,IAAI,CAAC,GAAGqM,OAAO,CAAC,CAACC;YAChD,MAAMC,iBAAiBC,IAAAA,0BAAgB,EAACF;YACxC,IAAI,CAACpM,aAAa,CAACqM,eAAe,EAAE;gBAClCrM,aAAa,CAACqM,eAAe,GAAG,EAAE;YACpC;YACArM,aAAa,CAACqM,eAAe,CAAChT,IAAI,CAAC+S;QACrC;QACA,OAAOpM;IACT;IAEA,MAAgBoL,IACd9V,GAAkB,EAClB2B,GAAmB,EACnBzB,SAA6B,EACd;QACf,OAAO8M,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC2I,GAAG,EAAE,UAC3C,IAAI,CAACmB,OAAO,CAACjX,KAAK2B,KAAKzB;IAE3B;IAEA,MAAc+W,QACZjX,GAAkB,EAClB2B,GAAmB,EACnBzB,SAA6B,EACd;QACf,MAAM,IAAI,CAAC0D,2BAA2B,CAAC5D,KAAK2B,KAAKzB;IACnD;IAEA,MAAcgX,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAOpK,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC+J,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,KAAKF,eAAepX,GAAG,CAACW,OAAO,CAAC,aAAa,IAAI;QAEvD,MAAMsL,MAAqD;YACzD,GAAGmL,cAAc;YACjB5P,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClB,6DAA6D;gBAC7DC,yBAAyB,CAAC,IAAI,CAACD,UAAU,CAAC+P,OAAO;gBACjDC,wBAAwBC,IAAAA,+CAA4B,EAClDH,IACA,IAAI,CAAC9X,UAAU,CAAC+J,eAAe;YAEnC;QACF;QAEA,MAAMmO,UAAU,MAAMP,GAAGlL;QACzB,IAAIyL,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAE1X,GAAG,EAAE2B,GAAG,EAAE,GAAGsK;QACrB,MAAM0L,iBAAiBhW,IAAImM,UAAU;QACrC,MAAM,EAAEgB,IAAI,EAAE8I,IAAI,EAAE,GAAGF;QACvB,IAAI,EAAEG,YAAY,EAAE,GAAGH;QACvB,IAAI,CAAC/V,IAAImW,IAAI,EAAE;YACb,MAAM,EAAEzR,aAAa,EAAEsB,eAAe,EAAE7C,GAAG,EAAE,GAAG,IAAI,CAAC0C,UAAU;YAE/D,oDAAoD;YACpD,IAAI1C,KAAK;gBACPnD,IAAIoW,SAAS,CAAC,iBAAiB;gBAC/BF,eAAehY;YACjB;YAEA,IAAIgY,gBAAgBA,aAAaG,MAAM,KAAKnY,WAAW;gBACrDgY,aAAaG,MAAM,GAAG,IAAI,CAACxY,UAAU,CAACgK,UAAU;YAClD;YAEA,MAAM,IAAI,CAACyO,gBAAgB,CAACjY,KAAK2B,KAAK;gBACpCpB,QAAQuO;gBACR8I;gBACAvR;gBACAsB;gBACAkQ;YACF;YACAlW,IAAImM,UAAU,GAAG6J;QACnB;IACF;IAEA,MAAcO,cACZf,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAMnL,MAAqD;YACzD,GAAGmL,cAAc;YACjB5P,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAMiQ,UAAU,MAAMP,GAAGlL;QACzB,IAAIyL,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQ5I,IAAI,CAACqJ,iBAAiB;IACvC;IAEA,MAAaC,OACXpY,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChB6O,QAA4B,CAAC,CAAC,EAC9B9O,SAAkC,EAClCmY,iBAAiB,KAAK,EACP;QACf,OAAOrL,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACiL,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAACtY,KAAK2B,KAAKxB,UAAU6O,OAAO9O,WAAWmY;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,wBAAwBC,IAAAA,+CAAwB;QACtD,IAAID,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBE,SAAS;QACxC;QAEA,IAAI,IAAI,CAAC3T,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAOlF;QACT;QAEA,OAAO,IAAI,CAAC8Y,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAO9Y;IACT;IAEA,MAAcyY,WACZtY,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChB6O,QAA4B,CAAC,CAAC,EAC9B9O,SAAkC,EAClCmY,iBAAiB,KAAK,EACP;YA4BZrY;QA3BH,IAAI,CAACG,SAASyY,UAAU,CAAC,MAAM;YAC7BnM,QAAQ/H,IAAI,CACV,CAAC,8BAA8B,EAAEvE,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAAC+E,aAAa,CAAC2T,YAAY,IAC/B1Y,aAAa,YACb,CAAE,MAAM,IAAI,CAAC2Y,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxC3Y,WAAW;QACb;QAEA,MAAMmX,KAAKtX,IAAIW,OAAO,CAAC,aAAa,IAAI;QACxC,IAAI,CAAC6G,UAAU,CAAC+P,OAAO,GAAGwB,IAAAA,iBAAU,EAACzB;QAErC,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAACe,kBACD,CAAC,IAAI,CAACtT,WAAW,IACjB,CAAC1C,IAAAA,2BAAc,EAACrC,KAAK,oBACpBA,CAAAA,EAAAA,WAAAA,IAAIsB,GAAG,qBAAPtB,SAASM,KAAK,CAAC,kBACb,IAAI,CAACqF,YAAY,IAAI3F,IAAIsB,GAAG,CAAEhB,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAACsM,aAAa,CAAC5M,KAAK2B,KAAKzB;QACtC;QAEA,IAAI8Y,IAAAA,qBAAa,EAAC7Y,WAAW;YAC3B,OAAO,IAAI,CAACmC,SAAS,CAACtC,KAAK2B,KAAKzB;QAClC;QAEA,OAAO,IAAI,CAACgX,IAAI,CAAC,CAACjL,MAAQ,IAAI,CAACgN,gBAAgB,CAAChN,MAAM;YACpDjM;YACA2B;YACAxB;YACA6O;QACF;IACF;IAEA,MAAgBkK,eAAe,EAC7B/Y,QAAQ,EAMT,EAGE;YAGC;QAFF,+DAA+D;QAC/D,MAAMgZ,iBACJ,oDAAA,IAAI,CAACpR,oBAAoB,GAAGqR,aAAa,CAACjZ,SAAS,qBAAnD,kDAAqD0R,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvCwH,aAAaxZ;YACbyZ,cAAcC,IAAAA,4BAAkB,EAACJ;QACnC;IACF;IAEA,MAAcK,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,OAAO1M,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAACqM,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,OACEC,IAAAA,8CAA0B,EAACD,qBAC3B,IAAI,CAACjP,yBAAyB,CAACmP,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACJ;QACrB;IAEJ;IAEUK,cACRla,GAAkB,EAClB2B,GAAmB,EACnBwY,SAAkB,EAClBN,gBAAwB,EAClB;QACN,MAAMO,iBAAiB,GAAGxZ,4BAAU,CAAC,EAAE,EAAEyZ,+CAA6B,CAAC,EAAE,EAAEvZ,6CAA2B,CAAC,EAAE,EAAEC,qDAAmC,EAAE;QAChJ,MAAM6M,eAAevL,IAAAA,2BAAc,EAACrC,KAAK,mBAAmB;QAE5D,IAAIsa,qBAAqB;QAEzB,IAAIH,aAAa,IAAI,CAACP,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FlY,IAAI4Y,YAAY,CAAC,QAAQ,GAAGH,eAAe,EAAE,EAAEI,0BAAQ,EAAE;YACzDF,qBAAqB;QACvB,OAAO,IAAIH,aAAavM,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnGjM,IAAI4Y,YAAY,CAAC,QAAQH;QAC3B;QAEA,IAAI,CAACE,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAOta,IAAIW,OAAO,CAAC6Z,0BAAQ,CAAC;QAC9B;IACF;IAEA,MAAcb,mCACZ,EACE3Z,GAAG,EACH2B,GAAG,EACHxB,QAAQ,EACRqH,YAAYiT,IAAI,EAC8B,EAChD,EAAEC,UAAU,EAAE1L,KAAK,EAAwB,EACV;YAcJ0L,uBAyJzB,uBA4CAC,OAiHY,wBAm2BdC;QAtqCF,IAAIza,aAAa0a,qCAA0B,EAAE;YAC3C1a,WAAW;QACb;QACA,MAAM2a,kBAAkB3a,aAAa;QACrC,MAAM4a,YACJ5a,aAAa,UAAW2a,mBAAmBnZ,IAAImM,UAAU,KAAK;QAChE,MAAMkN,YACJ7a,aAAa,UAAW2a,mBAAmBnZ,IAAImM,UAAU,KAAK;QAChE,MAAMqM,YAAYO,WAAWP,SAAS,KAAK;QAE3C,MAAMc,iBAAiB,CAAC,CAACP,WAAWQ,kBAAkB;QACtD,IAAIC,oBAAoB,CAAC,CAACT,WAAWxB,cAAc;QACnD,MAAMkC,yBAAyBC,IAAAA,kDAAyB,EAACrb;QACzD,MAAMsb,qBAAqB,CAAC,GAACZ,wBAAAA,WAAWa,SAAS,qBAApBb,sBAAsBc,eAAe;QAClE,IAAIC,QAAQ,CAAC,CAACf,WAAWgB,cAAc;QACvC,uFAAuF;QACvF,MAAM9N,eAAevL,IAAAA,2BAAc,EAACrC,KAAK,mBAAmB;QAE5D,yEAAyE;QACzE,yEAAyE;QACzE,mEAAmE;QACnE,oEAAoE;QACpE,mEAAmE;QACnE,qCAAqC;QACrC,IACE,CAAC,IAAI,CAAC+E,WAAW,IACjB,IAAI,CAACvF,UAAU,CAACC,YAAY,CAACkc,yBAAyB,IACtD/N,cACA;YACA,MAAMjN,UAAUX,IAAIW,OAAO;YAC3B,MAAMib,eAAeC,IAAAA,uDAA8B,EACjDlb,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,EAClDF,OAAO,CAACI,qDAAmC,CAACF,WAAW,GAAG,EAC1DF,OAAO,CAAC0Z,+CAA6B,CAACxZ,WAAW,GAAG,EACpDF,OAAO,CAAC6Z,0BAAQ,CAAC3Z,WAAW,GAAG;YAEjC,MAAMib,aACJzZ,IAAAA,2BAAc,EAACrC,KAAK,8BACpB,IAAImQ,IAAInQ,IAAIsB,GAAG,IAAI,IAAI,oBAAoBya,YAAY,CAAC9N,GAAG,CACzD+N,sCAAoB;YAGxB,IAAIJ,iBAAiBE,YAAY;gBAC/B,iEAAiE;gBACjE,mEAAmE;gBACnE,iFAAiF;gBACjF,6EAA6E;gBAC7E,6EAA6E;gBAC7E,MAAMxa,MAAM,IAAI6O,IAAInQ,IAAIsB,GAAG,IAAI,IAAI;gBACnC2a,IAAAA,8DAAkC,EAAC3a,KAAKsa;gBACxCja,IAAImM,UAAU,GAAG;gBACjBnM,IAAIoW,SAAS,CAAC,YAAY,GAAGzW,IAAInB,QAAQ,GAAGmB,IAAI4a,MAAM,EAAE;gBACxDva,IAAImN,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;QACF;QAEA,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIsB,cAAc7O,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,IAAI,IAAInB,QAAQ,IAAI;QAEtD,IAAIgc,sBAAsB9Z,IAAAA,2BAAc,EAACrC,KAAK,iBAAiBqQ;QAE/D,IAAI,CAAC6J,aAAa,CAACla,KAAK2B,KAAKwY,WAAWgC;QAExC,IAAI9C;QACJ,IAAIC;QACJ,IAAI8C,cAAc;QAElB,MAAMC,YAAYnL,IAAAA,sBAAc,EAACwJ,WAAWlJ,IAAI;QAEhD,MAAMmJ,oBAAoB,IAAI,CAAC5S,oBAAoB;QAEnD,IAAIoS,aAAakC,WAAW;YAC1B,MAAMC,cAAc,MAAM,IAAI,CAACpD,cAAc,CAAC;gBAC5C/Y;gBACAqR,MAAMkJ,WAAWlJ,IAAI;gBACrB2I;gBACA7F,gBAAgBtU,IAAIW,OAAO;YAC7B;YAEA0Y,cAAciD,YAAYjD,WAAW;YACrCC,eAAegD,YAAYhD,YAAY;YACvC8C,cAAc,OAAO9C,iBAAiB;YAEtC,IAAI,IAAI,CAAC9Z,UAAU,CAAC+I,MAAM,KAAK,UAAU;gBACvC,MAAMiJ,OAAOkJ,WAAWlJ,IAAI;gBAC5B,IAAI,CAAC6H,aAAa;oBAChB,MAAM,qBAEL,CAFK,IAAIja,MACR,CAAC,MAAM,EAAEoS,KAAK,wGAAwG,CAAC,GADnH,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAM+K,uBAAuBC,IAAAA,wCAAmB,EAACL;gBACjD,IAAI,CAAC9C,YAAYoD,QAAQ,CAACF,uBAAuB;oBAC/C,MAAM,qBAEL,CAFK,IAAInd,MACR,CAAC,MAAM,EAAEoS,KAAK,oBAAoB,EAAE+K,qBAAqB,8EAA8E,CAAC,GADpI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAIH,aAAa;gBACfjB,oBAAoB;YACtB;QACF;QAEA,IACEiB,gBACA/C,+BAAAA,YAAaoD,QAAQ,CAACN,yBACtB,mDAAmD;QACnD,+BAA+B;QAC/Bnc,IAAIW,OAAO,CAAC,sBAAsB,EAClC;YACA8a,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAACjU,UAAU,CAAC1C,GAAG,EAAE;YAC/B2W,UAAU,CAAC,CAACd,kBAAkB+B,MAAM,CAACC,IAAAA,gBAAO,EAACxc,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAMyc,oBACJ,CAAC,CACCva,CAAAA,IAAAA,2BAAc,EAACrC,KAAK,oBACnBA,IAAIW,OAAO,CAAC,gBAAgB,IAC3B,AAAC,IAAI,CAACuE,aAAa,CAASiP,eAAe,KAE9CsH,CAAAA,SAASR,cAAa;QAEzB;;;KAGC,GACD,MAAM4B,uBACJxa,IAAAA,2BAAc,EAACrC,KAAK,2BAA2B;QAEjD,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACyb,SACDzb,IAAIW,OAAO,CAAC,wBAAwB,IACpC,CAAEoa,CAAAA,aAAa5a,aAAa,SAAQ,GACpC;YACAwB,IAAIoW,SAAS,CAAC9H,+BAAmB,EAAE9P;YACnCwB,IAAIoW,SAAS,CAAC,qBAAqB;YACnCpW,IAAIoW,SAAS,CACX,iBACA;YAEFpW,IAAImN,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,iEAAiE;QACjE,IACE0M,SACA,IAAI,CAAC1W,WAAW,IAChB/E,IAAIW,OAAO,CAACsP,+BAAmB,CAAC,IAChCjQ,IAAIsB,GAAG,CAACsX,UAAU,CAAC,gBACnB;YACA5Y,IAAIsB,GAAG,GAAG,IAAI,CAACsP,iBAAiB,CAAC5Q,IAAIsB,GAAG;QAC1C;QAEA,MAAM0Q,SAAS3P,IAAAA,2BAAc,EAACrC,KAAK;QACnC,MAAMqD,gBAAgBoY,SAClB,wBAAA,IAAI,CAACjc,UAAU,CAACqG,IAAI,qBAApB,sBAAsBxC,aAAa,GACnChB,IAAAA,2BAAc,EAACrC,KAAK;QAExB,IACE,CAAC,CAACA,IAAIW,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACgB,IAAImM,UAAU,IAAInM,IAAImM,UAAU,KAAK,GAAE,GACzC;YACAnM,IAAIoW,SAAS,CACX,yBACA,GAAG/F,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK7R,UAAU;QAE9C;QAEA,IAAI2c;QACJ,IAAIpC,WAAWoC,WAAW,EAAE;YAC1BA,cAAcpC,WAAWoC,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAACrW,eAAe,IACpB,OAAOoW,gBAAgB,eACvBE,IAAAA,4BAAoB,EAACF;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAMG,2BACJ/a,QAAQC,GAAG,CAAC+a,0CAA0C,KAAK,OAC3D,OAAOlO,MAAMmO,aAAa,KAAK,eAC/BJ;QAEF,sEAAsE;QACtE,6CAA6C;QAC7C,MAAMK,6BACJH,4BAA4BjO,MAAMmO,aAAa,KAAK;QAEtD,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAME,oBACJN,mBACC,CAAA,EACCpC,QAAAA,kBAAkB+B,MAAM,CAACvc,SAAS,IAClCwa,kBAAkBvB,aAAa,CAACjZ,SAAS,qBAF1C,AACCwa,MAEC2C,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BL,4BACE,CAAA,IAAI,CAACzV,UAAU,CAAC1C,GAAG,KAAK,QACvB,IAAI,CAACG,qBAAqB,KAAK,IAAG,CAAE;QAE5C,MAAMsY,qBACJN,4BAA4BI;QAE9B,oEAAoE;QACpE,iEAAiE;QACjE,MAAMG,yBACJD,sBAAsB,IAAI,CAAC/V,UAAU,CAAC1C,GAAG,KAAK;QAEhD,MAAM2Y,uBAAuBL,8BAA8BC;QAE3D,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMK,mBAAmBL,oBACrBhb,IAAAA,2BAAc,EAACrC,KAAK,eACpBH;QAEJ,0EAA0E;QAC1E,wEAAwE;QACxE,0DAA0D;QAC1D,MAAM8d,sBACJN,qBAAqBzP,gBAAgB,CAACiP;QAExC,yEAAyE;QACzE,iEAAiE;QACjE,yEAAyE;QACzE,yEAAyE;QACzE,MAAMe,wBAAwBvb,IAAAA,2BAAc,EAC1CrC,KACA;QAGF,MAAM6d,YAAYC,IAAAA,mCAAgB,EAAC9d;QACnC,IAAI6d,aAAaR,mBAAmB;YAClC5B,QAAQ;YACR,IAAI,CAACjU,UAAU,CAACgQ,sBAAsB,GAAG;QAC3C;QAEA,gEAAgE;QAChE,IAAIuD,aAAa,CAAC6B,qBAAqB,CAAChP,cAAc;YACpDjM,IAAImM,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAIiQ,8BAAmB,CAACtB,QAAQ,CAACtc,WAAW;YAC1CwB,IAAImM,UAAU,GAAGkQ,SAAS7d,SAAS8d,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,+CAA+C;QAC/C,CAAC7C,0BACD,uCAAuC;QACvC,CAACsC,oBACD,CAAC3C,aACD,CAACC,aACD7a,aAAa,aACbH,IAAIoM,MAAM,KAAK,UACfpM,IAAIoM,MAAM,KAAK,SACd,CAAA,OAAOsO,WAAWa,SAAS,KAAK,YAAYE,KAAI,GACjD;YACA9Z,IAAImM,UAAU,GAAG;YACjBnM,IAAIoW,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtCpW,IAAImN,IAAI,CAAC,sBAAsBC,IAAI;YACnC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAO2L,WAAWa,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACL3D,MAAM;gBACN,0DAA0D;gBAC1D9I,MAAMoP,qBAAY,CAACC,UAAU,CAACzD,WAAWa,SAAS;YACpD;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAASvM,SAAS,CAACA,MAAMnH,GAAG,EAAE,OAAOmH,MAAMnH,GAAG;QAElD,IAAI4S,KAAKhT,uBAAuB,KAAK,MAAM;gBAIhCiT;YAHT,MAAMpD,KAAKtX,IAAIW,OAAO,CAAC,aAAa,IAAI;YACxC,MAAMyd,eAAeC,IAAAA,YAAK,EAAC/G;YAC3B,MAAMgH,sBACJ,SAAO5D,uBAAAA,WAAW6D,QAAQ,qBAAnB7D,qBAAqBc,eAAe,MAAK,cAChD,oFAAoF;YACpFgD,gCAAqB,IAAI9D,WAAW6D,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClD9D,KAAKhT,uBAAuB,GAC1B,CAACgU,SAAS,CAAC2C,gBAAgB,CAACpP,MAAMnH,GAAG,IAAIyW;QAC7C;QAEA,2DAA2D;QAC3D,IAAI,CAAC1B,qBAAqBzC,aAAaM,KAAK3V,GAAG,EAAE;YAC/C2V,KAAKhT,uBAAuB,GAAG;QACjC;QAEA,MAAM3B,WAAU,yBAAA,IAAI,CAACtG,UAAU,CAACqG,IAAI,qBAApB,uBAAsBC,OAAO;QAE7C,IAAI2Y;QACJ,IAAIC,gBAAgB;QAEpB,IAAIzD,kBAAkBQ,SAAStB,WAAW;YACxC,8DAA8D;YAC9D,IAAIjY,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,MAAM,EAAEuc,iBAAiB,EAAE,GACzBxZ,QAAQ;gBACVsZ,cAAcE,kBACZ3e,KACA2B,KACA,IAAI,CAAC6F,UAAU,CAACM,YAAY,EAC5B,CAAC,CAAC,IAAI,CAACtI,UAAU,CAACC,YAAY,CAACmf,kBAAkB;gBAEnDF,gBAAgBD,gBAAgB;YAClC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,gCAAgC;QAChC,IACEtE,aACA,CAACM,KAAK3V,GAAG,IACT,CAAC4Z,iBACDjD,SACA7N,gBACA,CAAC+P,uBACA,CAAA,CAACkB,IAAAA,4BAAa,EAACpE,KAAKqE,OAAO,KAC1B,AAAC,IAAI,CAAC5Z,aAAa,CAASiP,eAAe,AAAD,GAC5C;YACA/S,IAAAA,sCAAkB,EAACpB,IAAIW,OAAO;QAChC;QAEA,IAAI,EAAEoe,oBAAoB,EAAEC,uBAAuB,EAAE,GACnDC,IAAAA,mCAAyB,EAACjf,KAAK,IAAI,CAACwH,UAAU,CAACM,YAAY;QAE7D,IAAI2T,SAAS,IAAI,CAAC1W,WAAW,IAAI/E,IAAIW,OAAO,CAACsP,+BAAmB,CAAC,EAAE;YACjE,uEAAuE;YACvEkM,sBAAsB9L;QACxB;QAEAA,cAAcmM,IAAAA,wCAAmB,EAACnM;QAClC8L,sBAAsBK,IAAAA,wCAAmB,EAACL;QAC1C,IAAI,IAAI,CAACnW,gBAAgB,EAAE;YACzBmW,sBAAsB,IAAI,CAACnW,gBAAgB,CAAC9E,SAAS,CAACib;QACxD;QAEA,MAAM+C,iBAAiB,CAACC;YACtB,MAAMtQ,WAAW;gBACfuQ,aAAaD,SAASE,SAAS,CAACC,YAAY;gBAC5CxR,YAAYqR,SAASE,SAAS,CAACE,mBAAmB;gBAClDpX,UAAUgX,SAASE,SAAS,CAACG,sBAAsB;YACrD;YACA,MAAM1R,aAAa2R,IAAAA,iCAAiB,EAAC5Q;YACrC,MAAM,EAAE1G,QAAQ,EAAE,GAAG,IAAI,CAAC3I,UAAU;YAEpC,IACE2I,YACA0G,SAAS1G,QAAQ,KAAK,SACtB0G,SAASuQ,WAAW,CAACxG,UAAU,CAAC,MAChC;gBACA/J,SAASuQ,WAAW,GAAG,GAAGjX,WAAW0G,SAASuQ,WAAW,EAAE;YAC7D;YAEA,IAAIvQ,SAASuQ,WAAW,CAACxG,UAAU,CAAC,MAAM;gBACxC/J,SAASuQ,WAAW,GAAGxQ,IAAAA,+BAAwB,EAACC,SAASuQ,WAAW;YACtE;YAEAzd,IACGkN,QAAQ,CAACA,SAASuQ,WAAW,EAAEtR,YAC/BgB,IAAI,CAACD,SAASuQ,WAAW,EACzBrQ,IAAI;QACT;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAI6N,mBAAmB;YACrBT,sBAAsB,IAAI,CAACvL,iBAAiB,CAACuL;YAC7C9L,cAAc,IAAI,CAACO,iBAAiB,CAACP;QACvC;QAEA,IAAIqP,cAA6B;QACjC,IACE,CAAChB,iBACDjD,SACA,CAAChB,KAAKhT,uBAAuB,IAC7B,CAAC2T,0BACD,CAACsC,oBACD,CAACC,qBACD;YACA+B,cAAc,GAAG1N,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACvC,AAAC7R,CAAAA,aAAa,OAAOgc,wBAAwB,GAAE,KAAMnK,SACjD,KACAmK,sBACHnN,MAAMnH,GAAG,GAAG,SAAS,IAAI;QAC9B;QAEA,IAAI,AAACkT,CAAAA,aAAaC,SAAQ,KAAMS,OAAO;YACrCiE,cAAc,GAAG1N,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK7R,WAC5C6O,MAAMnH,GAAG,GAAG,SAAS,IACrB;QACJ;QAEA,IAAI6X,aAAa;YACfA,cAAcC,IAAAA,kCAAgB,EAACD;YAE/B,+CAA+C;YAC/CA,cACEA,gBAAgB,YAAYvf,aAAa,MAAM,MAAMuf;QACzD;QAEA,sDAAsD;QACtD,MAAMtL,mBACJlS,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,AAACzC,WAAmB6U,kBAAkB,GAClC,AAAC7U,WAAmB6U,kBAAkB,GACtC,MAAM,IAAI,CAACH,mBAAmB,CAAC;YAC7BC,gBAAgBlL,OAAOkK,MAAM,CAAC,CAAC,GAAGtT,IAAIW,OAAO;QAC/C;QAEN,0EAA0E;QAC1EyT,iBAAiBG,iBAAiB;QAoBlC,MAAMqL,WAAqB,OAAO,EAChCpP,SAAS,EACTqP,gBAAgB,KAAK,EACrBC,mBAAmB,EACpB;YACC,2DAA2D;YAC3D,IAAIrY,0BAGF,AAFA,uEAAuE;YACvE,6DAA6D;YAC5D,CAACmV,qBAAqBnC,KAAK3V,GAAG,KAAK,QACpC,qEAAqE;YACrE,gBAAgB;YACf,CAAC2W,SAAS,CAACN,qBACZ,mEAAmE;YACnE,QAAQ;YACR,OAAO3K,cAAc,YACrB,sEAAsE;YACtE,uBAAuB;YACvBmN;YAEF,MAAMoC,YAAYve,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,IAAI,IAAI,MAAM0N,KAAK;YAErD,mDAAmD;YACnD,kBAAkB;YAClB,IAAIyL,KAAK3Y,MAAM,EAAE;gBACfsH,OAAOC,IAAI,CAACoR,KAAK3Y,MAAM,EAAE+U,OAAO,CAAC,CAACpE;oBAChC,OAAOsN,SAAS,CAACtN,IAAI;gBACvB;YACF;YACA,MAAMuN,mBACJ3P,gBAAgB,OAAO,IAAI,CAAC7Q,UAAU,CAACqD,aAAa;YAEtD,MAAMod,cAAcxe,IAAAA,WAAS,EAAC;gBAC5BtB,UAAU,GAAGgc,sBAAsB6D,mBAAmB,MAAM,IAAI;gBAChE,uDAAuD;gBACvDhR,OAAO+Q;YACT;YAEA,uEAAuE;YACvE,MAAMG,uBAAuBrC,aAAaR;YAE1C,MAAM7V,aAA+B;gBACnC,GAAGkT,UAAU;gBACb,GAAGD,IAAI;gBACP,GAAIN,YACA;oBACE/F;oBACA,gEAAgE;oBAChE,+DAA+D;oBAC/D,4DAA4D;oBAC5D,WAAW;oBACX+L,cAAc1E,SAAS,CAACjL,aAAa,CAACmN;oBACtCyC,eAAe,IAAI,CAAC5gB,UAAU,CAACC,YAAY,CAAC2gB,aAAa;gBAC3D,IACA,CAAC,CAAC;gBACNxD;gBACAqD;gBACAjO;gBACAlM;gBACAzC;gBACAub,oBAAoB,IAAI,CAACpf,UAAU,CAACC,YAAY,CAACmf,kBAAkB;gBACnE,uFAAuF;gBACvF,8DAA8D;gBAC9D,SAAS;gBACTyB,gBACEpF,kBAAkBK,qBACd7Z,IAAAA,WAAS,EAAC;oBACR,iEAAiE;oBACjE,UAAU;oBACVtB,UAAU,GAAGkQ,cAAc2P,mBAAmB,MAAM,IAAI;oBACxDhR,OAAO+Q;gBACT,KACAE;gBACNxgB,cAAc;oBACZ,GAAGgb,KAAKhb,YAAY;oBACpB4d;gBACF;gBACA5V;gBACAyY;gBACAnB;gBACAuB,aAAa5B;gBACbtD;gBACA5K;gBACAkI,WAAW,IAAI,CAACH,YAAY;gBAC5BgI,SAAS5e,IAAI4e,OAAO,CAACrW,IAAI,CAACvI;gBAC1B6e,kBAAkB3gB;gBAClB,wBAAwB;gBACxB4gB,cAAc,AAAC,IAAI,CAASA,YAAY;YAC1C;YAEA,IAAIlD,sBAAsBC,wBAAwB;gBAChD/V,0BAA0B;gBAC1BD,WAAWkZ,UAAU,GAAG;gBACxBlZ,WAAWC,uBAAuB,GAAG;gBACrCD,WAAWmZ,kBAAkB,GAAG;gBAChCnZ,WAAW2Y,YAAY,GAAG;gBAC1B3Y,WAAWgW,sBAAsB,GAAGA;YACtC;YAEA,qEAAqE;YACrE,wBAAwB;YACxB,IAAIjd;YAEJ,IAAIuc,aAAa;gBACf,IACE8D,IAAAA,6BAAqB,EAAC9D,gBACtB+D,IAAAA,0BAAkB,EAAC/D,gBACnBE,IAAAA,4BAAoB,EAACF,cACrB;oBACA,mDAAmD;oBACnD,IACE9c,IAAIoM,MAAM,KAAK,aACf,CAAC2O,aACD,CAAC6F,IAAAA,6BAAqB,EAAC9D,cACvB;wBACA,MAAMgE,IAAAA,0BAAY,EAAC9gB,KAAK2B,KAAK,IAAIiU,SAAS,MAAM;4BAAEmL,QAAQ;wBAAI;wBAC9D,OAAO;oBACT;oBAEA,MAAMC,UAAU9R,IAAAA,0BAAiB,EAAClP,OAAOA,IAAIoO,eAAe,GAAGpO;oBAC/D,MAAM2V,WAAWpH,IAAAA,2BAAkB,EAAC5M,OAAOA,IAAI6M,gBAAgB,GAAG7M;oBAElE,IACE+Y,WAAWuG,YAAY,CAAC5K,OAAO,IAC/BnU,QAAQC,GAAG,CAACC,YAAY,KAAK,QAC7B;wBACA,MAAM8e,gBAAgB1f,IAAAA,UAAQ,EAC5Ba,IAAAA,2BAAc,EAACrC,KAAK,cAAcA,IAAIsB,GAAG;wBAE3C,IAAI6f,eAAeD,cAAc/gB,QAAQ,IAAI;wBAE7C,KAAK,MAAM6D,cAAc;4BACvB,IAAI,CAAC5D,WAAW,CAACC,kBAAkB;4BACnC,IAAI,CAACD,WAAW,CAACa,WAAW;4BAC5B,IAAI,CAACb,WAAW,CAACe,GAAG;yBACrB,CAAE;4BACD,IAAI6C,8BAAAA,WAAY1D,KAAK,CAAC6gB,eAAe;gCACnCA,eAAend,WAAW9C,SAAS,CAACigB;4BACtC;wBACF;wBAEA,6DAA6D;wBAC7D,0FAA0F;wBAC1F,sEAAsE;wBACtE,IAAI,CAAE,CAAA,IAAI,CAACpc,WAAW,IAAI+V,eAAc,GAAI;4BAC1CkG,QAAQ1f,GAAG,GAAG,GAAG6f,eAAeD,cAAchF,MAAM,IAAI,IAAI;wBAC9D;wBAEA,wCAAwC;wBACxC3F,IAAAA,2BAAc,EAACyK,SAAS3e,IAAAA,2BAAc,EAACrC;wBACvCgB,IAAAA,2BAAc,EAACggB,SAAS,cAAc,IAAI,CAACrc,GAAG;wBAC9C3D,IAAAA,2BAAc,EAACggB,SAAS,WAAW,IAAI,CAACxb,OAAO;wBAC/CxE,IAAAA,2BAAc,EAACggB,SAAS,iBAAiBnB;wBACzC7e,IAAAA,2BAAc,EAACggB,SAAS,SAAShS;wBACjChO,IAAAA,2BAAc,EAACggB,SAAS,UAAUvG,KAAK3Y,MAAM;wBAC7Cd,IAAAA,2BAAc,EACZggB,SACA,gBACA,IAAI,CAACxZ,UAAU,CAAC4Z,YAAY;wBAE9BpgB,IAAAA,2BAAc,EAACggB,SAAS,eAAe,IAAI,CAACjc,WAAW;wBAEvD,IAAIyC,WAAWwE,GAAG,EAAE;4BAClBhL,IAAAA,2BAAc,EAACggB,SAAS,eAAexZ,WAAWwE,GAAG;wBACvD;wBAEA,MAAMqK,UAMuBqE,WAAWuG,YAAY,CAAC5K,OAAO;wBAE5D,MAAMgL,kBACJ,qDAAqD;wBACrD,qDAAqD;wBACrD,qDAAqD;wBACrD,0BAA0B;wBAC1Bnf,QAAQC,GAAG,CAACmf,QAAQ,KAAK,gBACrB,IAAIC,MAAMP,SAAS;4BACjB/S,KAAIuT,MAAW,EAAEC,IAAI;gCACnB,IAAI,OAAOD,MAAM,CAACC,KAAK,KAAK,YAAY;oCACtC,OAAOD,MAAM,CAACC,KAAK,CAACvX,IAAI,CAACsX;gCAC3B;gCACA,OAAOA,MAAM,CAACC,KAAK;4BACrB;4BACAC,KAAIF,MAAW,EAAEC,IAAI,EAAE/O,KAAK;gCAC1B,IAAI+O,SAAS,gBAAgB;;oCACzBzhB,IAAY2hB,YAAY,GAAGjP;gCAC/B;gCACA8O,MAAM,CAACC,KAAK,GAAG/O;gCACf,OAAO;4BACT;wBACF,KACAsO;wBAENzgB,SAAS,MAAM8V,QAAQgL,iBAAiB1L,UAAU;4BAChD+C,WAAW,IAAI,CAACH,YAAY;wBAC9B;wBAEA,uCAAuC;wBACvC,OAAO;oBACT,OAAO;wBACL,IAAIsI,IAAAA,0BAAkB,EAAC/D,cAAc;4BACnC,wEAAwE;4BACxE,sEAAsE;4BACtE,iCAAiC;4BACjC,4HAA4H;4BAC5HtV,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;4BACnDI,WAAWoa,uBAAuB,GAChClH,WAAWkH,uBAAuB;4BAEpC,iDAAiD;4BACjD,IAAI;gCACFrhB,SAAS,MAAMuc,YAAY1E,MAAM,CAC/B4I,SACArL,UACA;oCACEnE,MAAMrR;oCACN2B,QAAQ2Y,KAAK3Y,MAAM;oCACnBkN;oCACAxH;oCACAqa,eAAe;wCACb5f,SAAS,IAAI,CAACA,OAAO;wCACrBsF,cAAc,IAAI,CAAC/H,UAAU,CAAC+H,YAAY;wCAC1CsR,cACE,IAAI,CAAC3T,aAAa,CAAC2T,YAAY,IAAIhZ;oCACvC;oCACAiiB,eAAe;wCACbC,YAAYlC;wCACZS,aAAa9Y,WAAW8Y,WAAW;wCACnC0B,+BAA+B3f,IAAAA,2BAAc,EAC3CrC,KACA;oCAEJ;gCACF;4BAEJ,EAAE,OAAOgM,KAAK;gCACZ,MAAM,IAAI,CAAC/B,6BAA6B,CAAC+B,KAAKhM,KAAK;oCACjDiiB,YAAY;oCACZC,WAAW/hB;oCACXgiB,WAAW;oCACXC,kBAAkBC,IAAAA,2BAAmB,EAAC;wCACpClC,cAAc1E;wCACdsD,sBAAsBvX,WAAWuX,oBAAoB;oCACvD;gCACF;gCACA,MAAM/S;4BACR;wBACF,OAAO;4BACL,MAAMsW,UAAS5H,WAAWoC,WAAW;4BAErC,4EAA4E;4BAC5E,8DAA8D;4BAC9D,4HAA4H;4BAC5HtV,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;4BAEnD,MAAMmb,UAAsC;gCAC1C/Q,MAAMuJ,YAAY,SAAS5a;gCAC3B2B,QAAQ2Y,KAAK3Y,MAAM;gCACnBkN;gCACA8Q;gCACAtY;gCACA9H,0BAA0B,IAAI,CAACH,2BAA2B;gCAC1DsiB,eAAe;oCACb5f,SAAS,IAAI,CAACA,OAAO;gCACvB;4BACF;4BAEA,4DAA4D;4BAC5D,iEAAiE;4BACjE,wCAAwC;4BACxC,IACE,IAAI,CAACzC,UAAU,CAACC,YAAY,CAACkK,SAAS,IACtC,IAAI,CAACnC,UAAU,CAAC1C,GAAG,IACnB,CAAC+X,wBACD,CAACzB,wBACD;gCACA,MAAMoH,SAAS,MAAMF,QAAOE,MAAM,CAACxiB,KAAK2B,KAAK4gB;gCAE7C,6DAA6D;gCAC7D,yBAAyB;gCACzB,IAAIC,OAAOC,QAAQ,CAACC,qBAAqB,EAAE;oCACzClb,WAAWkb,qBAAqB,GAC9BF,OAAOC,QAAQ,CAACC,qBAAqB;gCACzC;4BACF;4BAEA,iDAAiD;4BACjDniB,SAAS,MAAM+hB,QAAOlK,MAAM,CAACpY,KAAK2B,KAAK4gB;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAM,qBAAiD,CAAjD,IAAInjB,MAAM,yCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAgD;gBACxD;YACF,OAAO;gBACL,oEAAoE;gBACpE,iBAAiB;gBACjBmB,SAAS,MAAM,IAAI,CAACoiB,UAAU,CAAC3iB,KAAK2B,KAAKxB,UAAU6O,OAAOxH;YAC5D;YAEA,MAAM,EAAEib,QAAQ,EAAE,GAAGliB;YAErB,MAAM,EACJsX,YAAY,EACZlX,UAAU,CAAC,CAAC,EACZ,oEAAoE;YACpEiiB,WAAWC,SAAS,EACrB,GAAGJ;YAEJ,IAAII,WAAW;gBACbliB,OAAO,CAACmiB,kCAAsB,CAAC,GAAGD;YACpC;YAEA,2DAA2D;;YACzD7iB,IAAY2hB,YAAY,GAAGc,SAASd,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACExH,aACAsB,SACA5D,CAAAA,gCAAAA,aAAckL,UAAU,MAAK,KAC7B,CAAC,IAAI,CAACvb,UAAU,CAAC1C,GAAG,IACpB,CAACuY,mBACD;gBACA,MAAM2F,oBAAoBP,SAASO,iBAAiB;gBAEpD,MAAMhX,MAAM,qBAOX,CAPW,IAAI5M,MACd,CAAC,+CAA+C,EAAEiR,cAChD2S,CAAAA,qCAAAA,kBAAmBC,WAAW,IAC1B,CAAC,UAAU,EAAED,kBAAkBC,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAOZ;gBAEA,IAAID,qCAAAA,kBAAmBE,KAAK,EAAE;oBAC5B,MAAMA,QAAQF,kBAAkBE,KAAK;oBACrClX,IAAIkX,KAAK,GAAGlX,IAAImX,OAAO,GAAGD,MAAMpgB,SAAS,CAACogB,MAAME,OAAO,CAAC;gBAC1D;gBAEA,MAAMpX;YACR;YAEA,uEAAuE;YACvE,iBAAiB;YAEjB,uBAAuB;YACvB,IAAI,gBAAgByW,YAAYA,SAASY,UAAU,EAAE;gBACnD,OAAO;oBACL3Q,OAAO;oBACPmF;gBACF;YACF;YAEA,uBAAuB;YACvB,IAAI4K,SAASa,UAAU,EAAE;gBACvB,OAAO;oBACL5Q,OAAO;wBACLrF,MAAMkW,8BAAe,CAACC,QAAQ;wBAC9BC,OAAOhB,SAAStD,QAAQ,IAAIsD,SAASiB,UAAU;oBACjD;oBACA7L;gBACF;YACF;YAEA,mBAAmB;YACnB,IAAItX,OAAOojB,MAAM,EAAE;gBACjB,OAAO;YACT;YAEA,kEAAkE;YAClE,IAAIxJ,WAAW;gBACb,OAAO;oBACLzH,OAAO;wBACLrF,MAAMkW,8BAAe,CAACK,QAAQ;wBAC9BC,MAAMtjB;wBACNI;wBACAmjB,SAASrB,SAASiB,UAAU;wBAC5BlT,WAAWiS,SAASjS,SAAS;wBAC7BuQ,QAAQ0B,SAAS3U,UAAU;wBAC3BiW,aAAatB,SAASsB,WAAW;oBACnC;oBACAlM;gBACF;YACF;YAEA,OAAO;gBACLnF,OAAO;oBACLrF,MAAMkW,8BAAe,CAACS,KAAK;oBAC3BH,MAAMtjB;oBACN4e,UAAUsD,SAAStD,QAAQ,IAAIsD,SAASiB,UAAU;oBAClD/iB;oBACAogB,QAAQ5G,YAAYxY,IAAImM,UAAU,GAAGjO;gBACvC;gBACAgY;YACF;QACF;QAEA,IAAIoM,oBAAuC,OAAO,EAChDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACf;YACC,MAAMC,eAAe,CAAC,IAAI,CAAC7c,UAAU,CAAC1C,GAAG;YACzC,MAAMwf,aAAaJ,eAAeviB,IAAImW,IAAI;YAE1C,sEAAsE;YACtE,IAAI,CAACuB,eAAegD,WAAW;gBAC7B,IAAIlB,mBAAmB;oBACrB,MAAMmB,cAAc,MAAM,IAAI,CAACpD,cAAc,CAAC;wBAC5C/Y;wBACAmU,gBAAgBtU,IAAIW,OAAO;wBAC3BwZ;wBACA3I,MAAMkJ,WAAWlJ,IAAI;oBACvB;oBAEA6H,cAAciD,YAAYjD,WAAW;oBACrCC,eAAegD,YAAYhD,YAAY;gBACzC,OAAO;oBACLD,cAAcxZ;oBACdyZ,eAAeiL,sBAAY,CAACC,SAAS;gBACvC;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,IACElL,iBAAiBiL,sBAAY,CAACE,SAAS,IACvCpG,IAAAA,YAAK,EAACre,IAAIW,OAAO,CAAC,aAAa,IAAI,KACnC;gBACA2Y,eAAeiL,sBAAY,CAACG,sBAAsB;YACpD;YAEA,wDAAwD;YACxD,iCAAiC;YACjC,IACE3F,wBACAC,2BACA,CAACmF,sBACD,CAAC,IAAI,CAACpf,WAAW,EACjB;gBACA,MAAM,IAAI,CAACzC,SAAS,CAACtC,KAAK2B;gBAC1B,OAAO;YACT;YAEA,IAAIwiB,CAAAA,sCAAAA,mBAAoBQ,OAAO,MAAK,CAAC,GAAG;gBACtC5F,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACCzF,CAAAA,iBAAiBiL,sBAAY,CAACC,SAAS,IAAIL,kBAAiB,GAC7D;gBACA7K,eAAeiL,sBAAY,CAACG,sBAAsB;YACpD;YAEA,sEAAsE;YACtE,uDAAuD;YACvD,EAAE;YACF,sEAAsE;YACtE,8DAA8D;YAC9D,EAAE;YACF,sEAAsE;YACtE,0BAA0B;YAC1B,IAAIE,gBAAgBlF;YACpB,IAAI,CAACkF,iBAAiBnK,KAAK3V,GAAG,IAAIqV,WAAW;gBAC3CyK,gBAAgBjF,IAAAA,kCAAgB,EAACxD;YACnC;YACA,IAAIyI,iBAAiB5V,MAAMnH,GAAG,EAAE;gBAC9B+c,gBAAgBA,cAAchV,OAAO,CAAC,UAAU;YAClD;YAEA,MAAMiV,8BACJD,kBAAiBvL,+BAAAA,YAAaoD,QAAQ,CAACmI;YAEzC,qEAAqE;YACrE,kCAAkC;YAElC,IAAI,IAAI,CAACplB,UAAU,CAACC,YAAY,CAAC6J,qBAAqB,EAAE;gBACtDgQ,eAAeiL,sBAAY,CAACG,sBAAsB;YACpD;YAEA,oEAAoE;YACpE,kCAAkC;YAClC,EAAE;YACF,gCAAgC;YAChC,0CAA0C;YAC1C,wEAAwE;YACxE,iEAAiE;YACjE,yBAAyB;YACzB,iEAAiE;YACjE,qEAAqE;YACrE,EAAE;YACF,IACExiB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC,IAAI,CAAC2C,WAAW,IACjBuU,iBAAiBiL,sBAAY,CAACG,sBAAsB,IACpDE,iBACA,CAACN,cACD,CAAC5F,iBACDrC,aACCgI,CAAAA,gBAAgB,CAAChL,eAAe,CAACwL,2BAA0B,GAC5D;gBACA,IAGE,AAFA,2DAA2D;gBAC3D,kBAAkB;gBACjBR,CAAAA,gBAAiBhL,eAAeA,CAAAA,+BAAAA,YAAa5W,MAAM,IAAG,CAAC,KACxD,2DAA2D;gBAC3D6W,iBAAiBiL,sBAAY,CAACC,SAAS,EACvC;oBACA,MAAM,IAAIzO,wCAAe;gBAC3B;gBAEA,IAAI+O;gBAEJ,kCAAkC;gBAClC,IAAIjE,IAAAA,0BAAkB,EAACnG,WAAWoC,WAAW,KAAK,CAACF,mBAAmB;oBACpE,gEAAgE;oBAChE,oCAAoC;oBACpCkI,mBAAmB,MAAM,IAAI,CAAC5Z,aAAa,CAAC+C,GAAG,CAC7CoW,eAAgBrS,SAAS,CAAC,CAAC,EAAEA,SAAS7R,UAAU,GAAGA,WAAY,MAC/D,yDAAyD;oBACzD,OAAO,EACLgkB,oBAAoBY,6BAA6B,IAAI,EACtD;wBACC,2DAA2D;wBAC3D,8DAA8D;wBAC9D,gEAAgE;wBAChE,iEAAiE;wBACjE,YAAY;wBACZ,IAAIV,cAAc;4BAChB,OAAOW,IAAAA,4BAAoB,EAACD;wBAC9B;wBAEA,kEAAkE;wBAClE,UAAU;wBACV,OAAOnF,SAAS;4BACdpP,WAAW3Q;4BACX,2DAA2D;4BAC3D,+DAA+D;4BAC/D,qBAAqB;4BACrBggB,eAAe;4BACfC,qBAAqB;wBACvB;oBACF,GACA;wBACEmF,WAAWC,oBAAS,CAAClB,KAAK;wBAC1B5P;wBACAiJ;wBACA0E,YAAY;oBACd;gBAEJ,OAGK,IACH1E,qBACAL,IAAAA,4BAAoB,EAACtC,WAAWoC,WAAW,KAC3C,CAAClP,cACD;oBACA,gEAAgE;oBAChE,oCAAoC;oBACpCkX,mBAAmB,MAAM,IAAI,CAAC5Z,aAAa,CAAC+C,GAAG,CAC7CoW,eAAelkB,WAAW,MAC1B,yDAAyD;oBACzD,UACEyf,SAAS;4BACP,4DAA4D;4BAC5D,QAAQ;4BACRpP,WAAW3Q;4BACXggB,eAAehgB;4BACfigB,qBACE,yDAAyD;4BACzD,wDAAwD;4BACxD,YAAY;4BACZuE,gBAAgB5G,uBACZ0H,IAAAA,sCAAsB,EAAChlB,YACvB;wBACR,IACF;wBACE8kB,WAAWC,oBAAS,CAACtB,QAAQ;wBAC7BxP;wBACAiJ;wBACA0E,YAAY;oBACd;gBAEJ;gBAEA,wEAAwE;gBACxE,IAAI+C,qBAAqB,MAAM,OAAO;gBAEtC,qEAAqE;gBACrE,IAAIA,kBAAkB;oBACpB,sEAAsE;oBACtE,iCAAiC;oBACjC,OAAOA,iBAAiBjN,YAAY;oBAEpC,OAAOiN;gBACT;YACF;YAEA,wEAAwE;YACxE,oEAAoE;YACpE,MAAMtU,YACJ,CAACuO,wBAAwB,CAACqF,kBAAkB1G,mBACxCA,mBACA7d;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACE,AAAC0d,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAOhN,cAAc,aACrB;gBACA,OAAO;oBACLqH,cAAc;wBAAEkL,YAAY;wBAAG/K,QAAQnY;oBAAU;oBACjD6S,OAAO;wBACLrF,MAAMkW,8BAAe,CAACS,KAAK;wBAC3BH,MAAM3F,qBAAY,CAACC,UAAU,CAAC;wBAC9BgB,UAAU,CAAC;wBACXxe,SAASd;wBACTkhB,QAAQlhB;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAMigB,sBACJzD,aACAgB,qBACChb,CAAAA,IAAAA,2BAAc,EAACrC,KAAK,0BAA0Byd,oBAAmB,IAC9D0H,IAAAA,sCAAsB,EAAChlB,YACvB;YAEN,sBAAsB;YACtB,OAAOyf,SAAS;gBACdpP;gBACAqP,eAAehgB;gBACfigB;YACF;QACF;QAEA,IACE5d,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,wDAAwD;QACxDsY,WAAWuG,YAAY,CAAC5K,OAAO,IAC9BwK,CAAAA,IAAAA,0BAAkB,EAACnG,WAAWoC,WAAW,KACxC8D,IAAAA,6BAAqB,EAAClG,WAAWoC,WAAW,KAC5CE,IAAAA,4BAAoB,EAACtC,WAAWoC,WAAW,CAAA,GAC7C;YACA,IACEA,CAAAA,+BAAAA,YAAasI,KAAK,KAClBlU,IAAAA,sBAAc,EAAC/Q,aACdua,CAAAA,WAAWxB,cAAc,IAAIiB,SAAQ,GACtC;gBACA,MAAM,IAAI,CAACjB,cAAc,CAAC;oBACxB/Y;oBACAmU,gBAAgBtU,IAAIW,OAAO;oBAC3B6Q,MAAMkJ,WAAWlJ,IAAI;oBACrB2I;gBACF;YACF;YACA,MAAMyF,SAAS;gBACbpP,WAAW3Q;gBACXggB,eAAe;gBACfC,qBAAqB;YACvB;YACA,OAAO;QACT;QAEA,MAAMlF,aAAa,MAAM,IAAI,CAAC1P,aAAa,CAAC+C,GAAG,CAC7CyR,aACAuE,mBACA;YACEgB,WACE,sEAAsE;YACtE,qCAAqC;YACrCnI,CAAAA,+BAAAA,YAAazL,UAAU,CAAChE,IAAI,KAC3B8M,CAAAA,YAAY+K,oBAAS,CAACtB,QAAQ,GAAGsB,oBAAS,CAAClB,KAAK,AAAD;YAClD5P;YACA2K;YACAsG,YAAYrlB,IAAIW,OAAO,CAAC2kB,OAAO,KAAK;YACpCjI;QACF;QAGF,IAAIqB,eAAe;YACjB/c,IAAIoW,SAAS,CACX,iBACA;QAEJ;QAEA,IAAI,CAAC6C,YAAY;YACf,IACE8E,eACA,CAAEX,CAAAA,wBAAwBC,uBAAsB,KAChD,CAAC6B,IAAAA,0BAAkB,EAACnG,WAAWoC,WAAW,KAC1C,CAAC8D,IAAAA,6BAAqB,EAAClG,WAAWoC,WAAW,KAC7C,CAACE,IAAAA,4BAAoB,EAACtC,WAAWoC,WAAW,GAC5C;gBACA,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,qBAA8D,CAA9D,IAAI1d,MAAM,sDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;YACA,OAAO;QACT;QAEA,MAAMmmB,cACJ3K,EAAAA,oBAAAA,WAAWlI,KAAK,qBAAhBkI,kBAAkBvN,IAAI,MAAKkW,8BAAe,CAACK,QAAQ,IACnD,OAAOhJ,WAAWlI,KAAK,CAAClC,SAAS,KAAK;QAExC,IACEiL,SACA,yEAAyE;QACzE,kEAAkE;QAClE,gDAAgD;QAChD,CAACkC,uBACA,CAAA,CAAC4H,eAAe1I,oBAAmB,GACpC;YACA,IAAI,CAAC,IAAI,CAAC9X,WAAW,EAAE;gBACrB,gDAAgD;gBAChD,iCAAiC;gBACjCpD,IAAIoW,SAAS,CACX,kBACAgH,uBACI,gBACAnE,WAAW4K,MAAM,GACf,SACA5K,WAAW+J,OAAO,GAChB,UACA;YAEZ;YACA,0EAA0E;YAC1E,yDAAyD;YACzDhjB,IAAIoW,SAAS,CAAC0N,0CAAwB,EAAE;QAC1C;QAEA,MAAM,EAAE/S,OAAOgT,UAAU,EAAE,GAAG9K;QAE9B,yDAAyD;QACzD,IAAI8K,CAAAA,8BAAAA,WAAYrY,IAAI,MAAKkW,8BAAe,CAACoC,KAAK,EAAE;YAC9C,MAAM,qBAAgE,CAAhE,IAAIC,8BAAc,CAAC,+CAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,sDAAsD;QACtD,IAAI/N;QAEJ,0EAA0E;QAC1E,oCAAoC;QACpC,IAAI6F,kBAAkB;YACpB7F,eAAe;gBAAEkL,YAAY;gBAAG/K,QAAQnY;YAAU;QACpD,OAKK,IACH,IAAI,CAACkF,WAAW,IAChB6I,gBACA,CAACiP,wBACDQ,mBACA;YACAxF,eAAe;gBAAEkL,YAAY;gBAAG/K,QAAQnY;YAAU;QACpD,OAAO,IAAI,CAAC,IAAI,CAAC2H,UAAU,CAAC1C,GAAG,IAAKmW,kBAAkB,CAAC2B,mBAAoB;YACzE,2DAA2D;YAC3D,IAAI8B,eAAe;gBACjB7G,eAAe;oBAAEkL,YAAY;oBAAG/K,QAAQnY;gBAAU;YACpD,OAIK,IAAI,CAAC4b,OAAO;gBACf,IAAI,CAAC9Z,IAAIkkB,SAAS,CAAC,kBAAkB;oBACnChO,eAAe;wBAAEkL,YAAY;wBAAG/K,QAAQnY;oBAAU;gBACpD;YACF,OAQK,IAAIkb,WAAW;gBAClB,MAAM+K,qBAAqBzjB,IAAAA,2BAAc,EAACrC,KAAK;gBAE/C6X,eAAe;oBACbkL,YACE,OAAO+C,uBAAuB,cAAc,IAAIA;oBAClD9N,QAAQnY;gBACV;YACF,OAAO,IAAImb,WAAW;gBACpBnD,eAAe;oBAAEkL,YAAY;oBAAG/K,QAAQnY;gBAAU;YACpD,OAAO,IAAI+a,WAAW/C,YAAY,EAAE;gBAClC,wEAAwE;gBACxE,oBAAoB;gBACpB,IAAI,OAAO+C,WAAW/C,YAAY,CAACkL,UAAU,KAAK,UAAU;wBAUtDnI;oBATJ,IAAIA,WAAW/C,YAAY,CAACkL,UAAU,GAAG,GAAG;wBAC1C,MAAM,qBAEL,CAFK,IAAI3jB,MACR,CAAC,2CAA2C,EAAEwb,WAAW/C,YAAY,CAACkL,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAlL,eAAe;wBACbkL,YAAYnI,WAAW/C,YAAY,CAACkL,UAAU;wBAC9C/K,QACE4C,EAAAA,2BAAAA,WAAW/C,YAAY,qBAAvB+C,yBAAyB5C,MAAM,KAAI,IAAI,CAACxY,UAAU,CAACgK,UAAU;oBACjE;gBACF,OAGK;oBACHqO,eAAe;wBAAEkL,YAAYgD,0BAAc;wBAAE/N,QAAQnY;oBAAU;gBACjE;YACF;QACF;QAEA+a,WAAW/C,YAAY,GAAGA;QAE1B,IACE,OAAO+F,0BAA0B,YACjC8H,CAAAA,8BAAAA,WAAYrY,IAAI,MAAKkW,8BAAe,CAACK,QAAQ,IAC7C8B,WAAW3B,WAAW,EACtB;gBAea2B;YAdb,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YAEtE,oEAAoE;YACpE,uEAAuE;YACvE,wEAAwE;YACxE,sEAAsE;YACtE,sEAAsE;YACtE,wDAAwD;YACxD/jB,IAAIoW,SAAS,CAACiO,0CAAwB,EAAE;YAExC,sEAAsE;YACtE,8CAA8C;YAC9C,MAAMC,QAAOP,sBAAAA,WAAW/kB,OAAO,qBAAlB+kB,mBAAoB,CAAC5C,kCAAsB,CAAC;YACzD,IAAI,IAAI,CAAC/d,WAAW,IAAI0W,SAASwK,QAAQ,OAAOA,SAAS,UAAU;gBACjEtkB,IAAIoW,SAAS,CAAC+K,kCAAsB,EAAEmD;YACxC;YAEA,MAAMC,iBAAiBR,WAAW3B,WAAW,CAAC9V,GAAG,CAAC2P;YAClD,IAAIsI,mBAAmBrmB,WAAW;gBAChC,YAAY;gBACZ,OAAO;oBACL+X,MAAM;oBACN9I,MAAMoP,qBAAY,CAACC,UAAU,CAAC+H;oBAC9B,mEAAmE;oBACnE,+BAA+B;oBAC/BrO,cAAc+C,WAAW/C,YAAY;gBACvC;YACF;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,sEAAsE;YACtE,qEAAqE;YACrE,oEAAoE;YACpE,gCAAgC;YAChClW,IAAImM,UAAU,GAAG;YACjB,OAAO;gBACL8J,MAAM;gBACN9I,MAAMoP,qBAAY,CAACC,UAAU,CAAC;gBAC9BtG,YAAY,EAAE+C,8BAAAA,WAAY/C,YAAY;YACxC;QACF;QAEA,yEAAyE;QACzE,8BAA8B;QAC9B,MAAMsO,eAAe9jB,IAAAA,2BAAc,EAACrC,KAAK;QACzC,IAAImmB,cAAc;gBASRvL,oBAEIA;YAVZ,MAAM1W,WAAW,MAAMiiB,aACrB;gBACE,GAAGvL,UAAU;gBACb,0CAA0C;gBAC1C,wCAAwC;gBACxClI,OAAO;oBACL,GAAGkI,WAAWlI,KAAK;oBACnBrF,MACEuN,EAAAA,qBAAAA,WAAWlI,KAAK,qBAAhBkI,mBAAkBvN,IAAI,MAAKkW,8BAAe,CAACK,QAAQ,GAC/C,UACAhJ,qBAAAA,WAAWlI,KAAK,qBAAhBkI,mBAAkBvN,IAAI;gBAC9B;YACF,GACA;gBACE/L,KAAKe,IAAAA,2BAAc,EAACrC,KAAK;YAC3B;YAEF,IAAIkE,UAAU;gBACZ,0CAA0C;gBAC1C,OAAO;YACT;QACF;QAEA,IAAI,CAACwhB,YAAY;gBAQb9K;YAPF,oDAAoD;YACpD,qDAAqD;YACrD,4DAA4D;YAC5D,2BAA2B;YAC3B5Z,IAAAA,2BAAc,EACZhB,KACA,uBACA4a,4BAAAA,WAAW/C,YAAY,qBAAvB+C,0BAAyBmI,UAAU;YAGrC,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAInI,WAAW/C,YAAY,IAAI,CAAClW,IAAIkkB,SAAS,CAAC,kBAAkB;gBAC9DlkB,IAAIoW,SAAS,CACX,iBACAqO,IAAAA,mCAAqB,EAACxL,WAAW/C,YAAY;YAEjD;YACA,IAAI+E,mBAAmB;gBACrBjb,IAAImM,UAAU,GAAG;gBACjBnM,IAAImN,IAAI,CAAC,qBAAqBC,IAAI;gBAClC,OAAO;YACT;YAEA,IAAI,IAAI,CAACvH,UAAU,CAAC1C,GAAG,EAAE;gBACvB9D,IAAAA,2BAAc,EAAChB,KAAK,iCAAiCG;YACvD;YACA,MAAM,IAAI,CAACmC,SAAS,CAACtC,KAAK2B,KAAK;gBAAExB;gBAAU6O;YAAM,GAAG;YACpD,OAAO;QACT,OAAO,IAAI0W,WAAWrY,IAAI,KAAKkW,8BAAe,CAACC,QAAQ,EAAE;YACvD,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAI5I,WAAW/C,YAAY,IAAI,CAAClW,IAAIkkB,SAAS,CAAC,kBAAkB;gBAC9DlkB,IAAIoW,SAAS,CACX,iBACAqO,IAAAA,mCAAqB,EAACxL,WAAW/C,YAAY;YAEjD;YAEA,IAAI+E,mBAAmB;gBACrB,OAAO;oBACLhF,MAAM;oBACN9I,MAAMoP,qBAAY,CAACC,UAAU,CAC3B,6BAA6B;oBAC7BkI,KAAKC,SAAS,CAACZ,WAAWjC,KAAK;oBAEjC5L,cAAc+C,WAAW/C,YAAY;gBACvC;YACF,OAAO;gBACL,MAAMqH,eAAewG,WAAWjC,KAAK;gBACrC,OAAO;YACT;QACF,OAAO,IAAIiC,WAAWrY,IAAI,KAAKkW,8BAAe,CAACgD,SAAS,EAAE;YACxD,qDAAqD;YACrD,MAAM,qBAAuD,CAAvD,IAAInnB,MAAM,CAAC,0CAA0C,CAAC,GAAtD,qBAAA;uBAAA;4BAAA;8BAAA;YAAsD;QAC9D,OAAO,IAAIsmB,WAAWrY,IAAI,KAAKkW,8BAAe,CAACK,QAAQ,EAAE;gBAkC1C8B;YAjCb,oEAAoE;YACpE,gBAAgB;YAChB,IAAIH,eAAe7H,kBAAkB;gBACnC,MAAM,qBAEL,CAFK,IAAIte,MACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIsmB,WAAW/kB,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAG+kB,WAAW/kB,OAAO;gBAAC;gBAExC,IAAI,CAAC,IAAI,CAACoE,WAAW,IAAI,CAAC0W,OAAO;oBAC/B,OAAO9a,OAAO,CAACmiB,kCAAsB,CAAC;gBACxC;gBAEA,KAAK,IAAI,CAACrQ,KAAKC,MAAM,IAAItJ,OAAOmD,OAAO,CAAC5L,SAAU;oBAChD,IAAI,OAAO+R,UAAU,aAAa;oBAElC,IAAII,MAAMC,OAAO,CAACL,QAAQ;wBACxB,KAAK,MAAMO,KAAKP,MAAO;4BACrB/Q,IAAI4Y,YAAY,CAAC9H,KAAKQ;wBACxB;oBACF,OAAO,IAAI,OAAOP,UAAU,UAAU;wBACpCA,QAAQA,MAAMnD,QAAQ;wBACtB5N,IAAI4Y,YAAY,CAAC9H,KAAKC;oBACxB,OAAO;wBACL/Q,IAAI4Y,YAAY,CAAC9H,KAAKC;oBACxB;gBACF;YACF;YAEA,sEAAsE;YACtE,8CAA8C;YAC9C,MAAMuT,QAAOP,uBAAAA,WAAW/kB,OAAO,qBAAlB+kB,oBAAoB,CAAC5C,kCAAsB,CAAC;YACzD,IAAI,IAAI,CAAC/d,WAAW,IAAI0W,SAASwK,QAAQ,OAAOA,SAAS,UAAU;gBACjEtkB,IAAIoW,SAAS,CAAC+K,kCAAsB,EAAEmD;YACxC;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAIP,WAAW3E,MAAM,IAAK,CAAA,CAACnT,gBAAgB,CAACyP,iBAAgB,GAAI;gBAC9D1b,IAAImM,UAAU,GAAG4X,WAAW3E,MAAM;YACpC;YAEA,gGAAgG;YAChG,IACE,CAAC,IAAI,CAAChc,WAAW,IACjB2gB,WAAW3E,MAAM,IACjByF,sCAAkB,CAACd,WAAW3E,MAAM,CAAC,IACrCnT,cACA;gBACAjM,IAAImM,UAAU,GAAG;YACnB;YAEA,sCAAsC;YACtC,IAAIyX,aAAa;gBACf5jB,IAAIoW,SAAS,CAACiO,0CAAwB,EAAE;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAIpY,gBAAgB,CAAC8Q,eAAe;gBAClC,8DAA8D;gBAC9D,IAAI,OAAOgH,WAAW5B,OAAO,KAAK,aAAa;oBAC7C,IAAI4B,WAAWlV,SAAS,EAAE;wBACxB,MAAM,qBAA0D,CAA1D,IAAIpR,MAAM,kDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyD;oBACjE;oBAEA,OAAO;wBACLwY,MAAM;wBACN9I,MAAM4W,WAAW7B,IAAI;wBACrB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/EhM,cAAc8F,sBACV;4BAAEoF,YAAY;4BAAG/K,QAAQnY;wBAAU,IACnC+a,WAAW/C,YAAY;oBAC7B;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO;oBACLD,MAAM;oBACN9I,MAAMoP,qBAAY,CAACC,UAAU,CAACuH,WAAW5B,OAAO;oBAChDjM,cAAc+C,WAAW/C,YAAY;gBACvC;YACF;YAEA,mCAAmC;YACnC,IAAI/I,OAAO4W,WAAW7B,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAAC0B,eAAe,IAAI,CAACxgB,WAAW,EAAE;gBACpC,OAAO;oBACL6S,MAAM;oBACN9I;oBACA+I,cAAc+C,WAAW/C,YAAY;gBACvC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAI0F,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnD1O,KAAK2X,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,CAACC,yBAAY,CAACC,MAAM,CAACC,aAAa;wBACpDJ,WAAWhQ,KAAK;oBAClB;gBACF;gBAGF,OAAO;oBACLgB,MAAM;oBACN9I;oBACA+I,cAAc;wBAAEkL,YAAY;wBAAG/K,QAAQnY;oBAAU;gBACnD;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAMonB,cAAc,IAAIC;YACxBpY,KAAK2X,KAAK,CAACQ,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzEvH,SAAS;gBACPpP,WAAWkV,WAAWlV,SAAS;gBAC/BqP,eAAehgB;gBACf,sEAAsE;gBACtE,YAAY;gBACZigB,qBAAqB;YACvB,GACGnJ,IAAI,CAAC,OAAOpW;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,qBAAwD,CAAxD,IAAInB,MAAM,gDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuD;gBAC/D;gBAEA,IAAImB,EAAAA,gBAAAA,OAAOmS,KAAK,qBAAZnS,cAAc8M,IAAI,MAAKkW,8BAAe,CAACK,QAAQ,EAAE;wBAELrjB;oBAD9C,MAAM,qBAEL,CAFK,IAAInB,MACR,CAAC,yCAAyC,GAAEmB,iBAAAA,OAAOmS,KAAK,qBAAZnS,eAAc8M,IAAI,EAAE,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,6CAA6C;gBAC7C,MAAM9M,OAAOmS,KAAK,CAACmR,IAAI,CAACuD,MAAM,CAACH,YAAYI,QAAQ;YACrD,GACCC,KAAK,CAAC,CAACtb;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1Dib,YAAYI,QAAQ,CAACE,KAAK,CAACvb,KAAKsb,KAAK,CAAC,CAACE;oBACrC/a,QAAQC,KAAK,CAAC,8BAA8B8a;gBAC9C;YACF;YAEF,OAAO;gBACL5P,MAAM;gBACN9I;gBACA,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrC+I,cAAc;oBAAEkL,YAAY;oBAAG/K,QAAQnY;gBAAU;YACnD;QACF,OAAO,IAAI+c,mBAAmB;YAC5B,OAAO;gBACLhF,MAAM;gBACN9I,MAAMoP,qBAAY,CAACC,UAAU,CAACkI,KAAKC,SAAS,CAACZ,WAAWvG,QAAQ;gBAChEtH,cAAc+C,WAAW/C,YAAY;YACvC;QACF,OAAO;YACL,OAAO;gBACLD,MAAM;gBACN9I,MAAM4W,WAAW7B,IAAI;gBACrBhM,cAAc+C,WAAW/C,YAAY;YACvC;QACF;IACF;IAEQjH,kBAAkB5O,IAAY,EAAEylB,cAAc,IAAI,EAAE;QAC1D,IAAIzlB,KAAKya,QAAQ,CAAC,IAAI,CAACxa,OAAO,GAAG;YAC/B,MAAMylB,YAAY1lB,KAAKc,SAAS,CAC9Bd,KAAKohB,OAAO,CAAC,IAAI,CAACnhB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACQ,MAAM;YAGlDT,OAAO6O,IAAAA,wCAAmB,EAAC6W,UAAU9X,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAAC5J,gBAAgB,IAAIyhB,aAAa;YACxC,OAAO,IAAI,CAACzhB,gBAAgB,CAAC9E,SAAS,CAACc;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChC2lB,oBAAoBzZ,KAAa,EAAE;QAC3C,IAAI,IAAI,CAAC/J,kBAAkB,CAACwC,GAAG,EAAE;gBACP;YAAxB,MAAMihB,mBAAkB,sBAAA,IAAI,CAACld,aAAa,qBAAlB,mBAAoB,CAACwD,MAAM;YAEnD,IAAI,CAAC0Z,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACd5b,GAAkD,EAClD6b,gBAAyB,EACzB;YAkBgB;QAjBhB,MAAM,EAAE9Y,KAAK,EAAE7O,QAAQ,EAAE,GAAG8L;QAE5B,MAAM8b,WAAW,IAAI,CAACJ,mBAAmB,CAACxnB;QAC1C,MAAMga,YAAYrH,MAAMC,OAAO,CAACgV;QAEhC,IAAIvW,OAAOrR;QACX,IAAIga,WAAW;YACb,4EAA4E;YAC5E3I,OAAOuW,QAAQ,CAACA,SAAStlB,MAAM,GAAG,EAAE;QACtC;QAEA,MAAMlC,SAAS,MAAM,IAAI,CAACynB,kBAAkB,CAAC;YAC3ChW,QAAQ3P,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;YAChCwR;YACAxC;YACAlN,QAAQmK,IAAIzE,UAAU,CAAC1F,MAAM,IAAI,CAAC;YAClCqY;YACA8N,YAAY,CAAC,GAAC,oCAAA,IAAI,CAACzoB,UAAU,CAACC,YAAY,CAACyoB,GAAG,qBAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAI7nB,QAAQ;YACVyM,IAAAA,iBAAS,IAAGqb,oBAAoB,CAAC,cAAcloB;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAACqZ,8BAA8B,CAACvN,KAAK1L;YACxD,EAAE,OAAOyL,KAAK;gBACZ,MAAMsc,oBAAoBtc,eAAe+J,wCAAe;gBAExD,IAAI,CAACuS,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAM9b;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAciN,iBACZhN,GAAkD,EACjB;QACjC,OAAOe,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAAC8L,gBAAgB,EAC/B;YACE7L,UAAU,CAAC,cAAc,CAAC;YAC1BI,YAAY;gBACV,cAAcvB,IAAI9L,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAACooB,oBAAoB,CAACtc;QACnC;IAEJ;IAQA,MAAcsc,qBACZtc,GAAkD,EACjB;YAmBzB;QAlBR,MAAM,EAAEjM,GAAG,EAAE2B,GAAG,EAAEqN,KAAK,EAAE7O,QAAQ,EAAE,GAAG8L;QACtC,IAAIuF,OAAOrR;QACX,MAAM2nB,mBACJzlB,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE,uBAAuB;QAEjD,IACE,CAAC,IAAI,CAAC+E,WAAW,IACjB,IAAI,CAACvF,UAAU,CAACC,YAAY,CAACkc,yBAAyB,EACtD;YACA3a,IAAAA,2BAAc,EACZiL,IAAIjM,GAAG,EACP,2BACAgP,KAAK,CAACgN,sCAAoB,CAAC;QAE/B;QACA,OAAOhN,KAAK,CAACgN,sCAAoB,CAAC;QAElC,MAAMlc,UAAwB;YAC5B+F,IAAI,GAAE,qBAAA,IAAI,CAAC9C,YAAY,qBAAjB,mBAAmBylB,WAAW,CAACxoB,KAAKG;QAC5C;QAEA,IAAI;YACF,WAAW,MAAMG,SAAS,IAAI,CAACwK,QAAQ,CAAC2d,QAAQ,CAACtoB,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAM4oB,eAAermB,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAAC+E,WAAW,IACjB,OAAO2jB,iBAAiB,YACxBxX,IAAAA,sBAAc,EAACwX,gBAAgB,OAC/BA,iBAAiBpoB,MAAM+Q,UAAU,CAAClR,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAMI,SAAS,MAAM,IAAI,CAACsnB,mBAAmB,CAC3C;oBACE,GAAG5b,GAAG;oBACN9L,UAAUG,MAAM+Q,UAAU,CAAClR,QAAQ;oBACnCqH,YAAY;wBACV,GAAGyE,IAAIzE,UAAU;wBACjB1F,QAAQxB,MAAMwB,MAAM;oBACtB;gBACF,GACAgmB;gBAEF,IAAIvnB,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAAC2E,aAAa,CAACiP,eAAe,EAAE;gBACtC,sDAAsD;gBACtDlI,IAAI9L,QAAQ,GAAG,IAAI,CAAC+E,aAAa,CAACiP,eAAe,CAAC3C,IAAI;gBACtD,MAAMjR,SAAS,MAAM,IAAI,CAACsnB,mBAAmB,CAAC5b,KAAK6b;gBACnD,IAAIvnB,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAOmM,OAAO;YACd,MAAMV,MAAMkK,IAAAA,uBAAc,EAACxJ;YAE3B,IAAIA,iBAAiBic,wBAAiB,EAAE;gBACtClc,QAAQC,KAAK,CACX,yCACA2Z,KAAKC,SAAS,CACZ;oBACE9U;oBACAlQ,KAAK2K,IAAIjM,GAAG,CAACsB,GAAG;oBAChB4O,aAAajE,IAAIjM,GAAG,CAACW,OAAO,CAACsP,+BAAmB,CAAC;oBACjD2Y,SAASvmB,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;oBACjCsS,YAAY,CAAC,CAACjQ,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;oBACtC6oB,YAAYxmB,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAMgM;YACR;YAEA,IAAIA,eAAe+J,wCAAe,IAAI+R,kBAAkB;gBACtD,MAAM9b;YACR;YACA,IAAIA,eAAegI,kBAAW,IAAIhI,eAAeiI,qBAAc,EAAE;gBAC/DtS,IAAImM,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAACgb,qBAAqB,CAAC7c,KAAKD;YAC/C;YAEArK,IAAImM,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAACgL,OAAO,CAAC,SAAS;gBAC9B9X,IAAAA,2BAAc,EAACiL,IAAIjM,GAAG,EAAE,qBAAqB;gBAC7C,MAAM,IAAI,CAAC8oB,qBAAqB,CAAC7c,KAAKD;gBACtCtI,IAAAA,8BAAiB,EAACuI,IAAIjM,GAAG,EAAE;YAC7B;YAEA,MAAM+oB,iBAAiB/c,eAAe9M;YAEtC,IAAI,CAAC6pB,gBAAgB;gBACnB,IACE,AAAC,IAAI,CAAChkB,WAAW,IAAI7C,QAAQC,GAAG,CAACC,YAAY,KAAK,UAClD,IAAI,CAACoF,UAAU,CAAC1C,GAAG,EACnB;oBACA,IAAIkkB,IAAAA,gBAAO,EAAChd,MAAMA,IAAIwF,IAAI,GAAGA;oBAC7B,MAAMxF;gBACR;gBACA,IAAI,CAACW,QAAQ,CAACuJ,IAAAA,uBAAc,EAAClK;YAC/B;YACA,MAAM2J,WAAW,MAAM,IAAI,CAACmT,qBAAqB,CAC/C7c,KACA8c,iBAAiB,AAAC/c,IAA0B1M,UAAU,GAAG0M;YAE3D,OAAO2J;QACT;QAEA,MAAM/T,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IACED,cACA,CAAC,CAACqK,IAAIjM,GAAG,CAACW,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACgB,IAAImM,UAAU,IAAInM,IAAImM,UAAU,KAAK,OAAOnM,IAAImM,UAAU,KAAK,GAAE,GACnE;YACA,MAAMkE,SAAS3P,IAAAA,2BAAc,EAACrC,KAAK;YAEnC2B,IAAIoW,SAAS,CACX,yBACA,GAAG/F,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK7R,UAAU;YAE5CwB,IAAImM,UAAU,GAAG;YACjBnM,IAAIoW,SAAS,CAAC,gBAAgB;YAC9BpW,IAAImN,IAAI,CAAC;YACTnN,IAAIoN,IAAI;YACR,OAAO;QACT;QAEApN,IAAImM,UAAU,GAAG;QACjB,OAAO,IAAI,CAACgb,qBAAqB,CAAC7c,KAAK;IACzC;IAEA,MAAagd,aACXjpB,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChB6O,QAAwB,CAAC,CAAC,EACF;QACxB,OAAOhC,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC8b,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAAClpB,KAAK2B,KAAKxB,UAAU6O;QACnD;IACF;IAEA,MAAcka,iBACZlpB,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChB6O,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAACkJ,aAAa,CAAC,CAACjM,MAAQ,IAAI,CAACgN,gBAAgB,CAAChN,MAAM;YAC7DjM;YACA2B;YACAxB;YACA6O;QACF;IACF;IAEA,MAAakF,YACXlI,GAAiB,EACjBhM,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChB6O,QAA4B,CAAC,CAAC,EAC9Bma,aAAa,IAAI,EACF;QACf,OAAOnc,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC+G,WAAW,EAAE;YACnD,OAAO,IAAI,CAACkV,eAAe,CAACpd,KAAKhM,KAAK2B,KAAKxB,UAAU6O,OAAOma;QAC9D;IACF;IAEA,MAAcC,gBACZpd,GAAiB,EACjBhM,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChB6O,QAA4B,CAAC,CAAC,EAC9Bma,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACdxnB,IAAIoW,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAACb,IAAI,CACd,OAAOjL;YACL,MAAM0J,WAAW,MAAM,IAAI,CAACmT,qBAAqB,CAAC7c,KAAKD;YACvD,IAAI,IAAI,CAACjH,WAAW,IAAIpD,IAAImM,UAAU,KAAK,KAAK;gBAC9C,MAAM9B;YACR;YACA,OAAO2J;QACT,GACA;YAAE3V;YAAK2B;YAAKxB;YAAU6O;QAAM;IAEhC;IAQA,MAAc8Z,sBACZ7c,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOgB,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC2b,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACO,yBAAyB,CAACpd,KAAKD;QAC7C;IACF;IAEA,MAAgBqd,0BACdpd,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAACxE,UAAU,CAAC1C,GAAG,IAAImH,IAAI9L,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACLyX,MAAM;gBACN9I,MAAMoP,qBAAY,CAACC,UAAU,CAAC;YAChC;QACF;QACA,MAAM,EAAExc,GAAG,EAAEqN,KAAK,EAAE,GAAG/C;QAEvB,IAAI;YACF,IAAI1L,SAAsC;YAE1C,MAAM+oB,QAAQ3nB,IAAImM,UAAU,KAAK;YACjC,IAAIyb,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAACnlB,kBAAkB,CAACwC,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CpG,SAAS,MAAM,IAAI,CAACynB,kBAAkB,CAAC;wBACrChW,QAAQ3P,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;wBAChCwR,MAAMgY,2CAAgC;wBACtCxa;wBACAlN,QAAQ,CAAC;wBACTqY,WAAW;wBACXiO,cAAc;wBACd9mB,KAAK2K,IAAIjM,GAAG,CAACsB,GAAG;oBAClB;oBACAioB,eAAehpB,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAACuY,OAAO,CAAC,SAAU;oBAC3CvY,SAAS,MAAM,IAAI,CAACynB,kBAAkB,CAAC;wBACrChW,QAAQ3P,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;wBAChCwR,MAAM;wBACNxC;wBACAlN,QAAQ,CAAC;wBACTqY,WAAW;wBACX,qEAAqE;wBACrEiO,cAAc;wBACd9mB,KAAK2K,IAAIjM,GAAG,CAACsB,GAAG;oBAClB;oBACAioB,eAAehpB,WAAW;gBAC5B;YACF;YACA,IAAIkpB,aAAa,CAAC,CAAC,EAAE9nB,IAAImM,UAAU,EAAE;YAErC,IACE,CAACzL,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE,wBACzB,CAACO,UACDwd,8BAAmB,CAACtB,QAAQ,CAACgN,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAACjiB,UAAU,CAAC1C,GAAG,EAAE;oBACjDvE,SAAS,MAAM,IAAI,CAACynB,kBAAkB,CAAC;wBACrChW,QAAQ3P,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;wBAChCwR,MAAMiY;wBACNza;wBACAlN,QAAQ,CAAC;wBACTqY,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACTiO,cAAc;wBACd9mB,KAAK2K,IAAIjM,GAAG,CAACsB,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAACf,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAACynB,kBAAkB,CAAC;oBACrChW,QAAQ3P,IAAAA,2BAAc,EAAC4J,IAAIjM,GAAG,EAAE;oBAChCwR,MAAM;oBACNxC;oBACAlN,QAAQ,CAAC;oBACTqY,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACTiO,cAAc;oBACd9mB,KAAK2K,IAAIjM,GAAG,CAACsB,GAAG;gBAClB;gBACAmoB,aAAa;YACf;YAEA,IACEvnB,QAAQC,GAAG,CAACmf,QAAQ,KAAK,gBACzB,CAACiI,gBACA,MAAM,IAAI,CAACzQ,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAACvU,oBAAoB;YAC3B;YAEA,IAAI,CAAChE,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAACiH,UAAU,CAAC1C,GAAG,EAAE;oBACvB,OAAO;wBACL8S,MAAM;wBACN,mDAAmD;wBACnD9I,MAAMoP,qBAAY,CAACC,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC;oBAEd;gBACF;gBAEA,MAAM,IAAIjf,kBACR,qBAA8C,CAA9C,IAAIE,MAAM,sCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YAEjD;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAImB,OAAOma,UAAU,CAACoC,WAAW,EAAE;gBACjC9b,IAAAA,2BAAc,EAACiL,IAAIjM,GAAG,EAAE,SAAS;oBAC/BqR,YAAY9Q,OAAOma,UAAU,CAACoC,WAAW,CAACzL,UAAU;oBACpDvP,QAAQjC;gBACV;YACF,OAAO;gBACL6D,IAAAA,8BAAiB,EAACuI,IAAIjM,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAACwZ,8BAA8B,CAC9C;oBACE,GAAGvN,GAAG;oBACN9L,UAAUspB;oBACVjiB,YAAY;wBACV,GAAGyE,IAAIzE,UAAU;wBACjBwE;oBACF;gBACF,GACAzL;YAEJ,EAAE,OAAOmpB,oBAAoB;gBAC3B,IAAIA,8BAA8B3T,wCAAe,EAAE;oBACjD,MAAM,qBAAmD,CAAnD,IAAI3W,MAAM,2CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAkD;gBAC1D;gBACA,MAAMsqB;YACR;QACF,EAAE,OAAOhd,OAAO;YACd,MAAMid,oBAAoBzT,IAAAA,uBAAc,EAACxJ;YACzC,MAAMqc,iBAAiBY,6BAA6BzqB;YACpD,IAAI,CAAC6pB,gBAAgB;gBACnB,IAAI,CAACpc,QAAQ,CAACgd;YAChB;YACAhoB,IAAImM,UAAU,GAAG;YACjB,MAAM8b,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9D5d,IAAIjM,GAAG,CAACsB,GAAG;YAGb,IAAIsoB,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;gBACnC5oB,IAAAA,2BAAc,EAACiL,IAAIjM,GAAG,EAAE,SAAS;oBAC/BqR,YAAYuY,mBAAmB9M,WAAW,CAAEzL,UAAU;oBACtDvP,QAAQjC;gBACV;gBAEA,OAAO,IAAI,CAAC2Z,8BAA8B,CACxC;oBACE,GAAGvN,GAAG;oBACN9L,UAAU;oBACVqH,YAAY;wBACV,GAAGyE,IAAIzE,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtCwE,KAAK+c,iBACDY,kBAAkBrqB,UAAU,GAC5BqqB;oBACN;gBACF,GACA;oBACE3a;oBACA0L,YAAYkP;gBACd;YAEJ;YACA,OAAO;gBACLhS,MAAM;gBACN9I,MAAMoP,qBAAY,CAACC,UAAU,CAAC;YAChC;QACF;IACF;IAEA,MAAa2L,kBACX9d,GAAiB,EACjBhM,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChB6O,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAACkJ,aAAa,CAAC,CAACjM,MAAQ,IAAI,CAAC6c,qBAAqB,CAAC7c,KAAKD,MAAM;YACvEhM;YACA2B;YACAxB;YACA6O;QACF;IACF;IAEA,MAAa1M,UACXtC,GAAkB,EAClB2B,GAAmB,EACnBzB,SAA8D,EAC9DipB,aAAa,IAAI,EACF;QACf,MAAM,EAAEhpB,QAAQ,EAAE6O,KAAK,EAAE,GAAG9O,YAAYA,YAAYsB,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,EAAG;QAEvE,uDAAuD;QACvD,IAAI,IAAI,CAAC9B,UAAU,CAACqG,IAAI,EAAE;YACxB,IAAI,CAACxD,IAAAA,2BAAc,EAACrC,KAAK,WAAW;gBAClCgB,IAAAA,2BAAc,EAAChB,KAAK,UAAU,IAAI,CAACR,UAAU,CAACqG,IAAI,CAACxC,aAAa;YAClE;YACArC,IAAAA,2BAAc,EAAChB,KAAK,iBAAiB,IAAI,CAACR,UAAU,CAACqG,IAAI,CAACxC,aAAa;QACzE;QAEA1B,IAAImM,UAAU,GAAG;QACjB,OAAO,IAAI,CAACoG,WAAW,CAAC,MAAMlU,KAAK2B,KAAKxB,UAAW6O,OAAOma;IAC5D;AACF", "ignoreList": [0]}