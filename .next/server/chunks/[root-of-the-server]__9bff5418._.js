module.exports = {

"[project]/.next-internal/server/app/api/compress-video/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/app/api/compress-video/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fluent$2d$ffmpeg$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fluent-ffmpeg/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
;
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        const options = JSON.parse(formData.get('options') || '{}');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // 验证文件类型
        if (!file.type.includes('video')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be a video'
            }, {
                status: 400
            });
        }
        // 创建临时目录
        const tempDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'temp');
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].mkdir(tempDir, {
            recursive: true
        });
        // 生成唯一文件名
        const fileId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const inputExt = file.name.split('.').pop() || 'mp4';
        const outputExt = options.outputFormat || 'mp4';
        const inputPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(tempDir, `${fileId}_input.${inputExt}`);
        const outputPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(tempDir, `${fileId}_output.${outputExt}`);
        // 保存上传的文件
        const buffer = Buffer.from(await file.arrayBuffer());
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].writeFile(inputPath, buffer);
        // 获取原始文件大小
        const originalSize = buffer.length;
        try {
            // 执行FFmpeg压缩
            await new Promise((resolve, reject)=>{
                let command = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fluent$2d$ffmpeg$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(inputPath);
                // 视频编解码器设置 - 使用更兼容的编解码器
                let codec = options.codec || 'libx264';
                // 检查并使用可用的编解码器
                try {
                    command = command.videoCodec(codec);
                } catch (codecError) {
                    console.warn(`Codec ${codec} not available, trying alternatives`);
                    // 尝试备用编解码器
                    const fallbackCodecs = [
                        'libx264',
                        'mpeg4',
                        'libxvid',
                        'copy'
                    ];
                    let codecSet = false;
                    for (const fallbackCodec of fallbackCodecs){
                        try {
                            command = command.videoCodec(fallbackCodec);
                            codec = fallbackCodec;
                            codecSet = true;
                            console.log(`Using fallback codec: ${fallbackCodec}`);
                            break;
                        } catch (e) {
                            console.warn(`Codec ${fallbackCodec} also not available`);
                        }
                    }
                    if (!codecSet) {
                        throw new Error('No compatible video codec available');
                    }
                }
                // 音频编解码器 - 使用更兼容的设置
                try {
                    command = command.audioCodec('aac').audioBitrate('128k');
                } catch (audioError) {
                    console.warn('AAC codec not available, trying alternatives');
                    try {
                        command = command.audioCodec('mp3').audioBitrate('128k');
                    } catch (mp3Error) {
                        console.warn('MP3 codec not available, copying audio stream');
                        command = command.audioCodec('copy');
                    }
                }
                // 质量控制 - 使用CRF (Constant Rate Factor)
                let crf = 23 // 默认值，平衡质量和大小
                ;
                if (options.quality) {
                    // quality: 0.1-1.0 -> CRF: 51-18 (数值越小质量越好)
                    crf = Math.round(51 - options.quality * 33);
                    crf = Math.max(18, Math.min(51, crf)); // 限制在18-51范围内
                }
                command = command.outputOptions([
                    '-crf',
                    crf.toString()
                ]);
                // 预设 - 平衡编码速度和压缩效率
                command = command.outputOptions([
                    '-preset',
                    'medium'
                ]);
                // 尺寸控制
                if (options.maxWidth || options.maxHeight) {
                    const width = options.maxWidth || -1;
                    const height = options.maxHeight || -1;
                    command = command.size(`${width}x${height}`);
                }
                // 帧率控制
                if (options.fps) {
                    command = command.fps(options.fps);
                }
                // 比特率控制（可选，CRF优先）
                if (options.bitrate) {
                    command = command.videoBitrate(options.bitrate);
                }
                // 移除元数据
                if (options.removeMetadata) {
                    command = command.outputOptions([
                        '-map_metadata',
                        '-1'
                    ]);
                }
                // 针对不同格式的优化
                if (outputExt === 'mp4') {
                    // MP4优化：快速启动
                    command = command.outputOptions([
                        '-movflags',
                        '+faststart'
                    ]);
                } else if (outputExt === 'webm') {
                    // WebM优化
                    command = command.videoCodec('libvpx-vp9').audioCodec('libopus');
                }
                // 两遍编码（可选，用于更好的质量控制）
                if (options.quality && options.quality > 0.8) {
                    command = command.outputOptions([
                        '-pass',
                        '1',
                        '-f',
                        'null'
                    ]);
                }
                command.output(outputPath).on('start', (commandLine)=>{
                    console.log('FFmpeg command:', commandLine);
                }).on('progress', (progress)=>{
                    console.log('Processing: ' + (progress.percent || 0) + '% done');
                }).on('end', ()=>{
                    console.log('Video compression finished');
                    resolve();
                }).on('error', (err)=>{
                    console.error('FFmpeg error:', err);
                    reject(err);
                }).run();
            });
            // 读取压缩后的文件
            const compressedBuffer = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].readFile(outputPath);
            const compressedSize = compressedBuffer.length;
            const compressionRatio = (originalSize - compressedSize) / originalSize * 100;
            // 清理临时文件
            try {
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(inputPath);
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(outputPath);
            } catch (cleanupError) {
                console.warn('Failed to cleanup temp files:', cleanupError);
            }
            // 返回压缩结果
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](compressedBuffer, {
                status: 200,
                headers: {
                    'Content-Type': `video/${outputExt}`,
                    'Content-Length': compressedSize.toString(),
                    'X-Original-Size': originalSize.toString(),
                    'X-Compressed-Size': compressedSize.toString(),
                    'X-Compression-Ratio': compressionRatio.toFixed(2)
                }
            });
        } catch (ffmpegError) {
            // 清理临时文件
            try {
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(inputPath);
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(outputPath);
            } catch (cleanupError) {
                console.warn('Failed to cleanup temp files after error:', cleanupError);
            }
            console.error('FFmpeg processing failed:', ffmpegError);
            // 提供更详细的错误信息
            let errorMessage = 'Video processing failed';
            let errorDetails = ffmpegError;
            if (ffmpegError instanceof Error) {
                if (ffmpegError.message.includes('codec') && ffmpegError.message.includes('not available')) {
                    errorMessage = 'Video codec not supported';
                    errorDetails = 'The requested video codec is not available on this server. Please try a different format or contact support.';
                } else if (ffmpegError.message.includes('Invalid data found')) {
                    errorMessage = 'Invalid video file';
                    errorDetails = 'The uploaded file appears to be corrupted or in an unsupported format.';
                } else if (ffmpegError.message.includes('Permission denied')) {
                    errorMessage = 'Server permission error';
                    errorDetails = 'Server does not have permission to process the file. Please try again later.';
                } else {
                    errorDetails = ffmpegError.message;
                }
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: errorMessage,
                details: errorDetails,
                timestamp: new Date().toISOString()
            }, {
                status: 500
            });
        }
    } catch (error) {
        console.error('API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            details: error
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9bff5418._.js.map