{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Zap, Menu, X, Home, Info, DollarSign, ImageIcon, FileImage, Video } from 'lucide-react'\n\ninterface HeaderProps {\n  currentPath?: string\n}\n\nexport function Header({ currentPath = '/' }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: Home },\n    {\n      name: 'Compress',\n      href: '/compress',\n      icon: Zap,\n      submenu: [\n        { name: 'Images', href: '/image-compress', icon: ImageIcon, description: 'PNG, JPEG, WebP' },\n        { name: 'GIF Animation', href: '/gif-compress', icon: FileImage, description: 'Animated GIFs' },\n        { name: 'Videos', href: '/video-compress', icon: Video, description: 'MP4, AVI, MOV' },\n      ]\n    },\n    { name: 'About', href: '/about', icon: Info },\n    { name: 'Pricing', href: '/pricing', icon: DollarSign },\n  ]\n\n  const isActive = (href: string) => {\n    if (href === '/' && currentPath === '/') return true\n    if (href !== '/' && currentPath.startsWith(href)) return true\n    // Special handling for compress pages\n    if (href === '/compress' && (\n      currentPath.startsWith('/image-compress') ||\n      currentPath.startsWith('/gif-compress') ||\n      currentPath.startsWith('/video-compress')\n    )) return true\n    return false\n  }\n\n  return (\n    <header className=\"bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n              <Zap className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                {item.submenu ? (\n                  <>\n                    <Link\n                      href={item.href}\n                      className={`\n                        flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                        ${isActive(item.href)\n                          ? 'text-blue-600 bg-blue-50'\n                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                        }\n                      `}\n                    >\n                      <item.icon className=\"w-4 h-4\" />\n                      <span>{item.name}</span>\n                    </Link>\n\n                    {/* Dropdown Menu */}\n                    <div className=\"absolute top-full left-0 pt-2 w-64 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200\">\n                        <div className=\"p-2\">\n                          {item.submenu.map((subItem) => (\n                            <Link\n                              key={subItem.name}\n                              href={subItem.href}\n                              className={`\n                                flex items-center space-x-3 px-3 py-3 rounded-lg text-sm transition-colors\n                                ${currentPath === subItem.href\n                                  ? 'text-blue-600 bg-blue-50'\n                                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'\n                                }\n                              `}\n                            >\n                              <subItem.icon className=\"w-5 h-5 flex-shrink-0\" />\n                              <div>\n                                <div className=\"font-medium\">{subItem.name}</div>\n                                <div className=\"text-xs text-gray-500\">{subItem.description}</div>\n                              </div>\n                            </Link>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className={`\n                      flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                      ${isActive(item.href)\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    <item.icon className=\"w-4 h-4\" />\n                    <span>{item.name}</span>\n                  </Link>\n                )}\n              </div>\n            ))}\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"w-6 h-6\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-2\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  <Link\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`\n                      flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-colors\n                      ${isActive(item.href)\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    <item.icon className=\"w-5 h-5\" />\n                    <span>{item.name}</span>\n                  </Link>\n\n                  {/* Mobile Submenu */}\n                  {item.submenu && (\n                    <div className=\"ml-8 mt-2 space-y-1\">\n                      {item.submenu.map((subItem) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          onClick={() => setIsMobileMenuOpen(false)}\n                          className={`\n                            flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors\n                            ${currentPath === subItem.href\n                              ? 'text-blue-600 bg-blue-50'\n                              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                            }\n                          `}\n                        >\n                          <subItem.icon className=\"w-4 h-4 flex-shrink-0\" />\n                          <div>\n                            <div className=\"font-medium\">{subItem.name}</div>\n                            <div className=\"text-xs text-gray-400\">{subItem.description}</div>\n                          </div>\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n\n// Breadcrumb component for better navigation\ninterface BreadcrumbProps {\n  items: Array<{\n    label: string\n    href?: string\n  }>\n}\n\nexport function Breadcrumb({ items }: BreadcrumbProps) {\n  return (\n    <nav className=\"flex items-center space-x-2 text-sm text-gray-600 mb-6\">\n      {items.map((item, index) => (\n        <div key={index} className=\"flex items-center space-x-2\">\n          {index > 0 && (\n            <span className=\"text-gray-400\">/</span>\n          )}\n          {item.href ? (\n            <Link \n              href={item.href}\n              className=\"hover:text-gray-900 transition-colors\"\n            >\n              {item.label}\n            </Link>\n          ) : (\n            <span className=\"text-gray-900 font-medium\">{item.label}</span>\n          )}\n        </div>\n      ))}\n    </nav>\n  )\n}\n\n// Footer component\nexport function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerSections = [\n    {\n      title: 'Tools',\n      links: [\n        { name: 'Image Compressor', href: '/compress?type=image' },\n        { name: 'Video Compressor', href: '/compress?type=video' },\n        { name: 'GIF Optimizer', href: '/compress?type=gif' },\n        { name: 'Batch Processor', href: '/compress?mode=batch' },\n      ]\n    },\n    {\n      title: 'Support',\n      links: [\n        { name: 'Help Center', href: '/help' },\n        { name: 'Contact Us', href: '/contact' },\n        { name: 'FAQ', href: '/faq' },\n        { name: 'API Documentation', href: '/docs' },\n      ]\n    },\n    {\n      title: 'Company',\n      links: [\n        { name: 'About Us', href: '/about' },\n        { name: 'Privacy Policy', href: '/privacy' },\n        { name: 'Terms of Service', href: '/terms' },\n        { name: 'Blog', href: '/blog' },\n      ]\n    }\n  ]\n\n  return (\n    <footer className=\"bg-gray-50 border-t border-gray-200\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div>\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n            </div>\n            <p className=\"text-gray-600 text-sm leading-relaxed\">\n              The fastest and most secure way to compress your images and videos. \n              All processing happens locally in your browser.\n            </p>\n          </div>\n\n          {/* Footer sections */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"font-semibold text-gray-900 mb-4\">{section.title}</h4>\n              <ul className=\"space-y-2\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <Link \n                      href={link.href}\n                      className=\"text-gray-600 hover:text-gray-900 text-sm transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"border-t border-gray-200 mt-8 pt-8 text-center text-gray-600 text-sm\">\n          <p className=\"mb-2\">&copy; {currentYear} CompressHub. All rights reserved.</p>\n          <p>\n            By using this site you accept the{' '}\n            <Link href=\"/terms\" className=\"text-blue-600 hover:text-blue-800 underline\">\n              terms of use\n            </Link>\n            {' '}and our{' '}\n            <Link href=\"/privacy\" className=\"text-blue-600 hover:text-blue-800 underline\">\n              privacy policy\n            </Link>\n            .\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAUO,SAAS,OAAO,EAAE,cAAc,GAAG,EAAe;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,mMAAA,CAAA,OAAI;QAAC;QACtC;YACE,MAAM;YACN,MAAM;YACN,MAAM,gMAAA,CAAA,MAAG;YACT,SAAS;gBACP;oBAAE,MAAM;oBAAU,MAAM;oBAAmB,MAAM,wMAAA,CAAA,YAAS;oBAAE,aAAa;gBAAkB;gBAC3F;oBAAE,MAAM;oBAAiB,MAAM;oBAAiB,MAAM,gNAAA,CAAA,YAAS;oBAAE,aAAa;gBAAgB;gBAC9F;oBAAE,MAAM;oBAAU,MAAM;oBAAmB,MAAM,oMAAA,CAAA,QAAK;oBAAE,aAAa;gBAAgB;aACtF;QACH;QACA;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC5C;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,kNAAA,CAAA,aAAU;QAAC;KACvD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,OAAO,gBAAgB,KAAK,OAAO;QAChD,IAAI,SAAS,OAAO,YAAY,UAAU,CAAC,OAAO,OAAO;QACzD,sCAAsC;QACtC,IAAI,SAAS,eAAe,CAC1B,YAAY,UAAU,CAAC,sBACvB,YAAY,UAAU,CAAC,oBACvB,YAAY,UAAU,CAAC,kBACzB,GAAG,OAAO;QACV,OAAO;IACT;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oCAAoB,WAAU;8CAC5B,KAAK,OAAO,iBACX;;0DACE,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC;;wBAEV,EAAE,SAAS,KAAK,IAAI,IAChB,6BACA,qDACH;sBACH,CAAC;;kEAED,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,8OAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;0DAIlB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,QAAQ,IAAI;gEAClB,WAAW,CAAC;;gCAEV,EAAE,gBAAgB,QAAQ,IAAI,GAC1B,6BACA,qDACH;8BACH,CAAC;;kFAED,8OAAC,QAAQ,IAAI;wEAAC,WAAU;;;;;;kFACxB,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAe,QAAQ,IAAI;;;;;;0FAC1C,8OAAC;gFAAI,WAAU;0FAAyB,QAAQ,WAAW;;;;;;;;;;;;;+DAbxD,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;qEAsB7B,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;sBAEV,EAAE,SAAS,KAAK,IAAI,IAChB,6BACA,qDACH;oBACH,CAAC;;0DAED,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;mCAxDZ,KAAK,IAAI;;;;;;;;;;sCAgEvB,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAET,iCACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,kCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;;kDACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC;;sBAEV,EAAE,SAAS,KAAK,IAAI,IAChB,6BACA,qDACH;oBACH,CAAC;;0DAED,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;oCAIjB,KAAK,OAAO,kBACX,8OAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC;;4BAEV,EAAE,gBAAgB,QAAQ,IAAI,GAC1B,6BACA,qDACH;0BACH,CAAC;;kEAED,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;kEACxB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAe,QAAQ,IAAI;;;;;;0EAC1C,8OAAC;gEAAI,WAAU;0EAAyB,QAAQ,WAAW;;;;;;;;;;;;;+CAdxD,QAAQ,IAAI;;;;;;;;;;;+BArBjB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDnC;AAUO,SAAS,WAAW,EAAE,KAAK,EAAmB;IACnD,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAAgB,WAAU;;oBACxB,QAAQ,mBACP,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;oBAEjC,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,KAAK,IAAI;wBACf,WAAU;kCAET,KAAK,KAAK;;;;;6CAGb,8OAAC;wBAAK,WAAU;kCAA6B,KAAK,KAAK;;;;;;;eAZjD;;;;;;;;;;AAkBlB;AAGO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAAqB;gBACpD;oBAAE,MAAM;oBAAmB,MAAM;gBAAuB;aACzD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAe,MAAM;gBAAQ;gBACrC;oBAAE,MAAM;oBAAc,MAAM;gBAAW;gBACvC;oBAAE,MAAM;oBAAO,MAAM;gBAAO;gBAC5B;oBAAE,MAAM;oBAAqB,MAAM;gBAAQ;aAC5C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAS;gBACnC;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAoB,MAAM;gBAAS;gBAC3C;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAC/B;QACH;KACD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;wBAOtD,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC,QAAQ,KAAK;;;;;;kDAC/D,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAkB3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAO;gCAAQ;gCAAY;;;;;;;sCACxC,8OAAC;;gCAAE;gCACiC;8CAClC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA8C;;;;;;gCAG3E;gCAAI;gCAAQ;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8C;;;;;;gCAEvE;;;;;;;;;;;;;;;;;;;;;;;;AAOnB", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/BrandCarousel.tsx"], "sourcesContent": ["'use client'\n\nconst brands = [\n  { name: 'Google', logo: '/brands/google.svg' },\n  { name: 'Microsoft', logo: '/brands/microsoft.svg' },\n  { name: 'Apple', logo: '/brands/apple.svg' },\n  { name: 'Amazon', logo: '/brands/amazon.svg' },\n  { name: 'Netflix', logo: '/brands/netflix.svg' },\n  { name: 'Spotify', logo: '/brands/spotify.svg' },\n  { name: 'Adobe', logo: '/brands/adobe.svg' },\n  { name: 'Tesla', logo: '/brands/tesla.svg' },\n  { name: 'Uber', logo: '/brands/uber.svg' },\n  { name: 'Airbnb', logo: '/brands/airbnb.svg' },\n  { name: 'Dropbox', logo: '/brands/dropbox.svg' },\n  { name: 'Slack', logo: '/brands/slack.svg' },\n  { name: 'Samsung', logo: '/brands/samsung.svg' },\n  { name: 'Sony', logo: '/brands/sony.svg' },\n  { name: 'Walmart', logo: '/brands/walmart.svg' },\n  { name: 'Bank of America', logo: '/brands/boa.svg' }\n]\n\nexport function BrandCarousel() {\n  return (\n\n    <section className=\"py-16 bg-gray-50 overflow-hidden\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Trusted by Leading Companies Worldwide\n          </h2>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">\n            Join thousands of businesses that rely on our compression technology to optimize their digital assets and improve performance.\n          </p>\n        </div>\n\n        <div className=\"relative overflow-hidden\">\n          <div\n            className=\"flex animate-marquee space-x-16\"\n            style={{\n              maskImage: 'linear-gradient(to right, transparent, black 10%, black 90%, transparent)',\n              WebkitMaskImage: 'linear-gradient(to right, transparent, black 10%, black 90%, transparent)',\n              width: 'calc(200% + 4rem)' // Ensure enough width for seamless loop\n            }}\n          >\n            {/* First set of brands */}\n            {brands.map((brand, index) => (\n              <div\n                key={`first-${index}`}\n                className=\"flex items-center justify-center min-w-[120px] h-16 grayscale hover:grayscale-0 transition-all duration-300 opacity-60 hover:opacity-100 flex-shrink-0\"\n              >\n                <div className=\"text-lg font-semibold text-gray-400 hover:text-gray-600 transition-colors whitespace-nowrap\">\n                  {brand.name}\n                </div>\n              </div>\n            ))}\n\n            {/* Duplicate set for seamless loop */}\n            {brands.map((brand, index) => (\n              <div\n                key={`second-${index}`}\n                className=\"flex items-center justify-center min-w-[120px] h-16 grayscale hover:grayscale-0 transition-all duration-300 opacity-60 hover:opacity-100 flex-shrink-0\"\n              >\n                <div className=\"text-lg font-semibold text-gray-400 hover:text-gray-600 transition-colors whitespace-nowrap\">\n                  {brand.name}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS;IACb;QAAE,MAAM;QAAU,MAAM;IAAqB;IAC7C;QAAE,MAAM;QAAa,MAAM;IAAwB;IACnD;QAAE,MAAM;QAAS,MAAM;IAAoB;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAqB;IAC7C;QAAE,MAAM;QAAW,MAAM;IAAsB;IAC/C;QAAE,MAAM;QAAW,MAAM;IAAsB;IAC/C;QAAE,MAAM;QAAS,MAAM;IAAoB;IAC3C;QAAE,MAAM;QAAS,MAAM;IAAoB;IAC3C;QAAE,MAAM;QAAQ,MAAM;IAAmB;IACzC;QAAE,MAAM;QAAU,MAAM;IAAqB;IAC7C;QAAE,MAAM;QAAW,MAAM;IAAsB;IAC/C;QAAE,MAAM;QAAS,MAAM;IAAoB;IAC3C;QAAE,MAAM;QAAW,MAAM;IAAsB;IAC/C;QAAE,MAAM;QAAQ,MAAM;IAAmB;IACzC;QAAE,MAAM;QAAW,MAAM;IAAsB;IAC/C;QAAE,MAAM;QAAmB,MAAM;IAAkB;CACpD;AAEM,SAAS;IACd,qBAEE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;8BAKjD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,WAAW;4BACX,iBAAiB;4BACjB,OAAO,oBAAoB,wCAAwC;wBACrE;;4BAGC,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI;;;;;;mCAJR,CAAC,MAAM,EAAE,OAAO;;;;;4BAUxB,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI;;;;;;mCAJR,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;AAatC", "debugId": null}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/ImageComparison.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\n\ninterface ImageComparisonProps {\n  beforeImage: string\n  afterImage: string\n  beforeLabel?: string\n  afterLabel?: string\n  beforeSize?: string\n  afterSize?: string\n}\n\nexport function ImageComparison({\n  beforeImage,\n  afterImage,\n  beforeLabel = 'ORIGINAL',\n  afterLabel = 'COMPRESSED',\n  beforeSize = '2.1 MB',\n  afterSize = '312 KB'\n}: ImageComparisonProps) {\n  const [sliderPosition, setSliderPosition] = useState(50)\n  const [isDragging, setIsDragging] = useState(false)\n  const containerRef = useRef<HTMLDivElement>(null)\n\n  const handleMouseDown = () => {\n    setIsDragging(true)\n  }\n\n  const handleMouseUp = () => {\n    setIsDragging(false)\n  }\n\n  const handleMouseMove = (e: MouseEvent) => {\n    if (!isDragging || !containerRef.current) return\n\n    const rect = containerRef.current.getBoundingClientRect()\n    const x = e.clientX - rect.left\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))\n    setSliderPosition(percentage)\n  }\n\n  const handleTouchMove = (e: TouchEvent) => {\n    if (!isDragging || !containerRef.current) return\n\n    const rect = containerRef.current.getBoundingClientRect()\n    const x = e.touches[0].clientX - rect.left\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))\n    setSliderPosition(percentage)\n  }\n\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove)\n      document.addEventListener('mouseup', handleMouseUp)\n      document.addEventListener('touchmove', handleTouchMove)\n      document.addEventListener('touchend', handleMouseUp)\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove)\n      document.removeEventListener('mouseup', handleMouseUp)\n      document.removeEventListener('touchmove', handleTouchMove)\n      document.removeEventListener('touchend', handleMouseUp)\n    }\n  }, [isDragging])\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h3 className=\"text-sm font-semibold text-blue-600 uppercase tracking-wide mb-2\">\n            LOSSLESS COMPRESSION TECHNOLOGY\n          </h3>\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">\n            Can You Tell the Difference?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Move the slider to compare the compressed image with the original. \n            <br />\n            The file size is reduced by more than 85% with no visible quality loss!\n          </p>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto select-none\">\n          <div\n            ref={containerRef}\n            className=\"relative overflow-hidden rounded-2xl shadow-2xl bg-white\"\n            style={{ aspectRatio: '16/10' }}\n          >\n            {/* After Image (Right side - compressed) */}\n            <img\n              src={afterImage}\n              alt=\"Compressed\"\n              className=\"absolute inset-0 w-full h-full object-cover select-none\"\n              style={{\n                clipPath: `inset(0 0 0 ${sliderPosition}%)`\n              }}\n            />\n            \n            {/* Before Image (Left side - original) */}\n            <img\n              src={beforeImage}\n              alt=\"Original\"\n              className=\"absolute inset-0 w-full h-full object-cover\"\n              style={{\n                clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`\n              }}\n            />\n\n            {/* Slider */}\n            <div\n              className=\"absolute top-0 bottom-0 w-1 bg-white shadow-2xl cursor-ew-resize flex items-center justify-center z-10\"\n              style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}\n              onMouseDown={handleMouseDown}\n              onTouchStart={handleMouseDown}\n            >\n              {/* Vertical line */}\n              <div className=\"absolute top-0 bottom-0 w-1 bg-white shadow-lg\"></div>\n\n              {/* Handle */}\n              <div className=\"w-10 h-10 bg-white rounded-full shadow-2xl flex items-center justify-center border-2 border-gray-200 hover:border-blue-400 transition-colors\">\n                {/* Left arrow */}\n                <div className=\"absolute left-1 w-0 h-0 border-t-2 border-b-2 border-r-4 border-transparent border-r-gray-400\"></div>\n                {/* Right arrow */}\n                <div className=\"absolute right-1 w-0 h-0 border-t-2 border-b-2 border-l-4 border-transparent border-l-gray-400\"></div>\n              </div>\n            </div>\n\n            {/* Labels */}\n            <div className=\"absolute bottom-6 left-6 bg-black/80 text-white px-4 py-3 rounded-lg backdrop-blur-sm\">\n              <div className=\"text-sm font-bold tracking-wide\">{beforeLabel}</div>\n              <div className=\"text-xs opacity-90 mt-1\">{beforeSize}</div>\n            </div>\n\n            <div className=\"absolute top-6 right-6 bg-blue-600/90 text-white px-4 py-3 rounded-lg backdrop-blur-sm\">\n              <div className=\"text-sm font-bold tracking-wide\">{afterLabel}</div>\n              <div className=\"text-xs opacity-90 mt-1\">{afterSize}</div>\n            </div>\n          </div>\n\n          {/* Curved arrows and annotations */}\n          <div className=\"relative mt-12\">\n            <div className=\"absolute left-1/4 -top-8 transform -translate-x-1/2\">\n              <div className=\"flex flex-col items-center\">\n                <svg width=\"60\" height=\"40\" viewBox=\"0 0 60 40\" className=\"text-blue-500 mb-2\">\n                  <path\n                    d=\"M10 35 Q30 5 50 35\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    fill=\"none\"\n                    markerEnd=\"url(#arrowhead-blue)\"\n                  />\n                  <defs>\n                    <marker id=\"arrowhead-blue\" markerWidth=\"10\" markerHeight=\"7\"\n                     refX=\"9\" refY=\"3.5\" orient=\"auto\">\n                      <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"currentColor\" />\n                    </marker>\n                  </defs>\n                </svg>\n                <div className=\"bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg\">\n                  Darker places stay intact\n                </div>\n              </div>\n            </div>\n\n            <div className=\"absolute right-1/4 -top-8 transform translate-x-1/2\">\n              <div className=\"flex flex-col items-center\">\n                <svg width=\"60\" height=\"40\" viewBox=\"0 0 60 40\" className=\"text-green-500 mb-2\">\n                  <path\n                    d=\"M50 35 Q30 5 10 35\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    fill=\"none\"\n                    markerEnd=\"url(#arrowhead-green)\"\n                  />\n                  <defs>\n                    <marker id=\"arrowhead-green\" markerWidth=\"10\" markerHeight=\"7\"\n                     refX=\"9\" refY=\"3.5\" orient=\"auto\">\n                      <polygon points=\"0 0, 10 3.5, 0 7\" fill=\"currentColor\" />\n                    </marker>\n                  </defs>\n                </svg>\n                <div className=\"bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg\">\n                  Tiniest details are still there\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\n// Default export with sample images\nexport default function DefaultImageComparison() {\n  return (\n    <ImageComparison\n      beforeImage=\"/images/sample-original.jpg\"\n      afterImage=\"/images/sample-compressed.jpg\"\n      beforeLabel=\"ORIGINAL\"\n      afterLabel=\"COMPRESSED\"\n      beforeSize=\"2.1 MB\"\n      afterSize=\"312 KB\"\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaO,SAAS,gBAAgB,EAC9B,WAAW,EACX,UAAU,EACV,cAAc,UAAU,EACxB,aAAa,YAAY,EACzB,aAAa,QAAQ,EACrB,YAAY,QAAQ,EACC;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,cAAc,CAAC,aAAa,OAAO,EAAE;QAE1C,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;QACvD,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;QAC/B,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,IAAI,KAAK,KAAK,GAAI;QAChE,kBAAkB;IACpB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,cAAc,CAAC,aAAa,OAAO,EAAE;QAE1C,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;QACvD,MAAM,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,IAAI;QAC1C,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,IAAI,KAAK,KAAK,GAAI;QAChE,kBAAkB;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,YAAY;QACxC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,YAAY;QAC3C;IACF,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;;gCAA0C;8CAErD,8OAAC;;;;;gCAAK;;;;;;;;;;;;;8BAKV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,aAAa;4BAAQ;;8CAG9B,8OAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;oCACV,OAAO;wCACL,UAAU,CAAC,YAAY,EAAE,eAAe,EAAE,CAAC;oCAC7C;;;;;;8CAIF,8OAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;oCACV,OAAO;wCACL,UAAU,CAAC,QAAQ,EAAE,MAAM,eAAe,MAAM,CAAC;oCACnD;;;;;;8CAIF,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,MAAM,GAAG,eAAe,CAAC,CAAC;wCAAE,WAAW;oCAAmB;oCACnE,aAAa;oCACb,cAAc;;sDAGd,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,WAAU;;kEACxD,8OAAC;wDACC,GAAE;wDACF,QAAO;wDACP,aAAY;wDACZ,MAAK;wDACL,WAAU;;;;;;kEAEZ,8OAAC;kEACC,cAAA,8OAAC;4DAAO,IAAG;4DAAiB,aAAY;4DAAK,cAAa;4DACzD,MAAK;4DAAI,MAAK;4DAAM,QAAO;sEAC1B,cAAA,8OAAC;gEAAQ,QAAO;gEAAmB,MAAK;;;;;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAI,WAAU;0DAA4E;;;;;;;;;;;;;;;;;8CAM/F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,WAAU;;kEACxD,8OAAC;wDACC,GAAE;wDACF,QAAO;wDACP,aAAY;wDACZ,MAAK;wDACL,WAAU;;;;;;kEAEZ,8OAAC;kEACC,cAAA,8OAAC;4DAAO,IAAG;4DAAkB,aAAY;4DAAK,cAAa;4DAC1D,MAAK;4DAAI,MAAK;4DAAM,QAAO;sEAC1B,cAAA,8OAAC;gEAAQ,QAAO;gEAAmB,MAAK;;;;;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAI,WAAU;0DAA6E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5G;AAGe,SAAS;IACtB,qBACE,8OAAC;QACC,aAAY;QACZ,YAAW;QACX,aAAY;QACZ,YAAW;QACX,YAAW;QACX,WAAU;;;;;;AAGhB", "debugId": null}}]}