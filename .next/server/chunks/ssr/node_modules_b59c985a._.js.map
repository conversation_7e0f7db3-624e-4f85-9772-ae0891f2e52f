{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/route-modules/pages/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/pages/module.js')\n} else {\n  if (process.env.NODE_ENV === 'development') {\n    if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/pages-turbo.runtime.dev.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/pages.runtime.dev.js')\n    }\n  } else {\n    if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/pages-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,QAAQ,KAAK,WAAe;QAC1C,IAAIN,QAAQC,GAAG,CAACM,SAAS,eAAE;YACzBJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO;;IAGT,OAAO;;AAOT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/route-kind.ts"], "sourcesContent": ["export const enum RouteKind {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  IMAGE = 'IMAGE',\n}\n"], "names": ["RouteKind"], "mappings": ";;;AAAO,IAAWA,YAAAA,WAAAA,GAAAA,SAAAA,SAAAA;IAChB;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;IAED;;GAEC,GAAA,SAAA,CAAA,YAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,WAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,YAAA,GAAA;IAGD;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;WAtBeA;MAwBjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/lib/trace/constants.ts"], "sourcesContent": ["/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n"], "names": ["BaseServerSpan", "LoadComponentsSpan", "NextServerSpan", "NextNodeServerSpan", "StartServerSpan", "RenderSpan", "AppRenderSpan", "RouterSpan", "NodeSpan", "AppRouteRouteHandlersSpan", "ResolveMetadataSpan", "MiddlewareSpan", "NextVanillaSpanAllowlist", "LogSpanAllowList"], "mappings": "AAAA;;;;;EAKE,GAEF,4CAA4C;AAC5C,4BAA4B;;;;;;;;;;;;;;;;AAE5B,IAAKA,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;;;;;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAeL,IAAKC,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;WAAAA;EAAAA,sBAAAA,CAAAA;AAKL,IAAKC,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAOL,IAAKC,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BH,wDAAwD;;;;;WA5BrDA;EAAAA,sBAAAA,CAAAA;AAmCL,IAAKC,kBAAAA,WAAAA,GAAAA,SAAAA,eAAAA;;WAAAA;EAAAA,mBAAAA,CAAAA;AAIL,IAAKC,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;;;;;WAAAA;EAAAA,cAAAA,CAAAA;AAQL,IAAKC,gBAAAA,WAAAA,GAAAA,SAAAA,aAAAA;;;;;WAAAA;EAAAA,iBAAAA,CAAAA;AAOL,IAAKC,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;WAAAA;EAAAA,cAAAA,CAAAA;AAIL,IAAKC,WAAAA,WAAAA,GAAAA,SAAAA,QAAAA;;WAAAA;EAAAA,YAAAA,CAAAA;AAIL,IAAKC,4BAAAA,WAAAA,GAAAA,SAAAA,yBAAAA;;WAAAA;EAAAA,6BAAAA,CAAAA;AAIL,IAAKC,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;WAAAA;EAAAA,uBAAAA,CAAAA;AAKL,IAAKC,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;WAAAA;EAAAA,kBAAAA,CAAAA;AAmBE,MAAMC,2BAA2B;;;;;;;;;;;;;;;;;CAiBvC,CAAA;AAIM,MAAMC,mBAAmB;;;;CAI/B,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GACD;;;AAAO,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/lib/trace/tracer.ts"], "sourcesContent": ["import type { FetchEventResult } from '../../web/types'\nimport type { TextMapSetter } from '@opentelemetry/api'\nimport type { SpanTypes } from './constants'\nimport { LogSpanAllowList, NextVanillaSpanAllowlist } from './constants'\n\nimport type {\n  ContextAPI,\n  Span,\n  SpanOptions,\n  Tracer,\n  AttributeValue,\n  TextMapGetter,\n} from 'next/dist/compiled/@opentelemetry/api'\nimport { isThenable } from '../../../shared/lib/is-thenable'\n\nlet api: typeof import('next/dist/compiled/@opentelemetry/api')\n\n// we want to allow users to use their own version of @opentelemetry/api if they\n// want to, so we try to require it first, and if it fails we fall back to the\n// version that is bundled with Next.js\n// this is because @opentelemetry/api has to be synced with the version of\n// @opentelemetry/tracing that is used, and we don't want to force users to use\n// the version that is bundled with Next.js.\n// the API is ~stable, so this should be fine\nif (process.env.NEXT_RUNTIME === 'edge') {\n  api = require('@opentelemetry/api') as typeof import('@opentelemetry/api')\n} else {\n  try {\n    api = require('@opentelemetry/api') as typeof import('@opentelemetry/api')\n  } catch (err) {\n    api =\n      require('next/dist/compiled/@opentelemetry/api') as typeof import('next/dist/compiled/@opentelemetry/api')\n  }\n}\n\nconst { context, propagation, trace, SpanStatusCode, SpanKind, ROOT_CONTEXT } =\n  api\n\nexport class BubbledError extends Error {\n  constructor(\n    public readonly bubble?: boolean,\n    public readonly result?: FetchEventResult\n  ) {\n    super()\n  }\n}\n\nexport function isBubbledError(error: unknown): error is BubbledError {\n  if (typeof error !== 'object' || error === null) return false\n  return error instanceof BubbledError\n}\n\nconst closeSpanWithError = (span: Span, error?: Error) => {\n  if (isBubbledError(error) && error.bubble) {\n    span.setAttribute('next.bubble', true)\n  } else {\n    if (error) {\n      span.recordException(error)\n    }\n    span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message })\n  }\n  span.end()\n}\n\ntype TracerSpanOptions = Omit<SpanOptions, 'attributes'> & {\n  parentSpan?: Span\n  spanName?: string\n  attributes?: Partial<Record<AttributeNames, AttributeValue | undefined>>\n  hideSpan?: boolean\n}\n\ninterface NextTracer {\n  getContext(): ContextAPI\n\n  /**\n   * Instruments a function by automatically creating a span activated on its\n   * scope.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its second parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   *\n   */\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n\n  /**\n   * Wrap a function to automatically create a span activated on its\n   * scope when it's called.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its last parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   */\n  wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n\n  /**\n   * Starts and returns a new Span representing a logical unit of work.\n   *\n   * This method do NOT modify the current Context by default. In result, any inner span will not\n   * automatically set its parent context to the span created by this method unless manually activate\n   * context via `tracer.getContext().with`. `trace`, or `wrap` is generally recommended as it gracefully\n   * handles context activation. (ref: https://github.com/open-telemetry/opentelemetry-js/issues/1923)\n   */\n  startSpan(type: SpanTypes): Span\n  startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n\n  /**\n   * Returns currently activated span if current context is in the scope of the span.\n   * Returns undefined otherwise.\n   */\n  getActiveScopeSpan(): Span | undefined\n\n  /**\n   * Returns trace propagation data for the currently active context. The format is equal to data provided\n   * through the OpenTelemetry propagator API.\n   */\n  getTracePropagationData(): ClientTraceDataEntry[]\n}\n\ntype NextAttributeNames =\n  | 'next.route'\n  | 'next.page'\n  | 'next.rsc'\n  | 'next.segment'\n  | 'next.span_name'\n  | 'next.span_type'\n  | 'next.clientComponentLoadCount'\ntype OTELAttributeNames = `http.${string}` | `net.${string}`\ntype AttributeNames = NextAttributeNames | OTELAttributeNames\n\n/** we use this map to propagate attributes from nested spans to the top span */\nconst rootSpanAttributesStore = new Map<\n  number,\n  Map<AttributeNames, AttributeValue | undefined>\n>()\nconst rootSpanIdKey = api.createContextKey('next.rootSpanId')\nlet lastSpanId = 0\nconst getSpanId = () => lastSpanId++\n\nexport interface ClientTraceDataEntry {\n  key: string\n  value: string\n}\n\nconst clientTraceDataSetter: TextMapSetter<ClientTraceDataEntry[]> = {\n  set(carrier, key, value) {\n    carrier.push({\n      key,\n      value,\n    })\n  },\n}\n\nclass NextTracerImpl implements NextTracer {\n  /**\n   * Returns an instance to the trace with configured name.\n   * Since wrap / trace can be defined in any place prior to actual trace subscriber initialization,\n   * This should be lazily evaluated.\n   */\n  private getTracerInstance(): Tracer {\n    return trace.getTracer('next.js', '0.0.1')\n  }\n\n  public getContext(): ContextAPI {\n    return context\n  }\n\n  public getTracePropagationData(): ClientTraceDataEntry[] {\n    const activeContext = context.active()\n    const entries: ClientTraceDataEntry[] = []\n    propagation.inject(activeContext, entries, clientTraceDataSetter)\n    return entries\n  }\n\n  public getActiveScopeSpan(): Span | undefined {\n    return trace.getSpan(context?.active())\n  }\n\n  public withPropagatedContext<T, C>(\n    carrier: C,\n    fn: () => T,\n    getter?: TextMapGetter<C>\n  ): T {\n    const activeContext = context.active()\n    if (trace.getSpanContext(activeContext)) {\n      // Active span is already set, too late to propagate.\n      return fn()\n    }\n    const remoteContext = propagation.extract(activeContext, carrier, getter)\n    return context.with(remoteContext, fn)\n  }\n\n  // Trace, wrap implementation is inspired by datadog trace implementation\n  // (https://datadoghq.dev/dd-trace-js/interfaces/tracer.html#trace).\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(...args: Array<any>) {\n    const [type, fnOrOptions, fnOrEmpty] = args\n\n    // coerce options form overload\n    const {\n      fn,\n      options,\n    }: {\n      fn: (span?: Span, done?: (error?: Error) => any) => T | Promise<T>\n      options: TracerSpanOptions\n    } =\n      typeof fnOrOptions === 'function'\n        ? {\n            fn: fnOrOptions,\n            options: {},\n          }\n        : {\n            fn: fnOrEmpty,\n            options: { ...fnOrOptions },\n          }\n\n    const spanName = options.spanName ?? type\n\n    if (\n      (!NextVanillaSpanAllowlist.includes(type) &&\n        process.env.NEXT_OTEL_VERBOSE !== '1') ||\n      options.hideSpan\n    ) {\n      return fn()\n    }\n\n    // Trying to get active scoped span to assign parent. If option specifies parent span manually, will try to use it.\n    let spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    let isRootSpan = false\n\n    if (!spanContext) {\n      spanContext = context?.active() ?? ROOT_CONTEXT\n      isRootSpan = true\n    } else if (trace.getSpanContext(spanContext)?.isRemote) {\n      isRootSpan = true\n    }\n\n    const spanId = getSpanId()\n\n    options.attributes = {\n      'next.span_name': spanName,\n      'next.span_type': type,\n      ...options.attributes,\n    }\n\n    return context.with(spanContext.setValue(rootSpanIdKey, spanId), () =>\n      this.getTracerInstance().startActiveSpan(\n        spanName,\n        options,\n        (span: Span) => {\n          const startTime =\n            'performance' in globalThis && 'measure' in performance\n              ? globalThis.performance.now()\n              : undefined\n\n          const onCleanup = () => {\n            rootSpanAttributesStore.delete(spanId)\n            if (\n              startTime &&\n              process.env.NEXT_OTEL_PERFORMANCE_PREFIX &&\n              LogSpanAllowList.includes(type || ('' as any))\n            ) {\n              performance.measure(\n                `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(\n                  type.split('.').pop() || ''\n                ).replace(\n                  /[A-Z]/g,\n                  (match: string) => '-' + match.toLowerCase()\n                )}`,\n                {\n                  start: startTime,\n                  end: performance.now(),\n                }\n              )\n            }\n          }\n\n          if (isRootSpan) {\n            rootSpanAttributesStore.set(\n              spanId,\n              new Map(\n                Object.entries(options.attributes ?? {}) as [\n                  AttributeNames,\n                  AttributeValue | undefined,\n                ][]\n              )\n            )\n          }\n          try {\n            if (fn.length > 1) {\n              return fn(span, (err) => closeSpanWithError(span, err))\n            }\n\n            const result = fn(span)\n            if (isThenable(result)) {\n              // If there's error make sure it throws\n              return result\n                .then((res) => {\n                  span.end()\n                  // Need to pass down the promise result,\n                  // it could be react stream response with error { error, stream }\n                  return res\n                })\n                .catch((err) => {\n                  closeSpanWithError(span, err)\n                  throw err\n                })\n                .finally(onCleanup)\n            } else {\n              span.end()\n              onCleanup()\n            }\n\n            return result\n          } catch (err: any) {\n            closeSpanWithError(span, err)\n            onCleanup()\n            throw err\n          }\n        }\n      )\n    )\n  }\n\n  public wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap(...args: Array<any>) {\n    const tracer = this\n    const [name, options, fn] =\n      args.length === 3 ? args : [args[0], {}, args[1]]\n\n    if (\n      !NextVanillaSpanAllowlist.includes(name) &&\n      process.env.NEXT_OTEL_VERBOSE !== '1'\n    ) {\n      return fn\n    }\n\n    return function (this: any) {\n      let optionsObj = options\n      if (typeof optionsObj === 'function' && typeof fn === 'function') {\n        optionsObj = optionsObj.apply(this, arguments)\n      }\n\n      const lastArgId = arguments.length - 1\n      const cb = arguments[lastArgId]\n\n      if (typeof cb === 'function') {\n        const scopeBoundCb = tracer.getContext().bind(context.active(), cb)\n        return tracer.trace(name, optionsObj, (_span, done) => {\n          arguments[lastArgId] = function (err: any) {\n            done?.(err)\n            return scopeBoundCb.apply(this, arguments)\n          }\n\n          return fn.apply(this, arguments)\n        })\n      } else {\n        return tracer.trace(name, optionsObj, () => fn.apply(this, arguments))\n      }\n    }\n  }\n\n  public startSpan(type: SpanTypes): Span\n  public startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n  public startSpan(...args: Array<any>): Span {\n    const [type, options]: [string, TracerSpanOptions | undefined] = args as any\n\n    const spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    return this.getTracerInstance().startSpan(type, options, spanContext)\n  }\n\n  private getSpanContext(parentSpan?: Span) {\n    const spanContext = parentSpan\n      ? trace.setSpan(context.active(), parentSpan)\n      : undefined\n\n    return spanContext\n  }\n\n  public getRootSpanAttributes() {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    return rootSpanAttributesStore.get(spanId)\n  }\n\n  public setRootSpanAttribute(key: AttributeNames, value: AttributeValue) {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    const attributes = rootSpanAttributesStore.get(spanId)\n    if (attributes) {\n      attributes.set(key, value)\n    }\n  }\n}\n\nconst getTracer = (() => {\n  const tracer = new NextTracerImpl()\n\n  return () => tracer\n})()\n\nexport { getTracer, SpanStatusCode, SpanKind }\nexport type { NextTracer, Span, SpanOptions, ContextAPI, TracerSpanOptions }\n"], "names": ["LogSpanAllowList", "NextVanillaSpanAllowlist", "isThenable", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "SpanStatusCode", "SpanKind", "ROOT_CONTEXT", "BubbledError", "Error", "constructor", "bubble", "result", "isBubbledError", "error", "closeSpanWithError", "span", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "clientTraceDataSetter", "set", "carrier", "key", "value", "push", "NextTracerImpl", "getTracerInstance", "getTracer", "getContext", "getTracePropagationData", "activeContext", "active", "entries", "inject", "getActiveScopeSpan", "getSpan", "withPropagatedContext", "fn", "getter", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "spanName", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "startTime", "globalThis", "performance", "now", "undefined", "onCleanup", "delete", "NEXT_OTEL_PERFORMANCE_PREFIX", "measure", "split", "pop", "replace", "match", "toLowerCase", "start", "Object", "length", "then", "res", "catch", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "getRootSpanAttributes", "getValue", "get", "setRootSpanAttribute"], "mappings": ";;;;;;;AAGA,SAASA,gBAAgB,EAAEC,wBAAwB,QAAQ,cAAa;AAUxE,SAASC,UAAU,QAAQ,kCAAiC;;;AAE5D,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAI;QACFH,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MACEI,QAAQ;IACZ;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAC3EX;AAEK,MAAMY,qBAAqBC;IAChCC,YACkBC,MAAgB,EAChBC,MAAyB,CACzC;QACA,KAAK,IAAA,IAAA,CAHWD,MAAAA,GAAAA,QAAAA,IAAAA,CACAC,MAAAA,GAAAA;IAGlB;AACF;AAEO,SAASC,eAAeC,KAAc;IAC3C,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM,OAAO;IACxD,OAAOA,iBAAiBN;AAC1B;AAEA,MAAMO,qBAAqB,CAACC,MAAYF;IACtC,IAAID,eAAeC,UAAUA,MAAMH,MAAM,EAAE;QACzCK,KAAKC,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIH,OAAO;YACTE,KAAKE,eAAe,CAACJ;QACvB;QACAE,KAAKG,SAAS,CAAC;YAAEC,MAAMf,eAAegB,KAAK;YAAEC,OAAO,EAAER,SAAAA,OAAAA,KAAAA,IAAAA,MAAOQ,OAAO;QAAC;IACvE;IACAN,KAAKO,GAAG;AACV;AA2GA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB9B,IAAI+B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAOxB,MAAME,wBAA+D;IACnEC,KAAIC,OAAO,EAAEC,GAAG,EAAEC,KAAK;QACrBF,QAAQG,IAAI,CAAC;YACXF;YACAC;QACF;IACF;AACF;AAEA,MAAME;IACJ;;;;GAIC,GACOC,oBAA4B;QAClC,OAAOjC,MAAMkC,SAAS,CAAC,WAAW;IACpC;IAEOC,aAAyB;QAC9B,OAAOrC;IACT;IAEOsC,0BAAkD;QACvD,MAAMC,gBAAgBvC,QAAQwC,MAAM;QACpC,MAAMC,UAAkC,EAAE;QAC1CxC,YAAYyC,MAAM,CAACH,eAAeE,SAASb;QAC3C,OAAOa;IACT;IAEOE,qBAAuC;QAC5C,OAAOzC,MAAM0C,OAAO,CAAC5C,WAAAA,OAAAA,KAAAA,IAAAA,QAASwC,MAAM;IACtC;IAEOK,sBACLf,OAAU,EACVgB,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMR,gBAAgBvC,QAAQwC,MAAM;QACpC,IAAItC,MAAM8C,cAAc,CAACT,gBAAgB;YACvC,qDAAqD;YACrD,OAAOO;QACT;QACA,MAAMG,gBAAgBhD,YAAYiD,OAAO,CAACX,eAAeT,SAASiB;QAClE,OAAO/C,QAAQmD,IAAI,CAACF,eAAeH;IACrC;IAsBO5C,MAAS,GAAGkD,IAAgB,EAAE;YAwCxBlD;QAvCX,MAAM,CAACmD,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJN,EAAE,EACFU,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACER,IAAIQ;YACJE,SAAS,CAAC;QACZ,IACA;YACEV,IAAIS;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,MAAMG,WAAWD,QAAQC,QAAQ,IAAIJ;QAErC,IACG,qKAAC7D,2BAAAA,CAAyBkE,QAAQ,CAACL,SAClC1D,QAAQC,GAAG,CAAC+D,iBAAiB,KAAK,OACpCH,QAAQI,QAAQ,EAChB;YACA,OAAOd;QACT;QAEA,mHAAmH;QACnH,IAAIe,cAAc,IAAI,CAACb,cAAc,CACnCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASM,UAAU,KAAI,IAAI,CAACnB,kBAAkB;QAEhD,IAAIoB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAc7D,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASwC,MAAM,EAAA,KAAMnC;YACnC0D,aAAa;QACf,OAAO,IAAA,CAAI7D,wBAAAA,MAAM8C,cAAc,CAACa,YAAAA,KAAAA,OAAAA,KAAAA,IAArB3D,sBAAmC8D,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAStC;QAEf6B,QAAQU,UAAU,GAAG;YACnB,kBAAkBT;YAClB,kBAAkBJ;YAClB,GAAGG,QAAQU,UAAU;QACvB;QAEA,OAAOlE,QAAQmD,IAAI,CAACU,YAAYM,QAAQ,CAAC3C,eAAeyC,SAAS,IAC/D,IAAI,CAAC9B,iBAAiB,GAAGiC,eAAe,CACtCX,UACAD,SACA,CAAC1C;gBACC,MAAMuD,YACJ,iBAAiBC,cAAc,aAAaC,cACxCD,WAAWC,WAAW,CAACC,GAAG,KAC1BC;gBAEN,MAAMC,YAAY;oBAChBpD,wBAAwBqD,MAAM,CAACV;oBAC/B,IACEI,aACA1E,QAAQC,GAAG,CAACgF,4BAA4B,IACxCrF,uLAAAA,CAAiBmE,QAAQ,CAACL,QAAS,KACnC;wBACAkB,YAAYM,OAAO,CACjB,GAAGlF,QAAQC,GAAG,CAACgF,4BAA4B,CAAC,MAAM,EAChDvB,CAAAA,KAAKyB,KAAK,CAAC,KAAKC,GAAG,MAAM,EAAC,EAC1BC,OAAO,CACP,UACA,CAACC,QAAkB,MAAMA,MAAMC,WAAW,KACzC,EACH;4BACEC,OAAOd;4BACPhD,KAAKkD,YAAYC,GAAG;wBACtB;oBAEJ;gBACF;gBAEA,IAAIT,YAAY;oBACdzC,wBAAwBO,GAAG,CACzBoC,QACA,IAAI1C,IACF6D,OAAO3C,OAAO,CAACe,QAAQU,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIpB,GAAGuC,MAAM,GAAG,GAAG;wBACjB,OAAOvC,GAAGhC,MAAM,CAACf,MAAQc,mBAAmBC,MAAMf;oBACpD;oBAEA,MAAMW,SAASoC,GAAGhC;oBAClB,wKAAIrB,aAAAA,EAAWiB,SAAS;wBACtB,uCAAuC;wBACvC,OAAOA,OACJ4E,IAAI,CAAC,CAACC;4BACLzE,KAAKO,GAAG;4BACR,wCAAwC;4BACxC,iEAAiE;4BACjE,OAAOkE;wBACT,GACCC,KAAK,CAAC,CAACzF;4BACNc,mBAAmBC,MAAMf;4BACzB,MAAMA;wBACR,GACC0F,OAAO,CAACf;oBACb,OAAO;wBACL5D,KAAKO,GAAG;wBACRqD;oBACF;oBAEA,OAAOhE;gBACT,EAAE,OAAOX,KAAU;oBACjBc,mBAAmBC,MAAMf;oBACzB2E;oBACA,MAAM3E;gBACR;YACF;IAGN;IAaO2F,KAAK,GAAGtC,IAAgB,EAAE;QAC/B,MAAMuC,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMpC,SAASV,GAAG,GACvBM,KAAKiC,MAAM,KAAK,IAAIjC,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,qKAAC5D,2BAAAA,CAAyBkE,QAAQ,CAACkC,SACnCjG,QAAQC,GAAG,CAAC+D,iBAAiB,KAAK,KAClC;YACA,OAAOb;QACT;QAEA,OAAO;YACL,IAAI+C,aAAarC;YACjB,IAAI,OAAOqC,eAAe,cAAc,OAAO/C,OAAO,YAAY;gBAChE+C,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUV,MAAM,GAAG;YACrC,MAAMY,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOtD,UAAU,GAAG8D,IAAI,CAACnG,QAAQwC,MAAM,IAAIyD;gBAChE,OAAON,OAAOzF,KAAK,CAAC0F,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUjG,GAAQ;wBACvCsG,QAAAA,OAAAA,KAAAA,IAAAA,KAAOtG;wBACP,OAAOmG,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOjD,GAAGgD,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOzF,KAAK,CAAC0F,MAAMC,YAAY,IAAM/C,GAAGgD,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGlD,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMS,cAAc,IAAI,CAACb,cAAc,CACrCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASM,UAAU,KAAI,IAAI,CAACnB,kBAAkB;QAEhD,OAAO,IAAI,CAACR,iBAAiB,GAAGmE,SAAS,CAACjD,MAAMG,SAASK;IAC3D;IAEQb,eAAec,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChB5D,MAAMqG,OAAO,CAACvG,QAAQwC,MAAM,IAAIsB,cAChCW;QAEJ,OAAOZ;IACT;IAEO2C,wBAAwB;QAC7B,MAAMvC,SAASjE,QAAQwC,MAAM,GAAGiE,QAAQ,CAACjF;QACzC,OAAOF,wBAAwBoF,GAAG,CAACzC;IACrC;IAEO0C,qBAAqB5E,GAAmB,EAAEC,KAAqB,EAAE;QACtE,MAAMiC,SAASjE,QAAQwC,MAAM,GAAGiE,QAAQ,CAACjF;QACzC,MAAM0C,aAAa5C,wBAAwBoF,GAAG,CAACzC;QAC/C,IAAIC,YAAY;YACdA,WAAWrC,GAAG,CAACE,KAAKC;QACtB;IACF;AACF;AAEA,MAAMI,YAAa,CAAA;IACjB,MAAMuD,SAAS,IAAIzD;IAEnB,OAAO,IAAMyD;AACf,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["searchParamsToUrlQuery", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "urlQueryToSearchParams", "URLSearchParams", "Object", "item", "append", "set", "assign", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;AAEO,SAASA,uBACdC,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASG,uBAAuBZ,KAAqB;IAC1D,MAAMD,eAAe,IAAIc;IACzB,KAAK,MAAM,CAACZ,KAAKC,MAAM,IAAIY,OAAOX,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMa,QAAQb,MAAO;gBACxBH,aAAaiB,MAAM,CAACf,KAAKO,uBAAuBO;YAClD;QACF,OAAO;YACLhB,aAAakB,GAAG,CAAChB,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASmB,OACdC,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMrB,gBAAgBqB,iBAAkB;QAC3C,KAAK,MAAMnB,OAAOF,aAAasB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACrB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDgB,OAAOH,MAAM,CAACf,KAAKC;QACrB;IACF;IAEA,OAAOiB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["querystring", "slashedProtocols", "formatUrl", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "urlObjectKeys", "formatWithValidation", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;;;;;;AAIzC,YAAYA,iBAAiB,gBAAe;;AAE5C,MAAMC,mBAAmB;AAElB,SAASC,UAAUC,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,uLAAOf,YAAYgB,aAAsB,CAACP;IACpD;IAEA,IAAIQ,SAASd,OAAOc,MAAM,IAAKR,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASY,QAAQ,CAAC,MAAMZ,YAAY;IAErD,IACEH,OAAOgB,OAAO,IACZ,CAAA,CAACb,YAAYL,iBAAiBmB,IAAI,CAACd,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIS,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDV,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCM,SAASA,OAAOL,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWU,SAAST;AAClD;AAEO,MAAMa,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAA;AAEM,SAASC,qBAAqBC,GAAc;IACjD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,IAAIH,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAACT,cAAcU,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO5B,UAAUqB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/request-meta.ts"], "sourcesContent": ["/* eslint-disable no-redeclare */\nimport type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { BaseNextRequest } from './base-http'\nimport type { CloneableBody } from './body-streams'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from './response-cache'\nimport type { PagesDevOverlayBridgeType } from '../next-devtools/userspace/pages/pages-dev-overlay-setup'\n\n// FIXME: (wyattjoh) this is a temporary solution to allow us to pass data between bundled modules\nexport const NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta')\n\nexport type NextIncomingMessage = (BaseNextRequest | IncomingMessage) & {\n  [NEXT_REQUEST_META]?: RequestMeta\n}\n\nexport interface RequestMeta {\n  /**\n   * The query that was used to make the request.\n   */\n  initQuery?: ParsedUrlQuery\n\n  /**\n   * The URL that was used to make the request.\n   */\n  initURL?: string\n\n  /**\n   * The protocol that was used to make the request.\n   */\n  initProtocol?: string\n\n  /**\n   * The body that was read from the request. This is used to allow the body to\n   * be read multiple times.\n   */\n  clonableBody?: CloneableBody\n\n  /**\n   * True when the request matched a locale domain that was configured in the\n   * next.config.js file.\n   */\n  isLocaleDomain?: boolean\n\n  /**\n   * True when the request had locale information stripped from the pathname\n   * part of the URL.\n   */\n  didStripLocale?: boolean\n\n  /**\n   * If the request had it's URL rewritten, this is the URL it was rewritten to.\n   */\n  rewroteURL?: string\n\n  /**\n   * The cookies that were added by middleware and were added to the response.\n   */\n  middlewareCookie?: string[]\n\n  /**\n   * The match on the request for a given route.\n   */\n  match?: RouteMatch\n\n  /**\n   * The incremental cache to use for the request.\n   */\n  incrementalCache?: any\n\n  /**\n   * The server components HMR cache, only for dev.\n   */\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  /**\n   * Equals the segment path that was used for the prefetch RSC request.\n   */\n  segmentPrefetchRSCRequest?: string\n\n  /**\n   * True when the request is for the prefetch flight data.\n   */\n  isPrefetchRSCRequest?: true\n\n  /**\n   * True when the request is for the flight data.\n   */\n  isRSCRequest?: true\n\n  /**\n   * A search param set by the Next.js client when performing RSC requests.\n   * Because some CDNs do not vary their cache entries on our custom headers,\n   * this search param represents a hash of the header values. For any cached\n   * RSC request, we should verify that the hash matches before responding.\n   * Otherwise this can lead to cache poisoning.\n   * TODO: Consider not using custom request headers at all, and instead encode\n   * everything into the search param.\n   */\n  cacheBustingSearchParam?: string\n\n  /**\n   * True when the request is for the `/_next/data` route using the pages\n   * router.\n   */\n  isNextDataReq?: true\n\n  /**\n   * Postponed state to use for resumption. If present it's assumed that the\n   * request is for a page that has postponed (there are no guarantees that the\n   * page actually has postponed though as it would incur an additional cache\n   * lookup).\n   */\n  postponed?: string\n\n  /**\n   * If provided, this will be called when a response cache entry was generated\n   * or looked up in the cache.\n   */\n  onCacheEntry?: (\n    cacheEntry: any,\n    requestMeta: any\n  ) => Promise<boolean | void> | boolean | void\n\n  /**\n   * The previous revalidate before rendering 404 page for notFound: true\n   */\n  notFoundRevalidate?: number | false\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage?: string\n\n  /**\n   * The path we routed to and should be invoked\n   */\n  invokePath?: string\n\n  /**\n   * The specific page output we should be matching\n   */\n  invokeOutput?: string\n\n  /**\n   * The status we are invoking the request with from routing\n   */\n  invokeStatus?: number\n\n  /**\n   * The routing error we are invoking with\n   */\n  invokeError?: Error\n\n  /**\n   * The query parsed for the invocation\n   */\n  invokeQuery?: Record<string, undefined | string | string[]>\n\n  /**\n   * Whether the request is a middleware invocation\n   */\n  middlewareInvoke?: boolean\n\n  /**\n   * Whether the request should render the fallback shell or not.\n   */\n  renderFallbackShell?: boolean\n\n  /**\n   * Whether the request is for the custom error page.\n   */\n  customErrorRender?: true\n\n  /**\n   * Whether to bubble up the NoFallbackError to the caller when a 404 is\n   * returned.\n   */\n  bubbleNoFallback?: true\n\n  /**\n   * True when the request had locale information inferred from the default\n   * locale.\n   */\n  localeInferredFromDefault?: true\n\n  /**\n   * The locale that was inferred or explicitly set for the request.\n   */\n  locale?: string\n\n  /**\n   * The default locale that was inferred or explicitly set for the request.\n   */\n  defaultLocale?: string\n\n  /**\n   * The project dir the server is running in\n   */\n  projectDir?: string\n\n  /**\n   * The dist directory the server is currently using\n   */\n  distDir?: string\n\n  /**\n   * Whether we are generating the fallback version of the page in dev mode\n   */\n  isIsrFallback?: boolean\n\n  /**\n   * The query after resolving routes\n   */\n  query?: ParsedUrlQuery\n\n  /**\n   * The params after resolving routes\n   */\n  params?: ParsedUrlQuery\n\n  /**\n   * The AMP validator to use in development\n   */\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n\n  /**\n   * ErrorOverlay component to use in development for pages router\n   */\n  PagesErrorDebug?: PagesDevOverlayBridgeType\n\n  /**\n   * Whether server is in minimal mode (this will be replaced with more\n   * specific flags in future)\n   */\n  minimalMode?: boolean\n}\n\n/**\n * Gets the request metadata. If no key is provided, the entire metadata object\n * is returned.\n *\n * @param req the request to get the metadata from\n * @param key the key to get from the metadata (optional)\n * @returns the value for the key or the entire metadata object\n */\nexport function getRequestMeta(\n  req: NextIncomingMessage,\n  key?: undefined\n): RequestMeta\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key: K\n): RequestMeta[K]\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key?: K\n): RequestMeta | RequestMeta[K] {\n  const meta = req[NEXT_REQUEST_META] || {}\n  return typeof key === 'string' ? meta[key] : meta\n}\n\n/**\n * Sets the request metadata.\n *\n * @param req the request to set the metadata on\n * @param meta the metadata to set\n * @returns the mutated request metadata\n */\nexport function setRequestMeta(req: NextIncomingMessage, meta: RequestMeta) {\n  req[NEXT_REQUEST_META] = meta\n  return meta\n}\n\n/**\n * Adds a value to the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to set\n * @param value the value to set\n * @returns the mutated request metadata\n */\nexport function addRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K,\n  value: RequestMeta[K]\n) {\n  const meta = getRequestMeta(request)\n  meta[key] = value\n  return setRequestMeta(request, meta)\n}\n\n/**\n * Removes a key from the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to remove\n * @returns the mutated request metadata\n */\nexport function removeRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K\n) {\n  const meta = getRequestMeta(request)\n  delete meta[key]\n  return setRequestMeta(request, meta)\n}\n\ntype NextQueryMetadata = {\n  /**\n   * The `_rsc` query parameter used for cache busting to ensure that the RSC\n   * requests do not get cached by the browser explicitly.\n   */\n  [NEXT_RSC_UNION_QUERY]?: string\n}\n\nexport type NextParsedUrlQuery = ParsedUrlQuery &\n  NextQueryMetadata & {\n    amp?: '1'\n  }\n\nexport interface NextUrlWithParsedQuery extends UrlWithParsedQuery {\n  query: NextParsedUrlQuery\n}\n"], "names": ["NEXT_REQUEST_META", "Symbol", "for", "getRequestMeta", "req", "key", "meta", "setRequestMeta", "addRequestMeta", "request", "value", "removeRequestMeta"], "mappings": "AAAA,+BAA+B,GAW/B,kGAAkG;;;;;;;;AAC3F,MAAMA,oBAAoBC,OAAOC,GAAG,CAAC,2BAA0B;AAoP/D,SAASC,eACdC,GAAwB,EACxBC,GAAO;IAEP,MAAMC,OAAOF,GAAG,CAACJ,kBAAkB,IAAI,CAAC;IACxC,OAAO,OAAOK,QAAQ,WAAWC,IAAI,CAACD,IAAI,GAAGC;AAC/C;AASO,SAASC,eAAeH,GAAwB,EAAEE,IAAiB;IACxEF,GAAG,CAACJ,kBAAkB,GAAGM;IACzB,OAAOA;AACT;AAUO,SAASE,eACdC,OAA4B,EAC5BJ,GAAM,EACNK,KAAqB;IAErB,MAAMJ,OAAOH,eAAeM;IAC5BH,IAAI,CAACD,IAAI,GAAGK;IACZ,OAAOH,eAAeE,SAASH;AACjC;AASO,SAASK,kBACdF,OAA4B,EAC5BJ,GAAM;IAEN,MAAMC,OAAOH,eAAeM;IAC5B,OAAOH,IAAI,CAACD,IAAI;IAChB,OAAOE,eAAeE,SAASH;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/app-render/interop-default.ts"], "sourcesContent": ["/**\n * Interop between \"export default\" and \"module.exports\".\n */\nexport function interopDefault(mod: any) {\n  return mod.default || mod\n}\n"], "names": ["interopDefault", "mod", "default"], "mappings": "AAAA;;CAEC,GACD;;;AAAO,SAASA,eAAeC,GAAQ;IACrC,OAAOA,IAAIC,OAAO,IAAID;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/instrumentation/utils.ts"], "sourcesContent": ["export function getRevalidateReason(params: {\n  isOnDemandRevalidate?: boolean\n  isRevalidate?: boolean\n}): 'on-demand' | 'stale' | undefined {\n  if (params.isOnDemandRevalidate) {\n    return 'on-demand'\n  }\n  if (params.isRevalidate) {\n    return 'stale'\n  }\n  return undefined\n}\n"], "names": ["getRevalidateReason", "params", "isOnDemandRevalidate", "isRevalidate", "undefined"], "mappings": ";;;AAAO,SAASA,oBAAoBC,MAGnC;IACC,IAAIA,OAAOC,oBAAoB,EAAE;QAC/B,OAAO;IACT;IACA,IAAID,OAAOE,YAAY,EAAE;QACvB,OAAO;IACT;IACA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC,GACD;;;AAAO,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["parsePath", "pathHasPrefix", "path", "prefix", "pathname", "startsWith"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAc;;AASjC,SAASC,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,yLAAGJ,YAAAA,EAAUE;IAC/B,OAAOE,aAAaD,UAAUC,SAASC,UAAU,CAACF,SAAS;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/page-path/normalize-data-path.ts"], "sourcesContent": ["import { pathHasPrefix } from '../router/utils/path-has-prefix'\n\n/**\n * strip _next/data/<build-id>/ prefix and .json suffix\n */\nexport function normalizeDataPath(pathname: string) {\n  if (!pathHasPrefix(pathname || '/', '/_next/data')) {\n    return pathname\n  }\n  pathname = pathname\n    .replace(/\\/_next\\/data\\/[^/]{1,}/, '')\n    .replace(/\\.json$/, '')\n\n  if (pathname === '/index') {\n    return '/'\n  }\n  return pathname\n}\n"], "names": ["pathHasPrefix", "normalizeDataPath", "pathname", "replace"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,kCAAiC;;AAKxD,SAASC,kBAAkBC,QAAgB;IAChD,IAAI,+LAACF,gBAAAA,EAAcE,YAAY,KAAK,gBAAgB;QAClD,OAAOA;IACT;IACAA,WAAWA,SACRC,OAAO,CAAC,2BAA2B,IACnCA,OAAO,CAAC,WAAW;IAEtB,IAAID,aAAa,UAAU;QACzB,OAAO;IACT;IACA,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/lib/detached-promise.ts"], "sourcesContent": ["/**\n * A `Promise.withResolvers` implementation that exposes the `resolve` and\n * `reject` functions on a `Promise`.\n *\n * @see https://tc39.es/proposal-promise-with-resolvers/\n */\nexport class DetachedPromise<T = any> {\n  public readonly resolve: (value: T | PromiseLike<T>) => void\n  public readonly reject: (reason: any) => void\n  public readonly promise: Promise<T>\n\n  constructor() {\n    let resolve: (value: T | PromiseLike<T>) => void\n    let reject: (reason: any) => void\n\n    // Create the promise and assign the resolvers to the object.\n    this.promise = new Promise<T>((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n\n    // We know that resolvers is defined because the Promise constructor runs\n    // synchronously.\n    this.resolve = resolve!\n    this.reject = reject!\n  }\n}\n"], "names": ["Detached<PERSON>romise", "constructor", "resolve", "reject", "promise", "Promise", "res", "rej"], "mappings": "AAAA;;;;;CAKC,GACD;;;AAAO,MAAMA;IAKXC,aAAc;QACZ,IAAIC;QACJ,IAAIC;QAEJ,6DAA6D;QAC7D,IAAI,CAACC,OAAO,GAAG,IAAIC,QAAW,CAACC,KAAKC;YAClCL,UAAUI;YACVH,SAASI;QACX;QAEA,yEAAyE;QACzE,iBAAiB;QACjB,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/lib/batcher.ts"], "sourcesContent": ["import type { SchedulerFn } from './scheduler'\n\nimport { DetachedPromise } from './detached-promise'\n\ntype CacheKeyFn<K, C extends string | number | null> = (\n  key: K\n) => PromiseLike<C> | C\n\ntype BatcherOptions<K, C extends string | number | null> = {\n  cacheKeyFn?: CacheKeyFn<K, C>\n  schedulerFn?: SchedulerFn<void>\n}\n\ntype WorkFn<V, C> = (\n  key: C,\n  resolve: (value: V | PromiseLike<V>) => void\n) => Promise<V>\n\n/**\n * A wrapper for a function that will only allow one call to the function to\n * execute at a time.\n */\nexport class Batcher<K, V, C extends string | number | null> {\n  private readonly pending = new Map<C, Promise<V>>()\n\n  protected constructor(\n    private readonly cacheKeyFn?: CacheKeyFn<K, C>,\n    /**\n     * A function that will be called to schedule the wrapped function to be\n     * executed. This defaults to a function that will execute the function\n     * immediately.\n     */\n    private readonly schedulerFn: SchedulerFn<void> = (fn) => fn()\n  ) {}\n\n  /**\n   * Creates a new instance of PendingWrapper. If the key extends a string or\n   * number, the key will be used as the cache key. If the key is an object, a\n   * cache key function must be provided.\n   */\n  public static create<K extends string | number | null, V>(\n    options?: BatcherOptions<K, K>\n  ): Batcher<K, V, K>\n  public static create<K, V, C extends string | number | null>(\n    options: BatcherOptions<K, C> &\n      Required<Pick<BatcherOptions<K, C>, 'cacheKeyFn'>>\n  ): Batcher<K, V, C>\n  public static create<K, V, C extends string | number | null>(\n    options?: BatcherOptions<K, C>\n  ): Batcher<K, V, C> {\n    return new Batcher<K, V, C>(options?.cacheKeyFn, options?.schedulerFn)\n  }\n\n  /**\n   * Wraps a function in a promise that will be resolved or rejected only once\n   * for a given key. This will allow multiple calls to the function to be\n   * made, but only one will be executed at a time. The result of the first\n   * call will be returned to all callers.\n   *\n   * @param key the key to use for the cache\n   * @param fn the function to wrap\n   * @returns a promise that resolves to the result of the function\n   */\n  public async batch(key: K, fn: WorkFn<V, C>): Promise<V> {\n    const cacheKey = (this.cacheKeyFn ? await this.cacheKeyFn(key) : key) as C\n    if (cacheKey === null) {\n      return fn(cacheKey, Promise.resolve)\n    }\n\n    const pending = this.pending.get(cacheKey)\n    if (pending) return pending\n\n    const { promise, resolve, reject } = new DetachedPromise<V>()\n    this.pending.set(cacheKey, promise)\n\n    this.schedulerFn(async () => {\n      try {\n        const result = await fn(cacheKey, resolve)\n\n        // Resolving a promise multiple times is a no-op, so we can safely\n        // resolve all pending promises with the same result.\n        resolve(result)\n      } catch (err) {\n        reject(err)\n      } finally {\n        this.pending.delete(cacheKey)\n      }\n    })\n\n    return promise\n  }\n}\n"], "names": ["Detached<PERSON>romise", "<PERSON><PERSON>", "cacheKeyFn", "schedulerFn", "fn", "pending", "Map", "create", "options", "batch", "key", "cache<PERSON>ey", "Promise", "resolve", "get", "promise", "reject", "set", "result", "err", "delete"], "mappings": ";;;AAEA,SAASA,eAAe,QAAQ,qBAAoB;;AAoB7C,MAAMC;IAGX,YACmBC,UAA6B,EAC9C;;;;KAIC,GACgBC,cAAiC,CAACC,KAAOA,IAAI,CAC9D;aAPiBF,UAAAA,GAAAA;aAMAC,WAAAA,GAAAA;aATFE,OAAAA,GAAU,IAAIC;IAU5B;IAcH,OAAcC,OACZC,OAA8B,EACZ;QAClB,OAAO,IAAIP,QAAiBO,WAAAA,OAAAA,KAAAA,IAAAA,QAASN,UAAU,EAAEM,WAAAA,OAAAA,KAAAA,IAAAA,QAASL,WAAW;IACvE;IAEA;;;;;;;;;GASC,GACD,MAAaM,MAAMC,GAAM,EAAEN,EAAgB,EAAc;QACvD,MAAMO,WAAY,IAAI,CAACT,UAAU,GAAG,MAAM,IAAI,CAACA,UAAU,CAACQ,OAAOA;QACjE,IAAIC,aAAa,MAAM;YACrB,OAAOP,GAAGO,UAAUC,QAAQC,OAAO;QACrC;QAEA,MAAMR,UAAU,IAAI,CAACA,OAAO,CAACS,GAAG,CAACH;QACjC,IAAIN,SAAS,OAAOA;QAEpB,MAAM,EAAEU,OAAO,EAAEF,OAAO,EAAEG,MAAM,EAAE,GAAG,+JAAIhB,kBAAAA;QACzC,IAAI,CAACK,OAAO,CAACY,GAAG,CAACN,UAAUI;QAE3B,IAAI,CAACZ,WAAW,CAAC;YACf,IAAI;gBACF,MAAMe,SAAS,MAAMd,GAAGO,UAAUE;gBAElC,kEAAkE;gBAClE,qDAAqD;gBACrDA,QAAQK;YACV,EAAE,OAAOC,KAAK;gBACZH,OAAOG;YACT,SAAU;gBACR,IAAI,CAACd,OAAO,CAACe,MAAM,CAACT;YACtB;QACF;QAEA,OAAOI;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["scheduleOnNextTick", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "scheduleImmediate", "setImmediate", "atLeastOneTask", "waitAtLeastOneReactRenderTask", "r"], "mappings": "AAGA;;;;;CAKC,GACD;;;;;;AAAO,MAAMA,qBAAqB,CAACC;IACjC,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;aAElC;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF,EAAC;AAQM,MAAMS,oBAAoB,CAACT;IAChC,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACLI,aAAaV;IACf;AACF,EAAC;AAOM,SAASW;IACd,OAAO,IAAIV,QAAc,CAACC,UAAYO,kBAAkBP;AAC1D;AAWO,SAASU;IACd,IAAIR,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACL,OAAO,IAAIL,QAAQ,CAACY,IAAMH,aAAaG;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/response-cache/types.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport type RenderResult from '../render-result'\nimport type { CacheControl, Revalidate } from '../lib/cache-control'\nimport type { RouteKind } from '../route-kind'\n\nexport interface ResponseCacheBase {\n  get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalCache\n      /**\n       * This is a hint to the cache to help it determine what kind of route\n       * this is so it knows where to look up the cache entry from. If not\n       * provided it will test the filesystem to check.\n       */\n      routeKind: RouteKind\n\n      /**\n       * True if this is a fallback request.\n       */\n      isFallback?: boolean\n\n      /**\n       * True if the route is enabled for PPR.\n       */\n      isRoutePPREnabled?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null>\n}\n\n// The server components HMR cache might store other data as well in the future,\n// at which point this should be refactored to a discriminated union type.\nexport interface ServerComponentsHmrCache {\n  get(key: string): CachedFetchData | undefined\n  set(key: string, data: CachedFetchData): void\n}\n\nexport type CachedFetchData = {\n  headers: Record<string, string>\n  body: string\n  url: string\n  status?: number\n}\n\nexport const enum CachedRouteKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  REDIRECT = 'REDIRECT',\n  IMAGE = 'IMAGE',\n}\n\nexport interface CachedFetchValue {\n  kind: CachedRouteKind.FETCH\n  data: CachedFetchData\n  // tags are only present with file-system-cache\n  // fetch cache stores tags outside of cache entry\n  tags?: string[]\n  revalidate: number\n}\n\nexport interface CachedRedirectValue {\n  kind: CachedRouteKind.REDIRECT\n  props: Object\n}\n\nexport interface CachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  rscData: Buffer | undefined\n  status: number | undefined\n  postponed: string | undefined\n  headers: OutgoingHttpHeaders | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface CachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  pageData: Object\n  status: number | undefined\n  headers: OutgoingHttpHeaders | undefined\n}\n\nexport interface CachedRouteValue {\n  kind: CachedRouteKind.APP_ROUTE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  body: Buffer\n  status: number\n  headers: OutgoingHttpHeaders\n}\n\nexport interface CachedImageValue {\n  kind: CachedRouteKind.IMAGE\n  etag: string\n  upstreamEtag: string\n  buffer: Buffer\n  extension: string\n  isMiss?: boolean\n  isStale?: boolean\n}\n\nexport interface IncrementalCachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  rscData: Buffer | undefined\n  headers: OutgoingHttpHeaders | undefined\n  postponed: string | undefined\n  status: number | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface IncrementalCachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  pageData: Object\n  headers: OutgoingHttpHeaders | undefined\n  status: number | undefined\n}\n\nexport interface IncrementalResponseCacheEntry {\n  cacheControl?: CacheControl\n  /**\n   * timestamp in milliseconds to revalidate after\n   */\n  revalidateAfter?: Revalidate\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  isMiss?: boolean\n  value: Exclude<IncrementalCacheValue, CachedFetchValue> | null\n}\n\nexport interface IncrementalFetchCacheEntry {\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  value: CachedFetchValue\n}\n\nexport type IncrementalCacheEntry =\n  | IncrementalResponseCacheEntry\n  | IncrementalFetchCacheEntry\n\nexport type IncrementalCacheValue =\n  | CachedRedirectValue\n  | IncrementalCachedPageValue\n  | IncrementalCachedAppPageValue\n  | CachedImageValue\n  | CachedFetchValue\n  | CachedRouteValue\n\nexport type ResponseCacheValue =\n  | CachedRedirectValue\n  | CachedPageValue\n  | CachedAppPageValue\n  | CachedImageValue\n  | CachedRouteValue\n\nexport type ResponseCacheEntry = {\n  cacheControl?: CacheControl\n  value: ResponseCacheValue | null\n  isStale?: boolean | -1\n  isMiss?: boolean\n}\n\n/**\n * @param hasResolved whether the responseGenerator has resolved it's promise\n * @param previousCacheEntry the previous cache entry if it exists or the current\n */\nexport type ResponseGenerator = (state: {\n  hasResolved: boolean\n  previousCacheEntry?: IncrementalResponseCacheEntry | null\n  isRevalidating?: boolean\n  span?: any\n}) => Promise<ResponseCacheEntry | null>\n\nexport const enum IncrementalCacheKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  IMAGE = 'IMAGE',\n}\n\nexport interface GetIncrementalFetchCacheContext {\n  kind: IncrementalCacheKind.FETCH\n  revalidate?: Revalidate\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n  softTags?: string[]\n}\n\nexport interface GetIncrementalResponseCacheContext {\n  kind: Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH>\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback: boolean\n}\n\nexport interface SetIncrementalFetchCacheContext {\n  fetchCache: true\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n  isImplicitBuildTimeCache?: boolean\n}\n\nexport interface SetIncrementalResponseCacheContext {\n  fetchCache?: false\n  cacheControl?: CacheControl\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback?: boolean\n}\n\nexport interface IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n\nexport interface IncrementalCache extends IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind"], "mappings": ";;;;AA+CO,IAAWA,kBAAAA,WAAAA,GAAAA,SAAAA,eAAAA;;;;;;;WAAAA;MAOjB;AA0IM,IAAWC,uBAAAA,WAAAA,GAAAA,SAAAA,oBAAAA;;;;;;WAAAA;MAMjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/stream-utils/encoded-tags.ts"], "sourcesContent": ["export const ENCODED_TAGS = {\n  // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n  OPENING: {\n    // <html\n    HTML: new Uint8Array([60, 104, 116, 109, 108]),\n    // <body\n    BODY: new Uint8Array([60, 98, 111, 100, 121]),\n  },\n  CLOSED: {\n    // </head>\n    HEAD: new Uint8Array([60, 47, 104, 101, 97, 100, 62]),\n    // </body>\n    BODY: new Uint8Array([60, 47, 98, 111, 100, 121, 62]),\n    // </html>\n    HTML: new Uint8Array([60, 47, 104, 116, 109, 108, 62]),\n    // </body></html>\n    BODY_AND_HTML: new Uint8Array([\n      60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62,\n    ]),\n  },\n  META: {\n    // Only the match the prefix cause the suffix can be different wether it's xml compatible or not \">\" or \"/>\"\n    // <meta name=\"«nxt-icon»\"\n    // This is a special mark that will be replaced by the icon insertion script tag.\n    ICON_MARK: new Uint8Array([\n      60, 109, 101, 116, 97, 32, 110, 97, 109, 101, 61, 34, 194, 171, 110, 120,\n      116, 45, 105, 99, 111, 110, 194, 187, 34,\n    ]),\n  },\n} as const\n"], "names": ["ENCODED_TAGS", "OPENING", "HTML", "Uint8Array", "BODY", "CLOSED", "HEAD", "BODY_AND_HTML", "META", "ICON_MARK"], "mappings": ";;;AAAO,MAAMA,eAAe;IAC1B,iHAAiH;IACjHC,SAAS;QACP,QAAQ;QACRC,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAK;YAAK;YAAK;SAAI;QAC7C,QAAQ;QACRC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;SAAI;IAC9C;IACAE,QAAQ;QACN,UAAU;QACVC,MAAM,IAAIH,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAI;YAAK;SAAG;QACpD,UAAU;QACVC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;SAAG;QACpD,UAAU;QACVD,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAAG;QACrD,iBAAiB;QACjBI,eAAe,IAAIJ,WAAW;YAC5B;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAC5D;IACH;IACAK,MAAM;QACJ,4GAA4G;QAC5G,0BAA0B;QAC1B,iFAAiF;QACjFC,WAAW,IAAIN,WAAW;YACxB;YAAI;YAAK;YAAK;YAAK;YAAI;YAAI;YAAK;YAAI;YAAK;YAAK;YAAI;YAAI;YAAK;YAAK;YAAK;YACrE;YAAK;YAAI;YAAK;YAAI;YAAK;YAAK;YAAK;YAAK;SACvC;IACH;AACF,EAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/stream-utils/uint8array-helpers.ts"], "sourcesContent": ["/**\n * Find the starting index of Uint8Array `b` within Uint8Array `a`.\n */\nexport function indexOfUint8Array(a: Uint8Array, b: Uint8Array) {\n  if (b.length === 0) return 0\n  if (a.length === 0 || b.length > a.length) return -1\n\n  // start iterating through `a`\n  for (let i = 0; i <= a.length - b.length; i++) {\n    let completeMatch = true\n    // from index `i`, iterate through `b` and check for mismatch\n    for (let j = 0; j < b.length; j++) {\n      // if the values do not match, then this isn't a complete match, exit `b` iteration early and iterate to next index of `a`.\n      if (a[i + j] !== b[j]) {\n        completeMatch = false\n        break\n      }\n    }\n\n    if (completeMatch) {\n      return i\n    }\n  }\n\n  return -1\n}\n\n/**\n * Check if two Uint8Arrays are strictly equivalent.\n */\nexport function isEquivalentUint8Arrays(a: Uint8Array, b: Uint8Array) {\n  if (a.length !== b.length) return false\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) return false\n  }\n\n  return true\n}\n\n/**\n * Remove Uint8Array `b` from Uint8Array `a`.\n *\n * If `b` is not in `a`, `a` is returned unchanged.\n *\n * Otherwise, the function returns a new Uint8Array instance with size `a.length - b.length`\n */\nexport function removeFromUint8Array(a: Uint8Array, b: Uint8Array) {\n  const tagIndex = indexOfUint8Array(a, b)\n  if (tagIndex === 0) return a.subarray(b.length)\n  if (tagIndex > -1) {\n    const removed = new Uint8Array(a.length - b.length)\n    removed.set(a.slice(0, tagIndex))\n    removed.set(a.slice(tagIndex + b.length), tagIndex)\n    return removed\n  } else {\n    return a\n  }\n}\n"], "names": ["indexOfUint8Array", "a", "b", "length", "i", "completeMatch", "j", "isEquivalentUint8Arrays", "removeFromUint8Array", "tagIndex", "subarray", "removed", "Uint8Array", "set", "slice"], "mappings": "AAAA;;CAEC,GACD;;;;;AAAO,SAASA,kBAAkBC,CAAa,EAAEC,CAAa;IAC5D,IAAIA,EAAEC,MAAM,KAAK,GAAG,OAAO;IAC3B,IAAIF,EAAEE,MAAM,KAAK,KAAKD,EAAEC,MAAM,GAAGF,EAAEE,MAAM,EAAE,OAAO,CAAC;IAEnD,8BAA8B;IAC9B,IAAK,IAAIC,IAAI,GAAGA,KAAKH,EAAEE,MAAM,GAAGD,EAAEC,MAAM,EAAEC,IAAK;QAC7C,IAAIC,gBAAgB;QACpB,6DAA6D;QAC7D,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,EAAEC,MAAM,EAAEG,IAAK;YACjC,2HAA2H;YAC3H,IAAIL,CAAC,CAACG,IAAIE,EAAE,KAAKJ,CAAC,CAACI,EAAE,EAAE;gBACrBD,gBAAgB;gBAChB;YACF;QACF;QAEA,IAAIA,eAAe;YACjB,OAAOD;QACT;IACF;IAEA,OAAO,CAAC;AACV;AAKO,SAASG,wBAAwBN,CAAa,EAAEC,CAAa;IAClE,IAAID,EAAEE,MAAM,KAAKD,EAAEC,MAAM,EAAE,OAAO;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIH,EAAEE,MAAM,EAAEC,IAAK;QACjC,IAAIH,CAAC,CAACG,EAAE,KAAKF,CAAC,CAACE,EAAE,EAAE,OAAO;IAC5B;IAEA,OAAO;AACT;AASO,SAASI,qBAAqBP,CAAa,EAAEC,CAAa;IAC/D,MAAMO,WAAWT,kBAAkBC,GAAGC;IACtC,IAAIO,aAAa,GAAG,OAAOR,EAAES,QAAQ,CAACR,EAAEC,MAAM;IAC9C,IAAIM,WAAW,CAAC,GAAG;QACjB,MAAME,UAAU,IAAIC,WAAWX,EAAEE,MAAM,GAAGD,EAAEC,MAAM;QAClDQ,QAAQE,GAAG,CAACZ,EAAEa,KAAK,CAAC,GAAGL;QACvBE,QAAQE,GAAG,CAACZ,EAAEa,KAAK,CAACL,WAAWP,EAAEC,MAAM,GAAGM;QAC1C,OAAOE;IACT,OAAO;QACL,OAAOV;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/errors/constants.ts"], "sourcesContent": ["export const MISSING_ROOT_TAGS_ERROR = 'NEXT_MISSING_ROOT_TAGS'\n"], "names": ["MISSING_ROOT_TAGS_ERROR"], "mappings": ";;;AAAO,MAAMA,0BAA0B,yBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/segment-cache/output-export-prefetch-encoding.ts"], "sourcesContent": ["// In output: export mode, the build id is added to the start of the HTML\n// document, directly after the doctype declaration. During a prefetch, the\n// client performs a range request to get the build id, so it can check whether\n// the target page belongs to the same build.\n//\n// The first 64 bytes of the document are requested. The exact number isn't\n// too important; it must be larger than the build id + doctype + closing and\n// ending comment markers, but it doesn't need to match the end of the\n// comment exactly.\n//\n// Build ids are 21 bytes long in the default implementation, though this\n// can be overridden in the Next.js config. For the purposes of this check,\n// it's OK to only match the start of the id, so we'll truncate it if exceeds\n// a certain length.\n\nconst DOCTYPE_PREFIX = '<!DOCTYPE html>' // 15 bytes\nconst MAX_BUILD_ID_LENGTH = 24\n\n// Request the first 64 bytes. The Range header is inclusive of the end value.\nexport const DOC_PREFETCH_RANGE_HEADER_VALUE = 'bytes=0-63'\n\nfunction escapeBuildId(buildId: string) {\n  // If the build id is longer than the given limit, it's OK for our purposes\n  // to only match the beginning.\n  const truncated = buildId.slice(0, MAX_BUILD_ID_LENGTH)\n  // Replace hyphens with underscores so it doesn't break the HTML comment.\n  // (Unlikely, but if this did happen it would break the whole document.)\n  return truncated.replace(/-/g, '_')\n}\n\nexport function insertBuildIdComment(originalHtml: string, buildId: string) {\n  if (\n    // Skip if the build id contains a closing comment marker.\n    buildId.includes('-->') ||\n    // React always inserts a doctype at the start of the document. Skip if it\n    // isn't present. Shouldn't happen; suggests an issue elsewhere.\n    !originalHtml.startsWith(DOCTYPE_PREFIX)\n  ) {\n    // Return the original HTML unchanged. This means the document will not\n    // be prefetched.\n    // TODO: The build id comment is currently only used during prefetches, but\n    // if we eventually use this mechanism for regular navigations, we may need\n    // to error during build if we fail to insert it for some reason.\n    return originalHtml\n  }\n  // The comment must be inserted after the doctype.\n  return originalHtml.replace(\n    DOCTYPE_PREFIX,\n    DOCTYPE_PREFIX + '<!--' + escapeBuildId(buildId) + '-->'\n  )\n}\n\nexport function doesExportedHtmlMatchBuildId(\n  partialHtmlDocument: string,\n  buildId: string\n) {\n  // Check whether the document starts with the expected buildId.\n  return partialHtmlDocument.startsWith(\n    DOCTYPE_PREFIX + '<!--' + escapeBuildId(buildId) + '-->'\n  )\n}\n"], "names": ["DOCTYPE_PREFIX", "MAX_BUILD_ID_LENGTH", "DOC_PREFETCH_RANGE_HEADER_VALUE", "escapeBuildId", "buildId", "truncated", "slice", "replace", "insertBuildIdComment", "originalHtml", "includes", "startsWith", "doesExportedHtmlMatchBuildId", "partialHtmlDocument"], "mappings": "AAAA,yEAAyE;AACzE,2EAA2E;AAC3E,+EAA+E;AAC/E,6CAA6C;AAC7C,EAAE;AACF,2EAA2E;AAC3E,6EAA6E;AAC7E,sEAAsE;AACtE,mBAAmB;AACnB,EAAE;AACF,yEAAyE;AACzE,2EAA2E;AAC3E,6EAA6E;AAC7E,oBAAoB;;;;;;AAEpB,MAAMA,iBAAiB,kBAAkB,WAAW;;AACpD,MAAMC,sBAAsB;AAGrB,MAAMC,kCAAkC,aAAY;AAE3D,SAASC,cAAcC,OAAe;IACpC,2EAA2E;IAC3E,+BAA+B;IAC/B,MAAMC,YAAYD,QAAQE,KAAK,CAAC,GAAGL;IACnC,yEAAyE;IACzE,wEAAwE;IACxE,OAAOI,UAAUE,OAAO,CAAC,MAAM;AACjC;AAEO,SAASC,qBAAqBC,YAAoB,EAAEL,OAAe;IACxE,IAEEA,AADA,QACQM,QAAQ,CAAC,UACjB,+BAF0D,2CAEgB;IAC1E,gEAAgE;IAChE,CAACD,aAAaE,UAAU,CAACX,iBACzB;QACA,uEAAuE;QACvE,iBAAiB;QACjB,2EAA2E;QAC3E,2EAA2E;QAC3E,iEAAiE;QACjE,OAAOS;IACT;IACA,kDAAkD;IAClD,OAAOA,aAAaF,OAAO,CACzBP,gBACAA,iBAAiB,SAASG,cAAcC,WAAW;AAEvD;AAEO,SAASQ,6BACdC,mBAA2B,EAC3BT,OAAe;IAEf,+DAA+D;IAC/D,OAAOS,oBAAoBF,UAAU,CACnCX,iBAAiB,SAASG,cAAcC,WAAW;AAEvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encoded-tags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\nimport { insertBuildIdComment } from '../../shared/lib/segment-cache/output-export-prefetch-encoding'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nfunction createPrefetchCommentStream(\n  isBuildTimePrerendering: boolean,\n  buildId: string\n): TransformStream<Uint8Array, Uint8Array> {\n  // Insert an extra comment at the beginning of the HTML document. This must\n  // come after the DOCTYPE, which is inserted by React.\n  //\n  // The first chunk sent by React will contain the doctype. After that, we can\n  // pass through the rest of the chunks as-is.\n  let didTransformFirstChunk = false\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (isBuildTimePrerendering && !didTransformFirstChunk) {\n        didTransformFirstChunk = true\n        const decoder = new TextDecoder('utf-8', { fatal: true })\n        const chunkStr = decoder.decode(chunk, {\n          stream: true,\n        })\n        const updatedChunkStr = insertBuildIdComment(chunkStr, buildId)\n        controller.enqueue(encoder.encode(updatedChunkStr))\n        return\n      }\n      controller.enqueue(chunk)\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: {\n    renderToReadableStream: typeof import('react-dom/server').renderToReadableStream\n  }\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createMetadataTransformStream(\n  insert: () => Promise<string> | string\n): TransformStream<Uint8Array, Uint8Array> {\n  let chunkIndex = -1\n  let isMarkRemoved = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      let iconMarkIndex = -1\n      let closedHeadIndex = -1\n      chunkIndex++\n\n      if (isMarkRemoved) {\n        controller.enqueue(chunk)\n        return\n      }\n      let iconMarkLength = 0\n      // Only search for the closed head tag once\n      if (iconMarkIndex === -1) {\n        iconMarkIndex = indexOfUint8Array(chunk, ENCODED_TAGS.META.ICON_MARK)\n        if (iconMarkIndex === -1) {\n          controller.enqueue(chunk)\n          return\n        } else {\n          // When we found the `<meta name=\"«nxt-icon»\"` tag prefix, we will remove it from the chunk.\n          // Its close tag could either be `/>` or `>`, checking the next char to ensure we cover both cases.\n          iconMarkLength = ENCODED_TAGS.META.ICON_MARK.length\n          // Check if next char is /, this is for xml mode.\n          if (chunk[iconMarkIndex + iconMarkLength] === 47) {\n            iconMarkLength += 2\n          } else {\n            // The last char is `>`\n            iconMarkLength++\n          }\n        }\n      }\n\n      // Check if icon mark is inside <head> tag in the first chunk.\n      if (chunkIndex === 0) {\n        closedHeadIndex = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        if (iconMarkIndex !== -1) {\n          // The mark icon is located in the 1st chunk before the head tag.\n          // We do not need to insert the script tag in this case because it's in the head.\n          // Just remove the icon mark from the chunk.\n          if (iconMarkIndex < closedHeadIndex) {\n            const replaced = new Uint8Array(chunk.length - iconMarkLength)\n\n            // Remove the icon mark from the chunk.\n            replaced.set(chunk.subarray(0, iconMarkIndex))\n            replaced.set(\n              chunk.subarray(iconMarkIndex + iconMarkLength),\n              iconMarkIndex\n            )\n            chunk = replaced\n          } else {\n            // The icon mark is after the head tag, replace and insert the script tag at that position.\n            const insertion = await insert()\n            const encodedInsertion = encoder.encode(insertion)\n            const insertionLength = encodedInsertion.length\n            const replaced = new Uint8Array(\n              chunk.length - iconMarkLength + insertionLength\n            )\n            replaced.set(chunk.subarray(0, iconMarkIndex))\n            replaced.set(encodedInsertion, iconMarkIndex)\n            replaced.set(\n              chunk.subarray(iconMarkIndex + iconMarkLength),\n              iconMarkIndex + insertionLength\n            )\n            chunk = replaced\n          }\n          isMarkRemoved = true\n        }\n        // If there's no icon mark located, it will be handled later when if present in the following chunks.\n      } else {\n        // When it's appeared in the following chunks, we'll need to\n        // remove the mark and then insert the script tag at that position.\n        const insertion = await insert()\n        const encodedInsertion = encoder.encode(insertion)\n        const insertionLength = encodedInsertion.length\n        // Replace the icon mark with the hoist script or empty string.\n        const replaced = new Uint8Array(\n          chunk.length - iconMarkLength + insertionLength\n        )\n        // Set the first part of the chunk, before the icon mark.\n        replaced.set(chunk.subarray(0, iconMarkIndex))\n        // Set the insertion after the icon mark.\n        replaced.set(encodedInsertion, iconMarkIndex)\n\n        // Set the rest of the chunk after the icon mark.\n        replaced.set(\n          chunk.subarray(iconMarkIndex + iconMarkLength),\n          iconMarkIndex + insertionLength\n        )\n        chunk = replaced\n        isMarkRemoved = true\n      }\n      controller.enqueue(chunk)\n    },\n  })\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  isBuildTimePrerendering: boolean\n  buildId: string\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    isBuildTimePrerendering,\n    buildId,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Add build id comment to start of the HTML document (in export mode)\n    createPrefetchCommentStream(isBuildTimePrerendering, buildId),\n\n    // Transform metadata\n    createMetadataTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  isBuildTimePrerendering: boolean\n  buildId: string\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    isBuildTimePrerendering,\n    buildId,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Add build id comment to start of the HTML document (in export mode)\n      .pipeThrough(\n        createPrefetchCommentStream(isBuildTimePrerendering, buildId)\n      )\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["getTracer", "AppRenderSpan", "Detached<PERSON>romise", "scheduleImmediate", "atLeastOneTask", "ENCODED_TAGS", "indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "MISSING_ROOT_TAGS_ERROR", "insertBuildIdComment", "voidCatch", "encoder", "TextEncoder", "chainStreams", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "streamFromString", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "streamFromBuffer", "chunk", "streamToBuffer", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "streamToString", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "createBufferedTransformStream", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "createPrefetchCommentStream", "isBuildTimePrerendering", "buildId", "didTransformFirstChunk", "chunkStr", "updatedChunkStr", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "trace", "renderToReadableStream", "createMetadataTransformStream", "insert", "chunkIndex", "isMarkRemoved", "iconMarkIndex", "closedHeadIndex", "iconMarkLength", "META", "ICON_MARK", "CLOSED", "HEAD", "replaced", "subarray", "insertion", "encodedInsertion", "<PERSON><PERSON><PERSON><PERSON>", "createHeadInsertionTransformStream", "inserted", "hasBytes", "index", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "BODY", "HTML", "createRootLayoutValidatorStream", "foundHtml", "foundBody", "OPENING", "missingTags", "map", "c", "join", "chainTransformers", "transformers", "transformer", "pipeThrough", "continueFizzStream", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "split", "allReady", "continueDynamicPrerender", "prerenderStream", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "createDocumentClosingStream"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,sBAAqB;AACvE,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SACEC,iBAAiB,EACjBC,uBAAuB,EACvBC,oBAAoB,QACf,uBAAsB;AAC7B,SAASC,uBAAuB,QAAQ,oCAAmC;AAC3E,SAASC,oBAAoB,QAAQ,iEAAgE;;;;;;;;;AAErG,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEb,SAASC,aACd,GAAGC,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,OAAA,cAAiE,CAAjE,IAAIC,MAAM,yDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAACjB;IAEd,OAAOO;AACT;AAEO,SAASW,iBAAiBC,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,SAASC,iBAAiBC,KAAa;IAC5C,OAAO,IAAIP,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACI;YACnBL,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,eAAeG,eACpBC,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEO,eAAeO,eACpBV,MAAkC,EAClCW,MAAoB;IAEpB,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMjB,SAASE,OAAQ;QAChC,IAAIW,UAAAA,OAAAA,KAAAA,IAAAA,OAAQK,OAAO,EAAE;YACnB,OAAOD;QACT;QAEAA,UAAUH,QAAQK,MAAM,CAACnB,OAAO;YAAEE,QAAQ;QAAK;IACjD;IAEAe,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEO,SAASG;IAId,IAAIC,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAAC7B;QACb,yDAAyD;QACzD,IAAI4B,SAAS;QAEb,MAAME,WAAW,8JAAI7D,mBAAAA;QACrB2D,UAAUE;6JAEV5D,oBAAAA,EAAkB;YAChB,IAAI;gBACF,MAAMmC,QAAQ,IAAI0B,WAAWJ;gBAC7B,IAAIK,cAAc;gBAElB,IAAK,IAAIzC,IAAI,GAAGA,IAAImC,eAAe3C,MAAM,EAAEQ,IAAK;oBAC9C,MAAM0C,gBAAgBP,cAAc,CAACnC,EAAE;oBACvCc,MAAM6B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5ET,eAAe3C,MAAM,GAAG;gBACxB4C,mBAAmB;gBACnB3B,WAAWC,OAAO,CAACI;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRuB,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIlD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,kDAAkD;YAClD0B,eAAeZ,IAAI,CAACT;YACpBsB,oBAAoBtB,MAAM8B,UAAU;YAEpC,sCAAsC;YACtCN,MAAM7B;QACR;QACA6B;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQxC,OAAO;QACxB;IACF;AACF;AAEA,SAASmD,4BACPC,uBAAgC,EAChCC,OAAe;IAEf,2EAA2E;IAC3E,sDAAsD;IACtD,EAAE;IACF,6EAA6E;IAC7E,6CAA6C;IAC7C,IAAIC,yBAAyB;IAC7B,OAAO,IAAIvD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,IAAIwC,2BAA2B,CAACE,wBAAwB;gBACtDA,yBAAyB;gBACzB,MAAMvB,UAAU,IAAIC,YAAY,SAAS;oBAAEC,OAAO;gBAAK;gBACvD,MAAMsB,WAAWxB,QAAQK,MAAM,CAACnB,OAAO;oBACrCE,QAAQ;gBACV;gBACA,MAAMqC,kBAAkBnE,yOAAAA,EAAqBkE,UAAUF;gBACvDzC,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC0C;gBAClC;YACF;YACA5C,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEO,SAASwC,0BAA0B,EACxCC,cAAc,EACdC,OAAO,EACPC,aAAa,EAOd;IACC,4KAAOjF,YAAAA,IAAYkF,KAAK,qKAACjF,gBAAAA,CAAckF,sBAAsB,EAAE,UAC7DJ,eAAeI,sBAAsB,CAACH,SAASC;AAEnD;AAEA,SAASG,8BACPC,MAAsC;IAEtC,IAAIC,aAAa,CAAC;IAClB,IAAIC,gBAAgB;IAEpB,OAAO,IAAInE,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/B,IAAIuD,gBAAgB,CAAC;YACrB,IAAIC,kBAAkB,CAAC;YACvBH;YAEA,IAAIC,eAAe;gBACjBtD,WAAWC,OAAO,CAACI;gBACnB;YACF;YACA,IAAIoD,iBAAiB;YACrB,2CAA2C;YAC3C,IAAIF,kBAAkB,CAAC,GAAG;gBACxBA,uMAAgBlF,oBAAAA,EAAkBgC,oLAAOjC,eAAAA,CAAasF,IAAI,CAACC,SAAS;gBACpE,IAAIJ,kBAAkB,CAAC,GAAG;oBACxBvD,WAAWC,OAAO,CAACI;oBACnB;gBACF,OAAO;oBACL,4FAA4F;oBAC5F,mGAAmG;oBACnGoD,8LAAiBrF,eAAAA,CAAasF,IAAI,CAACC,SAAS,CAAC5E,MAAM;oBACnD,iDAAiD;oBACjD,IAAIsB,KAAK,CAACkD,gBAAgBE,eAAe,KAAK,IAAI;wBAChDA,kBAAkB;oBACpB,OAAO;wBACL,uBAAuB;wBACvBA;oBACF;gBACF;YACF;YAEA,8DAA8D;YAC9D,IAAIJ,eAAe,GAAG;gBACpBG,yMAAkBnF,oBAAAA,EAAkBgC,oLAAOjC,eAAAA,CAAawF,MAAM,CAACC,IAAI;gBACnE,IAAIN,kBAAkB,CAAC,GAAG;oBACxB,iEAAiE;oBACjE,iFAAiF;oBACjF,4CAA4C;oBAC5C,IAAIA,gBAAgBC,iBAAiB;wBACnC,MAAMM,WAAW,IAAI/B,WAAW1B,MAAMtB,MAAM,GAAG0E;wBAE/C,uCAAuC;wBACvCK,SAAS5B,GAAG,CAAC7B,MAAM0D,QAAQ,CAAC,GAAGR;wBAC/BO,SAAS5B,GAAG,CACV7B,MAAM0D,QAAQ,CAACR,gBAAgBE,iBAC/BF;wBAEFlD,QAAQyD;oBACV,OAAO;wBACL,2FAA2F;wBAC3F,MAAME,YAAY,MAAMZ;wBACxB,MAAMa,mBAAmBtF,QAAQuB,MAAM,CAAC8D;wBACxC,MAAME,kBAAkBD,iBAAiBlF,MAAM;wBAC/C,MAAM+E,WAAW,IAAI/B,WACnB1B,MAAMtB,MAAM,GAAG0E,iBAAiBS;wBAElCJ,SAAS5B,GAAG,CAAC7B,MAAM0D,QAAQ,CAAC,GAAGR;wBAC/BO,SAAS5B,GAAG,CAAC+B,kBAAkBV;wBAC/BO,SAAS5B,GAAG,CACV7B,MAAM0D,QAAQ,CAACR,gBAAgBE,iBAC/BF,gBAAgBW;wBAElB7D,QAAQyD;oBACV;oBACAR,gBAAgB;gBAClB;YACA,qGAAqG;YACvG,OAAO;gBACL,4DAA4D;gBAC5D,mEAAmE;gBACnE,MAAMU,YAAY,MAAMZ;gBACxB,MAAMa,mBAAmBtF,QAAQuB,MAAM,CAAC8D;gBACxC,MAAME,kBAAkBD,iBAAiBlF,MAAM;gBAC/C,+DAA+D;gBAC/D,MAAM+E,WAAW,IAAI/B,WACnB1B,MAAMtB,MAAM,GAAG0E,iBAAiBS;gBAElC,yDAAyD;gBACzDJ,SAAS5B,GAAG,CAAC7B,MAAM0D,QAAQ,CAAC,GAAGR;gBAC/B,yCAAyC;gBACzCO,SAAS5B,GAAG,CAAC+B,kBAAkBV;gBAE/B,iDAAiD;gBACjDO,SAAS5B,GAAG,CACV7B,MAAM0D,QAAQ,CAACR,gBAAgBE,iBAC/BF,gBAAgBW;gBAElB7D,QAAQyD;gBACRR,gBAAgB;YAClB;YACAtD,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEA,SAAS8D,mCACPf,MAA6B;IAE7B,IAAIgB,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAIlF,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/BqE,WAAW;YAEX,MAAML,YAAY,MAAMZ;YACxB,IAAIgB,UAAU;gBACZ,IAAIJ,WAAW;oBACb,MAAMC,mBAAmBtF,QAAQuB,MAAM,CAAC8D;oBACxChE,WAAWC,OAAO,CAACgE;gBACrB;gBACAjE,WAAWC,OAAO,CAACI;YACrB,OAAO;gBACL,0JAA0J;gBAC1J,MAAMiE,QAAQjG,2MAAAA,EAAkBgC,oLAAOjC,eAAAA,CAAawF,MAAM,CAACC,IAAI;gBAC/D,wDAAwD;gBACxD,uEAAuE;gBACvE,IAAIS,UAAU,CAAC,GAAG;oBAChB,IAAIN,WAAW;wBACb,MAAMC,mBAAmBtF,QAAQuB,MAAM,CAAC8D;wBACxC,kEAAkE;wBAClE,OAAO;wBACP,8CAA8C;wBAC9C,mCAAmC;wBACnC,yEAAyE;wBACzE,MAAMO,sBAAsB,IAAIxC,WAC9B1B,MAAMtB,MAAM,GAAGkF,iBAAiBlF,MAAM;wBAExC,0DAA0D;wBAC1DwF,oBAAoBrC,GAAG,CAAC7B,MAAMmE,KAAK,CAAC,GAAGF;wBACvC,qCAAqC;wBACrCC,oBAAoBrC,GAAG,CAAC+B,kBAAkBK;wBAC1C,+BAA+B;wBAC/BC,oBAAoBrC,GAAG,CACrB7B,MAAMmE,KAAK,CAACF,QACZA,QAAQL,iBAAiBlF,MAAM;wBAEjCiB,WAAWC,OAAO,CAACsE;oBACrB,OAAO;wBACLvE,WAAWC,OAAO,CAACI;oBACrB;oBACA+D,WAAW;gBACb,OAAO;oBACL,6FAA6F;oBAC7F,gFAAgF;oBAChF,8EAA8E;oBAC9E,OAAO;oBACP,gEAAgE;oBAChE,6CAA6C;oBAC7C,IAAIJ,WAAW;wBACbhE,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC8D;oBACpC;oBACAhE,WAAWC,OAAO,CAACI;oBACnB+D,WAAW;gBACb;YACF;QACF;QACA,MAAMvC,OAAM7B,UAAU;YACpB,gEAAgE;YAChE,IAAIqE,UAAU;gBACZ,MAAML,YAAY,MAAMZ;gBACxB,IAAIY,WAAW;oBACbhE,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC8D;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASS,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAI/C;IAEJ,MAAMC,QAAQ,CAAC7B;QACb,MAAM8B,WAAW,+JAAI7D,kBAAAA;QACrB2D,UAAUE;6JAEV5D,oBAAAA,EAAkB;YAChB,IAAI;gBACF8B,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACwE;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACR9C,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIlD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,wCAAwC;YACxC,IAAIsE,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACV9C,MAAM7B;QACR;QACA6B,OAAM7B,UAAU;YACd,IAAI4B,SAAS,OAAOA,QAAQxC,OAAO;YACnC,IAAIuF,SAAS;YAEb,aAAa;YACb3E,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACwE;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPrE,MAAkC;IAElC,IAAIsE,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAa/E,UAA4C;QACtE,IAAI6E,MAAM;YACR;QACF;QAEA,MAAMrE,SAASD,OAAOE,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,2JAAMtC,iBAAAA;QAEN,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEwC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRmE,cAAc;oBACd;gBACF;gBAEA9E,WAAWC,OAAO,CAACW;YACrB;QACF,EAAE,OAAOoE,KAAK;YACZhF,WAAWiF,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI7F,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,6DAA6D;YAC7D,IAAI,CAACwE,MAAM;gBACTA,OAAOE,aAAa/E;YACtB;QACF;QACA6B,OAAM7B,UAAU;YACd,IAAI8E,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAa/E;QAC9B;IACF;AACF;AAEA,MAAMkF,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIjG,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,IAAIoF,aAAa;gBACf,OAAOpF,WAAWC,OAAO,CAACI;YAC5B;YAEA,MAAMiE,QAAQjG,2MAAAA,EAAkBgC,oLAAOjC,eAAAA,CAAawF,MAAM,CAACyB,aAAa;YACxE,IAAIf,QAAQ,CAAC,GAAG;gBACdc,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAI/E,MAAMtB,MAAM,kLAAKX,eAAAA,CAAawF,MAAM,CAACyB,aAAa,CAACtG,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAMuG,SAASjF,MAAMmE,KAAK,CAAC,GAAGF;gBAC9BtE,WAAWC,OAAO,CAACqF;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAIjF,MAAMtB,MAAM,gLAAGX,eAAAA,CAAawF,MAAM,CAACyB,aAAa,CAACtG,MAAM,GAAGuF,OAAO;oBACnE,uCAAuC;oBACvC,MAAMiB,QAAQlF,MAAMmE,KAAK,CACvBF,qLAAQlG,eAAAA,CAAawF,MAAM,CAACyB,aAAa,CAACtG,MAAM;oBAElDiB,WAAWC,OAAO,CAACsF;gBACrB;YACF,OAAO;gBACLvF,WAAWC,OAAO,CAACI;YACrB;QACF;QACAwB,OAAM7B,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,8KAAC7B,eAAAA,CAAawF,MAAM,CAACyB,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAIrG,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,2LACE1B,0BAAAA,EAAwB+B,oLAAOjC,eAAAA,CAAawF,MAAM,CAACyB,aAAa,4LAChE/G,0BAAAA,EAAwB+B,OAAOjC,4LAAAA,CAAawF,MAAM,CAAC6B,IAAI,4LACvDnH,0BAAAA,EAAwB+B,oLAAOjC,eAAAA,CAAawF,MAAM,CAAC8B,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFrF,+LAAQ9B,uBAAAA,EAAqB8B,oLAAOjC,eAAAA,CAAawF,MAAM,CAAC6B,IAAI;YAC5DpF,SAAQ9B,6MAAAA,EAAqB8B,oLAAOjC,eAAAA,CAAawF,MAAM,CAAC8B,IAAI;YAE5D1F,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAOO,SAASsF;IAId,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI1G,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC4F,cACDvH,0MAAAA,EAAkBgC,oLAAOjC,eAAAA,CAAa0H,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACDxH,2MAAAA,EAAkBgC,oLAAOjC,eAAAA,CAAa0H,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA7F,WAAWC,OAAO,CAACI;QACrB;QACAwB,OAAM7B,UAAU;YACd,MAAM+F,cAAmC,EAAE;YAC3C,IAAI,CAACH,WAAWG,YAAYjF,IAAI,CAAC;YACjC,IAAI,CAAC+E,WAAWE,YAAYjF,IAAI,CAAC;YAEjC,IAAI,CAACiF,YAAYhH,MAAM,EAAE;YAEzBiB,WAAWC,OAAO,CAChBtB,QAAQuB,MAAM,CACZ,CAAC;;+CAEoC,EAAE6F,YAChCC,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,YAAYhH,MAAM,GAAG,IAAI,UAAU,IACnC;sCACoB,uKAAEP,0BAAAA,CAAwB;;;UAGtD,CAAC;QAGP;IACF;AACF;AAEA,SAAS2H,kBACPlH,QAA2B,EAC3BmH,YAAyD;IAEzD,IAAI7F,SAAStB;IACb,KAAK,MAAMoH,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElB9F,SAASA,OAAO+F,WAAW,CAACD;IAC9B;IACA,OAAO9F;AACT;AAgBO,eAAegG,mBACpBC,YAAiC,EACjC,EACE9B,MAAM,EACN+B,iBAAiB,EACjBC,kBAAkB,EAClBlE,uBAAuB,EACvBC,OAAO,EACPkE,qBAAqB,EACrBC,yBAAyB,EACzBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBpC,SAASA,OAAOqC,KAAK,CAAC7B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIwB,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOb,kBAAkBK,cAAc;QACrC,qDAAqD;QACrD/E;QAEA,sEAAsE;QACtEc,4BAA4BC,yBAAyBC;QAErD,qBAAqB;QACrBU,8BAA8ByD;QAE9B,wBAAwB;QACxBE,kBAAkB,QAAQA,eAAe/H,MAAM,GAAG,IAC9C0F,2BAA2BqC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoB7B,4BAA4B6B,qBAAqB;QAErE,yDAAyD;QACzDI,qBAAqBlB,oCAAoC;QAEzD,kDAAkD;QAClDR;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/EhB,mCAAmCwC;KACpC;AACH;AAOO,eAAeM,yBACpBC,eAA2C,EAC3C,EACEP,qBAAqB,EACrBC,yBAAyB,EACO;IAElC,OACEM,gBACE,qDAAqD;KACpDZ,WAAW,CAAC7E,iCACZ6E,WAAW,CAACd,2CACb,gCAAgC;KAC/Bc,WAAW,CAACnC,mCAAmCwC,wBAChD,qBAAqB;KACpBL,WAAW,CAACnD,8BAA8ByD;AAEjD;AAUO,eAAeO,wBACpBD,eAA2C,EAC3C,EACET,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACzBpE,uBAAuB,EACvBC,OAAO,EACwB;IAEjC,OACEyE,gBACE,qDAAqD;KACpDZ,WAAW,CAAC7E,iCACb,sEAAsE;KACrE6E,WAAW,CACV/D,4BAA4BC,yBAAyBC,UAEvD,gCAAgC;KAC/B6D,WAAW,CAACnC,mCAAmCwC,wBAChD,qBAAqB;KACpBL,WAAW,CAACnD,8BAA8ByD,4BAC3C,+EAA+E;KAC9EN,WAAW,CAAC1B,4BAA4B6B,oBACzC,kDAAkD;KACjDH,WAAW,CAACnB;AAEnB;AAQO,eAAeiC,0BACpBZ,YAAwC,EACxC,EACEC,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACH;IAExB,OACEJ,aACE,qDAAqD;KACpDF,WAAW,CAAC7E,iCACb,gCAAgC;KAC/B6E,WAAW,CAACnC,mCAAmCwC,wBAChD,qBAAqB;KACpBL,WAAW,CAACnD,8BAA8ByD,4BAC3C,+EAA+E;KAC9EN,WAAW,CAAC1B,4BAA4B6B,oBACzC,kDAAkD;KACjDH,WAAW,CAACnB;AAEnB;AAEO,SAASkC;IACd,OAAOzH,iBAAiBsF;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/lib/constants.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS =\n  'private-next-rsc-track-dynamic-import'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n"], "names": ["NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "MATCHED_PATH_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "ACTION_SUFFIX", "NEXT_DATA_SUFFIX", "NEXT_META_SUFFIX", "NEXT_BODY_SUFFIX", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_RESUME_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_IMPLICIT_TAG_ID", "CACHE_ONE_YEAR", "INFINITE_CACHE", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "INSTRUMENTATION_HOOK_FILENAME", "PAGES_DIR_ALIAS", "DOT_NEXT_ALIAS", "ROOT_DIR_ALIAS", "APP_DIR_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "SERVER_PROPS_EXPORT_ERROR", "GSP_NO_RETURNED_VALUE", "GSSP_NO_RETURNED_VALUE", "UNSTABLE_REVALIDATE_RENAME_ERROR", "GSSP_COMPONENT_MEMBER_ERROR", "NON_STANDARD_NODE_ENV", "SSG_FALLBACK_EXPORT_ERROR", "ESLINT_DEFAULT_DIRS", "SERVER_RUNTIME", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "WEBPACK_LAYERS", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "WEBPACK_RESOURCE_QUERIES", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAMA,0BAA0B,OAAM;AACtC,MAAMC,kCAAkC,OAAM;AAE9C,MAAMC,sBAAsB,iBAAgB;AAC5C,MAAMC,8BAA8B,yBAAwB;AAC5D,MAAMC,6CACX,sCAAqC;AAEhC,MAAMC,sBAAsB,gBAAe;AAC3C,MAAMC,0BAA0B,YAAW;AAC3C,MAAMC,qBAAqB,eAAc;AACzC,MAAMC,aAAa,OAAM;AACzB,MAAMC,gBAAgB,UAAS;AAC/B,MAAMC,mBAAmB,QAAO;AAChC,MAAMC,mBAAmB,QAAO;AAChC,MAAMC,mBAAmB,QAAO;AAEhC,MAAMC,yBAAyB,oBAAmB;AAClD,MAAMC,qCAAqC,0BAAyB;AACpE,MAAMC,yCACX,8BAA6B;AAExB,MAAMC,qBAAqB,cAAa;AAIxC,MAAMC,2BAA2B,IAAG;AACpC,MAAMC,4BAA4B,IAAG;AACrC,MAAMC,iCAAiC,KAAI;AAC3C,MAAMC,6BAA6B,QAAO;AAG1C,MAAMC,iBAAiB,SAAQ;AAK/B,MAAMC,iBAAiB,WAAU;AAGjC,MAAMC,sBAAsB,aAAY;AACxC,MAAMC,6BAA6B,CAAC,SAAS,EAAED,qBAAqB,CAAA;AAGpE,MAAME,gCAAgC,kBAAiB;AAIvD,MAAMC,kBAAkB,qBAAoB;AAC5C,MAAMC,iBAAiB,mBAAkB;AACzC,MAAMC,iBAAiB,wBAAuB;AAC9C,MAAMC,gBAAgB,uBAAsB;AAC5C,MAAMC,0BAA0B,iCAAgC;AAChE,MAAMC,4BAA4B,mCAAkC;AACpE,MAAMC,yBAAyB,oCAAmC;AAClE,MAAMC,0BAA0B,iCAAgC;AAChE,MAAMC,mCACX,wCAAuC;AAClC,MAAMC,8BAA8B,qCAAoC;AACxE,MAAMC,kCACX,yCAAwC;AAEnC,MAAMC,iCAAiC,CAAC,6KAA6K,CAAC,CAAA;AAEtN,MAAMC,iCAAiC,CAAC,mGAAmG,CAAC,CAAA;AAE5I,MAAMC,uCAAuC,CAAC,uFAAuF,CAAC,CAAA;AAEtI,MAAMC,4BAA4B,CAAC,sHAAsH,CAAC,CAAA;AAE1J,MAAMC,6CAA6C,CAAC,uGAAuG,CAAC,CAAA;AAE5J,MAAMC,4BAA4B,CAAC,uHAAuH,CAAC,CAAA;AAE3J,MAAMC,wBACX,6FAA4F;AACvF,MAAMC,yBACX,iGAAgG;AAE3F,MAAMC,mCACX,uEACA,mCAAkC;AAE7B,MAAMC,8BAA8B,CAAC,wJAAwJ,CAAC,CAAA;AAE9L,MAAMC,wBAAwB,CAAC,iNAAiN,CAAC,CAAA;AAEjP,MAAMC,4BAA4B,CAAC,wJAAwJ,CAAC,CAAA;AAE5L,MAAMC,sBAAsB;IAAC;IAAO;IAAS;IAAc;IAAO;CAAM,CAAA;AAExE,MAAMC,iBAAgD;IAC3DC,MAAM;IACNC,kBAAkB;IAClBC,QAAQ;AACV,EAAC;AAED;;;CAGC,GACD,MAAMC,uBAAuB;IAC3B;;GAEC,GACDC,QAAQ;IACR;;;GAGC,GACDC,uBAAuB;IACvB;;GAEC,GACDC,qBAAqB;IACrB;;GAEC,GACDC,eAAe;IACf;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,WAAW;IACX;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,cAAc;IACd;;GAEC,GACDC,cAAc;AAChB;AAKA,MAAMC,iBAAiB;IACrB,GAAGd,oBAAoB;IACvBe,OAAO;QACLC,cAAc;YACZhB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;SACnC;QACDa,YAAY;YACVjB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDW,eAAe;YACb,YAAY;YACZlB,qBAAqBK,OAAO;YAC5BL,qBAAqBM,OAAO;SAC7B;QACDa,YAAY;YACVnB,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;SACrC;QACDU,SAAS;YACPpB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBC,MAAM;YAC3BD,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDc,UAAU;YACR,+BAA+B;YAC/BrB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBI,aAAa;SACnC;IACH;AACF;AAEA,MAAMkB,2BAA2B;IAC/BC,cAAc;IACdC,UAAU;IACVC,eAAe;IACfC,mBAAmB;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/web/utils.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../lib/constants'\n\n/**\n * Converts a Node.js IncomingHttpHeaders object to a Headers object. Any\n * headers with multiple values will be joined with a comma and space. Any\n * headers that have an undefined value will be ignored and others will be\n * coerced to strings.\n *\n * @param nodeHeaders the headers object to convert\n * @returns the converted headers object\n */\nexport function fromNodeOutgoingHttpHeaders(\n  nodeHeaders: OutgoingHttpHeaders\n): Headers {\n  const headers = new Headers()\n  for (let [key, value] of Object.entries(nodeHeaders)) {\n    const values = Array.isArray(value) ? value : [value]\n    for (let v of values) {\n      if (typeof v === 'undefined') continue\n      if (typeof v === 'number') {\n        v = v.toString()\n      }\n\n      headers.append(key, v)\n    }\n  }\n  return headers\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n  \n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nexport function splitCookiesString(cookiesString: string) {\n  var cookiesStrings = []\n  var pos = 0\n  var start\n  var ch\n  var lastComma\n  var nextStart\n  var cookiesSeparatorFound\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1\n    }\n    return pos < cookiesString.length\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos)\n\n    return ch !== '=' && ch !== ';' && ch !== ','\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos\n    cookiesSeparatorFound = false\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos)\n      if (ch === ',') {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos\n        pos += 1\n\n        skipWhitespace()\n        nextStart = pos\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === '=') {\n          // we found cookies separator\n          cookiesSeparatorFound = true\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart\n          cookiesStrings.push(cookiesString.substring(start, lastComma))\n          start = pos\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1\n        }\n      } else {\n        pos += 1\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length))\n    }\n  }\n\n  return cookiesStrings\n}\n\n/**\n * Converts a Headers object to a Node.js OutgoingHttpHeaders object. This is\n * required to support the set-cookie header, which may have multiple values.\n *\n * @param headers the headers object to convert\n * @returns the converted headers object\n */\nexport function toNodeOutgoingHttpHeaders(\n  headers: Headers\n): OutgoingHttpHeaders {\n  const nodeHeaders: OutgoingHttpHeaders = {}\n  const cookies: string[] = []\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      if (key.toLowerCase() === 'set-cookie') {\n        // We may have gotten a comma joined string of cookies, or multiple\n        // set-cookie headers. We need to merge them into one header array\n        // to represent all the cookies.\n        cookies.push(...splitCookiesString(value))\n        nodeHeaders[key] = cookies.length === 1 ? cookies[0] : cookies\n      } else {\n        nodeHeaders[key] = value\n      }\n    }\n  }\n  return nodeHeaders\n}\n\n/**\n * Validate the correctness of a user-provided URL.\n */\nexport function validateURL(url: string | URL): string {\n  try {\n    return String(new URL(String(url)))\n  } catch (error: any) {\n    throw new Error(\n      `URL is malformed \"${String(\n        url\n      )}\". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,\n      { cause: error }\n    )\n  }\n}\n\n/**\n * Normalizes `nxtP` and `nxtI` query param values to remove the prefix.\n * This function does not mutate the input key.\n */\nexport function normalizeNextQueryParam(key: string): null | string {\n  const prefixes = [NEXT_QUERY_PARAM_PREFIX, NEXT_INTERCEPTION_MARKER_PREFIX]\n  for (const prefix of prefixes) {\n    if (key !== prefix && key.startsWith(prefix)) {\n      return key.substring(prefix.length)\n    }\n  }\n  return null\n}\n"], "names": ["NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "fromNodeOutgoingHttpHeaders", "nodeHeaders", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "toString", "append", "splitCookiesString", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "toNodeOutgoingHttpHeaders", "cookies", "toLowerCase", "validateURL", "url", "String", "URL", "error", "Error", "cause", "normalizeNextQueryParam", "prefixes", "prefix", "startsWith"], "mappings": ";;;;;;;AACA,SACEA,+BAA+B,EAC/BC,uBAAuB,QAClB,sBAAqB;;AAWrB,SAASC,4BACdC,WAAgC;IAEhC,MAAMC,UAAU,IAAIC;IACpB,KAAK,IAAI,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACN,aAAc;QACpD,MAAMO,SAASC,MAAMC,OAAO,CAACL,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,IAAIM,KAAKH,OAAQ;YACpB,IAAI,OAAOG,MAAM,aAAa;YAC9B,IAAI,OAAOA,MAAM,UAAU;gBACzBA,IAAIA,EAAEC,QAAQ;YAChB;YAEAV,QAAQW,MAAM,CAACT,KAAKO;QACtB;IACF;IACA,OAAOT;AACT;AAYO,SAASY,mBAAmBC,aAAqB;IACtD,IAAIC,iBAAiB,EAAE;IACvB,IAAIC,MAAM;IACV,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IAEJ,SAASC;QACP,MAAON,MAAMF,cAAcS,MAAM,IAAI,KAAKC,IAAI,CAACV,cAAcW,MAAM,CAACT,MAAO;YACzEA,OAAO;QACT;QACA,OAAOA,MAAMF,cAAcS,MAAM;IACnC;IAEA,SAASG;QACPR,KAAKJ,cAAcW,MAAM,CAACT;QAE1B,OAAOE,OAAO,OAAOA,OAAO,OAAOA,OAAO;IAC5C;IAEA,MAAOF,MAAMF,cAAcS,MAAM,CAAE;QACjCN,QAAQD;QACRK,wBAAwB;QAExB,MAAOC,iBAAkB;YACvBJ,KAAKJ,cAAcW,MAAM,CAACT;YAC1B,IAAIE,OAAO,KAAK;gBACd,uEAAuE;gBACvEC,YAAYH;gBACZA,OAAO;gBAEPM;gBACAF,YAAYJ;gBAEZ,MAAOA,MAAMF,cAAcS,MAAM,IAAIG,iBAAkB;oBACrDV,OAAO;gBACT;gBAEA,8BAA8B;gBAC9B,IAAIA,MAAMF,cAAcS,MAAM,IAAIT,cAAcW,MAAM,CAACT,SAAS,KAAK;oBACnE,6BAA6B;oBAC7BK,wBAAwB;oBACxB,2DAA2D;oBAC3DL,MAAMI;oBACNL,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOE;oBACnDF,QAAQD;gBACV,OAAO;oBACL,uCAAuC;oBACvC,8BAA8B;oBAC9BA,MAAMG,YAAY;gBACpB;YACF,OAAO;gBACLH,OAAO;YACT;QACF;QAEA,IAAI,CAACK,yBAAyBL,OAAOF,cAAcS,MAAM,EAAE;YACzDR,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOH,cAAcS,MAAM;QACzE;IACF;IAEA,OAAOR;AACT;AASO,SAASc,0BACd5B,OAAgB;IAEhB,MAAMD,cAAmC,CAAC;IAC1C,MAAM8B,UAAoB,EAAE;IAC5B,IAAI7B,SAAS;QACX,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIH,QAAQK,OAAO,GAAI;YAC5C,IAAIH,IAAI4B,WAAW,OAAO,cAAc;gBACtC,mEAAmE;gBACnE,kEAAkE;gBAClE,gCAAgC;gBAChCD,QAAQH,IAAI,IAAId,mBAAmBT;gBACnCJ,WAAW,CAACG,IAAI,GAAG2B,QAAQP,MAAM,KAAK,IAAIO,OAAO,CAAC,EAAE,GAAGA;YACzD,OAAO;gBACL9B,WAAW,CAACG,IAAI,GAAGC;YACrB;QACF;IACF;IACA,OAAOJ;AACT;AAKO,SAASgC,YAAYC,GAAiB;IAC3C,IAAI;QACF,OAAOC,OAAO,IAAIC,IAAID,OAAOD;IAC/B,EAAE,OAAOG,OAAY;QACnB,MAAM,OAAA,cAKL,CALK,IAAIC,MACR,CAAC,kBAAkB,EAAEH,OACnBD,KACA,4FAA4F,CAAC,EAC/F;YAAEK,OAAOF;QAAM,IAJX,qBAAA;mBAAA;wBAAA;0BAAA;QAKN;IACF;AACF;AAMO,SAASG,wBAAwBpC,GAAW;IACjD,MAAMqC,WAAW;yJAAC1C,0BAAAA;yJAAyBD,kCAAAA;KAAgC;IAC3E,KAAK,MAAM4C,UAAUD,SAAU;QAC7B,IAAIrC,QAAQsC,UAAUtC,IAAIuC,UAAU,CAACD,SAAS;YAC5C,OAAOtC,IAAIyB,SAAS,CAACa,OAAOlB,MAAM;QACpC;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2021, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/i18n/detect-domain-locale.ts"], "sourcesContent": ["import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n"], "names": ["detectDomainLocale", "domainItems", "hostname", "detectedLocale", "toLowerCase", "item", "domainHostname", "domain", "split", "defaultLocale", "locales", "some", "locale"], "mappings": ";;;AAEO,SAASA,mBACdC,WAAqC,EACrCC,QAAiB,EACjBC,cAAuB;IAEvB,IAAI,CAACF,aAAa;IAElB,IAAIE,gBAAgB;QAClBA,iBAAiBA,eAAeC,WAAW;IAC7C;IAEA,KAAK,MAAMC,QAAQJ,YAAa;YAEPI,cAIrBA;QALF,yBAAyB;QACzB,MAAMC,iBAAAA,CAAiBD,eAAAA,KAAKE,MAAM,KAAA,OAAA,KAAA,IAAXF,aAAaG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACJ,WAAW;QAChE,IACEF,aAAaI,kBACbH,mBAAmBE,KAAKI,aAAa,CAACL,WAAW,MAAA,CAAA,CACjDC,gBAAAA,KAAKK,OAAO,KAAA,OAAA,KAAA,IAAZL,cAAcM,IAAI,CAAC,CAACC,SAAWA,OAAOR,WAAW,OAAOD,eAAAA,GACxD;YACA,OAAOE;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2042, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC,GACD;;;AAAO,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2058, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["parsePath", "addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAc;;AAMjC,SAASC,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,yLAAGP,YAAAA,EAAUE;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2074, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/add-path-suffix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n"], "names": ["parsePath", "addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAc;;AAOjC,SAASC,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,yLAAGP,YAAAA,EAAUE;IAC5C,OAAQ,KAAEG,WAAWF,SAASG,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2090, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/add-locale.ts"], "sourcesContent": ["import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n"], "names": ["addPathPrefix", "pathHasPrefix", "addLocale", "path", "locale", "defaultLocale", "ignorePrefix", "lower", "toLowerCase"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,oBAAmB;AACjD,SAASC,aAAa,QAAQ,oBAAmB;;;AAO1C,SAASC,UACdC,IAAY,EACZC,MAAuB,EACvBC,aAAsB,EACtBC,YAAsB;IAEtB,4EAA4E;IAC5E,sBAAsB;IACtB,IAAI,CAACF,UAAUA,WAAWC,eAAe,OAAOF;IAEhD,MAAMI,QAAQJ,KAAKK,WAAW;IAE9B,2EAA2E;IAC3E,iCAAiC;IACjC,IAAI,CAACF,cAAc;QACjB,kMAAIL,gBAAAA,EAAcM,OAAO,SAAS,OAAOJ;QACzC,kMAAIF,gBAAAA,EAAcM,OAAQ,MAAGH,OAAOI,WAAW,KAAO,OAAOL;IAC/D;IAEA,qCAAqC;IACrC,qMAAOH,gBAAAA,EAAcG,MAAO,MAAGC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2115, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/format-next-pathname-info.ts"], "sourcesContent": ["import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n"], "names": ["removeTrailingSlash", "addPathPrefix", "addPathSuffix", "addLocale", "formatNextPathnameInfo", "info", "pathname", "locale", "buildId", "undefined", "defaultLocale", "ignorePrefix", "trailingSlash", "basePath", "endsWith"], "mappings": ";;;AACA,SAASA,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,SAAS,QAAQ,eAAc;;;;;AAOjC,SAASC,uBAAuBC,IAAkB;IACvD,IAAIC,iMAAWH,YAAAA,EACbE,KAAKC,QAAQ,EACbD,KAAKE,MAAM,EACXF,KAAKG,OAAO,GAAGC,YAAYJ,KAAKK,aAAa,EAC7CL,KAAKM,YAAY;IAGnB,IAAIN,KAAKG,OAAO,IAAI,CAACH,KAAKO,aAAa,EAAE;QACvCN,+MAAWN,sBAAAA,EAAoBM;IACjC;IAEA,IAAID,KAAKG,OAAO,EAAE;QAChBF,yMAAWJ,gBAAAA,gMACTD,gBAAAA,EAAcK,UAAW,iBAAcD,KAAKG,OAAO,GACnDH,KAAKC,QAAQ,KAAK,MAAM,eAAe;IAE3C;IAEAA,yMAAWL,gBAAAA,EAAcK,UAAUD,KAAKQ,QAAQ;IAChD,OAAO,CAACR,KAAKG,OAAO,IAAIH,KAAKO,aAAa,GACtC,CAACN,SAASQ,QAAQ,CAAC,qMACjBZ,gBAAAA,EAAcI,UAAU,OACxBA,+MACFN,sBAAAA,EAAoBM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/get-hostname.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\n\n/**\n * Takes an object with a hostname property (like a parsed URL) and some\n * headers that may contain Host and returns the preferred hostname.\n * @param parsed An object containing a hostname property.\n * @param headers A dictionary with headers containing a `host`.\n */\nexport function getHostname(\n  parsed: { hostname?: string | null },\n  headers?: OutgoingHttpHeaders\n): string | undefined {\n  // Get the hostname from the headers if it exists, otherwise use the parsed\n  // hostname.\n  let hostname: string\n  if (headers?.host && !Array.isArray(headers.host)) {\n    hostname = headers.host.toString().split(':', 1)[0]\n  } else if (parsed.hostname) {\n    hostname = parsed.hostname\n  } else return\n\n  return hostname.toLowerCase()\n}\n"], "names": ["getHostname", "parsed", "headers", "hostname", "host", "Array", "isArray", "toString", "split", "toLowerCase"], "mappings": "AAEA;;;;;CAK<PERSON>,GACD;;;AAAO,SAASA,YACdC,MAAoC,EACpCC,OAA6B;IAE7B,2EAA2E;IAC3E,YAAY;IACZ,IAAIC;IACJ,IAAID,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASE,IAAI,KAAI,CAACC,MAAMC,OAAO,CAACJ,QAAQE,IAAI,GAAG;QACjDD,WAAWD,QAAQE,IAAI,CAACG,QAAQ,GAAGC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;IACrD,OAAO,IAAIP,OAAOE,QAAQ,EAAE;QAC1BA,WAAWF,OAAOE,QAAQ;IAC5B,OAAO;IAEP,OAAOA,SAASM,WAAW;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/i18n/normalize-locale-path.ts"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n"], "names": ["cache", "WeakMap", "normalizeLocalePath", "pathname", "locales", "lowercasedLocales", "get", "map", "locale", "toLowerCase", "set", "detectedLocale", "segments", "split", "segment", "index", "indexOf", "slice", "length"], "mappings": "AAKA;;;;CAIC;;;AACD,MAAMA,QAAQ,IAAIC;AAWX,SAASC,oBACdC,QAAgB,EAChBC,OAA2B;IAE3B,sDAAsD;IACtD,IAAI,CAACA,SAAS,OAAO;QAAED;IAAS;IAEhC,iEAAiE;IACjE,IAAIE,oBAAoBL,MAAMM,GAAG,CAACF;IAClC,IAAI,CAACC,mBAAmB;QACtBA,oBAAoBD,QAAQG,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QAC9DT,MAAMU,GAAG,CAACN,SAASC;IACrB;IAEA,IAAIM;IAEJ,oEAAoE;IACpE,yEAAyE;IACzE,MAAMC,WAAWT,SAASU,KAAK,CAAC,KAAK;IAErC,0EAA0E;IAC1E,UAAU;IACV,IAAI,CAACD,QAAQ,CAAC,EAAE,EAAE,OAAO;QAAET;IAAS;IAEpC,0DAA0D;IAC1D,MAAMW,UAAUF,QAAQ,CAAC,EAAE,CAACH,WAAW;IAEvC,yEAAyE;IACzE,mCAAmC;IACnC,MAAMM,QAAQV,kBAAkBW,OAAO,CAACF;IACxC,IAAIC,QAAQ,GAAG,OAAO;QAAEZ;IAAS;IAEjC,oCAAoC;IACpCQ,iBAAiBP,OAAO,CAACW,MAAM;IAE/B,gDAAgD;IAChDZ,WAAWA,SAASc,KAAK,CAACN,eAAeO,MAAM,GAAG,MAAM;IAExD,OAAO;QAAEf;QAAUQ;IAAe;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2213, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/remove-path-prefix.ts"], "sourcesContent": ["import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n"], "names": ["pathHasPrefix", "removePathPrefix", "path", "prefix", "withoutPrefix", "slice", "length", "startsWith"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,oBAAmB;;AAU1C,SAASC,iBAAiBC,IAAY,EAAEC,MAAc;IAC3D,yEAAyE;IACzE,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,oBAAoB;IACpB,EAAE;IACF,kBAAkB;IAClB,mBAAmB;IACnB,oBAAoB;IACpB,uBAAuB;IACvB,wBAAwB;IACxB,yBAAyB;IACzB,IAAI,+LAACH,gBAAAA,EAAcE,MAAMC,SAAS;QAChC,OAAOD;IACT;IAEA,+CAA+C;IAC/C,MAAME,gBAAgBF,KAAKG,KAAK,CAACF,OAAOG,MAAM;IAE9C,2EAA2E;IAC3E,IAAIF,cAAcG,UAAU,CAAC,MAAM;QACjC,OAAOH;IACT;IAEA,4EAA4E;IAC5E,mDAAmD;IACnD,OAAQ,MAAGA;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/get-next-pathname-info.ts"], "sourcesContent": ["import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n"], "names": ["normalizeLocalePath", "removePathPrefix", "pathHasPrefix", "getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathnameNoDataPrefix", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "parseData", "result", "i18nProvider", "analyze", "locales", "locale", "detectedLocale"], "mappings": ";;;AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,aAAa,QAAQ,oBAAmB;;;;AAkD1C,SAASC,oBACdC,QAAgB,EAChBC,OAAgB;QAE0BA;IAA1C,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE,GAAGH,CAAAA,sBAAAA,QAAQI,UAAU,KAAA,OAAlBJ,sBAAsB,CAAC;IACjE,MAAMK,OAAyB;QAC7BN;QACAI,eAAeJ,aAAa,MAAMA,SAASO,QAAQ,CAAC,OAAOH;IAC7D;IAEA,IAAIF,YAAYJ,8MAAAA,EAAcQ,KAAKN,QAAQ,EAAEE,WAAW;QACtDI,KAAKN,QAAQ,GAAGH,oNAAAA,EAAiBS,KAAKN,QAAQ,EAAEE;QAChDI,KAAKJ,QAAQ,GAAGA;IAClB;IACA,IAAIM,uBAAuBF,KAAKN,QAAQ;IAExC,IACEM,KAAKN,QAAQ,CAACS,UAAU,CAAC,mBACzBH,KAAKN,QAAQ,CAACO,QAAQ,CAAC,UACvB;QACA,MAAMG,QAAQJ,KAAKN,QAAQ,CACxBW,OAAO,CAAC,oBAAoB,IAC5BA,OAAO,CAAC,WAAW,IACnBC,KAAK,CAAC;QAET,MAAMC,UAAUH,KAAK,CAAC,EAAE;QACxBJ,KAAKO,OAAO,GAAGA;QACfL,uBACEE,KAAK,CAAC,EAAE,KAAK,UAAW,MAAGA,MAAMI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS;QAE1D,sDAAsD;QACtD,kDAAkD;QAClD,IAAId,QAAQe,SAAS,KAAK,MAAM;YAC9BV,KAAKN,QAAQ,GAAGQ;QAClB;IACF;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,IAAIL,MAAM;QACR,IAAIc,SAAShB,QAAQiB,YAAY,GAC7BjB,QAAQiB,YAAY,CAACC,OAAO,CAACb,KAAKN,QAAQ,6LAC1CJ,sBAAAA,EAAoBU,KAAKN,QAAQ,EAAEG,KAAKiB,OAAO;QAEnDd,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACnBL;QAAhBX,KAAKN,QAAQ,GAAGiB,CAAAA,mBAAAA,OAAOjB,QAAQ,KAAA,OAAfiB,mBAAmBX,KAAKN,QAAQ;QAEhD,IAAI,CAACiB,OAAOK,cAAc,IAAIhB,KAAKO,OAAO,EAAE;YAC1CI,SAAShB,QAAQiB,YAAY,GACzBjB,QAAQiB,YAAY,CAACC,OAAO,CAACX,iNAC7BZ,sBAAAA,EAAoBY,sBAAsBL,KAAKiB,OAAO;YAE1D,IAAIH,OAAOK,cAAc,EAAE;gBACzBhB,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACrC;QACF;IACF;IACA,OAAOhB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2300, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/web/next-url.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport type { DomainLocale, I18NConfig } from '../config-shared'\nimport type { I18NProvider } from '../lib/i18n-provider'\n\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { formatNextPathnameInfo } from '../../shared/lib/router/utils/format-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\n\ninterface Options {\n  base?: string | URL\n  headers?: OutgoingHttpHeaders\n  forceLocale?: boolean\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  i18nProvider?: I18NProvider\n}\n\nconst REGEX_LOCALHOST_HOSTNAME =\n  /(?!^https?:\\/\\/)(127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\\[::1\\]|localhost)/\n\nfunction parseURL(url: string | URL, base?: string | URL) {\n  return new URL(\n    String(url).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost'),\n    base && String(base).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost')\n  )\n}\n\nconst Internal = Symbol('NextURLInternal')\n\nexport class NextURL {\n  private [Internal]: {\n    basePath: string\n    buildId?: string\n    flightSearchParameters?: Record<string, string>\n    defaultLocale?: string\n    domainLocale?: DomainLocale\n    locale?: string\n    options: Options\n    trailingSlash?: boolean\n    url: URL\n  }\n\n  constructor(input: string | URL, base?: string | URL, opts?: Options)\n  constructor(input: string | URL, opts?: Options)\n  constructor(\n    input: string | URL,\n    baseOrOpts?: string | URL | Options,\n    opts?: Options\n  ) {\n    let base: undefined | string | URL\n    let options: Options\n\n    if (\n      (typeof baseOrOpts === 'object' && 'pathname' in baseOrOpts) ||\n      typeof baseOrOpts === 'string'\n    ) {\n      base = baseOrOpts\n      options = opts || {}\n    } else {\n      options = opts || baseOrOpts || {}\n    }\n\n    this[Internal] = {\n      url: parseURL(input, base ?? options.base),\n      options: options,\n      basePath: '',\n    }\n\n    this.analyze()\n  }\n\n  private analyze() {\n    const info = getNextPathnameInfo(this[Internal].url.pathname, {\n      nextConfig: this[Internal].options.nextConfig,\n      parseData: !process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,\n      i18nProvider: this[Internal].options.i18nProvider,\n    })\n\n    const hostname = getHostname(\n      this[Internal].url,\n      this[Internal].options.headers\n    )\n    this[Internal].domainLocale = this[Internal].options.i18nProvider\n      ? this[Internal].options.i18nProvider.detectDomainLocale(hostname)\n      : detectDomainLocale(\n          this[Internal].options.nextConfig?.i18n?.domains,\n          hostname\n        )\n\n    const defaultLocale =\n      this[Internal].domainLocale?.defaultLocale ||\n      this[Internal].options.nextConfig?.i18n?.defaultLocale\n\n    this[Internal].url.pathname = info.pathname\n    this[Internal].defaultLocale = defaultLocale\n    this[Internal].basePath = info.basePath ?? ''\n    this[Internal].buildId = info.buildId\n    this[Internal].locale = info.locale ?? defaultLocale\n    this[Internal].trailingSlash = info.trailingSlash\n  }\n\n  private formatPathname() {\n    return formatNextPathnameInfo({\n      basePath: this[Internal].basePath,\n      buildId: this[Internal].buildId,\n      defaultLocale: !this[Internal].options.forceLocale\n        ? this[Internal].defaultLocale\n        : undefined,\n      locale: this[Internal].locale,\n      pathname: this[Internal].url.pathname,\n      trailingSlash: this[Internal].trailingSlash,\n    })\n  }\n\n  private formatSearch() {\n    return this[Internal].url.search\n  }\n\n  public get buildId() {\n    return this[Internal].buildId\n  }\n\n  public set buildId(buildId: string | undefined) {\n    this[Internal].buildId = buildId\n  }\n\n  public get locale() {\n    return this[Internal].locale ?? ''\n  }\n\n  public set locale(locale: string) {\n    if (\n      !this[Internal].locale ||\n      !this[Internal].options.nextConfig?.i18n?.locales.includes(locale)\n    ) {\n      throw new TypeError(\n        `The NextURL configuration includes no locale \"${locale}\"`\n      )\n    }\n\n    this[Internal].locale = locale\n  }\n\n  get defaultLocale() {\n    return this[Internal].defaultLocale\n  }\n\n  get domainLocale() {\n    return this[Internal].domainLocale\n  }\n\n  get searchParams() {\n    return this[Internal].url.searchParams\n  }\n\n  get host() {\n    return this[Internal].url.host\n  }\n\n  set host(value: string) {\n    this[Internal].url.host = value\n  }\n\n  get hostname() {\n    return this[Internal].url.hostname\n  }\n\n  set hostname(value: string) {\n    this[Internal].url.hostname = value\n  }\n\n  get port() {\n    return this[Internal].url.port\n  }\n\n  set port(value: string) {\n    this[Internal].url.port = value\n  }\n\n  get protocol() {\n    return this[Internal].url.protocol\n  }\n\n  set protocol(value: string) {\n    this[Internal].url.protocol = value\n  }\n\n  get href() {\n    const pathname = this.formatPathname()\n    const search = this.formatSearch()\n    return `${this.protocol}//${this.host}${pathname}${search}${this.hash}`\n  }\n\n  set href(url: string) {\n    this[Internal].url = parseURL(url)\n    this.analyze()\n  }\n\n  get origin() {\n    return this[Internal].url.origin\n  }\n\n  get pathname() {\n    return this[Internal].url.pathname\n  }\n\n  set pathname(value: string) {\n    this[Internal].url.pathname = value\n  }\n\n  get hash() {\n    return this[Internal].url.hash\n  }\n\n  set hash(value: string) {\n    this[Internal].url.hash = value\n  }\n\n  get search() {\n    return this[Internal].url.search\n  }\n\n  set search(value: string) {\n    this[Internal].url.search = value\n  }\n\n  get password() {\n    return this[Internal].url.password\n  }\n\n  set password(value: string) {\n    this[Internal].url.password = value\n  }\n\n  get username() {\n    return this[Internal].url.username\n  }\n\n  set username(value: string) {\n    this[Internal].url.username = value\n  }\n\n  get basePath() {\n    return this[Internal].basePath\n  }\n\n  set basePath(value: string) {\n    this[Internal].basePath = value.startsWith('/') ? value : `/${value}`\n  }\n\n  toString() {\n    return this.href\n  }\n\n  toJSON() {\n    return this.href\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      href: this.href,\n      origin: this.origin,\n      protocol: this.protocol,\n      username: this.username,\n      password: this.password,\n      host: this.host,\n      hostname: this.hostname,\n      port: this.port,\n      pathname: this.pathname,\n      search: this.search,\n      searchParams: this.searchParams,\n      hash: this.hash,\n    }\n  }\n\n  clone() {\n    return new NextURL(String(this), this[Internal].options)\n  }\n}\n"], "names": ["detectDomainLocale", "formatNextPathnameInfo", "getHostname", "getNextPathnameInfo", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "url", "base", "URL", "String", "replace", "Internal", "Symbol", "NextURL", "constructor", "input", "baseOrOpts", "opts", "options", "basePath", "analyze", "info", "pathname", "nextConfig", "parseData", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "i18nProvider", "hostname", "headers", "domainLocale", "i18n", "domains", "defaultLocale", "buildId", "locale", "trailingSlash", "formatPathname", "forceLocale", "undefined", "formatSearch", "search", "locales", "includes", "TypeError", "searchParams", "host", "value", "port", "protocol", "href", "hash", "origin", "password", "username", "startsWith", "toString", "toJSON", "for", "clone"], "mappings": ";;;AAIA,SAASA,kBAAkB,QAAQ,6CAA4C;AAC/E,SAASC,sBAAsB,QAAQ,0DAAyD;AAChG,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,mBAAmB,QAAQ,uDAAsD;;;;;AAc1F,MAAMC,2BACJ;AAEF,SAASC,SAASC,GAAiB,EAAEC,IAAmB;IACtD,OAAO,IAAIC,IACTC,OAAOH,KAAKI,OAAO,CAACN,0BAA0B,cAC9CG,QAAQE,OAAOF,MAAMG,OAAO,CAACN,0BAA0B;AAE3D;AAEA,MAAMO,WAAWC,OAAO;AAEjB,MAAMC;IAeXC,YACEC,KAAmB,EACnBC,UAAmC,EACnCC,IAAc,CACd;QACA,IAAIV;QACJ,IAAIW;QAEJ,IACG,OAAOF,eAAe,YAAY,cAAcA,cACjD,OAAOA,eAAe,UACtB;YACAT,OAAOS;YACPE,UAAUD,QAAQ,CAAC;QACrB,OAAO;YACLC,UAAUD,QAAQD,cAAc,CAAC;QACnC;QAEA,IAAI,CAACL,SAAS,GAAG;YACfL,KAAKD,SAASU,OAAOR,QAAQW,QAAQX,IAAI;YACzCW,SAASA;YACTC,UAAU;QACZ;QAEA,IAAI,CAACC,OAAO;IACd;IAEQA,UAAU;YAcV,wCAAA,mCAKJ,6BACA,yCAAA;QAnBF,MAAMC,+MAAOlB,sBAAAA,EAAoB,IAAI,CAACQ,SAAS,CAACL,GAAG,CAACgB,QAAQ,EAAE;YAC5DC,YAAY,IAAI,CAACZ,SAAS,CAACO,OAAO,CAACK,UAAU;YAC7CC,WAAW,CAACC,QAAQC,GAAG,CAACC,kCAAkC;YAC1DC,cAAc,IAAI,CAACjB,SAAS,CAACO,OAAO,CAACU,YAAY;QACnD;QAEA,MAAMC,WAAW3B,mLAAAA,EACf,IAAI,CAACS,SAAS,CAACL,GAAG,EAClB,IAAI,CAACK,SAAS,CAACO,OAAO,CAACY,OAAO;QAEhC,IAAI,CAACnB,SAAS,CAACoB,YAAY,GAAG,IAAI,CAACpB,SAAS,CAACO,OAAO,CAACU,YAAY,GAC7D,IAAI,CAACjB,SAAS,CAACO,OAAO,CAACU,YAAY,CAAC5B,kBAAkB,CAAC6B,oMACvD7B,qBAAAA,EAAAA,CACE,oCAAA,IAAI,CAACW,SAAS,CAACO,OAAO,CAACK,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,yCAAA,kCAAmCS,IAAI,KAAA,OAAA,KAAA,IAAvC,uCAAyCC,OAAO,EAChDJ;QAGN,MAAMK,gBACJ,CAAA,CAAA,8BAAA,IAAI,CAACvB,SAAS,CAACoB,YAAY,KAAA,OAAA,KAAA,IAA3B,4BAA6BG,aAAa,KAAA,CAAA,CAC1C,qCAAA,IAAI,CAACvB,SAAS,CAACO,OAAO,CAACK,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,0CAAA,mCAAmCS,IAAI,KAAA,OAAA,KAAA,IAAvC,wCAAyCE,aAAa;QAExD,IAAI,CAACvB,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAGD,KAAKC,QAAQ;QAC3C,IAAI,CAACX,SAAS,CAACuB,aAAa,GAAGA;QAC/B,IAAI,CAACvB,SAAS,CAACQ,QAAQ,GAAGE,KAAKF,QAAQ,IAAI;QAC3C,IAAI,CAACR,SAAS,CAACwB,OAAO,GAAGd,KAAKc,OAAO;QACrC,IAAI,CAACxB,SAAS,CAACyB,MAAM,GAAGf,KAAKe,MAAM,IAAIF;QACvC,IAAI,CAACvB,SAAS,CAAC0B,aAAa,GAAGhB,KAAKgB,aAAa;IACnD;IAEQC,iBAAiB;QACvB,kNAAOrC,yBAAAA,EAAuB;YAC5BkB,UAAU,IAAI,CAACR,SAAS,CAACQ,QAAQ;YACjCgB,SAAS,IAAI,CAACxB,SAAS,CAACwB,OAAO;YAC/BD,eAAe,CAAC,IAAI,CAACvB,SAAS,CAACO,OAAO,CAACqB,WAAW,GAC9C,IAAI,CAAC5B,SAAS,CAACuB,aAAa,GAC5BM;YACJJ,QAAQ,IAAI,CAACzB,SAAS,CAACyB,MAAM;YAC7Bd,UAAU,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;YACrCe,eAAe,IAAI,CAAC1B,SAAS,CAAC0B,aAAa;QAC7C;IACF;IAEQI,eAAe;QACrB,OAAO,IAAI,CAAC9B,SAAS,CAACL,GAAG,CAACoC,MAAM;IAClC;IAEA,IAAWP,UAAU;QACnB,OAAO,IAAI,CAACxB,SAAS,CAACwB,OAAO;IAC/B;IAEA,IAAWA,QAAQA,OAA2B,EAAE;QAC9C,IAAI,CAACxB,SAAS,CAACwB,OAAO,GAAGA;IAC3B;IAEA,IAAWC,SAAS;QAClB,OAAO,IAAI,CAACzB,SAAS,CAACyB,MAAM,IAAI;IAClC;IAEA,IAAWA,OAAOA,MAAc,EAAE;YAG7B,wCAAA;QAFH,IACE,CAAC,IAAI,CAACzB,SAAS,CAACyB,MAAM,IACtB,CAAA,CAAA,CAAC,oCAAA,IAAI,CAACzB,SAAS,CAACO,OAAO,CAACK,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,yCAAA,kCAAmCS,IAAI,KAAA,OAAA,KAAA,IAAvC,uCAAyCW,OAAO,CAACC,QAAQ,CAACR,OAAAA,GAC3D;YACA,MAAM,OAAA,cAEL,CAFK,IAAIS,UACR,CAAC,8CAA8C,EAAET,OAAO,CAAC,CAAC,GADtD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACzB,SAAS,CAACyB,MAAM,GAAGA;IAC1B;IAEA,IAAIF,gBAAgB;QAClB,OAAO,IAAI,CAACvB,SAAS,CAACuB,aAAa;IACrC;IAEA,IAAIH,eAAe;QACjB,OAAO,IAAI,CAACpB,SAAS,CAACoB,YAAY;IACpC;IAEA,IAAIe,eAAe;QACjB,OAAO,IAAI,CAACnC,SAAS,CAACL,GAAG,CAACwC,YAAY;IACxC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACpC,SAAS,CAACL,GAAG,CAACyC,IAAI;IAChC;IAEA,IAAIA,KAAKC,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACyC,IAAI,GAAGC;IAC5B;IAEA,IAAInB,WAAW;QACb,OAAO,IAAI,CAAClB,SAAS,CAACL,GAAG,CAACuB,QAAQ;IACpC;IAEA,IAAIA,SAASmB,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACuB,QAAQ,GAAGmB;IAChC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACtC,SAAS,CAACL,GAAG,CAAC2C,IAAI;IAChC;IAEA,IAAIA,KAAKD,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC2C,IAAI,GAAGD;IAC5B;IAEA,IAAIE,WAAW;QACb,OAAO,IAAI,CAACvC,SAAS,CAACL,GAAG,CAAC4C,QAAQ;IACpC;IAEA,IAAIA,SAASF,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC4C,QAAQ,GAAGF;IAChC;IAEA,IAAIG,OAAO;QACT,MAAM7B,WAAW,IAAI,CAACgB,cAAc;QACpC,MAAMI,SAAS,IAAI,CAACD,YAAY;QAChC,OAAO,GAAG,IAAI,CAACS,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACH,IAAI,GAAGzB,WAAWoB,SAAS,IAAI,CAACU,IAAI,EAAE;IACzE;IAEA,IAAID,KAAK7C,GAAW,EAAE;QACpB,IAAI,CAACK,SAAS,CAACL,GAAG,GAAGD,SAASC;QAC9B,IAAI,CAACc,OAAO;IACd;IAEA,IAAIiC,SAAS;QACX,OAAO,IAAI,CAAC1C,SAAS,CAACL,GAAG,CAAC+C,MAAM;IAClC;IAEA,IAAI/B,WAAW;QACb,OAAO,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;IACpC;IAEA,IAAIA,SAAS0B,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAG0B;IAChC;IAEA,IAAII,OAAO;QACT,OAAO,IAAI,CAACzC,SAAS,CAACL,GAAG,CAAC8C,IAAI;IAChC;IAEA,IAAIA,KAAKJ,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC8C,IAAI,GAAGJ;IAC5B;IAEA,IAAIN,SAAS;QACX,OAAO,IAAI,CAAC/B,SAAS,CAACL,GAAG,CAACoC,MAAM;IAClC;IAEA,IAAIA,OAAOM,KAAa,EAAE;QACxB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACoC,MAAM,GAAGM;IAC9B;IAEA,IAAIM,WAAW;QACb,OAAO,IAAI,CAAC3C,SAAS,CAACL,GAAG,CAACgD,QAAQ;IACpC;IAEA,IAAIA,SAASN,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACgD,QAAQ,GAAGN;IAChC;IAEA,IAAIO,WAAW;QACb,OAAO,IAAI,CAAC5C,SAAS,CAACL,GAAG,CAACiD,QAAQ;IACpC;IAEA,IAAIA,SAASP,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACiD,QAAQ,GAAGP;IAChC;IAEA,IAAI7B,WAAW;QACb,OAAO,IAAI,CAACR,SAAS,CAACQ,QAAQ;IAChC;IAEA,IAAIA,SAAS6B,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACQ,QAAQ,GAAG6B,MAAMQ,UAAU,CAAC,OAAOR,QAAQ,CAAC,CAAC,EAAEA,OAAO;IACvE;IAEAS,WAAW;QACT,OAAO,IAAI,CAACN,IAAI;IAClB;IAEAO,SAAS;QACP,OAAO,IAAI,CAACP,IAAI;IAClB;IAEA,CAACvC,OAAO+C,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLR,MAAM,IAAI,CAACA,IAAI;YACfE,QAAQ,IAAI,CAACA,MAAM;YACnBH,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU,IAAI,CAACA,QAAQ;YACvBD,UAAU,IAAI,CAACA,QAAQ;YACvBP,MAAM,IAAI,CAACA,IAAI;YACflB,UAAU,IAAI,CAACA,QAAQ;YACvBoB,MAAM,IAAI,CAACA,IAAI;YACf3B,UAAU,IAAI,CAACA,QAAQ;YACvBoB,QAAQ,IAAI,CAACA,MAAM;YACnBI,cAAc,IAAI,CAACA,YAAY;YAC/BM,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEAQ,QAAQ;QACN,OAAO,IAAI/C,QAAQJ,OAAO,IAAI,GAAG,IAAI,CAACE,SAAS,CAACO,OAAO;IACzD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/web/error.ts"], "sourcesContent": ["export class PageSignatureError extends Error {\n  constructor({ page }: { page: string }) {\n    super(`The middleware \"${page}\" accepts an async API directly with the form:\n  \n  export function middleware(request, event) {\n    return NextResponse.redirect('/new-location')\n  }\n  \n  Read more: https://nextjs.org/docs/messages/middleware-new-signature\n  `)\n  }\n}\n\nexport class RemovedPageError extends Error {\n  constructor() {\n    super(`The request.page has been deprecated in favour of \\`URLPattern\\`.\n  Read more: https://nextjs.org/docs/messages/middleware-request-page\n  `)\n  }\n}\n\nexport class RemovedUAError extends Error {\n  constructor() {\n    super(`The request.ua has been removed in favour of \\`userAgent\\` function.\n  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n  `)\n  }\n}\n"], "names": ["PageSignatureError", "Error", "constructor", "page", "RemovedPageError", "RemovedUAError"], "mappings": ";;;;;AAAO,MAAMA,2BAA2BC;IACtCC,YAAY,EAAEC,IAAI,EAAoB,CAAE;QACtC,KAAK,CAAC,CAAC,gBAAgB,EAAEA,KAAK;;;;;;;EAOhC,CAAC;IACD;AACF;AAEO,MAAMC,yBAAyBH;IACpCC,aAAc;QACZ,KAAK,CAAC,CAAC;;EAET,CAAC;IACD;AACF;AAEO,MAAMG,uBAAuBJ;IAClCC,aAAc;QACZ,KAAK,CAAC,CAAC;;EAET,CAAC;IACD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2531, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/compiled/%40edge-runtime/cookies/index.js"], "sourcesContent": ["\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  RequestCookies: () => RequestCookies,\n  ResponseCookies: () => ResponseCookies,\n  parseCookie: () => parseCookie,\n  parseSetCookie: () => parseSetCookie,\n  stringifyCookie: () => stringifyCookie\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/serialize.ts\nfunction stringifyCookie(c) {\n  var _a;\n  const attrs = [\n    \"path\" in c && c.path && `Path=${c.path}`,\n    \"expires\" in c && (c.expires || c.expires === 0) && `Expires=${(typeof c.expires === \"number\" ? new Date(c.expires) : c.expires).toUTCString()}`,\n    \"maxAge\" in c && typeof c.maxAge === \"number\" && `Max-Age=${c.maxAge}`,\n    \"domain\" in c && c.domain && `Domain=${c.domain}`,\n    \"secure\" in c && c.secure && \"Secure\",\n    \"httpOnly\" in c && c.httpOnly && \"HttpOnly\",\n    \"sameSite\" in c && c.sameSite && `SameSite=${c.sameSite}`,\n    \"partitioned\" in c && c.partitioned && \"Partitioned\",\n    \"priority\" in c && c.priority && `Priority=${c.priority}`\n  ].filter(Boolean);\n  const stringified = `${c.name}=${encodeURIComponent((_a = c.value) != null ? _a : \"\")}`;\n  return attrs.length === 0 ? stringified : `${stringified}; ${attrs.join(\"; \")}`;\n}\nfunction parseCookie(cookie) {\n  const map = /* @__PURE__ */ new Map();\n  for (const pair of cookie.split(/; */)) {\n    if (!pair)\n      continue;\n    const splitAt = pair.indexOf(\"=\");\n    if (splitAt === -1) {\n      map.set(pair, \"true\");\n      continue;\n    }\n    const [key, value] = [pair.slice(0, splitAt), pair.slice(splitAt + 1)];\n    try {\n      map.set(key, decodeURIComponent(value != null ? value : \"true\"));\n    } catch {\n    }\n  }\n  return map;\n}\nfunction parseSetCookie(setCookie) {\n  if (!setCookie) {\n    return void 0;\n  }\n  const [[name, value], ...attributes] = parseCookie(setCookie);\n  const {\n    domain,\n    expires,\n    httponly,\n    maxage,\n    path,\n    samesite,\n    secure,\n    partitioned,\n    priority\n  } = Object.fromEntries(\n    attributes.map(([key, value2]) => [\n      key.toLowerCase().replace(/-/g, \"\"),\n      value2\n    ])\n  );\n  const cookie = {\n    name,\n    value: decodeURIComponent(value),\n    domain,\n    ...expires && { expires: new Date(expires) },\n    ...httponly && { httpOnly: true },\n    ...typeof maxage === \"string\" && { maxAge: Number(maxage) },\n    path,\n    ...samesite && { sameSite: parseSameSite(samesite) },\n    ...secure && { secure: true },\n    ...priority && { priority: parsePriority(priority) },\n    ...partitioned && { partitioned: true }\n  };\n  return compact(cookie);\n}\nfunction compact(t) {\n  const newT = {};\n  for (const key in t) {\n    if (t[key]) {\n      newT[key] = t[key];\n    }\n  }\n  return newT;\n}\nvar SAME_SITE = [\"strict\", \"lax\", \"none\"];\nfunction parseSameSite(string) {\n  string = string.toLowerCase();\n  return SAME_SITE.includes(string) ? string : void 0;\n}\nvar PRIORITY = [\"low\", \"medium\", \"high\"];\nfunction parsePriority(string) {\n  string = string.toLowerCase();\n  return PRIORITY.includes(string) ? string : void 0;\n}\nfunction splitCookiesString(cookiesString) {\n  if (!cookiesString)\n    return [];\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\n// src/request-cookies.ts\nvar RequestCookies = class {\n  constructor(requestHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    this._headers = requestHeaders;\n    const header = requestHeaders.get(\"cookie\");\n    if (header) {\n      const parsed = parseCookie(header);\n      for (const [name, value] of parsed) {\n        this._parsed.set(name, { name, value });\n      }\n    }\n  }\n  [Symbol.iterator]() {\n    return this._parsed[Symbol.iterator]();\n  }\n  /**\n   * The amount of cookies received from the client\n   */\n  get size() {\n    return this._parsed.size;\n  }\n  get(...args) {\n    const name = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(name);\n  }\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed);\n    if (!args.length) {\n      return all.map(([_, value]) => value);\n    }\n    const name = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter(([n]) => n === name).map(([_, value]) => value);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  set(...args) {\n    const [name, value] = args.length === 1 ? [args[0].name, args[0].value] : args;\n    const map = this._parsed;\n    map.set(name, { name, value });\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value2]) => stringifyCookie(value2)).join(\"; \")\n    );\n    return this;\n  }\n  /**\n   * Delete the cookies matching the passed name or names in the request.\n   */\n  delete(names) {\n    const map = this._parsed;\n    const result = !Array.isArray(names) ? map.delete(names) : names.map((name) => map.delete(name));\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value]) => stringifyCookie(value)).join(\"; \")\n    );\n    return result;\n  }\n  /**\n   * Delete all the cookies in the cookies in the request.\n   */\n  clear() {\n    this.delete(Array.from(this._parsed.keys()));\n    return this;\n  }\n  /**\n   * Format the cookies in the request as a string for logging\n   */\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map((v) => `${v.name}=${encodeURIComponent(v.value)}`).join(\"; \");\n  }\n};\n\n// src/response-cookies.ts\nvar ResponseCookies = class {\n  constructor(responseHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    var _a, _b, _c;\n    this._headers = responseHeaders;\n    const setCookie = (_c = (_b = (_a = responseHeaders.getSetCookie) == null ? void 0 : _a.call(responseHeaders)) != null ? _b : responseHeaders.get(\"set-cookie\")) != null ? _c : [];\n    const cookieStrings = Array.isArray(setCookie) ? setCookie : splitCookiesString(setCookie);\n    for (const cookieString of cookieStrings) {\n      const parsed = parseSetCookie(cookieString);\n      if (parsed)\n        this._parsed.set(parsed.name, parsed);\n    }\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-get CookieStore#get} without the Promise.\n   */\n  get(...args) {\n    const key = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(key);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-getAll CookieStore#getAll} without the Promise.\n   */\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed.values());\n    if (!args.length) {\n      return all;\n    }\n    const key = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter((c) => c.name === key);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-set CookieStore#set} without the Promise.\n   */\n  set(...args) {\n    const [name, value, cookie] = args.length === 1 ? [args[0].name, args[0].value, args[0]] : args;\n    const map = this._parsed;\n    map.set(name, normalizeCookie({ name, value, ...cookie }));\n    replace(map, this._headers);\n    return this;\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-delete CookieStore#delete} without the Promise.\n   */\n  delete(...args) {\n    const [name, options] = typeof args[0] === \"string\" ? [args[0]] : [args[0].name, args[0]];\n    return this.set({ ...options, name, value: \"\", expires: /* @__PURE__ */ new Date(0) });\n  }\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map(stringifyCookie).join(\"; \");\n  }\n};\nfunction replace(bag, headers) {\n  headers.delete(\"set-cookie\");\n  for (const [, value] of bag) {\n    const serialized = stringifyCookie(value);\n    headers.append(\"set-cookie\", serialized);\n  }\n}\nfunction normalizeCookie(cookie = { name: \"\", value: \"\" }) {\n  if (typeof cookie.expires === \"number\") {\n    cookie.expires = new Date(cookie.expires);\n  }\n  if (cookie.maxAge) {\n    cookie.expires = new Date(Date.now() + cookie.maxAge * 1e3);\n  }\n  if (cookie.path === null || cookie.path === void 0) {\n    cookie.path = \"/\";\n  }\n  return cookie;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  RequestCookies,\n  ResponseCookies,\n  parseCookie,\n  parseSetCookie,\n  stringifyCookie\n});\n"], "names": [], "mappings": "AACA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ;IACnC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,QAAQ,QAAQ,QACzC,UAAU,IAAI,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACtH;IACA,OAAO;AACT;AACA,IAAI,eAAe,CAAC,MAAQ,YAAY,UAAU,CAAC,GAAG,cAAc;QAAE,OAAO;IAAK,IAAI;AAEtF,eAAe;AACf,IAAI,cAAc,CAAC;AACnB,SAAS,aAAa;IACpB,gBAAgB,IAAM;IACtB,iBAAiB,IAAM;IACvB,aAAa,IAAM;IACnB,gBAAgB,IAAM;IACtB,iBAAiB,IAAM;AACzB;AACA,OAAO,OAAO,GAAG,aAAa;AAE9B,mBAAmB;AACnB,SAAS,gBAAgB,CAAC;IACxB,IAAI;IACJ,MAAM,QAAQ;QACZ,UAAU,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;QACzC,aAAa,KAAK,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,WAAW,IAAI,KAAK,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,WAAW,IAAI;QAChJ,YAAY,KAAK,OAAO,EAAE,MAAM,KAAK,YAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE;QACtE,YAAY,KAAK,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE;QACjD,YAAY,KAAK,EAAE,MAAM,IAAI;QAC7B,cAAc,KAAK,EAAE,QAAQ,IAAI;QACjC,cAAc,KAAK,EAAE,QAAQ,IAAI,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE;QACzD,iBAAiB,KAAK,EAAE,WAAW,IAAI;QACvC,cAAc,KAAK,EAAE,QAAQ,IAAI,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE;KAC1D,CAAC,MAAM,CAAC;IACT,MAAM,cAAc,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,KAAK,KAAK;IACvF,OAAO,MAAM,MAAM,KAAK,IAAI,cAAc,GAAG,YAAY,EAAE,EAAE,MAAM,IAAI,CAAC,OAAO;AACjF;AACA,SAAS,YAAY,MAAM;IACzB,MAAM,MAAM,aAAa,GAAG,IAAI;IAChC,KAAK,MAAM,QAAQ,OAAO,KAAK,CAAC,OAAQ;QACtC,IAAI,CAAC,MACH;QACF,MAAM,UAAU,KAAK,OAAO,CAAC;QAC7B,IAAI,YAAY,CAAC,GAAG;YAClB,IAAI,GAAG,CAAC,MAAM;YACd;QACF;QACA,MAAM,CAAC,KAAK,MAAM,GAAG;YAAC,KAAK,KAAK,CAAC,GAAG;YAAU,KAAK,KAAK,CAAC,UAAU;SAAG;QACtE,IAAI;YACF,IAAI,GAAG,CAAC,KAAK,mBAAmB,SAAS,OAAO,QAAQ;QAC1D,EAAE,OAAM,CACR;IACF;IACA,OAAO;AACT;AACA,SAAS,eAAe,SAAS;IAC/B,IAAI,CAAC,WAAW;QACd,OAAO,KAAK;IACd;IACA,MAAM,CAAC,CAAC,MAAM,MAAM,EAAE,GAAG,WAAW,GAAG,YAAY;IACnD,MAAM,EACJ,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,WAAW,EACX,QAAQ,EACT,GAAG,OAAO,WAAW,CACpB,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,GAAK;YAChC,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM;YAChC;SACD;IAEH,MAAM,SAAS;QACb;QACA,OAAO,mBAAmB;QAC1B;QACA,GAAG,WAAW;YAAE,SAAS,IAAI,KAAK;QAAS,CAAC;QAC5C,GAAG,YAAY;YAAE,UAAU;QAAK,CAAC;QACjC,GAAG,OAAO,WAAW,YAAY;YAAE,QAAQ,OAAO;QAAQ,CAAC;QAC3D;QACA,GAAG,YAAY;YAAE,UAAU,cAAc;QAAU,CAAC;QACpD,GAAG,UAAU;YAAE,QAAQ;QAAK,CAAC;QAC7B,GAAG,YAAY;YAAE,UAAU,cAAc;QAAU,CAAC;QACpD,GAAG,eAAe;YAAE,aAAa;QAAK,CAAC;IACzC;IACA,OAAO,QAAQ;AACjB;AACA,SAAS,QAAQ,CAAC;IAChB,MAAM,OAAO,CAAC;IACd,IAAK,MAAM,OAAO,EAAG;QACnB,IAAI,CAAC,CAAC,IAAI,EAAE;YACV,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;QACpB;IACF;IACA,OAAO;AACT;AACA,IAAI,YAAY;IAAC;IAAU;IAAO;CAAO;AACzC,SAAS,cAAc,MAAM;IAC3B,SAAS,OAAO,WAAW;IAC3B,OAAO,UAAU,QAAQ,CAAC,UAAU,SAAS,KAAK;AACpD;AACA,IAAI,WAAW;IAAC;IAAO;IAAU;CAAO;AACxC,SAAS,cAAc,MAAM;IAC3B,SAAS,OAAO,WAAW;IAC3B,OAAO,SAAS,QAAQ,CAAC,UAAU,SAAS,KAAK;AACnD;AACA,SAAS,mBAAmB,aAAa;IACvC,IAAI,CAAC,eACH,OAAO,EAAE;IACX,IAAI,iBAAiB,EAAE;IACvB,IAAI,MAAM;IACV,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;QACP,MAAO,MAAM,cAAc,MAAM,IAAI,KAAK,IAAI,CAAC,cAAc,MAAM,CAAC,MAAO;YACzE,OAAO;QACT;QACA,OAAO,MAAM,cAAc,MAAM;IACnC;IACA,SAAS;QACP,KAAK,cAAc,MAAM,CAAC;QAC1B,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO;IAC5C;IACA,MAAO,MAAM,cAAc,MAAM,CAAE;QACjC,QAAQ;QACR,wBAAwB;QACxB,MAAO,iBAAkB;YACvB,KAAK,cAAc,MAAM,CAAC;YAC1B,IAAI,OAAO,KAAK;gBACd,YAAY;gBACZ,OAAO;gBACP;gBACA,YAAY;gBACZ,MAAO,MAAM,cAAc,MAAM,IAAI,iBAAkB;oBACrD,OAAO;gBACT;gBACA,IAAI,MAAM,cAAc,MAAM,IAAI,cAAc,MAAM,CAAC,SAAS,KAAK;oBACnE,wBAAwB;oBACxB,MAAM;oBACN,eAAe,IAAI,CAAC,cAAc,SAAS,CAAC,OAAO;oBACnD,QAAQ;gBACV,OAAO;oBACL,MAAM,YAAY;gBACpB;YACF,OAAO;gBACL,OAAO;YACT;QACF;QACA,IAAI,CAAC,yBAAyB,OAAO,cAAc,MAAM,EAAE;YACzD,eAAe,IAAI,CAAC,cAAc,SAAS,CAAC,OAAO,cAAc,MAAM;QACzE;IACF;IACA,OAAO;AACT;AAEA,yBAAyB;AACzB,IAAI,iBAAiB;IACnB,YAAY,cAAc,CAAE;QAC1B,cAAc,GACd,IAAI,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI;QACnC,IAAI,CAAC,QAAQ,GAAG;QAChB,MAAM,SAAS,eAAe,GAAG,CAAC;QAClC,IAAI,QAAQ;YACV,MAAM,SAAS,YAAY;YAC3B,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,OAAQ;gBAClC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;oBAAE;oBAAM;gBAAM;YACvC;QACF;IACF;IACA,CAAC,OAAO,QAAQ,CAAC,GAAG;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC;IACtC;IACA;;GAEC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IAC1B;IACA,IAAI,GAAG,IAAI,EAAE;QACX,MAAM,OAAO,OAAO,IAAI,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B;IACA,OAAO,GAAG,IAAI,EAAE;QACd,IAAI;QACJ,MAAM,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO;QACnC,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK;QACjC;QACA,MAAM,OAAO,OAAO,IAAI,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI;QAC9F,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,GAAK,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK;IAC7D;IACA,IAAI,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B;IACA,IAAI,GAAG,IAAI,EAAE;QACX,MAAM,CAAC,MAAM,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI;YAAC,IAAI,CAAC,EAAE,CAAC,IAAI;YAAE,IAAI,CAAC,EAAE,CAAC,KAAK;SAAC,GAAG;QAC1E,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,IAAI,GAAG,CAAC,MAAM;YAAE;YAAM;QAAM;QAC5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,UACA,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,GAAK,gBAAgB,SAAS,IAAI,CAAC;QAErE,OAAO,IAAI;IACb;IACA;;GAEC,GACD,OAAO,KAAK,EAAE;QACZ,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,MAAM,SAAS,CAAC,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,CAAC,CAAC,OAAS,IAAI,MAAM,CAAC;QAC1F,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,UACA,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,gBAAgB,QAAQ,IAAI,CAAC;QAEnE,OAAO;IACT;IACA;;GAEC,GACD,QAAQ;QACN,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;QACxC,OAAO,IAAI;IACb;IACA;;GAEC,GACD,CAAC,OAAO,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO,CAAC,eAAe,EAAE,KAAK,SAAS,CAAC,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,IAAI;IAC7E;IACA,WAAW;QACT,OAAO;eAAI,IAAI,CAAC,OAAO,CAAC,MAAM;SAAG,CAAC,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,mBAAmB,EAAE,KAAK,GAAG,EAAE,IAAI,CAAC;IAChG;AACF;AAEA,0BAA0B;AAC1B,IAAI,kBAAkB;IACpB,YAAY,eAAe,CAAE;QAC3B,cAAc,GACd,IAAI,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI;QACnC,IAAI,IAAI,IAAI;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,MAAM,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,gBAAgB,YAAY,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,gBAAgB,KAAK,OAAO,KAAK,gBAAgB,GAAG,CAAC,aAAa,KAAK,OAAO,KAAK,EAAE;QAClL,MAAM,gBAAgB,MAAM,OAAO,CAAC,aAAa,YAAY,mBAAmB;QAChF,KAAK,MAAM,gBAAgB,cAAe;YACxC,MAAM,SAAS,eAAe;YAC9B,IAAI,QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE;QAClC;IACF;IACA;;GAEC,GACD,IAAI,GAAG,IAAI,EAAE;QACX,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI;QAChE,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B;IACA;;GAEC,GACD,OAAO,GAAG,IAAI,EAAE;QACd,IAAI;QACJ,MAAM,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;QAC1C,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,OAAO;QACT;QACA,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI;QAC7F,OAAO,IAAI,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;IACtC;IACA,IAAI,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B;IACA;;GAEC,GACD,IAAI,GAAG,IAAI,EAAE;QACX,MAAM,CAAC,MAAM,OAAO,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI;YAAC,IAAI,CAAC,EAAE,CAAC,IAAI;YAAE,IAAI,CAAC,EAAE,CAAC,KAAK;YAAE,IAAI,CAAC,EAAE;SAAC,GAAG;QAC3F,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,IAAI,GAAG,CAAC,MAAM,gBAAgB;YAAE;YAAM;YAAO,GAAG,MAAM;QAAC;QACvD,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC1B,OAAO,IAAI;IACb;IACA;;GAEC,GACD,OAAO,GAAG,IAAI,EAAE;QACd,MAAM,CAAC,MAAM,QAAQ,GAAG,OAAO,IAAI,CAAC,EAAE,KAAK,WAAW;YAAC,IAAI,CAAC,EAAE;SAAC,GAAG;YAAC,IAAI,CAAC,EAAE,CAAC,IAAI;YAAE,IAAI,CAAC,EAAE;SAAC;QACzF,OAAO,IAAI,CAAC,GAAG,CAAC;YAAE,GAAG,OAAO;YAAE;YAAM,OAAO;YAAI,SAAS,aAAa,GAAG,IAAI,KAAK;QAAG;IACtF;IACA,CAAC,OAAO,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO,CAAC,gBAAgB,EAAE,KAAK,SAAS,CAAC,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,IAAI;IAC9E;IACA,WAAW;QACT,OAAO;eAAI,IAAI,CAAC,OAAO,CAAC,MAAM;SAAG,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC;IAC9D;AACF;AACA,SAAS,QAAQ,GAAG,EAAE,OAAO;IAC3B,QAAQ,MAAM,CAAC;IACf,KAAK,MAAM,GAAG,MAAM,IAAI,IAAK;QAC3B,MAAM,aAAa,gBAAgB;QACnC,QAAQ,MAAM,CAAC,cAAc;IAC/B;AACF;AACA,SAAS,gBAAgB,SAAS;IAAE,MAAM;IAAI,OAAO;AAAG,CAAC;IACvD,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;QACtC,OAAO,OAAO,GAAG,IAAI,KAAK,OAAO,OAAO;IAC1C;IACA,IAAI,OAAO,MAAM,EAAE;QACjB,OAAO,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,MAAM,GAAG;IACzD;IACA,IAAI,OAAO,IAAI,KAAK,QAAQ,OAAO,IAAI,KAAK,KAAK,GAAG;QAClD,OAAO,IAAI,GAAG;IAChB;IACA,OAAO;AACT;AACA,6DAA6D;AAC7D,KAAK,CAAC,OAAO,OAAO,GAAG;IACrB;IACA;IACA;IACA;IACA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/web/spec-extension/cookies.ts"], "sourcesContent": ["export {\n  RequestCookies,\n  ResponseCookies,\n  stringifyCookie,\n} from 'next/dist/compiled/@edge-runtime/cookies'\n"], "names": ["RequestCookies", "ResponseCookies", "string<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";AAAA,SACEA,cAAc,EACdC,eAAe,EACfC,eAAe,QACV,2CAA0C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2915, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/web/spec-extension/request.ts"], "sourcesContent": ["import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n\n    validateURL(url)\n\n    // node Request instance requires duplex option when a body\n    // is present or it errors, we don't handle this for\n    // Request being passed in since it would have already\n    // errored if this wasn't configured\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      if (init.body && init.duplex !== 'half') {\n        init.duplex = 'half'\n      }\n    }\n\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n  // see https://github.com/whatwg/fetch/pull/1457\n  duplex?: 'half'\n}\n"], "names": ["NextURL", "toNodeOutgoingHttpHeaders", "validateURL", "RemovedUAError", "RemovedPageError", "RequestCookies", "INTERNALS", "Symbol", "NextRequest", "Request", "constructor", "input", "init", "url", "String", "process", "env", "NEXT_RUNTIME", "body", "duplex", "nextUrl", "headers", "nextConfig", "cookies", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "for", "bodyUsed", "cache", "credentials", "destination", "Object", "fromEntries", "integrity", "keepalive", "method", "mode", "redirect", "referrer", "referrerPolicy", "signal", "page", "ua"], "mappings": ";;;;AACA,SAASA,OAAO,QAAQ,cAAa;AACrC,SAASC,yBAAyB,EAAEC,WAAW,QAAQ,WAAU;AACjE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,WAAU;AAC3D,SAASC,cAAc,QAAQ,YAAW;;;;;;AAEnC,MAAMC,YAAYC,OAAO,oBAAmB;AAO5C,MAAMC,oBAAoBC;IAO/BC,YAAYC,KAAwB,EAAEC,OAAoB,CAAC,CAAC,CAAE;QAC5D,MAAMC,MACJ,OAAOF,UAAU,YAAY,SAASA,QAAQA,MAAME,GAAG,GAAGC,OAAOH;mKAEnET,cAAAA,EAAYW;QAEZ,2DAA2D;QAC3D,oDAAoD;QACpD,sDAAsD;QACtD,oCAAoC;QACpC,IAAIE,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAQ;YACvC,IAAIL,KAAKM,IAAI,IAAIN,KAAKO,MAAM,KAAK,QAAQ;gBACvCP,KAAKO,MAAM,GAAG;YAChB;QACF;QAEA,IAAIR,iBAAiBF,SAAS,KAAK,CAACE,OAAOC;aACtC,KAAK,CAACC,KAAKD;QAEhB,MAAMQ,UAAU,iKAAIpB,UAAAA,CAAQa,KAAK;YAC/BQ,SAASpB,uLAAAA,EAA0B,IAAI,CAACoB,OAAO;YAC/CC,YAAYV,KAAKU,UAAU;QAC7B;QACA,IAAI,CAAChB,UAAU,GAAG;YAChBiB,SAAS,+KAAIlB,iBAAAA,CAAe,IAAI,CAACgB,OAAO;YACxCD;YACAP,KAAKE,QAAQC,GAAG,CAACQ,0BACbX,QAD+C,kBAE/CO,QAAQK,QAAQ;QACtB;IACF;IAEA,CAAClB,OAAOmB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLH,SAAS,IAAI,CAACA,OAAO;YACrBH,SAAS,IAAI,CAACA,OAAO;YACrBP,KAAK,IAAI,CAACA,GAAG;YACb,kCAAkC;YAClCc,UAAU,IAAI,CAACA,QAAQ;YACvBC,OAAO,IAAI,CAACA,KAAK;YACjBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,aAAa,IAAI,CAACA,WAAW;YAC7BT,SAASU,OAAOC,WAAW,CAAC,IAAI,CAACX,OAAO;YACxCY,WAAW,IAAI,CAACA,SAAS;YACzBC,WAAW,IAAI,CAACA,SAAS;YACzBC,QAAQ,IAAI,CAACA,MAAM;YACnBC,MAAM,IAAI,CAACA,IAAI;YACfC,UAAU,IAAI,CAACA,QAAQ;YACvBC,UAAU,IAAI,CAACA,QAAQ;YACvBC,gBAAgB,IAAI,CAACA,cAAc;YACnCC,QAAQ,IAAI,CAACA,MAAM;QACrB;IACF;IAEA,IAAWjB,UAAU;QACnB,OAAO,IAAI,CAACjB,UAAU,CAACiB,OAAO;IAChC;IAEA,IAAWH,UAAU;QACnB,OAAO,IAAI,CAACd,UAAU,CAACc,OAAO;IAChC;IAEA;;;;GAIC,GACD,IAAWqB,OAAO;QAChB,MAAM,2JAAIrC,mBAAAA;IACZ;IAEA;;;;GAIC,GACD,IAAWsC,KAAK;QACd,MAAM,2JAAIvC,iBAAAA;IACZ;IAEA,IAAWU,MAAM;QACf,OAAO,IAAI,CAACP,UAAU,CAACO,GAAG;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/base-http/helpers.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from './'\nimport type { NodeNextRequest, NodeNextResponse } from './node'\nimport type { WebNextRequest, WebNextResponse } from './web'\n\n/**\n * This file provides some helpers that should be used in conjunction with\n * explicit environment checks. When combined with the environment checks, it\n * will ensure that the correct typings are used as well as enable code\n * elimination.\n */\n\n/**\n * Type guard to determine if a request is a WebNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base request is a WebNextRequest.\n */\nexport const isWebNextRequest = (req: BaseNextRequest): req is WebNextRequest =>\n  process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a response is a WebNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base response is a WebNextResponse.\n */\nexport const isWebNextResponse = (\n  res: BaseNextResponse\n): res is WebNextResponse => process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a request is a NodeNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base request is a NodeNextRequest.\n */\nexport const isNodeNextRequest = (\n  req: BaseNextRequest\n): req is NodeNextRequest => process.env.NEXT_RUNTIME !== 'edge'\n\n/**\n * Type guard to determine if a response is a NodeNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base response is a NodeNextResponse.\n */\nexport const isNodeNextResponse = (\n  res: BaseNextResponse\n): res is NodeNextResponse => process.env.NEXT_RUNTIME !== 'edge'\n"], "names": ["isWebNextRequest", "req", "process", "env", "NEXT_RUNTIME", "isWebNextResponse", "res", "isNodeNextRequest", "isNodeNextResponse"], "mappings": "AAIA;;;;;CAK<PERSON>,GAED;;;;;CAKC,GACD;;;;;;AAAO,MAAMA,mBAAmB,CAACC,MAC/BC,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM;AAQ9B,MAAMC,oBAAoB,CAC/BC,MAC2BJ,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM;AAQzD,MAAMG,oBAAoB,CAC/BN,MAC2BC,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM;AAQzD,MAAMI,qBAAqB,CAChCF,MAC4BJ,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3027, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/web/spec-extension/adapters/next-request.ts"], "sourcesContent": ["import type { BaseNextRequest } from '../../../base-http'\nimport type { NodeNextRequest } from '../../../base-http/node'\nimport type { WebNextRequest } from '../../../base-http/web'\nimport type { Writable } from 'node:stream'\n\nimport { getRequestMeta } from '../../../request-meta'\nimport { fromNodeOutgoingHttpHeaders } from '../../utils'\nimport { NextRequest } from '../request'\nimport { isNodeNextRequest, isWebNextRequest } from '../../../base-http/helpers'\n\nexport const ResponseAbortedName = 'ResponseAborted'\nexport class ResponseAborted extends Error {\n  public readonly name = ResponseAbortedName\n}\n\n/**\n * Creates an AbortController tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * If the `close` event is fired before the `finish` event, then we'll send the\n * `abort` signal.\n */\nexport function createAbortController(response: Writable): AbortController {\n  const controller = new AbortController()\n\n  // If `finish` fires first, then `res.end()` has been called and the close is\n  // just us finishing the stream on our side. If `close` fires first, then we\n  // know the client disconnected before we finished.\n  response.once('close', () => {\n    if (response.writableFinished) return\n\n    controller.abort(new ResponseAborted())\n  })\n\n  return controller\n}\n\n/**\n * Creates an AbortSignal tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * This cannot be done with the request (IncomingMessage or Readable) because\n * the `abort` event will not fire if to data has been fully read (because that\n * will \"close\" the readable stream and nothing fires after that).\n */\nexport function signalFromNodeResponse(response: Writable): AbortSignal {\n  const { errored, destroyed } = response\n  if (errored || destroyed) {\n    return AbortSignal.abort(errored ?? new ResponseAborted())\n  }\n\n  const { signal } = createAbortController(response)\n  return signal\n}\n\nexport class NextRequestAdapter {\n  public static fromBaseNextRequest(\n    request: BaseNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromWebNextRequest(request)\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromNodeNextRequest(request, signal)\n    } else {\n      throw new Error('Invariant: Unsupported NextRequest type')\n    }\n  }\n\n  public static fromNodeNextRequest(\n    request: NodeNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: BodyInit | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD' && request.body) {\n      // @ts-expect-error - this is handled by undici, when streams/web land use it instead\n      body = request.body\n    }\n\n    let url: URL\n    if (request.url.startsWith('http')) {\n      url = new URL(request.url)\n    } else {\n      // Grab the full URL from the request metadata.\n      const base = getRequestMeta(request, 'initURL')\n      if (!base || !base.startsWith('http')) {\n        // Because the URL construction relies on the fact that the URL provided\n        // is absolute, we need to provide a base URL. We can't use the request\n        // URL because it's relative, so we use a dummy URL instead.\n        url = new URL(request.url, 'http://n')\n      } else {\n        url = new URL(request.url, base)\n      }\n    }\n\n    return new NextRequest(url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n\n  public static fromWebNextRequest(request: WebNextRequest): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: ReadableStream | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD') {\n      body = request.body\n    }\n\n    return new NextRequest(request.url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal: request.request.signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(request.request.signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n}\n"], "names": ["getRequestMeta", "fromNodeOutgoingHttpHeaders", "NextRequest", "isNodeNextRequest", "isWebNextRequest", "ResponseAbortedName", "ResponseAborted", "Error", "name", "createAbortController", "response", "controller", "AbortController", "once", "writableFinished", "abort", "signalFromNodeResponse", "errored", "destroyed", "AbortSignal", "signal", "NextRequestAdapter", "fromBaseNextRequest", "request", "process", "env", "NEXT_RUNTIME", "fromWebNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "headers", "duplex", "aborted"], "mappings": ";;;;;;;AAKA,SAASA,cAAc,QAAQ,wBAAuB;AACtD,SAASC,2BAA2B,QAAQ,cAAa;AACzD,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,6BAA4B;;;;;AAEzE,MAAMC,sBAAsB,kBAAiB;AAC7C,MAAMC,wBAAwBC;;QAA9B,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AASO,SAASI,sBAAsBC,QAAkB;IACtD,MAAMC,aAAa,IAAIC;IAEvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnDF,SAASG,IAAI,CAAC,SAAS;QACrB,IAAIH,SAASI,gBAAgB,EAAE;QAE/BH,WAAWI,KAAK,CAAC,IAAIT;IACvB;IAEA,OAAOK;AACT;AAUO,SAASK,uBAAuBN,QAAkB;IACvD,MAAM,EAAEO,OAAO,EAAEC,SAAS,EAAE,GAAGR;IAC/B,IAAIO,WAAWC,WAAW;QACxB,OAAOC,YAAYJ,KAAK,CAACE,WAAW,IAAIX;IAC1C;IAEA,MAAM,EAAEc,MAAM,EAAE,GAAGX,sBAAsBC;IACzC,OAAOU;AACT;AAEO,MAAMC;IACX,OAAcC,oBACZC,OAAwB,EACxBH,MAAmB,EACN;QACb,IAEE,AADA,6DAC6D,QADQ;QAErEI,QAAQC,GAAG,CAACC,YAAY,uBAAK,gLAC7BtB,mBAAAA,EAAiBmB,UACjB;;aAEK,IACL,AACA,6DAA6D,QADQ;QAErEC,QAAQC,GAAG,CAACC,YAAY,uBAAK,WAC7BvB,yLAAAA,EAAkBoB,UAClB;YACA,OAAOF,mBAAmBO,mBAAmB,CAACL,SAASH;QACzD,OAAO;YACL,MAAM,OAAA,cAAoD,CAApD,IAAIb,MAAM,4CAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D;IACF;IAEA,OAAcqB,oBACZL,OAAwB,EACxBH,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIS,OAAwB;QAC5B,IAAIN,QAAQO,MAAM,KAAK,SAASP,QAAQO,MAAM,KAAK,UAAUP,QAAQM,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAON,QAAQM,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIR,QAAQQ,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIV,QAAQQ,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,qKAAOlC,iBAAAA,EAAeuB,SAAS;YACrC,IAAI,CAACW,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIV,QAAQQ,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIV,QAAQQ,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,kLAAIhC,cAAAA,CAAY6B,KAAK;YAC1BD,QAAQP,QAAQO,MAAM;YACtBK,UAASlC,wLAAAA,EAA4BsB,QAAQY,OAAO;YACpDC,QAAQ;YACRhB;YACA,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIA,OAAOiB,OAAO,GACd,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;IAEA,OAAcF,mBAAmBJ,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIM,OAA8B;QAClC,IAAIN,QAAQO,MAAM,KAAK,SAASP,QAAQO,MAAM,KAAK,QAAQ;YACzDD,OAAON,QAAQM,IAAI;QACrB;QAEA,OAAO,kLAAI3B,cAAAA,CAAYqB,QAAQQ,GAAG,EAAE;YAClCD,QAAQP,QAAQO,MAAM;YACtBK,oKAASlC,8BAAAA,EAA4BsB,QAAQY,OAAO;YACpDC,QAAQ;YACRhB,QAAQG,QAAQA,OAAO,CAACH,MAAM;YAC9B,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIG,QAAQA,OAAO,CAACH,MAAM,CAACiB,OAAO,GAC9B,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/client-component-renderer-logger.ts"], "sourcesContent": ["import type { AppPageModule } from './route-modules/app-page/module'\n\n// Combined load times for loading client components\nlet clientComponentLoadStart = 0\nlet clientComponentLoadTimes = 0\nlet clientComponentLoadCount = 0\n\nexport function wrapClientComponentLoader(\n  ComponentMod: AppPageModule\n): AppPageModule['__next_app__'] {\n  if (!('performance' in globalThis)) {\n    return ComponentMod.__next_app__\n  }\n\n  return {\n    require: (...args) => {\n      const startTime = performance.now()\n\n      if (clientComponentLoadStart === 0) {\n        clientComponentLoadStart = startTime\n      }\n\n      try {\n        clientComponentLoadCount += 1\n        return ComponentMod.__next_app__.require(...args)\n      } finally {\n        clientComponentLoadTimes += performance.now() - startTime\n      }\n    },\n    loadChunk: (...args) => {\n      const startTime = performance.now()\n      const result = ComponentMod.__next_app__.loadChunk(...args)\n      // Avoid wrapping `loadChunk`'s result in an extra promise in case something like React depends on its identity.\n      // We only need to know when it's settled.\n      result.finally(() => {\n        clientComponentLoadTimes += performance.now() - startTime\n      })\n      return result\n    },\n  }\n}\n\nexport function getClientComponentLoaderMetrics(\n  options: { reset?: boolean } = {}\n) {\n  const metrics =\n    clientComponentLoadStart === 0\n      ? undefined\n      : {\n          clientComponentLoadStart,\n          clientComponentLoadTimes,\n          clientComponentLoadCount,\n        }\n\n  if (options.reset) {\n    clientComponentLoadStart = 0\n    clientComponentLoadTimes = 0\n    clientComponentLoadCount = 0\n  }\n\n  return metrics\n}\n"], "names": ["clientComponentLoadStart", "clientComponentLoadTimes", "clientComponentLoadCount", "wrapClientComponentLoader", "ComponentMod", "globalThis", "__next_app__", "require", "args", "startTime", "performance", "now", "loadChunk", "result", "finally", "getClientComponentLoaderMetrics", "options", "metrics", "undefined", "reset"], "mappings": "AAEA,oDAAoD;;;;;AACpD,IAAIA,2BAA2B;AAC/B,IAAIC,2BAA2B;AAC/B,IAAIC,2BAA2B;AAExB,SAASC,0BACdC,YAA2B;IAE3B,IAAI,CAAE,CAAA,iBAAiBC,UAAS,GAAI;QAClC,OAAOD,aAAaE,YAAY;IAClC;IAEA,OAAO;QACLC,SAAS,CAAC,GAAGC;YACX,MAAMC,YAAYC,YAAYC,GAAG;YAEjC,IAAIX,6BAA6B,GAAG;gBAClCA,2BAA2BS;YAC7B;YAEA,IAAI;gBACFP,4BAA4B;gBAC5B,OAAOE,aAAaE,YAAY,CAACC,OAAO,IAAIC;YAC9C,SAAU;gBACRP,4BAA4BS,YAAYC,GAAG,KAAKF;YAClD;QACF;QACAG,WAAW,CAAC,GAAGJ;YACb,MAAMC,YAAYC,YAAYC,GAAG;YACjC,MAAME,SAAST,aAAaE,YAAY,CAACM,SAAS,IAAIJ;YACtD,gHAAgH;YAChH,0CAA0C;YAC1CK,OAAOC,OAAO,CAAC;gBACbb,4BAA4BS,YAAYC,GAAG,KAAKF;YAClD;YACA,OAAOI;QACT;IACF;AACF;AAEO,SAASE,gCACdC,UAA+B,CAAC,CAAC;IAEjC,MAAMC,UACJjB,6BAA6B,IACzBkB,YACA;QACElB;QACAC;QACAC;IACF;IAEN,IAAIc,QAAQG,KAAK,EAAE;QACjBnB,2BAA2B;QAC3BC,2BAA2B;QAC3BC,2BAA2B;IAC7B;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/pipe-readable.ts"], "sourcesContent": ["import type { ServerResponse } from 'node:http'\n\nimport {\n  ResponseAbortedName,\n  createAbortController,\n} from './web/spec-extension/adapters/next-request'\nimport { DetachedPromise } from '../lib/detached-promise'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { getClientComponentLoaderMetrics } from './client-component-renderer-logger'\n\nexport function isAbortError(e: any): e is Error & { name: 'AbortError' } {\n  return e?.name === 'AbortError' || e?.name === ResponseAbortedName\n}\n\nfunction createWriterFromResponse(\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n): WritableStream<Uint8Array> {\n  let started = false\n\n  // Create a promise that will resolve once the response has drained. See\n  // https://nodejs.org/api/stream.html#stream_event_drain\n  let drained = new DetachedPromise<void>()\n  function onDrain() {\n    drained.resolve()\n  }\n  res.on('drain', onDrain)\n\n  // If the finish event fires, it means we shouldn't block and wait for the\n  // drain event.\n  res.once('close', () => {\n    res.off('drain', onDrain)\n    drained.resolve()\n  })\n\n  // Create a promise that will resolve once the response has finished. See\n  // https://nodejs.org/api/http.html#event-finish_1\n  const finished = new DetachedPromise<void>()\n  res.once('finish', () => {\n    finished.resolve()\n  })\n\n  // Create a writable stream that will write to the response.\n  return new WritableStream<Uint8Array>({\n    write: async (chunk) => {\n      // You'd think we'd want to use `start` instead of placing this in `write`\n      // but this ensures that we don't actually flush the headers until we've\n      // started writing chunks.\n      if (!started) {\n        started = true\n\n        if (\n          'performance' in globalThis &&\n          process.env.NEXT_OTEL_PERFORMANCE_PREFIX\n        ) {\n          const metrics = getClientComponentLoaderMetrics()\n          if (metrics) {\n            performance.measure(\n              `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,\n              {\n                start: metrics.clientComponentLoadStart,\n                end:\n                  metrics.clientComponentLoadStart +\n                  metrics.clientComponentLoadTimes,\n              }\n            )\n          }\n        }\n\n        res.flushHeaders()\n        getTracer().trace(\n          NextNodeServerSpan.startResponse,\n          {\n            spanName: 'start response',\n          },\n          () => undefined\n        )\n      }\n\n      try {\n        const ok = res.write(chunk)\n\n        // Added by the `compression` middleware, this is a function that will\n        // flush the partially-compressed response to the client.\n        if ('flush' in res && typeof res.flush === 'function') {\n          res.flush()\n        }\n\n        // If the write returns false, it means there's some backpressure, so\n        // wait until it's streamed before continuing.\n        if (!ok) {\n          await drained.promise\n\n          // Reset the drained promise so that we can wait for the next drain event.\n          drained = new DetachedPromise<void>()\n        }\n      } catch (err) {\n        res.end()\n        throw new Error('failed to write chunk to response', { cause: err })\n      }\n    },\n    abort: (err) => {\n      if (res.writableFinished) return\n\n      res.destroy(err)\n    },\n    close: async () => {\n      // if a waitUntil promise was passed, wait for it to resolve before\n      // ending the response.\n      if (waitUntilForEnd) {\n        await waitUntilForEnd\n      }\n\n      if (res.writableFinished) return\n\n      res.end()\n      return finished.promise\n    },\n  })\n}\n\nexport async function pipeToNodeResponse(\n  readable: ReadableStream<Uint8Array>,\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n) {\n  try {\n    // If the response has already errored, then just return now.\n    const { errored, destroyed } = res\n    if (errored || destroyed) return\n\n    // Create a new AbortController so that we can abort the readable if the\n    // client disconnects.\n    const controller = createAbortController(res)\n\n    const writer = createWriterFromResponse(res, waitUntilForEnd)\n\n    await readable.pipeTo(writer, { signal: controller.signal })\n  } catch (err: any) {\n    // If this isn't related to an abort error, re-throw it.\n    if (isAbortError(err)) return\n\n    throw new Error('failed to pipe response', { cause: err })\n  }\n}\n"], "names": ["ResponseAbortedName", "createAbortController", "Detached<PERSON>romise", "getTracer", "NextNodeServerSpan", "getClientComponentLoaderMetrics", "isAbortError", "e", "name", "createWriterFromResponse", "res", "waitUntilForEnd", "started", "drained", "onDrain", "resolve", "on", "once", "off", "finished", "WritableStream", "write", "chunk", "globalThis", "process", "env", "NEXT_OTEL_PERFORMANCE_PREFIX", "metrics", "performance", "measure", "start", "clientComponentLoadStart", "end", "clientComponentLoadTimes", "flushHeaders", "trace", "startResponse", "spanName", "undefined", "ok", "flush", "promise", "err", "Error", "cause", "abort", "writableFinished", "destroy", "close", "pipeToNodeResponse", "readable", "errored", "destroyed", "controller", "writer", "pipeTo", "signal"], "mappings": ";;;;AAEA,SACEA,mBAAmB,EACnBC,qBAAqB,QAChB,6CAA4C;AACnD,SAASC,eAAe,QAAQ,0BAAyB;AACzD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,+BAA+B,QAAQ,qCAAoC;;;;;;AAE7E,SAASC,aAAaC,CAAM;IACjC,OAAOA,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAGC,IAAI,MAAK,gBAAgBD,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAGC,IAAI,wMAAKR,sBAAAA;AACjD;AAEA,SAASS,yBACPC,GAAmB,EACnBC,eAAkC;IAElC,IAAIC,UAAU;IAEd,wEAAwE;IACxE,wDAAwD;IACxD,IAAIC,UAAU,+JAAIX,kBAAAA;IAClB,SAASY;QACPD,QAAQE,OAAO;IACjB;IACAL,IAAIM,EAAE,CAAC,SAASF;IAEhB,0EAA0E;IAC1E,eAAe;IACfJ,IAAIO,IAAI,CAAC,SAAS;QAChBP,IAAIQ,GAAG,CAAC,SAASJ;QACjBD,QAAQE,OAAO;IACjB;IAEA,yEAAyE;IACzE,kDAAkD;IAClD,MAAMI,WAAW,+JAAIjB,kBAAAA;IACrBQ,IAAIO,IAAI,CAAC,UAAU;QACjBE,SAASJ,OAAO;IAClB;IAEA,4DAA4D;IAC5D,OAAO,IAAIK,eAA2B;QACpCC,OAAO,OAAOC;YACZ,0EAA0E;YAC1E,wEAAwE;YACxE,0BAA0B;YAC1B,IAAI,CAACV,SAAS;gBACZA,UAAU;gBAEV,IACE,iBAAiBW,cACjBC,QAAQC,GAAG,CAACC,4BAA4B,EACxC;oBACA,MAAMC,kMAAUtB,kCAAAA;oBAChB,IAAIsB,SAAS;wBACXC,YAAYC,OAAO,CACjB,GAAGL,QAAQC,GAAG,CAACC,4BAA4B,CAAC,8BAA8B,CAAC,EAC3E;4BACEI,OAAOH,QAAQI,wBAAwB;4BACvCC,KACEL,QAAQI,wBAAwB,GAChCJ,QAAQM,wBAAwB;wBACpC;oBAEJ;gBACF;gBAEAvB,IAAIwB,YAAY;iBAChB/B,gLAAAA,IAAYgC,KAAK,qKACf/B,qBAAAA,CAAmBgC,aAAa,EAChC;oBACEC,UAAU;gBACZ,GACA,IAAMC;YAEV;YAEA,IAAI;gBACF,MAAMC,KAAK7B,IAAIW,KAAK,CAACC;gBAErB,sEAAsE;gBACtE,yDAAyD;gBACzD,IAAI,WAAWZ,OAAO,OAAOA,IAAI8B,KAAK,KAAK,YAAY;oBACrD9B,IAAI8B,KAAK;gBACX;gBAEA,qEAAqE;gBACrE,8CAA8C;gBAC9C,IAAI,CAACD,IAAI;oBACP,MAAM1B,QAAQ4B,OAAO;oBAErB,0EAA0E;oBAC1E5B,UAAU,+JAAIX,kBAAAA;gBAChB;YACF,EAAE,OAAOwC,KAAK;gBACZhC,IAAIsB,GAAG;gBACP,MAAM,OAAA,cAA8D,CAA9D,IAAIW,MAAM,qCAAqC;oBAAEC,OAAOF;gBAAI,IAA5D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;QACF;QACAG,OAAO,CAACH;YACN,IAAIhC,IAAIoC,gBAAgB,EAAE;YAE1BpC,IAAIqC,OAAO,CAACL;QACd;QACAM,OAAO;YACL,mEAAmE;YACnE,uBAAuB;YACvB,IAAIrC,iBAAiB;gBACnB,MAAMA;YACR;YAEA,IAAID,IAAIoC,gBAAgB,EAAE;YAE1BpC,IAAIsB,GAAG;YACP,OAAOb,SAASsB,OAAO;QACzB;IACF;AACF;AAEO,eAAeQ,mBACpBC,QAAoC,EACpCxC,GAAmB,EACnBC,eAAkC;IAElC,IAAI;QACF,6DAA6D;QAC7D,MAAM,EAAEwC,OAAO,EAAEC,SAAS,EAAE,GAAG1C;QAC/B,IAAIyC,WAAWC,WAAW;QAE1B,wEAAwE;QACxE,sBAAsB;QACtB,MAAMC,aAAapD,8NAAAA,EAAsBS;QAEzC,MAAM4C,SAAS7C,yBAAyBC,KAAKC;QAE7C,MAAMuC,SAASK,MAAM,CAACD,QAAQ;YAAEE,QAAQH,WAAWG,MAAM;QAAC;IAC5D,EAAE,OAAOd,KAAU;QACjB,wDAAwD;QACxD,IAAIpC,aAAaoC,MAAM;QAEvB,MAAM,OAAA,cAAoD,CAApD,IAAIC,MAAM,2BAA2B;YAAEC,OAAOF;QAAI,IAAlD,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/render-result.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders, ServerResponse } from 'http'\nimport type { CacheControl } from './lib/cache-control'\nimport type { FetchMetrics } from './base-http'\n\nimport {\n  chainStreams,\n  streamFromBuffer,\n  streamFromString,\n  streamToBuffer,\n  streamToString,\n} from './stream-utils/node-web-streams-helper'\nimport { isAbortError, pipeToNodeResponse } from './pipe-readable'\nimport type { RenderResumeDataCache } from './resume-data-cache/resume-data-cache'\n\ntype ContentTypeOption = string | undefined\n\nexport type AppPageRenderResultMetadata = {\n  flightData?: Buffer\n  cacheControl?: CacheControl\n  staticBailoutInfo?: {\n    stack?: string\n    description?: string\n  }\n\n  /**\n   * The postponed state if the render had postponed and needs to be resumed.\n   */\n  postponed?: string\n\n  /**\n   * The headers to set on the response that were added by the render.\n   */\n  headers?: OutgoingHttpHeaders\n  statusCode?: number\n  fetchTags?: string\n  fetchMetrics?: FetchMetrics\n\n  segmentData?: Map<string, Buffer>\n\n  /**\n   * In development, the resume data cache is warmed up before the render. This\n   * is attached to the metadata so that it can be used during the render. When\n   * prerendering, the filled resume data cache is also attached to the metadata\n   * so that it can be used when prerendering matching fallback shells.\n   */\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\nexport type PagesRenderResultMetadata = {\n  pageData?: any\n  cacheControl?: CacheControl\n  assetQueryString?: string\n  isNotFound?: boolean\n  isRedirect?: boolean\n}\n\nexport type StaticRenderResultMetadata = {}\n\nexport type RenderResultMetadata = AppPageRenderResultMetadata &\n  PagesRenderResultMetadata &\n  StaticRenderResultMetadata\n\nexport type RenderResultResponse =\n  | ReadableStream<Uint8Array>[]\n  | ReadableStream<Uint8Array>\n  | string\n  | Buffer\n  | null\n\nexport type RenderResultOptions<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> = {\n  contentType?: ContentTypeOption\n  waitUntil?: Promise<unknown>\n  metadata: Metadata\n}\n\nexport default class RenderResult<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> {\n  /**\n   * The detected content type for the response. This is used to set the\n   * `Content-Type` header.\n   */\n  public readonly contentType: ContentTypeOption\n\n  /**\n   * The metadata for the response. This is used to set the revalidation times\n   * and other metadata.\n   */\n  public readonly metadata: Readonly<Metadata>\n\n  /**\n   * The response itself. This can be a string, a stream, or null. If it's a\n   * string, then it's a static response. If it's a stream, then it's a\n   * dynamic response. If it's null, then the response was not found or was\n   * already sent.\n   */\n  private response: RenderResultResponse\n\n  /**\n   * Creates a new RenderResult instance from a static response.\n   *\n   * @param value the static response value\n   * @returns a new RenderResult instance\n   */\n  public static fromStatic(value: string | Buffer) {\n    return new RenderResult<StaticRenderResultMetadata>(value, { metadata: {} })\n  }\n\n  private readonly waitUntil?: Promise<unknown>\n\n  constructor(\n    response: RenderResultResponse,\n    { contentType, waitUntil, metadata }: RenderResultOptions<Metadata>\n  ) {\n    this.response = response\n    this.contentType = contentType\n    this.metadata = metadata\n    this.waitUntil = waitUntil\n  }\n\n  public assignMetadata(metadata: Metadata) {\n    Object.assign(this.metadata, metadata)\n  }\n\n  /**\n   * Returns true if the response is null. It can be null if the response was\n   * not found or was already sent.\n   */\n  public get isNull(): boolean {\n    return this.response === null\n  }\n\n  /**\n   * Returns false if the response is a string. It can be a string if the page\n   * was prerendered. If it's not, then it was generated dynamically.\n   */\n  public get isDynamic(): boolean {\n    return typeof this.response !== 'string'\n  }\n\n  public toUnchunkedBuffer(stream?: false): Buffer\n  public toUnchunkedBuffer(stream: true): Promise<Buffer>\n  public toUnchunkedBuffer(stream = false): Promise<Buffer> | Buffer {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToBuffer(this.readable)\n    }\n\n    return Buffer.from(this.response)\n  }\n\n  /**\n   * Returns the response if it is a string. If the page was dynamic, this will\n   * return a promise if the `stream` option is true, or it will throw an error.\n   *\n   * @param stream Whether or not to return a promise if the response is dynamic\n   * @returns The response as a string\n   */\n  public toUnchunkedString(stream?: false): string\n  public toUnchunkedString(stream: true): Promise<string>\n  public toUnchunkedString(stream = false): Promise<string> | string {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToString(this.readable)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Returns the response if it is a stream, or throws an error if it is a\n   * string.\n   */\n  private get readable(): ReadableStream<Uint8Array> {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be streamed')\n    }\n    if (typeof this.response === 'string') {\n      throw new Error('Invariant: static responses cannot be streamed')\n    }\n\n    if (Buffer.isBuffer(this.response)) {\n      return streamFromBuffer(this.response)\n    }\n\n    // If the response is an array of streams, then chain them together.\n    if (Array.isArray(this.response)) {\n      return chainStreams(...this.response)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Chains a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the end. When this response is piped, all of the streams will be piped\n   * one after the other.\n   *\n   * @param readable The new stream to chain\n   */\n  public chain(readable: ReadableStream<Uint8Array>) {\n    if (this.response === null) {\n      throw new Error('Invariant: response is null. This is a bug in Next.js')\n    }\n\n    // If the response is not an array of streams already, make it one.\n    let responses: ReadableStream<Uint8Array>[]\n    if (typeof this.response === 'string') {\n      responses = [streamFromString(this.response)]\n    } else if (Array.isArray(this.response)) {\n      responses = this.response\n    } else if (Buffer.isBuffer(this.response)) {\n      responses = [streamFromBuffer(this.response)]\n    } else {\n      responses = [this.response]\n    }\n\n    // Add the new stream to the array.\n    responses.push(readable)\n\n    // Update the response.\n    this.response = responses\n  }\n\n  /**\n   * Pipes the response to a writable stream. This will close/cancel the\n   * writable stream if an error is encountered. If this doesn't throw, then\n   * the writable stream will be closed or aborted.\n   *\n   * @param writable Writable stream to pipe the response to\n   */\n  public async pipeTo(writable: WritableStream<Uint8Array>): Promise<void> {\n    try {\n      await this.readable.pipeTo(writable, {\n        // We want to close the writable stream ourselves so that we can wait\n        // for the waitUntil promise to resolve before closing it. If an error\n        // is encountered, we'll abort the writable stream if we swallowed the\n        // error.\n        preventClose: true,\n      })\n\n      // If there is a waitUntil promise, wait for it to resolve before\n      // closing the writable stream.\n      if (this.waitUntil) await this.waitUntil\n\n      // Close the writable stream.\n      await writable.close()\n    } catch (err) {\n      // If this is an abort error, we should abort the writable stream (as we\n      // took ownership of it when we started piping). We don't need to re-throw\n      // because we handled the error.\n      if (isAbortError(err)) {\n        // Abort the writable stream if an error is encountered.\n        await writable.abort(err)\n\n        return\n      }\n\n      // We're not aborting the writer here as when this method throws it's not\n      // clear as to how so the caller should assume it's their responsibility\n      // to clean up the writer.\n      throw err\n    }\n  }\n\n  /**\n   * Pipes the response to a node response. This will close/cancel the node\n   * response if an error is encountered.\n   *\n   * @param res\n   */\n  public async pipeToNodeResponse(res: ServerResponse) {\n    await pipeToNodeResponse(this.readable, res, this.waitUntil)\n  }\n}\n"], "names": ["chainStreams", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "isAbortError", "pipeToNodeResponse", "RenderResult", "fromStatic", "value", "metadata", "constructor", "response", "contentType", "waitUntil", "assignMetadata", "Object", "assign", "isNull", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stream", "Error", "readable", "<PERSON><PERSON><PERSON>", "from", "toUnchunkedString", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "chain", "responses", "push", "pipeTo", "writable", "preventClose", "close", "err", "abort", "res"], "mappings": ";;;AAIA,SACEA,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,QACT,yCAAwC;AAC/C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;;;AAkEnD,MAAMC;IAuBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAsB,EAAE;QAC/C,OAAO,IAAIF,aAAyCE,OAAO;YAAEC,UAAU,CAAC;QAAE;IAC5E;IAIAC,YACEC,QAA8B,EAC9B,EAAEC,WAAW,EAAEC,SAAS,EAAEJ,QAAQ,EAAiC,CACnE;QACA,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACI,SAAS,GAAGA;IACnB;IAEOC,eAAeL,QAAkB,EAAE;QACxCM,OAAOC,MAAM,CAAC,IAAI,CAACP,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWQ,SAAkB;QAC3B,OAAO,IAAI,CAACN,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWO,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACP,QAAQ,KAAK;IAClC;IAIOQ,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOnB,mNAAAA,EAAe,IAAI,CAACoB,QAAQ;QACrC;QAEA,OAAOC,OAAOC,IAAI,CAAC,IAAI,CAACb,QAAQ;IAClC;IAWOc,kBAAkBL,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,WAAOlB,+MAAAA,EAAe,IAAI,CAACmB,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACX,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYW,WAAuC;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAAyD,CAAzD,IAAIU,MAAM,iDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwD;QAChE;QACA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,MAAM,OAAA,cAA2D,CAA3D,IAAIU,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,IAAIE,OAAOG,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YAClC,yMAAOX,mBAAAA,EAAiB,IAAI,CAACW,QAAQ;QACvC;QAEA,oEAAoE;QACpE,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YAChC,yMAAOZ,eAAAA,KAAgB,IAAI,CAACY,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACMkB,MAAMP,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAAkE,CAAlE,IAAIU,MAAM,0DAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiE;QACzE;QAEA,mEAAmE;QACnE,IAAIS;QACJ,IAAI,OAAO,IAAI,CAACnB,QAAQ,KAAK,UAAU;YACrCmB,YAAY;iBAAC7B,oNAAAA,EAAiB,IAAI,CAACU,QAAQ;aAAE;QAC/C,OAAO,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YACvCmB,YAAY,IAAI,CAACnB,QAAQ;QAC3B,OAAO,IAAIY,OAAOG,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YACzCmB,YAAY;kNAAC9B,mBAAAA,EAAiB,IAAI,CAACW,QAAQ;aAAE;QAC/C,OAAO;YACLmB,YAAY;gBAAC,IAAI,CAACnB,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCmB,UAAUC,IAAI,CAACT;QAEf,uBAAuB;QACvB,IAAI,CAACX,QAAQ,GAAGmB;IAClB;IAEA;;;;;;GAMC,GACD,MAAaE,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACX,QAAQ,CAACU,MAAM,CAACC,UAAU;gBACnC,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,SAAS;gBACTC,cAAc;YAChB;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,IAAI,IAAI,CAACrB,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS;YAExC,6BAA6B;YAC7B,MAAMoB,SAASE,KAAK;QACtB,EAAE,OAAOC,KAAK;YACZ,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC,KAAIhC,6KAAAA,EAAagC,MAAM;gBACrB,wDAAwD;gBACxD,MAAMH,SAASI,KAAK,CAACD;gBAErB;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,MAAMA;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAa/B,mBAAmBiC,GAAmB,EAAE;QACnD,qKAAMjC,qBAAAA,EAAmB,IAAI,CAACiB,QAAQ,EAAEgB,KAAK,IAAI,CAACzB,SAAS;IAC7D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3520, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/response-cache/utils.ts"], "sourcesContent": ["import {\n  CachedRouteK<PERSON>,\n  IncrementalCacheKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type IncrementalResponseCacheEntry,\n  type ResponseCacheEntry,\n} from './types'\n\nimport RenderResult from '../render-result'\nimport { RouteKind } from '../route-kind'\n\nexport async function fromResponseCacheEntry(\n  cacheEntry: ResponseCacheEntry\n): Promise<IncrementalResponseCacheEntry> {\n  return {\n    ...cacheEntry,\n    value:\n      cacheEntry.value?.kind === CachedRouteKind.PAGES\n        ? {\n            kind: CachedRouteKind.PAGES,\n            html: await cacheEntry.value.html.toUnchunkedString(true),\n            pageData: cacheEntry.value.pageData,\n            headers: cacheEntry.value.headers,\n            status: cacheEntry.value.status,\n          }\n        : cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n          ? {\n              kind: CachedRouteKind.APP_PAGE,\n              html: await cacheEntry.value.html.toUnchunkedString(true),\n              postponed: cacheEntry.value.postponed,\n              rscData: cacheEntry.value.rscData,\n              headers: cacheEntry.value.headers,\n              status: cacheEntry.value.status,\n              segmentData: cacheEntry.value.segmentData,\n            }\n          : cacheEntry.value,\n  }\n}\n\nexport async function toResponseCacheEntry(\n  response: IncrementalResponseCacheEntry | null\n): Promise<ResponseCacheEntry | null> {\n  if (!response) return null\n\n  return {\n    isMiss: response.isMiss,\n    isStale: response.isStale,\n    cacheControl: response.cacheControl,\n    value:\n      response.value?.kind === CachedRouteKind.PAGES\n        ? ({\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(response.value.html),\n            pageData: response.value.pageData,\n            headers: response.value.headers,\n            status: response.value.status,\n          } satisfies CachedPageValue)\n        : response.value?.kind === CachedRouteKind.APP_PAGE\n          ? ({\n              kind: CachedRouteKind.APP_PAGE,\n              html: RenderResult.fromStatic(response.value.html),\n              rscData: response.value.rscData,\n              headers: response.value.headers,\n              status: response.value.status,\n              postponed: response.value.postponed,\n              segmentData: response.value.segmentData,\n            } satisfies CachedAppPageValue)\n          : response.value,\n  }\n}\n\nexport function routeKindToIncrementalCacheKind(\n  routeKind: RouteKind\n): Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH> {\n  switch (routeKind) {\n    case RouteKind.PAGES:\n      return IncrementalCacheKind.PAGES\n    case RouteKind.APP_PAGE:\n      return IncrementalCacheKind.APP_PAGE\n    case RouteKind.IMAGE:\n      return IncrementalCacheKind.IMAGE\n    case RouteKind.APP_ROUTE:\n      return IncrementalCacheKind.APP_ROUTE\n    default:\n      throw new Error(`Unexpected route kind ${routeKind}`)\n  }\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind", "RenderResult", "RouteKind", "fromResponseCacheEntry", "cacheEntry", "value", "kind", "PAGES", "html", "toUnchunkedString", "pageData", "headers", "status", "APP_PAGE", "postponed", "rscData", "segmentData", "toResponseCacheEntry", "response", "isMiss", "isStale", "cacheControl", "fromStatic", "routeKindToIncrementalCacheKind", "routeKind", "IMAGE", "APP_ROUTE", "Error"], "mappings": ";;;;;AAAA,SACEA,eAAe,EACfC,oBAAoB,QAKf,UAAS;AAEhB,OAAOC,kBAAkB,mBAAkB;AAC3C,SAASC,SAAS,QAAQ,gBAAe;;;;AAElC,eAAeC,uBACpBC,UAA8B;QAK1BA,mBAQIA;IAXR,OAAO;QACL,GAAGA,UAAU;QACbC,OACED,CAAAA,CAAAA,oBAAAA,WAAWC,KAAK,KAAA,OAAA,KAAA,IAAhBD,kBAAkBE,IAAI,2KAAKP,kBAAAA,CAAgBQ,KAAK,GAC5C;YACED,2KAAMP,kBAAAA,CAAgBQ,KAAK;YAC3BC,MAAM,MAAMJ,WAAWC,KAAK,CAACG,IAAI,CAACC,iBAAiB,CAAC;YACpDC,UAAUN,WAAWC,KAAK,CAACK,QAAQ;YACnCC,SAASP,WAAWC,KAAK,CAACM,OAAO;YACjCC,QAAQR,WAAWC,KAAK,CAACO,MAAM;QACjC,IACAR,CAAAA,CAAAA,qBAAAA,WAAWC,KAAK,KAAA,OAAA,KAAA,IAAhBD,mBAAkBE,IAAI,2KAAKP,kBAAAA,CAAgBc,QAAQ,GACjD;YACEP,2KAAMP,kBAAAA,CAAgBc,QAAQ;YAC9BL,MAAM,MAAMJ,WAAWC,KAAK,CAACG,IAAI,CAACC,iBAAiB,CAAC;YACpDK,WAAWV,WAAWC,KAAK,CAACS,SAAS;YACrCC,SAASX,WAAWC,KAAK,CAACU,OAAO;YACjCJ,SAASP,WAAWC,KAAK,CAACM,OAAO;YACjCC,QAAQR,WAAWC,KAAK,CAACO,MAAM;YAC/BI,aAAaZ,WAAWC,KAAK,CAACW,WAAW;QAC3C,IACAZ,WAAWC,KAAK;IAC1B;AACF;AAEO,eAAeY,qBACpBC,QAA8C;QAS1CA,iBAQIA;IAfR,IAAI,CAACA,UAAU,OAAO;IAEtB,OAAO;QACLC,QAAQD,SAASC,MAAM;QACvBC,SAASF,SAASE,OAAO;QACzBC,cAAcH,SAASG,YAAY;QACnChB,OACEa,CAAAA,CAAAA,kBAAAA,SAASb,KAAK,KAAA,OAAA,KAAA,IAAda,gBAAgBZ,IAAI,2KAAKP,kBAAAA,CAAgBQ,KAAK,GACzC;YACCD,2KAAMP,kBAAAA,CAAgBQ,KAAK;YAC3BC,iKAAMP,UAAAA,CAAaqB,UAAU,CAACJ,SAASb,KAAK,CAACG,IAAI;YACjDE,UAAUQ,SAASb,KAAK,CAACK,QAAQ;YACjCC,SAASO,SAASb,KAAK,CAACM,OAAO;YAC/BC,QAAQM,SAASb,KAAK,CAACO,MAAM;QAC/B,IACAM,CAAAA,CAAAA,mBAAAA,SAASb,KAAK,KAAA,OAAA,KAAA,IAAda,iBAAgBZ,IAAI,2KAAKP,kBAAAA,CAAgBc,QAAQ,GAC9C;YACCP,2KAAMP,kBAAAA,CAAgBc,QAAQ;YAC9BL,iKAAMP,UAAAA,CAAaqB,UAAU,CAACJ,SAASb,KAAK,CAACG,IAAI;YACjDO,SAASG,SAASb,KAAK,CAACU,OAAO;YAC/BJ,SAASO,SAASb,KAAK,CAACM,OAAO;YAC/BC,QAAQM,SAASb,KAAK,CAACO,MAAM;YAC7BE,WAAWI,SAASb,KAAK,CAACS,SAAS;YACnCE,aAAaE,SAASb,KAAK,CAACW,WAAW;QACzC,IACAE,SAASb,KAAK;IACxB;AACF;AAEO,SAASkB,gCACdC,SAAoB;IAEpB,OAAQA;QACN,6JAAKtB,YAAAA,CAAUK,KAAK;YAClB,4KAAOP,uBAAAA,CAAqBO,KAAK;QACnC,6JAAKL,YAAAA,CAAUW,QAAQ;YACrB,4KAAOb,uBAAAA,CAAqBa,QAAQ;QACtC,6JAAKX,YAAAA,CAAUuB,KAAK;YAClB,4KAAOzB,uBAAAA,CAAqByB,KAAK;QACnC,6JAAKvB,YAAAA,CAAUwB,SAAS;YACtB,4KAAO1B,uBAAAA,CAAqB0B,SAAS;QACvC;YACE,MAAM,OAAA,cAA+C,CAA/C,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,WAAW,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/response-cache/index.ts"], "sourcesContent": ["import type {\n  Response<PERSON>acheEntry,\n  ResponseGenerator,\n  ResponseCacheBase,\n  IncrementalResponseCacheEntry,\n  IncrementalResponseCache,\n} from './types'\n\nimport { Batcher } from '../../lib/batcher'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport {\n  fromResponseCacheEntry,\n  routeKindToIncrementalCacheKind,\n  toResponseCacheEntry,\n} from './utils'\nimport type { RouteK<PERSON> } from '../route-kind'\n\nexport * from './types'\n\nexport default class ResponseCache implements ResponseCacheBase {\n  private readonly batcher = Batcher.create<\n    { key: string; isOnDemandRevalidate: boolean },\n    IncrementalResponseCacheEntry | null,\n    string\n  >({\n    // Ensure on-demand revalidate doesn't block normal requests, it should be\n    // safe to run an on-demand revalidate for the same key as a normal request.\n    cacheKeyFn: ({ key, isOnDemandRevalidate }) =>\n      `${key}-${isOnDemandRevalidate ? '1' : '0'}`,\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  private previousCacheItem?: {\n    key: string\n    entry: IncrementalResponseCacheEntry | null\n    expiresAt: number\n  }\n\n  // we don't use minimal_mode name here as this.minimal_mode is\n  // statically replace for server runtimes but we need it to\n  // be dynamic here\n  private minimal_mode?: boolean\n\n  constructor(minimal_mode: boolean) {\n    this.minimal_mode = minimal_mode\n  }\n\n  public async get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      routeKind: RouteKind\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalResponseCache\n      isRoutePPREnabled?: boolean\n      isFallback?: boolean\n      waitUntil?: (prom: Promise<any>) => void\n    }\n  ): Promise<ResponseCacheEntry | null> {\n    // If there is no key for the cache, we can't possibly look this up in the\n    // cache so just return the result of the response generator.\n    if (!key) {\n      return responseGenerator({ hasResolved: false, previousCacheEntry: null })\n    }\n\n    const {\n      incrementalCache,\n      isOnDemandRevalidate = false,\n      isFallback = false,\n      isRoutePPREnabled = false,\n      waitUntil,\n    } = context\n\n    const response = await this.batcher.batch(\n      { key, isOnDemandRevalidate },\n      (cacheKey, resolve) => {\n        const prom = (async () => {\n          // We keep the previous cache entry around to leverage when the\n          // incremental cache is disabled in minimal mode.\n          if (\n            this.minimal_mode &&\n            this.previousCacheItem?.key === cacheKey &&\n            this.previousCacheItem.expiresAt > Date.now()\n          ) {\n            return this.previousCacheItem.entry\n          }\n\n          // Coerce the kindHint into a given kind for the incremental cache.\n          const kind = routeKindToIncrementalCacheKind(context.routeKind)\n\n          let resolved = false\n          let cachedResponse: IncrementalResponseCacheEntry | null = null\n          try {\n            cachedResponse = !this.minimal_mode\n              ? await incrementalCache.get(key, {\n                  kind,\n                  isRoutePPREnabled: context.isRoutePPREnabled,\n                  isFallback,\n                })\n              : null\n\n            if (cachedResponse && !isOnDemandRevalidate) {\n              resolve(cachedResponse)\n              resolved = true\n\n              if (!cachedResponse.isStale || context.isPrefetch) {\n                // The cached value is still valid, so we don't need\n                // to update it yet.\n                return null\n              }\n            }\n\n            const cacheEntry = await responseGenerator({\n              hasResolved: resolved,\n              previousCacheEntry: cachedResponse,\n              isRevalidating: true,\n            })\n\n            // If the cache entry couldn't be generated, we don't want to cache\n            // the result.\n            if (!cacheEntry) {\n              // Unset the previous cache item if it was set.\n              if (this.minimal_mode) this.previousCacheItem = undefined\n              return null\n            }\n\n            const resolveValue = await fromResponseCacheEntry({\n              ...cacheEntry,\n              isMiss: !cachedResponse,\n            })\n            if (!resolveValue) {\n              // Unset the previous cache item if it was set.\n              if (this.minimal_mode) this.previousCacheItem = undefined\n              return null\n            }\n\n            // For on-demand revalidate wait to resolve until cache is set.\n            // Otherwise resolve now.\n            if (!isOnDemandRevalidate && !resolved) {\n              resolve(resolveValue)\n              resolved = true\n            }\n\n            // We want to persist the result only if it has a cache control value\n            // defined.\n            if (resolveValue.cacheControl) {\n              if (this.minimal_mode) {\n                this.previousCacheItem = {\n                  key: cacheKey,\n                  entry: resolveValue,\n                  expiresAt: Date.now() + 1000,\n                }\n              } else {\n                await incrementalCache.set(key, resolveValue.value, {\n                  cacheControl: resolveValue.cacheControl,\n                  isRoutePPREnabled,\n                  isFallback,\n                })\n              }\n            }\n\n            return resolveValue\n          } catch (err) {\n            // When a path is erroring we automatically re-set the existing cache\n            // with new revalidate and expire times to prevent non-stop retrying.\n            if (cachedResponse?.cacheControl) {\n              const newRevalidate = Math.min(\n                Math.max(cachedResponse.cacheControl.revalidate || 3, 3),\n                30\n              )\n\n              const newExpire =\n                cachedResponse.cacheControl.expire === undefined\n                  ? undefined\n                  : Math.max(\n                      newRevalidate + 3,\n                      cachedResponse.cacheControl.expire\n                    )\n\n              await incrementalCache.set(key, cachedResponse.value, {\n                cacheControl: { revalidate: newRevalidate, expire: newExpire },\n                isRoutePPREnabled,\n                isFallback,\n              })\n            }\n\n            // While revalidating in the background we can't reject as we already\n            // resolved the cache entry so log the error here.\n            if (resolved) {\n              console.error(err)\n              return null\n            }\n\n            // We haven't resolved yet, so let's throw to indicate an error.\n            throw err\n          }\n        })()\n\n        // we need to ensure background revalidates are\n        // passed to waitUntil\n        if (waitUntil) {\n          waitUntil(prom)\n        }\n        return prom\n      }\n    )\n\n    return toResponseCacheEntry(response)\n  }\n}\n"], "names": ["<PERSON><PERSON>", "scheduleOnNextTick", "fromResponseCacheEntry", "routeKindToIncrementalCacheKind", "toResponseCacheEntry", "ResponseCache", "constructor", "minimal_mode", "batcher", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "get", "responseGenerator", "context", "hasResolved", "previousCacheEntry", "incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "waitUntil", "response", "batch", "cache<PERSON>ey", "resolve", "prom", "previousCacheItem", "expiresAt", "Date", "now", "entry", "kind", "routeKind", "resolved", "cachedResponse", "isStale", "isPrefetch", "cacheEntry", "isRevalidating", "undefined", "resolveValue", "isMiss", "cacheControl", "set", "value", "err", "newRevalidate", "Math", "min", "max", "revalidate", "newExpire", "expire", "console", "error"], "mappings": ";;;AAQA,SAASA,OAAO,QAAQ,oBAAmB;AAC3C,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SACEC,sBAAsB,EACtBC,+BAA+B,EAC/BC,oBAAoB,QACf,UAAS;AAGhB,cAAc,UAAS;;;;;AAER,MAAMC;IA2BnBC,YAAYC,YAAqB,CAAE;aA1BlBC,OAAAA,kJAAUR,UAAAA,CAAQS,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,GAAGD,IAAI,CAAC,EAAEC,uBAAuB,MAAM,KAAK;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,8JAAaZ,qBAAAA;QACf;QAcE,IAAI,CAACM,YAAY,GAAGA;IACtB;IAEA,MAAaO,IACXH,GAAkB,EAClBI,iBAAoC,EACpCC,OAQC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACL,KAAK;YACR,OAAOI,kBAAkB;gBAAEE,aAAa;gBAAOC,oBAAoB;YAAK;QAC1E;QAEA,MAAM,EACJC,gBAAgB,EAChBP,uBAAuB,KAAK,EAC5BQ,aAAa,KAAK,EAClBC,oBAAoB,KAAK,EACzBC,SAAS,EACV,GAAGN;QAEJ,MAAMO,WAAW,MAAM,IAAI,CAACf,OAAO,CAACgB,KAAK,CACvC;YAAEb;YAAKC;QAAqB,GAC5B,CAACa,UAAUC;YACT,MAAMC,OAAQ,CAAA;oBAKV;gBAJF,+DAA+D;gBAC/D,iDAAiD;gBACjD,IACE,IAAI,CAACpB,YAAY,IACjB,CAAA,CAAA,0BAAA,IAAI,CAACqB,iBAAiB,KAAA,OAAA,KAAA,IAAtB,wBAAwBjB,GAAG,MAAKc,YAChC,IAAI,CAACG,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;oBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;gBACrC;gBAEA,mEAAmE;gBACnE,MAAMC,+KAAO9B,mCAAAA,EAAgCa,QAAQkB,SAAS;gBAE9D,IAAIC,WAAW;gBACf,IAAIC,iBAAuD;gBAC3D,IAAI;oBACFA,iBAAiB,CAAC,IAAI,CAAC7B,YAAY,GAC/B,MAAMY,iBAAiBL,GAAG,CAACH,KAAK;wBAC9BsB;wBACAZ,mBAAmBL,QAAQK,iBAAiB;wBAC5CD;oBACF,KACA;oBAEJ,IAAIgB,kBAAkB,CAACxB,sBAAsB;wBAC3Cc,QAAQU;wBACRD,WAAW;wBAEX,IAAI,CAACC,eAAeC,OAAO,IAAIrB,QAAQsB,UAAU,EAAE;4BACjD,oDAAoD;4BACpD,oBAAoB;4BACpB,OAAO;wBACT;oBACF;oBAEA,MAAMC,aAAa,MAAMxB,kBAAkB;wBACzCE,aAAakB;wBACbjB,oBAAoBkB;wBACpBI,gBAAgB;oBAClB;oBAEA,mEAAmE;oBACnE,cAAc;oBACd,IAAI,CAACD,YAAY;wBACf,+CAA+C;wBAC/C,IAAI,IAAI,CAAChC,YAAY,EAAE,IAAI,CAACqB,iBAAiB,GAAGa;wBAChD,OAAO;oBACT;oBAEA,MAAMC,eAAe,MAAMxC,kMAAAA,EAAuB;wBAChD,GAAGqC,UAAU;wBACbI,QAAQ,CAACP;oBACX;oBACA,IAAI,CAACM,cAAc;wBACjB,+CAA+C;wBAC/C,IAAI,IAAI,CAACnC,YAAY,EAAE,IAAI,CAACqB,iBAAiB,GAAGa;wBAChD,OAAO;oBACT;oBAEA,+DAA+D;oBAC/D,yBAAyB;oBACzB,IAAI,CAAC7B,wBAAwB,CAACuB,UAAU;wBACtCT,QAAQgB;wBACRP,WAAW;oBACb;oBAEA,qEAAqE;oBACrE,WAAW;oBACX,IAAIO,aAAaE,YAAY,EAAE;wBAC7B,IAAI,IAAI,CAACrC,YAAY,EAAE;4BACrB,IAAI,CAACqB,iBAAiB,GAAG;gCACvBjB,KAAKc;gCACLO,OAAOU;gCACPb,WAAWC,KAAKC,GAAG,KAAK;4BAC1B;wBACF,OAAO;4BACL,MAAMZ,iBAAiB0B,GAAG,CAAClC,KAAK+B,aAAaI,KAAK,EAAE;gCAClDF,cAAcF,aAAaE,YAAY;gCACvCvB;gCACAD;4BACF;wBACF;oBACF;oBAEA,OAAOsB;gBACT,EAAE,OAAOK,KAAK;oBACZ,qEAAqE;oBACrE,qEAAqE;oBACrE,IAAIX,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBQ,YAAY,EAAE;wBAChC,MAAMI,gBAAgBC,KAAKC,GAAG,CAC5BD,KAAKE,GAAG,CAACf,eAAeQ,YAAY,CAACQ,UAAU,IAAI,GAAG,IACtD;wBAGF,MAAMC,YACJjB,eAAeQ,YAAY,CAACU,MAAM,KAAKb,YACnCA,YACAQ,KAAKE,GAAG,CACNH,gBAAgB,GAChBZ,eAAeQ,YAAY,CAACU,MAAM;wBAG1C,MAAMnC,iBAAiB0B,GAAG,CAAClC,KAAKyB,eAAeU,KAAK,EAAE;4BACpDF,cAAc;gCAAEQ,YAAYJ;gCAAeM,QAAQD;4BAAU;4BAC7DhC;4BACAD;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,kDAAkD;oBAClD,IAAIe,UAAU;wBACZoB,QAAQC,KAAK,CAACT;wBACd,OAAO;oBACT;oBAEA,gEAAgE;oBAChE,MAAMA;gBACR;YACF,CAAA;YAEA,+CAA+C;YAC/C,sBAAsB;YACtB,IAAIzB,WAAW;gBACbA,UAAUK;YACZ;YACA,OAAOA;QACT;QAGF,gLAAOvB,uBAAAA,EAAqBmB;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3756, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/build/templates/helpers.ts"], "sourcesContent": ["/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */\nexport function hoist(module: any, name: string) {\n  // If the name is available in the module, return it.\n  if (name in module) {\n    return module[name]\n  }\n\n  // If a property called `then` exists, assume it's a promise and\n  // return a promise that resolves to the name.\n  if ('then' in module && typeof module.then === 'function') {\n    return module.then((mod: any) => hoist(mod, name))\n  }\n\n  // If we're trying to hoise the default export, and the module is a function,\n  // return the module itself.\n  if (typeof module === 'function' && name === 'default') {\n    return module\n  }\n\n  // Otherwise, return undefined.\n  return undefined\n}\n"], "names": ["hoist", "module", "name", "then", "mod", "undefined"], "mappings": "AAAA;;;;;;CAMC,GACD;;;AAAO,SAASA,MAAMC,MAAW,EAAEC,IAAY;IAC7C,qDAAqD;IACrD,IAAIA,QAAQD,QAAQ;QAClB,OAAOA,MAAM,CAACC,KAAK;IACrB;IAEA,gEAAgE;IAChE,8CAA8C;IAC9C,IAAI,UAAUD,UAAU,OAAOA,OAAOE,IAAI,KAAK,YAAY;QACzD,OAAOF,OAAOE,IAAI,CAAC,CAACC,MAAaJ,MAAMI,KAAKF;IAC9C;IAEA,6EAA6E;IAC7E,4BAA4B;IAC5B,IAAI,OAAOD,WAAW,cAAcC,SAAS,WAAW;QACtD,OAAOD;IACT;IAEA,+BAA+B;IAC/B,OAAOI;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3789, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3826, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/shared/lib/side-effect.tsx"], "sourcesContent": ["import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n"], "names": ["SideEffect", "isServer", "window", "useClientOnlyLayoutEffect", "useLayoutEffect", "useClientOnlyEffect", "useEffect", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate"], "mappings": ";;;+BAoBA,WAAA;;;eAAwBA;;;uBAnBuC;AAe/D,MAAMC,WAAW,OAAOC,SAAW;AACnC,MAAMC,4BAA4BF,uCAAW,KAAO,IAAIG,sBAAe;AACvE,MAAMC,sBAAsBJ,uCAAW,KAAO,IAAIK,gBAAS;AAE5C,SAASN,WAAWO,KAAsB;IACvD,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAE,GAAGF;IAEjD,SAASG;QACP,IAAIF,eAAeA,YAAYG,gBAAgB,EAAE;YAC/C,MAAMC,eAAeC,OAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,YAAYG,gBAAgB,EAA0BM,MAAM,CACrEC;YAGJV,YAAYW,UAAU,CAACV,wBAAwBG,cAAcL;QAC/D;IACF;IAEA,IAAIN,oCAAU;YACZO;QAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+BY,GAAG,CAACb,MAAMc,QAAQ;QACjDX;IACF;IAEAP,0BAA0B;YACxBK;QAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+BY,GAAG,CAACb,MAAMc,QAAQ;QACjD,OAAO;gBACLb;YAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+Bc,MAAM,CAACf,MAAMc,QAAQ;QACtD;IACF;IAEA,kFAAkF;IAClF,oFAAoF;IACpF,gEAAgE;IAChE,qFAAqF;IACrF,mFAAmF;IACnFlB,0BAA0B;QACxB,IAAIK,aAAa;YACfA,YAAYe,cAAc,GAAGb;QAC/B;QACA,OAAO;YACL,IAAIF,aAAa;gBACfA,YAAYe,cAAc,GAAGb;YAC/B;QACF;IACF;IAEAL,oBAAoB;QAClB,IAAIG,eAAeA,YAAYe,cAAc,EAAE;YAC7Cf,YAAYe,cAAc;YAC1Bf,YAAYe,cAAc,GAAG;QAC/B;QACA,OAAO;YACL,IAAIf,eAAeA,YAAYe,cAAc,EAAE;gBAC7Cf,YAAYe,cAAc;gBAC1Bf,YAAYe,cAAc,GAAG;YAC/B;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3895, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/server/route-modules/pages/vendored/contexts/amp-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AmpContext\n"], "names": ["module", "exports", "require", "vendored", "AmpContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qHACRC,QAAQ,CAAC,WAAW,CAACC,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3902, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/server/route-modules/pages/vendored/contexts/head-manager-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HeadManagerContext\n"], "names": ["module", "exports", "require", "vendored", "HeadManagerContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qHACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3909, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/shared/lib/amp-mode.ts"], "sourcesContent": ["export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n"], "names": ["isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;+BAAgBA,eAAAA;;;eAAAA;;;AAAT,SAASA,YAAY,KAAA;IAAA,IAAA,EAC1BC,WAAW,KAAK,EAChBC,SAAS,KAAK,EACdC,WAAW,KAAK,EACjB,GAJ2B,UAAA,KAAA,IAIxB,CAAC,IAJuB;IAK1B,OAAOF,YAAaC,UAAUC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3928, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": ";;;+BAWS<PERSON>,YAAAA;;;eAAAA;;;AAXT,IAAIA,WAAW,CAACC,KAAe;AAC/B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;IACzC,MAAMC,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3953, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/shared/lib/head.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n"], "names": ["defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "React", "Fragment", "concat", "Children", "toArray", "props", "children", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "has", "add", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "filter", "map", "c", "process", "env", "NODE_ENV", "srcMessage", "warnOnce", "cloneElement", "Head", "ampState", "useContext", "AmpStateContext", "headManager", "HeadManagerContext", "Effect", "reduceComponentsToState", "isInAmpMode"], "mappings": ";;;;;;;;;;;;;;IAgLA,OAAmB,EAAA;eAAnB;;IAnKgBA,WAAW,EAAA;eAAXA;;;;;;iEAX4B;qEACzB;yCACa;iDACG;yBACP;0BACH;AAMlB,SAASA,YAAYC,SAAiB;IAAjBA,IAAAA,cAAAA,KAAAA,GAAAA,YAAY;IACtC,MAAMC,OAAO;sBAAC,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA;YAAKC,SAAQ;WAAY;KAAa;IACrD,IAAI,CAACH,WAAW;QACdC,KAAKG,IAAI,CAAA,WAAA,GACP,CAAA,GAAA,YAAA,GAAA,EAACF,QAAAA;YAAKG,MAAK;YAAWC,SAAQ;WAAyB;IAE3D;IACA,OAAOL;AACT;AAEA,SAASM,iBACPC,IAAoC,EACpCC,KAA2C;IAE3C,8FAA8F;IAC9F,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,UAAU;QAC1D,OAAOD;IACT;IACA,kCAAkC;IAClC,IAAIC,MAAMC,IAAI,KAAKC,OAAAA,OAAK,CAACC,QAAQ,EAAE;QACjC,OAAOJ,KAAKK,MAAM,CAEhBF,AADA,OACAA,OAAK,CAACG,QAAQ,CAACC,OAAO,CAACN,MAAMO,KAAK,CAACC,QAAQ,EAAEC,MAAM,CACjD,AACA,CACEC,cACAC,uBAL+F,6DAEE;YAKjG,IACE,OAAOA,kBAAkB,YACzB,OAAOA,kBAAkB,UACzB;gBACA,OAAOD;YACT;YACA,OAAOA,aAAaN,MAAM,CAACO;QAC7B,GACA,EAAE;IAGR;IACA,OAAOZ,KAAKK,MAAM,CAACJ;AACrB;AAEA,MAAMY,YAAY;IAAC;IAAQ;IAAa;IAAW;CAAW;AAE9D;;;;AAIA,GACA,SAASC;IACP,MAAMC,OAAO,IAAIC;IACjB,MAAMC,OAAO,IAAID;IACjB,MAAME,YAAY,IAAIF;IACtB,MAAMG,iBAAsD,CAAC;IAE7D,OAAO,CAACC;QACN,IAAIC,WAAW;QACf,IAAIC,SAAS;QAEb,IAAIF,EAAEG,GAAG,IAAI,OAAOH,EAAEG,GAAG,KAAK,YAAYH,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO,GAAG;YAChEF,SAAS;YACT,MAAMC,MAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO;YAC7C,IAAIT,KAAKW,GAAG,CAACH,MAAM;gBACjBF,WAAW;YACb,OAAO;gBACLN,KAAKY,GAAG,CAACJ;YACX;QACF;QAEA,wCAAwC;QACxC,OAAQH,EAAElB,IAAI;YACZ,KAAK;YACL,KAAK;gBACH,IAAIe,KAAKS,GAAG,CAACN,EAAElB,IAAI,GAAG;oBACpBmB,WAAW;gBACb,OAAO;oBACLJ,KAAKU,GAAG,CAACP,EAAElB,IAAI;gBACjB;gBACA;YACF,KAAK;gBACH,IAAK,IAAI0B,IAAI,GAAGC,MAAMhB,UAAUiB,MAAM,EAAEF,IAAIC,KAAKD,IAAK;oBACpD,MAAMG,WAAWlB,SAAS,CAACe,EAAE;oBAC7B,IAAI,CAACR,EAAEZ,KAAK,CAACwB,cAAc,CAACD,WAAW;oBAEvC,IAAIA,aAAa,WAAW;wBAC1B,IAAIb,UAAUQ,GAAG,CAACK,WAAW;4BAC3BV,WAAW;wBACb,OAAO;4BACLH,UAAUS,GAAG,CAACI;wBAChB;oBACF,OAAO;wBACL,MAAME,WAAWb,EAAEZ,KAAK,CAACuB,SAAS;wBAClC,MAAMG,aAAaf,cAAc,CAACY,SAAS,IAAI,IAAIf;wBACnD,IAAKe,CAAAA,aAAa,UAAU,CAACT,MAAK,KAAMY,WAAWR,GAAG,CAACO,WAAW;4BAChEZ,WAAW;wBACb,OAAO;4BACLa,WAAWP,GAAG,CAACM;4BACfd,cAAc,CAACY,SAAS,GAAGG;wBAC7B;oBACF;gBACF;gBACA;QACJ;QAEA,OAAOb;IACT;AACF;AAEA;;;CAGC,GACD,SAASc,iBACPC,oBAAoD,EACpD5B,KAAQ;IAER,MAAM,EAAEhB,SAAS,EAAE,GAAGgB;IACtB,OAAO4B,qBACJ1B,MAAM,CAACX,kBAAkB,EAAE,EAC3BsC,OAAO,GACPhC,MAAM,CAACd,YAAYC,WAAW6C,OAAO,IACrCC,MAAM,CAACxB,UACPuB,OAAO,GACPE,GAAG,CAAC,CAACC,GAA4BZ;QAChC,MAAML,MAAMiB,EAAEjB,GAAG,IAAIK;QACrB,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,yDAAyD;YACzD,IAAIH,EAAEtC,IAAI,KAAK,YAAYsC,EAAEhC,KAAK,CAAC,OAAO,KAAK,uBAAuB;gBACpE,MAAMoC,aAAaJ,EAAEhC,KAAK,CAAC,MAAM,GAC5B,4BAAyBgC,EAAEhC,KAAK,CAAC,MAAM,GAAC,MACxC;gBACLqC,CAAAA,GAAAA,UAAAA,QAAQ,EACL,mDAAgDD,aAAW;YAEhE,OAAO,IAAIJ,EAAEtC,IAAI,KAAK,UAAUsC,EAAEhC,KAAK,CAAC,MAAM,KAAK,cAAc;gBAC/DqC,CAAAA,GAAAA,UAAAA,QAAQ,EACL,wFAAqFL,EAAEhC,KAAK,CAAC,OAAO,GAAC;YAE1G;QACF;QACA,OAAA,WAAA,GAAOL,OAAAA,OAAK,CAAC2C,YAAY,CAACN,GAAG;YAAEjB;QAAI;IACrC;AACJ;AAEA;;;CAGC,GACD,SAASwB,KAAK,KAA2C;IAA3C,IAAA,EAAEtC,QAAQ,EAAiC,GAA3C;IACZ,MAAMuC,WAAWC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,yBAAAA,eAAe;IAC3C,MAAMC,cAAcF,CAAAA,GAAAA,OAAAA,UAAU,EAACG,iCAAAA,kBAAkB;IACjD,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,YAAAA,OAAM,EAAA;QACLC,yBAAyBnB;QACzBgB,aAAaA;QACb3D,WAAW+D,CAAAA,GAAAA,SAAAA,WAAW,EAACP;kBAEtBvC;;AAGP;MAEA,WAAesC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/server/request-meta.ts"], "sourcesContent": ["/* eslint-disable no-redeclare */\nimport type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { BaseNextRequest } from './base-http'\nimport type { CloneableBody } from './body-streams'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from './response-cache'\nimport type { PagesDevOverlayBridgeType } from '../next-devtools/userspace/pages/pages-dev-overlay-setup'\n\n// FIXME: (wyattjoh) this is a temporary solution to allow us to pass data between bundled modules\nexport const NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta')\n\nexport type NextIncomingMessage = (BaseNextRequest | IncomingMessage) & {\n  [NEXT_REQUEST_META]?: RequestMeta\n}\n\nexport interface RequestMeta {\n  /**\n   * The query that was used to make the request.\n   */\n  initQuery?: ParsedUrlQuery\n\n  /**\n   * The URL that was used to make the request.\n   */\n  initURL?: string\n\n  /**\n   * The protocol that was used to make the request.\n   */\n  initProtocol?: string\n\n  /**\n   * The body that was read from the request. This is used to allow the body to\n   * be read multiple times.\n   */\n  clonableBody?: CloneableBody\n\n  /**\n   * True when the request matched a locale domain that was configured in the\n   * next.config.js file.\n   */\n  isLocaleDomain?: boolean\n\n  /**\n   * True when the request had locale information stripped from the pathname\n   * part of the URL.\n   */\n  didStripLocale?: boolean\n\n  /**\n   * If the request had it's URL rewritten, this is the URL it was rewritten to.\n   */\n  rewroteURL?: string\n\n  /**\n   * The cookies that were added by middleware and were added to the response.\n   */\n  middlewareCookie?: string[]\n\n  /**\n   * The match on the request for a given route.\n   */\n  match?: RouteMatch\n\n  /**\n   * The incremental cache to use for the request.\n   */\n  incrementalCache?: any\n\n  /**\n   * The server components HMR cache, only for dev.\n   */\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  /**\n   * Equals the segment path that was used for the prefetch RSC request.\n   */\n  segmentPrefetchRSCRequest?: string\n\n  /**\n   * True when the request is for the prefetch flight data.\n   */\n  isPrefetchRSCRequest?: true\n\n  /**\n   * True when the request is for the flight data.\n   */\n  isRSCRequest?: true\n\n  /**\n   * A search param set by the Next.js client when performing RSC requests.\n   * Because some CDNs do not vary their cache entries on our custom headers,\n   * this search param represents a hash of the header values. For any cached\n   * RSC request, we should verify that the hash matches before responding.\n   * Otherwise this can lead to cache poisoning.\n   * TODO: Consider not using custom request headers at all, and instead encode\n   * everything into the search param.\n   */\n  cacheBustingSearchParam?: string\n\n  /**\n   * True when the request is for the `/_next/data` route using the pages\n   * router.\n   */\n  isNextDataReq?: true\n\n  /**\n   * Postponed state to use for resumption. If present it's assumed that the\n   * request is for a page that has postponed (there are no guarantees that the\n   * page actually has postponed though as it would incur an additional cache\n   * lookup).\n   */\n  postponed?: string\n\n  /**\n   * If provided, this will be called when a response cache entry was generated\n   * or looked up in the cache.\n   */\n  onCacheEntry?: (\n    cacheEntry: any,\n    requestMeta: any\n  ) => Promise<boolean | void> | boolean | void\n\n  /**\n   * The previous revalidate before rendering 404 page for notFound: true\n   */\n  notFoundRevalidate?: number | false\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage?: string\n\n  /**\n   * The path we routed to and should be invoked\n   */\n  invokePath?: string\n\n  /**\n   * The specific page output we should be matching\n   */\n  invokeOutput?: string\n\n  /**\n   * The status we are invoking the request with from routing\n   */\n  invokeStatus?: number\n\n  /**\n   * The routing error we are invoking with\n   */\n  invokeError?: Error\n\n  /**\n   * The query parsed for the invocation\n   */\n  invokeQuery?: Record<string, undefined | string | string[]>\n\n  /**\n   * Whether the request is a middleware invocation\n   */\n  middlewareInvoke?: boolean\n\n  /**\n   * Whether the request should render the fallback shell or not.\n   */\n  renderFallbackShell?: boolean\n\n  /**\n   * Whether the request is for the custom error page.\n   */\n  customErrorRender?: true\n\n  /**\n   * Whether to bubble up the NoFallbackError to the caller when a 404 is\n   * returned.\n   */\n  bubbleNoFallback?: true\n\n  /**\n   * True when the request had locale information inferred from the default\n   * locale.\n   */\n  localeInferredFromDefault?: true\n\n  /**\n   * The locale that was inferred or explicitly set for the request.\n   */\n  locale?: string\n\n  /**\n   * The default locale that was inferred or explicitly set for the request.\n   */\n  defaultLocale?: string\n\n  /**\n   * The project dir the server is running in\n   */\n  projectDir?: string\n\n  /**\n   * The dist directory the server is currently using\n   */\n  distDir?: string\n\n  /**\n   * Whether we are generating the fallback version of the page in dev mode\n   */\n  isIsrFallback?: boolean\n\n  /**\n   * The query after resolving routes\n   */\n  query?: ParsedUrlQuery\n\n  /**\n   * The params after resolving routes\n   */\n  params?: ParsedUrlQuery\n\n  /**\n   * The AMP validator to use in development\n   */\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n\n  /**\n   * ErrorOverlay component to use in development for pages router\n   */\n  PagesErrorDebug?: PagesDevOverlayBridgeType\n\n  /**\n   * Whether server is in minimal mode (this will be replaced with more\n   * specific flags in future)\n   */\n  minimalMode?: boolean\n}\n\n/**\n * Gets the request metadata. If no key is provided, the entire metadata object\n * is returned.\n *\n * @param req the request to get the metadata from\n * @param key the key to get from the metadata (optional)\n * @returns the value for the key or the entire metadata object\n */\nexport function getRequestMeta(\n  req: NextIncomingMessage,\n  key?: undefined\n): RequestMeta\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key: K\n): RequestMeta[K]\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key?: K\n): RequestMeta | RequestMeta[K] {\n  const meta = req[NEXT_REQUEST_META] || {}\n  return typeof key === 'string' ? meta[key] : meta\n}\n\n/**\n * Sets the request metadata.\n *\n * @param req the request to set the metadata on\n * @param meta the metadata to set\n * @returns the mutated request metadata\n */\nexport function setRequestMeta(req: NextIncomingMessage, meta: RequestMeta) {\n  req[NEXT_REQUEST_META] = meta\n  return meta\n}\n\n/**\n * Adds a value to the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to set\n * @param value the value to set\n * @returns the mutated request metadata\n */\nexport function addRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K,\n  value: RequestMeta[K]\n) {\n  const meta = getRequestMeta(request)\n  meta[key] = value\n  return setRequestMeta(request, meta)\n}\n\n/**\n * Removes a key from the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to remove\n * @returns the mutated request metadata\n */\nexport function removeRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K\n) {\n  const meta = getRequestMeta(request)\n  delete meta[key]\n  return setRequestMeta(request, meta)\n}\n\ntype NextQueryMetadata = {\n  /**\n   * The `_rsc` query parameter used for cache busting to ensure that the RSC\n   * requests do not get cached by the browser explicitly.\n   */\n  [NEXT_RSC_UNION_QUERY]?: string\n}\n\nexport type NextParsedUrlQuery = ParsedUrlQuery &\n  NextQueryMetadata & {\n    amp?: '1'\n  }\n\nexport interface NextUrlWithParsedQuery extends UrlWithParsedQuery {\n  query: NextParsedUrlQuery\n}\n"], "names": ["NEXT_REQUEST_META", "addRequestMeta", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "Symbol", "for", "req", "key", "meta", "request", "value"], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;;;;;;IAYlBA,iBAAiB,EAAA;eAAjBA;;IAgRGC,cAAc,EAAA;eAAdA;;IA5BAC,cAAc,EAAA;eAAdA;;IA6CAC,iBAAiB,EAAA;eAAjBA;;IA9BAC,cAAc,EAAA;eAAdA;;;AAnQT,MAAMJ,oBAAoBK,OAAOC,GAAG,CAAC;AAoPrC,SAASJ,eACdK,GAAwB,EACxBC,GAAO;IAEP,MAAMC,OAAOF,GAAG,CAACP,kBAAkB,IAAI,CAAC;IACxC,OAAO,OAAOQ,QAAQ,WAAWC,IAAI,CAACD,IAAI,GAAGC;AAC/C;AASO,SAASL,eAAeG,GAAwB,EAAEE,IAAiB;IACxEF,GAAG,CAACP,kBAAkB,GAAGS;IACzB,OAAOA;AACT;AAUO,SAASR,eACdS,OAA4B,EAC5BF,GAAM,EACNG,KAAqB;IAErB,MAAMF,OAAOP,eAAeQ;IAC5BD,IAAI,CAACD,IAAI,GAAGG;IACZ,OAAOP,eAAeM,SAASD;AACjC;AASO,SAASN,kBACdO,OAA4B,EAC5BF,GAAM;IAEN,MAAMC,OAAOP,eAAeQ;IAC5B,OAAOD,IAAI,CAACD,IAAI;IAChB,OAAOJ,eAAeM,SAASD;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/src/pages/_error.tsx"], "sourcesContent": ["import React from 'react'\nimport Head from '../shared/lib/head'\nimport type { NextPageContext } from '../shared/lib/utils'\n\nconst statusCodes: { [code: number]: string } = {\n  400: 'Bad Request',\n  404: 'This page could not be found',\n  405: 'Method Not Allowed',\n  500: 'Internal Server Error',\n}\n\nexport type ErrorProps = {\n  statusCode: number\n  hostname?: string\n  title?: string\n  withDarkMode?: boolean\n}\n\nfunction _getInitialProps({\n  req,\n  res,\n  err,\n}: NextPageContext): Promise<ErrorProps> | ErrorProps {\n  const statusCode =\n    res && res.statusCode ? res.statusCode : err ? err.statusCode! : 404\n\n  let hostname\n\n  if (typeof window !== 'undefined') {\n    hostname = window.location.hostname\n  } else if (req) {\n    const { getRequestMeta } =\n      require('../server/request-meta') as typeof import('../server/request-meta')\n\n    const initUrl = getRequestMeta(req, 'initURL')\n    if (initUrl) {\n      const url = new URL(initUrl)\n      hostname = url.hostname\n    }\n  }\n\n  return { statusCode, hostname }\n}\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  desc: {\n    lineHeight: '48px',\n  },\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '28px',\n  },\n  wrap: {\n    display: 'inline-block',\n  },\n}\n\n/**\n * `Error` component used for handling errors.\n */\nexport default class Error<P = {}> extends React.Component<P & ErrorProps> {\n  static displayName = 'ErrorPage'\n\n  static getInitialProps = _getInitialProps\n  static origGetInitialProps = _getInitialProps\n\n  render() {\n    const { statusCode, withDarkMode = true } = this.props\n    const title =\n      this.props.title ||\n      statusCodes[statusCode] ||\n      'An unexpected error has occurred'\n\n    return (\n      <div style={styles.error}>\n        <Head>\n          <title>\n            {statusCode\n              ? `${statusCode}: ${title}`\n              : 'Application error: a client-side exception has occurred'}\n          </title>\n        </Head>\n        <div style={styles.desc}>\n          <style\n            dangerouslySetInnerHTML={{\n              /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}${\n                withDarkMode\n                  ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}'\n                  : ''\n              }`,\n            }}\n          />\n\n          {statusCode ? (\n            <h1 className=\"next-error-h1\" style={styles.h1}>\n              {statusCode}\n            </h1>\n          ) : null}\n          <div style={styles.wrap}>\n            <h2 style={styles.h2}>\n              {this.props.title || statusCode ? (\n                title\n              ) : (\n                <>\n                  Application error: a client-side exception has occurred{' '}\n                  {Boolean(this.props.hostname) && (\n                    <>while loading {this.props.hostname}</>\n                  )}{' '}\n                  (see the browser console for more information)\n                </>\n              )}\n              .\n            </h2>\n          </div>\n        </div>\n      </div>\n    )\n  }\n}\n"], "names": ["Error", "statusCodes", "_getInitialProps", "req", "res", "err", "statusCode", "hostname", "window", "location", "getRequestMeta", "require", "initUrl", "url", "URL", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "h1", "margin", "paddingRight", "fontSize", "fontWeight", "verticalAlign", "h2", "wrap", "React", "Component", "render", "withDarkMode", "props", "title", "div", "style", "Head", "dangerouslySetInnerHTML", "__html", "className", "Boolean", "displayName", "getInitialProps", "origGetInitialProps"], "mappings": ";;;;;;eAgFqBA;;;;;gEAhFH;+DACD;AAGjB,MAAMC,cAA0C;IAC9C,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AASA,SAASC,iBAAiB,KAIR;IAJQ,IAAA,EACxBC,GAAG,EACHC,GAAG,EACHC,GAAG,EACa,GAJQ;IAKxB,MAAMC,aACJF,OAAOA,IAAIE,UAAU,GAAGF,IAAIE,UAAU,GAAGD,MAAMA,IAAIC,UAAU,GAAI;IAEnE,IAAIC;IAEJ,IAAI,OAAOC,WAAW,aAAa;;SAE5B,IAAIL,KAAK;QACd,MAAM,EAAEO,cAAc,EAAE,GACtBC,QAAQ;QAEV,MAAMC,UAAUF,eAAeP,KAAK;QACpC,IAAIS,SAAS;YACX,MAAMC,MAAM,IAAIC,IAAIF;YACpBL,WAAWM,IAAIN,QAAQ;QACzB;IACF;IAEA,OAAO;QAAED;QAAYC;IAAS;AAChC;AAEA,MAAMQ,SAA8C;IAClDC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,YAAY;IACd;IACAC,IAAI;QACFN,SAAS;QACTO,QAAQ;QACRC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,eAAe;IACjB;IACAC,IAAI;QACFH,UAAU;QACVC,YAAY;QACZL,YAAY;IACd;IACAQ,MAAM;QACJb,SAAS;IACX;AACF;AAKe,MAAMpB,cAAsBkC,OAAAA,OAAK,CAACC,SAAS;IAMxDC,SAAS;QACP,MAAM,EAAE9B,UAAU,EAAE+B,eAAe,IAAI,EAAE,GAAG,IAAI,CAACC,KAAK;QACtD,MAAMC,QACJ,IAAI,CAACD,KAAK,CAACC,KAAK,IAChBtC,WAAW,CAACK,WAAW,IACvB;QAEF,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACkC,OAAAA;YAAIC,OAAO1B,OAAOC,KAAK;;8BACtB,CAAA,GAAA,YAAA,GAAA,EAAC0B,MAAAA,OAAI,EAAA;8BACH,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACH,SAAAA;kCACEjC,aACMA,aAAW,OAAIiC,QAClB;;;8BAGR,CAAA,GAAA,YAAA,IAAA,EAACC,OAAAA;oBAAIC,OAAO1B,OAAOS,IAAI;;sCACrB,CAAA,GAAA,YAAA,GAAA,EAACiB,SAAAA;4BACCE,yBAAyB;gCACvB;;;;;;;;;;;;;;;;eAgBC,GACDC,QAAS,mGACPP,CAAAA,eACI,oIACA,EAAC;4BAET;;wBAGD/B,aAAAA,WAAAA,GACC,CAAA,GAAA,YAAA,GAAA,EAACoB,MAAAA;4BAAGmB,WAAU;4BAAgBJ,OAAO1B,OAAOW,EAAE;sCAC3CpB;6BAED;sCACJ,CAAA,GAAA,YAAA,GAAA,EAACkC,OAAAA;4BAAIC,OAAO1B,OAAOkB,IAAI;sCACrB,WAAA,GAAA,CAAA,GAAA,YAAA,IAAA,EAACD,MAAAA;gCAAGS,OAAO1B,OAAOiB,EAAE;;oCACjB,IAAI,CAACM,KAAK,CAACC,KAAK,IAAIjC,aACnBiC,QAAAA,WAAAA,GAEA,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;4CAAE;4CACwD;4CACvDO,QAAQ,IAAI,CAACR,KAAK,CAAC/B,QAAQ,KAAA,WAAA,GAC1B,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;oDAAE;oDAAe,IAAI,CAAC+B,KAAK,CAAC/B,QAAQ;;;4CACnC;4CAAI;;;oCAGT;;;;;;;;IAOd;AACF;AA3EqBP,MACZ+C,WAAAA,GAAc;AADF/C,MAGZgD,eAAAA,GAAkB9C;AAHNF,MAIZiD,mBAAAA,GAAsB/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4338, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/error.js"], "sourcesContent": ["module.exports = require('./dist/pages/_error')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/lib/cache-control.ts"], "sourcesContent": ["import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n"], "names": ["CACHE_ONE_YEAR", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>", "undefined"], "mappings": ";;;AAAA,SAASA,cAAc,QAAQ,sBAAqB;;AAgB7C,SAASC,sBAAsB,EACpCC,UAAU,EACVC,MAAM,EACO;IACb,MAAMC,YACJ,OAAOF,eAAe,YACtBC,WAAWE,aACXH,aAAaC,SACT,CAAC,yBAAyB,EAAEA,SAASD,YAAY,GACjD;IAEN,IAAIA,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,OAAOA,eAAe,UAAU;QACzC,OAAO,CAAC,SAAS,EAAEA,aAAaE,WAAW;IAC7C;IAEA,OAAO,CAAC,SAAS,mJAAEJ,iBAAAA,GAAiBI,WAAW;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4361, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["WEB_VITALS", "execOnce", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "isAbsoluteUrl", "url", "test", "getLocationOrigin", "protocol", "hostname", "port", "window", "location", "getURL", "href", "origin", "substring", "length", "getDisplayName", "Component", "displayName", "name", "isResSent", "res", "finished", "headersSent", "normalizeRepeatedSlashes", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "loadGetInitialProps", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "SP", "performance", "ST", "every", "method", "DecodeError", "NormalizeError", "PageNotFoundError", "constructor", "page", "code", "MissingStaticPage", "MiddlewareNotFoundError", "stringifyError", "error", "JSON", "stringify", "stack"], "mappings": "AAwCA;;;CAGC,GACD;;;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO,CAAS;AAsQvE,SAASC,SACdC,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMC,gBAAgB,CAACC,MAAgBF,mBAAmBG,IAAI,CAACD,KAAI;AAEnE,SAASE;IACd,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASG;IACd,MAAM,EAAEC,IAAI,EAAE,GAAGH,OAAOC,QAAQ;IAChC,MAAMG,SAASR;IACf,OAAOO,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASC,eAAkBC,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAASC,UAAUC,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASC,yBAAyBrB,GAAW;IAClD,MAAMsB,WAAWtB,IAAIuB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAeC,oBAIpBC,GAAgC,EAAEC,GAAM;IACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACrCJ;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAGvB,eAClBgB,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMlB,MAAMY,IAAIZ,GAAG,IAAKY,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACZ,GAAG;IAE9C,IAAI,CAACW,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIhB,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLwB,WAAW,MAAMV,oBAAoBE,IAAIhB,SAAS,EAAEgB,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIZ,OAAOD,UAAUC,MAAM;QACzB,OAAOqB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAGvB,eAClBgB,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAO3B,MAAM,KAAK,KAAK,CAACkB,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAE9B,eACDgB,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMK,KAAK,OAAOC,gBAAgB,YAAW;AAC7C,MAAMC,KACXF,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWG,KAAK,CACtD,CAACC,SAAW,OAAOH,WAAW,CAACG,OAAO,KAAK,YAC5C;AAEI,MAAMC,oBAAoBZ;AAAO;AACjC,MAAMa,uBAAuBb;AAAO;AACpC,MAAMc,0BAA0Bd;IAGrCe,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAACtC,IAAI,GAAG;QACZ,IAAI,CAACoB,OAAO,GAAI,kCAA+BiB;IACjD;AACF;AAEO,MAAME,0BAA0BlB;IACrCe,YAAYC,IAAY,EAAEjB,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCiB,OAAK,MAAGjB;IACjE;AACF;AAEO,MAAMoB,gCAAgCnB;IAE3Ce,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAAClB,OAAO,GAAI;IAClB;AACF;AAWO,SAASqB,eAAeC,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAExB,SAASsB,MAAMtB,OAAO;QAAEyB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4514, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/lib/redirect-status.ts"], "sourcesContent": ["import { RedirectStatusCode } from '../client/components/redirect-status-code'\n\nexport const allowedStatusCodes = new Set([301, 302, 303, 307, 308])\n\nexport function getRedirectStatus(route: {\n  statusCode?: number\n  permanent?: boolean\n}): number {\n  return (\n    route.statusCode ||\n    (route.permanent\n      ? RedirectStatusCode.PermanentRedirect\n      : RedirectStatusCode.TemporaryRedirect)\n  )\n}\n\n// for redirects we restrict matching /_next and for all routes\n// we add an optional trailing slash at the end for easier\n// configuring between trailingSlash: true/false\nexport function modifyRouteRegex(regex: string, restrictedPaths?: string[]) {\n  if (restrictedPaths) {\n    regex = regex.replace(\n      /\\^/,\n      `^(?!${restrictedPaths\n        .map((path) => path.replace(/\\//g, '\\\\/'))\n        .join('|')})`\n    )\n  }\n  regex = regex.replace(/\\$$/, '(?:\\\\/)?$')\n  return regex\n}\n"], "names": ["RedirectStatusCode", "allowedStatusCodes", "Set", "getRedirectStatus", "route", "statusCode", "permanent", "PermanentRedirect", "TemporaryRedirect", "modifyRouteRegex", "regex", "restrictedPaths", "replace", "map", "path", "join"], "mappings": ";;;;;AAAA,SAASA,kBAAkB,QAAQ,4CAA2C;;AAEvE,MAAMC,qBAAqB,IAAIC,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI,EAAC;AAE7D,SAASC,kBAAkBC,KAGjC;IACC,OACEA,MAAMC,UAAU,IACfD,CAAAA,MAAME,SAAS,sLACZN,qBAAAA,CAAmBO,iBAAiB,sLACpCP,qBAAAA,CAAmBQ,iBAAgB;AAE3C;AAKO,SAASC,iBAAiBC,KAAa,EAAEC,eAA0B;IACxE,IAAIA,iBAAiB;QACnBD,QAAQA,MAAME,OAAO,CACnB,MACA,CAAC,IAAI,EAAED,gBACJE,GAAG,CAAC,CAACC,OAASA,KAAKF,OAAO,CAAC,OAAO,QAClCG,IAAI,CAAC,KAAK,CAAC,CAAC;IAEnB;IACAL,QAAQA,MAAME,OAAO,CAAC,OAAO;IAC7B,OAAOF;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/lib/etag.ts"], "sourcesContent": ["/**\n * FNV-1a Hash implementation\n * <AUTHOR> (tjwebb) <<EMAIL>>\n *\n * Ported from https://github.com/tjwebb/fnv-plus/blob/master/index.js\n *\n * Simplified, optimized and add modified for 52 bit, which provides a larger hash space\n * and still making use of Javascript's 53-bit integer space.\n */\nexport const fnv1a52 = (str: string) => {\n  const len = str.length\n  let i = 0,\n    t0 = 0,\n    v0 = 0x2325,\n    t1 = 0,\n    v1 = 0x8422,\n    t2 = 0,\n    v2 = 0x9ce4,\n    t3 = 0,\n    v3 = 0xcbf2\n\n  while (i < len) {\n    v0 ^= str.charCodeAt(i++)\n    t0 = v0 * 435\n    t1 = v1 * 435\n    t2 = v2 * 435\n    t3 = v3 * 435\n    t2 += v0 << 8\n    t3 += v1 << 8\n    t1 += t0 >>> 16\n    v0 = t0 & 65535\n    t2 += t1 >>> 16\n    v1 = t1 & 65535\n    v3 = (t3 + (t2 >>> 16)) & 65535\n    v2 = t2 & 65535\n  }\n\n  return (\n    (v3 & 15) * 281474976710656 +\n    v2 * 4294967296 +\n    v1 * 65536 +\n    (v0 ^ (v3 >> 4))\n  )\n}\n\nexport const generateETag = (payload: string, weak = false) => {\n  const prefix = weak ? 'W/\"' : '\"'\n  return (\n    prefix + fnv1a52(payload).toString(36) + payload.length.toString(36) + '\"'\n  )\n}\n"], "names": ["fnv1a52", "str", "len", "length", "i", "t0", "v0", "t1", "v1", "t2", "v2", "t3", "v3", "charCodeAt", "generateETag", "payload", "weak", "prefix", "toString"], "mappings": "AAAA;;;;;;;;CAQC,GACD;;;;AAAO,MAAMA,UAAU,CAACC;IACtB,MAAMC,MAAMD,IAAIE,MAAM;IACtB,IAAIC,IAAI,GACNC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK;IAEP,MAAOR,IAAIF,IAAK;QACdI,MAAML,IAAIY,UAAU,CAACT;QACrBC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVH,MAAMH,MAAM;QACZK,MAAMH,MAAM;QACZD,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVI,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVK,KAAMD,KAAMF,CAAAA,OAAO,EAAC,IAAM;QAC1BC,KAAKD,KAAK;IACZ;IAEA,OACGG,CAAAA,KAAK,EAAC,IAAK,kBACZF,KAAK,aACLF,KAAK,QACJF,CAAAA,KAAMM,MAAM,CAAC;AAElB,EAAC;AAEM,MAAME,eAAe,CAACC,SAAiBC,OAAO,KAAK;IACxD,MAAMC,SAASD,OAAO,QAAQ;IAC9B,OACEC,SAASjB,QAAQe,SAASG,QAAQ,CAAC,MAAMH,QAAQZ,MAAM,CAACe,QAAQ,CAAC,MAAM;AAE3E,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/compiled/fresh/index.js"], "sourcesContent": ["(()=>{\"use strict\";var e={695:e=>{\n/*!\n * fresh\n * Copyright(c) 2012 <PERSON><PERSON>\n * Copyright(c) 2016-2017 <PERSON>\n * MIT Licensed\n */\nvar r=/(?:^|,)\\s*?no-cache\\s*?(?:,|$)/;e.exports=fresh;function fresh(e,a){var t=e[\"if-modified-since\"];var s=e[\"if-none-match\"];if(!t&&!s){return false}var i=e[\"cache-control\"];if(i&&r.test(i)){return false}if(s&&s!==\"*\"){var f=a[\"etag\"];if(!f){return false}var n=true;var u=parseTokenList(s);for(var _=0;_<u.length;_++){var o=u[_];if(o===f||o===\"W/\"+f||\"W/\"+o===f){n=false;break}}if(n){return false}}if(t){var p=a[\"last-modified\"];var v=!p||!(parseHttpDate(p)<=parseHttpDate(t));if(v){return false}}return true}function parseHttpDate(e){var r=e&&Date.parse(e);return typeof r===\"number\"?r:NaN}function parseTokenList(e){var r=0;var a=[];var t=0;for(var s=0,i=e.length;s<i;s++){switch(e.charCodeAt(s)){case 32:if(t===r){t=r=s+1}break;case 44:a.push(e.substring(t,r));t=r=s+1;break;default:r=s+1;break}}a.push(e.substring(t,r));return a}}};var r={};function __nccwpck_require__(a){var t=r[a];if(t!==undefined){return t.exports}var s=r[a]={exports:{}};var i=true;try{e[a](s,s.exports,__nccwpck_require__);i=false}finally{if(i)delete r[a]}return s.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var a=__nccwpck_require__(695);module.exports=a})();"], "names": [], "mappings": "AAAA,CAAC;IAAK;IAAa,IAAI,IAAE;QAAC,KAAI,CAAA;YAC9B;;;;;CAKC,GACD,IAAI,IAAE;YAAiC,EAAE,OAAO,GAAC;YAAM,SAAS,MAAM,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAE,CAAC,CAAC,oBAAoB;gBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;gBAAC,IAAG,CAAC,KAAG,CAAC,GAAE;oBAAC,OAAO;gBAAK;gBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;gBAAC,IAAG,KAAG,EAAE,IAAI,CAAC,IAAG;oBAAC,OAAO;gBAAK;gBAAC,IAAG,KAAG,MAAI,KAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,OAAO;oBAAC,IAAG,CAAC,GAAE;wBAAC,OAAO;oBAAK;oBAAC,IAAI,IAAE;oBAAK,IAAI,IAAE,eAAe;oBAAG,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;wBAAC,IAAG,MAAI,KAAG,MAAI,OAAK,KAAG,OAAK,MAAI,GAAE;4BAAC,IAAE;4BAAM;wBAAK;oBAAC;oBAAC,IAAG,GAAE;wBAAC,OAAO;oBAAK;gBAAC;gBAAC,IAAG,GAAE;oBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;oBAAC,IAAI,IAAE,CAAC,KAAG,CAAC,CAAC,cAAc,MAAI,cAAc,EAAE;oBAAE,IAAG,GAAE;wBAAC,OAAO;oBAAK;gBAAC;gBAAC,OAAO;YAAI;YAAC,SAAS,cAAc,CAAC;gBAAE,IAAI,IAAE,KAAG,KAAK,KAAK,CAAC;gBAAG,OAAO,OAAO,MAAI,WAAS,IAAE;YAAG;YAAC,SAAS,eAAe,CAAC;gBAAE,IAAI,IAAE;gBAAE,IAAI,IAAE,EAAE;gBAAC,IAAI,IAAE;gBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,GAAE,IAAI;oBAAC,OAAO,EAAE,UAAU,CAAC;wBAAI,KAAK;4BAAG,IAAG,MAAI,GAAE;gCAAC,IAAE,IAAE,IAAE;4BAAC;4BAAC;wBAAM,KAAK;4BAAG,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE;4BAAI,IAAE,IAAE,IAAE;4BAAE;wBAAM;4BAAQ,IAAE,IAAE;4BAAE;oBAAK;gBAAC;gBAAC,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE;gBAAI,OAAO;YAAC;QAAC;IAAC;IAAE,IAAI,IAAE,CAAC;IAAE,SAAS,oBAAoB,CAAC;QAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,MAAI,WAAU;YAAC,OAAO,EAAE,OAAO;QAAA;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,GAAC;YAAC,SAAQ,CAAC;QAAC;QAAE,IAAI,IAAE;QAAK,IAAG;YAAC,CAAC,CAAC,EAAE,CAAC,GAAE,EAAE,OAAO,EAAC;YAAqB,IAAE;QAAK,SAAQ;YAAC,IAAG,GAAE,OAAO,CAAC,CAAC,EAAE;QAAA;QAAC,OAAO,EAAE,OAAO;IAAA;IAAC,IAAG,OAAO,wBAAsB,aAAY,oBAAoB,EAAE,GAAC,kFAAU;IAAI,IAAI,IAAE,oBAAoB;IAAK,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4699, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\nexport const NEXT_ACTION_NOT_FOUND_HEADER = 'x-nextjs-action-not-found' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_ACTION_NOT_FOUND_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa,MAAc;AACjC,MAAMC,gBAAgB,cAAsB;AAI5C,MAAMC,gCAAgC,yBAAiC;AACvE,MAAMC,8BAA8B,uBAA+B;AAKnE,MAAMC,sCACX,+BAAuC;AAClC,MAAMC,0BAA0B,mBAA2B;AAC3D,MAAMC,+BAA+B,4BAAoC;AACzE,MAAMC,WAAW,WAAmB;AACpC,MAAMC,0BAA0B,mBAA2B;AAE3D,MAAMC,iBAAiB;IAC5BT;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEH,MAAMM,uBAAuB,OAAe;AAE5C,MAAMC,gCAAgC,sBAA8B;AACpE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,6BAA6B,0BAAkC;AACrE,MAAMC,8BAA8B,2BAAmC;AACvE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,+BAA+B,4BAAoC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4745, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/server/send-payload.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type RenderResult from './render-result'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { isResSent } from '../shared/lib/utils'\nimport { generateETag } from './lib/etag'\nimport fresh from 'next/dist/compiled/fresh'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { RSC_CONTENT_TYPE_HEADER } from '../client/components/app-router-headers'\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n\nexport async function sendRenderResult({\n  req,\n  res,\n  result,\n  type,\n  generateEtags,\n  poweredByHeader,\n  cacheControl,\n}: {\n  req: IncomingMessage\n  res: ServerResponse\n  result: RenderResult\n  type: 'html' | 'json' | 'rsc'\n  generateEtags: boolean\n  poweredByHeader: boolean\n  cacheControl: CacheControl | undefined\n}): Promise<void> {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && type === 'html') {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  // If cache control is already set on the response we don't\n  // override it to allow users to customize it via next.config\n  if (cacheControl && !res.getHeader('Cache-Control')) {\n    res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n  }\n\n  const payload = result.isDynamic ? null : result.toUnchunkedString()\n\n  if (generateEtags && payload !== null) {\n    const etag = generateETag(payload)\n    if (sendEtagResponse(req, res, etag)) {\n      return\n    }\n  }\n\n  if (!res.getHeader('Content-Type')) {\n    res.setHeader(\n      'Content-Type',\n      result.contentType\n        ? result.contentType\n        : type === 'rsc'\n          ? RSC_CONTENT_TYPE_HEADER\n          : type === 'json'\n            ? 'application/json'\n            : 'text/html; charset=utf-8'\n    )\n  }\n\n  if (payload) {\n    res.setHeader('Content-Length', Buffer.byteLength(payload))\n  }\n\n  if (req.method === 'HEAD') {\n    res.end(null)\n    return\n  }\n\n  if (payload !== null) {\n    res.end(payload)\n    return\n  }\n\n  // Pipe the render result to the response after we get a writer for it.\n  await result.pipeToNodeResponse(res)\n}\n"], "names": ["isResSent", "generateETag", "fresh", "getCacheControlHeader", "RSC_CONTENT_TYPE_HEADER", "sendEtagResponse", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "headers", "statusCode", "end", "sendRenderResult", "result", "type", "generateEtags", "poweredByHeader", "cacheControl", "<PERSON><PERSON><PERSON><PERSON>", "payload", "isDynamic", "toUnchunkedString", "contentType", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": ";;;;AAIA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,YAAY,QAAQ,aAAY;AACzC,OAAOC,WAAW,2BAA0B;AAC5C,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,uBAAuB,QAAQ,0CAAyC;;;;;;AAE1E,SAASC,iBACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,4JAAIN,UAAAA,EAAMI,IAAII,OAAO,EAAE;QAAEF;IAAK,IAAI;QAChCD,IAAII,UAAU,GAAG;QACjBJ,IAAIK,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeC,iBAAiB,EACrCP,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,YAAY,EASb;IACC,+JAAIlB,YAAAA,EAAUO,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,2DAA2D;IAC3D,6DAA6D;IAC7D,IAAIS,gBAAgB,CAACX,IAAIY,SAAS,CAAC,kBAAkB;QACnDZ,IAAIE,SAAS,CAAC,uLAAiBN,wBAAAA,EAAsBe;IACvD;IAEA,MAAME,UAAUN,OAAOO,SAAS,GAAG,OAAOP,OAAOQ,iBAAiB;IAElE,IAAIN,iBAAiBI,YAAY,MAAM;QACrC,MAAMZ,iKAAOP,eAAAA,EAAamB;QAC1B,IAAIf,iBAAiBC,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIY,SAAS,CAAC,iBAAiB;QAClCZ,IAAIE,SAAS,CACX,gBACAK,OAAOS,WAAW,GACdT,OAAOS,WAAW,GAClBR,SAAS,yLACPX,0BAAAA,GACAW,SAAS,SACP,qBACA;IAEZ;IAEA,IAAIK,SAAS;QACXb,IAAIE,SAAS,CAAC,kBAAkBe,OAAOC,UAAU,CAACL;IACpD;IAEA,IAAId,IAAIoB,MAAM,KAAK,QAAQ;QACzBnB,IAAIK,GAAG,CAAC;QACR;IACF;IAEA,IAAIQ,YAAY,MAAM;QACpBb,IAAIK,GAAG,CAACQ;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMN,OAAOa,kBAAkB,CAACpB;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4817, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;;;;AAC/J,MAAMA,yBACX,gSAA+R", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4827, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS.\n// By default, only googlebots are considered as DOM bots. Blow is where the regex is computed from:\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\nconst HEADLESS_BROWSER_BOT_UA_RE = /google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HEADLESS_BROWSER_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "isBot", "getBotType", "undefined"], "mappings": ";;;;;AAAA,SAASA,sBAAsB,QAAQ,cAAa;;AAEpD,mEAAmE;AACnE,oGAAoG;AACpG,4FAA4F;AAC5F,MAAMC,6BAA6B;AAE5B,MAAMC,iNAAgCF,yBAAAA,CAAuBG,MAAM,CAAA;;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOJ,2BAA2BK,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,wLAAOL,yBAAAA,CAAuBM,IAAI,CAACD;AACrC;AAEO,SAASG,MAAMH,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASI,WAAWJ,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4869, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/next/dist/src/build/templates/pages.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'node:http'\nimport type { ParsedUrlQuery } from 'node:querystring'\nimport { PagesRouteModule } from '../../server/route-modules/pages/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { addRequestMeta, getRequestMeta } from '../../server/request-meta'\nimport { interopDefault } from '../../server/app-render/interop-default'\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { normalizeDataPath } from '../../shared/lib/page-path/normalize-data-path'\nimport {\n  CachedRouteKind,\n  type CachedPageValue,\n  type CachedRedirectValue,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\n\nimport { hoist } from './helpers'\n\n// Import the app and document modules.\nimport * as document from 'VAR_MODULE_DOCUMENT'\nimport * as app from 'VAR_MODULE_APP'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\nimport {\n  getCacheControlHeader,\n  type CacheControl,\n} from '../../server/lib/cache-control'\nimport { normalizeRepeatedSlashes } from '../../shared/lib/utils'\nimport { getRedirectStatus } from '../../lib/redirect-status'\nimport { CACHE_ONE_YEAR } from '../../lib/constants'\nimport { sendRenderResult } from '../../server/send-payload'\nimport RenderResult from '../../server/render-result'\nimport { toResponseCacheEntry } from '../../server/response-cache/utils'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { removeTrailingSlash } from '../../shared/lib/router/utils/remove-trailing-slash'\n\n// Re-export the component (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export methods.\nexport const getStaticProps = hoist(userland, 'getStaticProps')\nexport const getStaticPaths = hoist(userland, 'getStaticPaths')\nexport const getServerSideProps = hoist(userland, 'getServerSideProps')\nexport const config = hoist(userland, 'config')\nexport const reportWebVitals = hoist(userland, 'reportWebVitals')\n\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(\n  userland,\n  'unstable_getStaticProps'\n)\nexport const unstable_getStaticPaths = hoist(\n  userland,\n  'unstable_getStaticPaths'\n)\nexport const unstable_getStaticParams = hoist(\n  userland,\n  'unstable_getStaticParams'\n)\nexport const unstable_getServerProps = hoist(\n  userland,\n  'unstable_getServerProps'\n)\nexport const unstable_getServerSideProps = hoist(\n  userland,\n  'unstable_getServerSideProps'\n)\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n  definition: {\n    kind: RouteKind.PAGES,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  components: {\n    // default export might not exist when optimized for data only\n    App: app.default,\n    Document: document.default,\n  },\n  userland,\n})\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n): Promise<void> {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return\n  }\n\n  const {\n    buildId,\n    query,\n    params,\n    parsedUrl,\n    originalQuery,\n    originalPathname,\n    buildManifest,\n    nextFontManifest,\n    serverFilesManifest,\n    reactLoadableManifest,\n    prerenderManifest,\n    isDraftMode,\n    isOnDemandRevalidate,\n    revalidateOnlyGenerated,\n    locale,\n    locales,\n    defaultLocale,\n    routerServerContext,\n    nextConfig,\n    resolvedPathname,\n  } = prepareResult\n\n  const isExperimentalCompile =\n    serverFilesManifest?.config?.experimental?.isExperimentalCompile\n\n  const hasServerProps = Boolean(getServerSideProps)\n  const hasStaticProps = Boolean(getStaticProps)\n  const hasStaticPaths = Boolean(getStaticPaths)\n  const hasGetInitialProps = Boolean(\n    (userland.default || userland).getInitialProps\n  )\n  const isAmp = query.amp && config.amp\n  let cacheKey: null | string = null\n  let isIsrFallback = false\n  let isNextDataRequest =\n    prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps)\n\n  const is404Page = srcPage === '/404'\n  const is500Page = srcPage === '/500'\n  const isErrorPage = srcPage === '/_error'\n\n  if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n    cacheKey = `${locale ? `/${locale}` : ''}${\n      (srcPage === '/' || resolvedPathname === '/') && locale\n        ? ''\n        : resolvedPathname\n    }${isAmp ? '.amp' : ''}`\n\n    if (is404Page || is500Page || isErrorPage) {\n      cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`\n    }\n\n    // ensure /index and / is normalized to one key\n    cacheKey = cacheKey === '/index' ? '/' : cacheKey\n  }\n\n  if (hasStaticPaths && !isDraftMode) {\n    const decodedPathname = removeTrailingSlash(\n      locale ? addPathPrefix(resolvedPathname, `/${locale}`) : resolvedPathname\n    )\n    const isPrerendered =\n      Boolean(prerenderManifest.routes[decodedPathname]) ||\n      prerenderManifest.notFoundRoutes.includes(decodedPathname)\n\n    const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage]\n\n    if (prerenderInfo) {\n      if (prerenderInfo.fallback === false && !isPrerendered) {\n        throw new NoFallbackError()\n      }\n\n      if (\n        typeof prerenderInfo.fallback === 'string' &&\n        !isPrerendered &&\n        !isNextDataRequest\n      ) {\n        isIsrFallback = true\n      }\n    }\n  }\n\n  // When serving a bot request, we want to serve a blocking render and not\n  // the prerendered page. This ensures that the correct content is served\n  // to the bot in the head.\n  if (\n    (isIsrFallback && isBot(req.headers['user-agent'] || '')) ||\n    getRequestMeta(req, 'minimalMode')\n  ) {\n    isIsrFallback = false\n  }\n\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  try {\n    const method = req.method || 'GET'\n\n    const resolvedUrl = formatUrl({\n      pathname: nextConfig.trailingSlash\n        ? parsedUrl.pathname\n        : removeTrailingSlash(parsedUrl.pathname || '/'),\n      // make sure to only add query values from original URL\n      query: hasStaticProps ? {} : originalQuery,\n    })\n\n    const publicRuntimeConfig: Record<string, string> =\n      routerServerContext?.publicRuntimeConfig || nextConfig.publicRuntimeConfig\n\n    const handleResponse = async (span?: Span) => {\n      const responseGenerator: ResponseGenerator = async ({\n        previousCacheEntry,\n      }) => {\n        const doRender = async () => {\n          try {\n            return await routeModule\n              .render(req, res, {\n                query:\n                  hasStaticProps && !isExperimentalCompile\n                    ? ({\n                        ...params,\n                        ...(isAmp\n                          ? {\n                              amp: query.amp,\n                            }\n                          : {}),\n                      } as ParsedUrlQuery)\n                    : {\n                        ...query,\n                        ...params,\n                      },\n                params,\n                page: srcPage,\n                renderContext: {\n                  isDraftMode,\n                  isFallback: isIsrFallback,\n                  developmentNotFoundSourcePage: getRequestMeta(\n                    req,\n                    'developmentNotFoundSourcePage'\n                  ),\n                },\n                sharedContext: {\n                  buildId,\n                  customServer:\n                    Boolean(routerServerContext?.isCustomServer) || undefined,\n                  deploymentId: process.env.NEXT_DEPLOYMENT_ID,\n                },\n                renderOpts: {\n                  params,\n                  routeModule,\n                  page: srcPage,\n                  pageConfig: config || {},\n                  Component: interopDefault(userland),\n                  ComponentMod: userland,\n                  getStaticProps,\n                  getStaticPaths,\n                  getServerSideProps,\n                  supportsDynamicResponse: !hasStaticProps,\n                  buildManifest,\n                  nextFontManifest,\n                  reactLoadableManifest,\n\n                  assetPrefix: nextConfig.assetPrefix,\n                  strictNextHead:\n                    nextConfig.experimental.strictNextHead ?? true,\n                  previewProps: prerenderManifest.preview,\n                  images: nextConfig.images as any,\n                  nextConfigOutput: nextConfig.output,\n                  optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                  nextScriptWorkers: Boolean(\n                    nextConfig.experimental.nextScriptWorkers\n                  ),\n                  domainLocales: nextConfig.i18n?.domains,\n                  crossOrigin: nextConfig.crossOrigin,\n\n                  multiZoneDraftMode,\n                  basePath: nextConfig.basePath,\n                  canonicalBase: nextConfig.amp.canonicalBase || '',\n                  ampOptimizerConfig: nextConfig.experimental.amp?.optimizer,\n                  disableOptimizedLoading:\n                    nextConfig.experimental.disableOptimizedLoading,\n                  largePageDataBytes:\n                    nextConfig.experimental.largePageDataBytes,\n                  // Only the `publicRuntimeConfig` key is exposed to the client side\n                  // It'll be rendered as part of __NEXT_DATA__ on the client side\n                  runtimeConfig:\n                    Object.keys(publicRuntimeConfig).length > 0\n                      ? publicRuntimeConfig\n                      : undefined,\n\n                  isExperimentalCompile,\n\n                  experimental: {\n                    clientTraceMetadata:\n                      nextConfig.experimental.clientTraceMetadata ||\n                      ([] as any),\n                  },\n\n                  locale,\n                  locales,\n                  defaultLocale,\n                  setIsrStatus: routerServerContext?.setIsrStatus,\n\n                  isNextDataRequest:\n                    isNextDataRequest && (hasServerProps || hasStaticProps),\n\n                  resolvedUrl,\n                  // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                  // and not the resolved URL to prevent a hydration mismatch on\n                  // asPath\n                  resolvedAsPath:\n                    hasServerProps || hasGetInitialProps\n                      ? formatUrl({\n                          // we use the original URL pathname less the _next/data prefix if\n                          // present\n                          pathname: isNextDataRequest\n                            ? normalizeDataPath(originalPathname)\n                            : originalPathname,\n                          query: originalQuery,\n                        })\n                      : resolvedUrl,\n\n                  isOnDemandRevalidate,\n\n                  ErrorDebug: getRequestMeta(req, 'PagesErrorDebug'),\n                  err: getRequestMeta(req, 'invokeError'),\n                  dev: routeModule.isDev,\n\n                  // needed for experimental.optimizeCss feature\n                  distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n\n                  ampSkipValidation:\n                    nextConfig.experimental.amp?.skipValidation,\n                  ampValidator: getRequestMeta(req, 'ampValidator'),\n                },\n              })\n              .then((renderResult): ResponseCacheEntry => {\n                const { metadata } = renderResult\n\n                let cacheControl: CacheControl | undefined =\n                  metadata.cacheControl\n\n                if ('isNotFound' in metadata && metadata.isNotFound) {\n                  return {\n                    value: null,\n                    cacheControl,\n                  } satisfies ResponseCacheEntry\n                }\n\n                // Handle `isRedirect`.\n                if (metadata.isRedirect) {\n                  return {\n                    value: {\n                      kind: CachedRouteKind.REDIRECT,\n                      props: metadata.pageData ?? metadata.flightData,\n                    } satisfies CachedRedirectValue,\n                    cacheControl,\n                  } satisfies ResponseCacheEntry\n                }\n\n                return {\n                  value: {\n                    kind: CachedRouteKind.PAGES,\n                    html: renderResult,\n                    pageData: renderResult.metadata.pageData,\n                    headers: renderResult.metadata.headers,\n                    status: renderResult.metadata.statusCode,\n                  },\n                  cacheControl,\n                }\n              })\n              .finally(() => {\n                if (!span) return\n\n                span.setAttributes({\n                  'http.status_code': res.statusCode,\n                  'next.rsc': false,\n                })\n\n                const rootSpanAttributes = tracer.getRootSpanAttributes()\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                  return\n                }\n\n                if (\n                  rootSpanAttributes.get('next.span_type') !==\n                  BaseServerSpan.handleRequest\n                ) {\n                  console.warn(\n                    `Unexpected root span type '${rootSpanAttributes.get(\n                      'next.span_type'\n                    )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n                  )\n                  return\n                }\n\n                const route = rootSpanAttributes.get('next.route')\n                if (route) {\n                  const name = `${method} ${route}`\n\n                  span.setAttributes({\n                    'next.route': route,\n                    'http.route': route,\n                    'next.span_name': name,\n                  })\n                  span.updateName(name)\n                } else {\n                  span.updateName(`${method} ${req.url}`)\n                }\n              })\n          } catch (err: unknown) {\n            // if this is a background revalidate we need to report\n            // the request error here as it won't be bubbled\n            if (previousCacheEntry?.isStale) {\n              await routeModule.onRequestError(\n                req,\n                err,\n                {\n                  routerKind: 'Pages Router',\n                  routePath: srcPage,\n                  routeType: 'render',\n                  revalidateReason: getRevalidateReason({\n                    isRevalidate: hasStaticProps,\n                    isOnDemandRevalidate,\n                  }),\n                },\n                routerServerContext\n              )\n            }\n            throw err\n          }\n        }\n\n        // if we've already generated this page we no longer\n        // serve the fallback\n        if (previousCacheEntry) {\n          isIsrFallback = false\n        }\n\n        if (isIsrFallback) {\n          const fallbackResponse = await routeModule.getResponseCache(req).get(\n            routeModule.isDev\n              ? null\n              : locale\n                ? `/${locale}${srcPage}`\n                : srcPage,\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              if (!routeModule.isDev) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n              return doRender()\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              isFallback: true,\n              isRoutePPREnabled: false,\n              isOnDemandRevalidate: false,\n              incrementalCache: await routeModule.getIncrementalCache(\n                req,\n                nextConfig,\n                prerenderManifest\n              ),\n              waitUntil: ctx.waitUntil,\n            }\n          )\n          if (fallbackResponse) {\n            // Remove the cache control from the response to prevent it from being\n            // used in the surrounding cache.\n            delete fallbackResponse.cacheControl\n            fallbackResponse.isMiss = true\n            return fallbackResponse\n          }\n        }\n\n        if (\n          !getRequestMeta(req, 'minimalMode') &&\n          isOnDemandRevalidate &&\n          revalidateOnlyGenerated &&\n          !previousCacheEntry\n        ) {\n          res.statusCode = 404\n          // on-demand revalidate always sets this header\n          res.setHeader('x-nextjs-cache', 'REVALIDATED')\n          res.end('This page could not be found')\n          return null\n        }\n\n        if (\n          isIsrFallback &&\n          previousCacheEntry?.value?.kind === CachedRouteKind.PAGES\n        ) {\n          return {\n            value: {\n              kind: CachedRouteKind.PAGES,\n              html: new RenderResult(\n                Buffer.from(previousCacheEntry.value.html),\n                {\n                  contentType: 'text/html;utf-8',\n                  metadata: {\n                    statusCode: previousCacheEntry.value.status,\n                    headers: previousCacheEntry.value.headers,\n                  },\n                }\n              ),\n              pageData: {},\n              status: previousCacheEntry.value.status,\n              headers: previousCacheEntry.value.headers,\n            } satisfies CachedPageValue,\n            cacheControl: { revalidate: 0, expire: undefined },\n          } satisfies ResponseCacheEntry\n        }\n        return doRender()\n      }\n\n      const result = await routeModule.handleResponse({\n        cacheKey,\n        req,\n        nextConfig,\n        routeKind: RouteKind.PAGES,\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated,\n        waitUntil: ctx.waitUntil,\n        responseGenerator: responseGenerator,\n        prerenderManifest,\n      })\n\n      // if we got a cache hit this wasn't an ISR fallback\n      // but it wasn't generated during build so isn't in the\n      // prerender-manifest\n      if (isIsrFallback && !result?.isMiss) {\n        isIsrFallback = false\n      }\n\n      // response is finished is no cache entry\n      if (!result) {\n        return\n      }\n\n      if (hasStaticProps && !getRequestMeta(req, 'minimalMode')) {\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : result.isMiss\n              ? 'MISS'\n              : result.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n\n      let cacheControl: CacheControl | undefined\n\n      if (!hasStaticProps || isIsrFallback) {\n        if (!res.getHeader('Cache-Control')) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n      } else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n\n        cacheControl = {\n          revalidate:\n            typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n          expire: undefined,\n        }\n      } else if (is500Page) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (result.cacheControl) {\n        // If the cache entry has a cache control with a revalidate value that's\n        // a number, use it.\n        if (typeof result.cacheControl.revalidate === 'number') {\n          if (result.cacheControl.revalidate < 1) {\n            throw new Error(\n              `Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`\n            )\n          }\n          cacheControl = {\n            revalidate: result.cacheControl.revalidate,\n            expire: result.cacheControl?.expire ?? nextConfig.expireTime,\n          }\n        } else {\n          // revalidate: false\n          cacheControl = {\n            revalidate: CACHE_ONE_YEAR,\n            expire: undefined,\n          }\n        }\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n      }\n\n      // notFound: true case\n      if (!result.value) {\n        // add revalidate metadata before rendering 404 page\n        // so that we can use this as source of truth for the\n        // cache-control header instead of what the 404 page returns\n        // for the revalidate value\n        addRequestMeta(\n          req,\n          'notFoundRevalidate',\n          result.cacheControl?.revalidate\n        )\n\n        res.statusCode = 404\n\n        if (isNextDataRequest) {\n          res.end('{\"notFound\":true}')\n          return\n        }\n        // TODO: should route-module itself handle rendering the 404\n        if (routerServerContext?.render404) {\n          await routerServerContext.render404(req, res, parsedUrl, false)\n        } else {\n          res.end('This page could not be found')\n        }\n        return\n      }\n\n      if (result.value.kind === CachedRouteKind.REDIRECT) {\n        if (isNextDataRequest) {\n          res.setHeader('content-type', 'application/json')\n          res.end(JSON.stringify(result.value.props))\n          return\n        } else {\n          const handleRedirect = (pageData: any) => {\n            const redirect = {\n              destination: pageData.pageProps.__N_REDIRECT,\n              statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n              basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n            }\n            const statusCode = getRedirectStatus(redirect)\n            const { basePath } = nextConfig\n\n            if (\n              basePath &&\n              redirect.basePath !== false &&\n              redirect.destination.startsWith('/')\n            ) {\n              redirect.destination = `${basePath}${redirect.destination}`\n            }\n\n            if (redirect.destination.startsWith('/')) {\n              redirect.destination = normalizeRepeatedSlashes(\n                redirect.destination\n              )\n            }\n\n            res.statusCode = statusCode\n            res.setHeader('Location', redirect.destination)\n            if (statusCode === RedirectStatusCode.PermanentRedirect) {\n              res.setHeader('Refresh', `0;url=${redirect.destination}`)\n            }\n            res.end(redirect.destination)\n          }\n          await handleRedirect(result.value.props)\n          return null\n        }\n      }\n\n      if (result.value.kind !== CachedRouteKind.PAGES) {\n        throw new Error(\n          `Invariant: received non-pages cache entry in pages handler`\n        )\n      }\n\n      // In dev, we should not cache pages for any reason.\n      if (routeModule.isDev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n      }\n\n      // Draft mode should never be cached\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      // when invoking _error before pages/500 we don't actually\n      // send the _error response\n      if (\n        getRequestMeta(req, 'customErrorRender') ||\n        (isErrorPage &&\n          getRequestMeta(req, 'minimalMode') &&\n          res.statusCode === 500)\n      ) {\n        return null\n      }\n\n      await sendRenderResult({\n        req,\n        res,\n        // If we are rendering the error page it's not a data request\n        // anymore\n        result:\n          isNextDataRequest && !isErrorPage && !is500Page\n            ? new RenderResult(\n                Buffer.from(JSON.stringify(result.value.pageData)),\n                {\n                  contentType: 'application/json',\n                  metadata: result.value.html.metadata,\n                }\n              )\n            : result.value.html,\n        generateEtags: nextConfig.generateEtags,\n        poweredByHeader: nextConfig.poweredByHeader,\n        cacheControl: routeModule.isDev ? undefined : cacheControl,\n        type: isNextDataRequest ? 'json' : 'html',\n      })\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse()\n    } else {\n      await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    if (!(err instanceof NoFallbackError)) {\n      await routeModule.onRequestError(\n        req,\n        err,\n        {\n          routerKind: 'Pages Router',\n          routePath: srcPage,\n          routeType: 'render',\n          revalidateReason: getRevalidateReason({\n            isRevalidate: hasStaticProps,\n            isOnDemandRevalidate,\n          }),\n        },\n        routerServerContext\n      )\n    }\n    // rethrow so that we can handle serving error page\n    throw err\n  }\n}\n"], "names": ["PagesRouteModule", "RouteKind", "BaseServerSpan", "getTracer", "SpanKind", "formatUrl", "addRequestMeta", "getRequestMeta", "interopDefault", "getRevalidateReason", "normalizeDataPath", "CachedRouteKind", "hoist", "document", "app", "userland", "getCacheControlHeader", "normalizeRepeatedSlashes", "getRedirectStatus", "CACHE_ONE_YEAR", "sendRenderResult", "RenderResult", "toResponseCacheEntry", "NoFallbackError", "RedirectStatusCode", "isBot", "addPathPrefix", "removeTrailingSlash", "getStaticProps", "getStaticPaths", "getServerSideProps", "config", "reportWebVitals", "unstable_getStaticProps", "unstable_getStaticPaths", "unstable_getStaticParams", "unstable_getServerProps", "unstable_getServerSideProps", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "bundlePath", "filename", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "components", "App", "default", "Document", "handler", "req", "res", "ctx", "serverFilesManifest", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "query", "params", "parsedUrl", "originalQuery", "originalPathname", "buildManifest", "nextFontManifest", "reactLoadableManifest", "prerenderManifest", "isDraftMode", "isOnDemandRevalidate", "revalidateOnlyGenerated", "locale", "locales", "defaultLocale", "routerServerContext", "nextConfig", "resolvedPathname", "isExperimentalCompile", "experimental", "hasServerProps", "Boolean", "hasStaticProps", "hasStaticPaths", "hasGetInitialProps", "getInitialProps", "isAmp", "amp", "cache<PERSON>ey", "isIsrFallback", "isNextDataRequest", "is404Page", "is500Page", "isErrorPage", "isDev", "decodedPathname", "isP<PERSON>endered", "routes", "notFoundRoutes", "includes", "prerenderInfo", "dynamicRoutes", "fallback", "headers", "tracer", "activeSpan", "getActiveScopeSpan", "method", "resolvedUrl", "trailingSlash", "publicRuntimeConfig", "handleResponse", "span", "responseGenerator", "previousCacheEntry", "doR<PERSON>", "render", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "developmentNotFoundSourcePage", "sharedContext", "customServer", "isCustomServer", "undefined", "deploymentId", "NEXT_DEPLOYMENT_ID", "renderOpts", "pageConfig", "Component", "ComponentMod", "supportsDynamicResponse", "assetPrefix", "strictNextHead", "previewProps", "preview", "images", "nextConfigOutput", "output", "optimizeCss", "nextScriptWorkers", "domainLocales", "i18n", "domains", "crossOrigin", "basePath", "canonicalBase", "ampOptimizerConfig", "optimizer", "disableOptimizedLoading", "largePageDataBytes", "runtimeConfig", "Object", "keys", "length", "clientTraceMetadata", "setIsrStatus", "resolvedAsPath", "ErrorDebug", "err", "dev", "ampSkipValidation", "skipValidation", "ampValidator", "then", "renderResult", "metadata", "cacheControl", "isNotFound", "value", "isRedirect", "REDIRECT", "props", "pageData", "flightData", "html", "status", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "isStale", "onRequestError", "routerKind", "routePath", "routeType", "revalidateReason", "isRevalidate", "fallbackResponse", "getResponseCache", "previousFallbackCacheEntry", "routeKind", "isRoutePPREnabled", "incrementalCache", "getIncrementalCache", "isMiss", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "from", "contentType", "revalidate", "expire", "result", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "Error", "expireTime", "render404", "JSON", "stringify", "handleRedirect", "redirect", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "startsWith", "PermanentRedirect", "generateEtags", "poweredByHeader", "type", "withPropagatedContext", "trace", "spanName", "SERVER", "attributes"], "mappings": ";;;;;;;;;;;;;;;AAEA,SAASA,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,SAAS,EAAEC,QAAQ,QAAmB,gCAA+B;AAC9E,SAASC,SAAS,QAAQ,2CAA0C;AACpE,SAASC,cAAc,EAAEC,cAAc,QAAQ,4BAA2B;AAC1E,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SAASC,iBAAiB,QAAQ,iDAAgD;;AAClF,SACEC,eAAe,QAKV,8BAA6B;AAEpC,SAASC,KAAK,QAAQ,YAAW;AAEjC,uCAAuC;AACvC,YAAYC,cAAc,sBAAqB;AAC/C,YAAYC,SAAS,iBAAgB;AAErC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;AACxC,SACEC,qBAAqB,QAEhB,iCAAgC;AACvC,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SAASC,iBAAiB,QAAQ,4BAA2B;AAC7D,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,OAAOC,kBAAkB,6BAA4B;AACrD,SAASC,oBAAoB,QAAQ,oCAAmC;AACxE,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,kBAAkB,QAAQ,+CAA8C;;AACjF,SAASC,KAAK,QAAQ,uCAAsC;AAC5D,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,mBAAmB,QAAQ,sDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAG1Ef,sKAAAA,EAAMG,wHAAU,WAAU;AAGlC,MAAMa,mLAAiBhB,QAAAA,EAAMG,wHAAU,kBAAiB;AACxD,MAAMc,mLAAiBjB,QAAAA,EAAMG,wHAAU,kBAAiB;AACxD,MAAMe,uLAAqBlB,QAAAA,EAAMG,wHAAU,sBAAqB;AAChE,MAAMgB,2KAASnB,QAAAA,EAAMG,wHAAU,UAAS;AACxC,MAAMiB,oLAAkBpB,QAAAA,EAAMG,wHAAU,mBAAkB;AAG1D,MAAMkB,4LAA0BrB,QAAAA,EACrCG,wHACA,2BACD;AACM,MAAMmB,8BAA0BtB,sKAAAA,EACrCG,wHACA,2BACD;AACM,MAAMoB,6LAA2BvB,QAAAA,EACtCG,wHACA,4BACD;AACM,MAAMqB,2LAA0BxB,SAAAA,EACrCG,wHACA,2BACD;AACM,MAAMsB,+LAA8BzB,SAAAA,EACzCG,wHACA,+BACD;AAGM,MAAMuB,cAAc,8LAAItC,mBAAAA,CAAiB;IAC9CuC,YAAY;QACVC,8JAAMvC,YAAAA,CAAUwC,KAAK;QACrBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,SAAI;IACjDC,YAAYH,QAAQC,GAAG,CAACG,2BAA2B,CAAI;IACvDC,YAAY;QACV,8DAA8D;QAC9DC,0HAAKvC,IAAIwC,MAAO;QAChBC,oIAAU1C,SAASyC,CAAO;IAC5B;cACAvC;AACF,GAAE;AAEK,eAAeyC,QACpBC,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;QAoDCC,0CAAAA;IAlDF,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAId,QAAQC,GAAG,CAACc,SAAS,eAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,OAAO,IAAIF,YAAY,UAAU;QAC/B,0CAA0C;QAC1CA,UAAU;IACZ;IACA,MAAMG,qBAAqBjB,QAAQC,GAAG,CACnCiB,4BAA4B;IAE/B,MAAMC,gBAAgB,MAAM5B,YAAY6B,OAAO,CAACV,KAAKC,KAAK;QACxDG;QACAG;IACF;IAEA,IAAI,CAACE,eAAe;QAClBR,IAAIU,UAAU,GAAG;QACjBV,IAAIW,GAAG,CAAC;QACRV,IAAIW,SAAS,IAAA,OAAA,KAAA,IAAbX,IAAIW,SAAS,CAAA,IAAA,CAAbX,KAAgBY,QAAQC,OAAO;QAC/B;IACF;IAEA,MAAM,EACJC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,gBAAgB,EAChBpB,mBAAmB,EACnBqB,qBAAqB,EACrBC,iBAAiB,EACjBC,WAAW,EACXC,oBAAoB,EACpBC,uBAAuB,EACvBC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,UAAU,EACVC,gBAAgB,EACjB,GAAGzB;IAEJ,MAAM0B,wBACJhC,uBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,8BAAAA,oBAAqB7B,MAAM,KAAA,OAAA,KAAA,IAAA,CAA3B6B,2CAAAA,4BAA6BiC,YAAY,KAAA,OAAA,KAAA,IAAzCjC,yCAA2CgC,qBAAqB;IAElE,MAAME,iBAAiBC,QAAQjE;IAC/B,MAAMkE,iBAAiBD,QAAQnE;IAC/B,MAAMqE,iBAAiBF,QAAQlE;IAC/B,MAAMqE,qBAAqBH,QACxBhF,CAAAA,uHAASuC,OAAO,IAAIvC,sHAAO,EAAGoF,eAAe;IAEhD,MAAMC,QAAQ1B,MAAM2B,GAAG,IAAItE,OAAOsE,GAAG;IACrC,IAAIC,WAA0B;IAC9B,IAAIC,gBAAgB;IACpB,IAAIC,oBACFtC,cAAcsC,iBAAiB,IAAKR,CAAAA,kBAAkBF,cAAa;IAErE,MAAMW,YAAY5C,YAAY;IAC9B,MAAM6C,YAAY7C,YAAY;IAC9B,MAAM8C,cAAc9C,YAAY;IAEhC,IAAI,CAACvB,YAAYsE,KAAK,IAAI,CAACzB,eAAea,gBAAgB;QACxDM,WAAW,GAAGhB,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACnCzB,CAAAA,YAAY,OAAO8B,qBAAqB,GAAE,KAAML,SAC7C,KACAK,mBACHS,QAAQ,SAAS,IAAI;QAExB,IAAIK,aAAaC,aAAaC,aAAa;YACzCL,WAAW,GAAGhB,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKzB,UAAUuC,QAAQ,SAAS,IAAI;QAC5E;QAEA,+CAA+C;QAC/CE,WAAWA,aAAa,WAAW,MAAMA;IAC3C;IAEA,IAAIL,kBAAkB,CAACd,aAAa;QAClC,MAAM0B,sNAAkBlF,sBAAAA,EACtB2D,uMAAS5D,gBAAAA,EAAciE,kBAAkB,CAAC,CAAC,EAAEL,QAAQ,IAAIK;QAE3D,MAAMmB,gBACJf,QAAQb,kBAAkB6B,MAAM,CAACF,gBAAgB,KACjD3B,kBAAkB8B,cAAc,CAACC,QAAQ,CAACJ;QAE5C,MAAMK,gBAAgBhC,kBAAkBiC,aAAa,CAACtD,QAAQ;QAE9D,IAAIqD,eAAe;YACjB,IAAIA,cAAcE,QAAQ,KAAK,SAAS,CAACN,eAAe;gBACtD,MAAM,kPAAIvF,kBAAAA;YACZ;YAEA,IACE,OAAO2F,cAAcE,QAAQ,KAAK,YAClC,CAACN,iBACD,CAACN,mBACD;gBACAD,gBAAgB;YAClB;QACF;IACF;IAEA,yEAAyE;IACzE,wEAAwE;IACxE,0BAA0B;IAC1B,IACGA,mNAAiB9E,QAAAA,EAAMgC,IAAI4D,OAAO,CAAC,aAAa,IAAI,qKACrD9G,iBAAAA,EAAekD,KAAK,gBACpB;QACA8C,gBAAgB;IAClB;IAEA,MAAMe,SAASnH,iLAAAA;IACf,MAAMoH,aAAaD,OAAOE,kBAAkB;IAE5C,IAAI;QACF,MAAMC,SAAShE,IAAIgE,MAAM,IAAI;QAE7B,MAAMC,oMAAcrH,YAAAA,EAAU;YAC5BsC,UAAU+C,WAAWiC,aAAa,GAC9B/C,UAAUjC,QAAQ,uMAClBhB,sBAAAA,EAAoBiD,UAAUjC,QAAQ,IAAI;YAC9C,uDAAuD;YACvD+B,OAAOsB,iBAAiB,CAAC,IAAInB;QAC/B;QAEA,MAAM+C,sBACJnC,CAAAA,uBAAAA,OAAAA,KAAAA,IAAAA,oBAAqBmC,mBAAmB,KAAIlC,WAAWkC,mBAAmB;QAE5E,MAAMC,iBAAiB,OAAOC;YAC5B,MAAMC,oBAAuC,OAAO,EAClDC,kBAAkB,EACnB;oBAwRGA;gBAvRF,MAAMC,WAAW;oBACf,IAAI;4BA0DmBvC,kBAMKA,8BAsDlBA;wBArHR,OAAO,MAAMpD,YACV4F,MAAM,CAACzE,KAAKC,KAAK;4BAChBgB,OACEsB,kBAAkB,CAACJ,wBACd;gCACC,GAAGjB,MAAM;gCACT,GAAIyB,QACA;oCACEC,KAAK3B,MAAM2B,GAAG;gCAChB,IACA,CAAC,CAAC;4BACR,IACA;gCACE,GAAG3B,KAAK;gCACR,GAAGC,MAAM;4BACX;4BACNA;4BACAjC,MAAMmB;4BACNsE,eAAe;gCACbhD;gCACAiD,YAAY7B;gCACZ8B,6LAA+B9H,iBAAAA,EAC7BkD,KACA;4BAEJ;4BACA6E,eAAe;gCACb7D;gCACA8D,cACExC,QAAQN,uBAAAA,OAAAA,KAAAA,IAAAA,oBAAqB+C,cAAc,KAAKC;gCAClDC,YAAAA,EAAc3F,QAAQC,GAAG,CAAC2F,kBAAkB;4BAC9C;4BACAC,YAAY;gCACVjE;gCACArC;gCACAI,MAAMmB;gCACNgF,YAAY9G,UAAU,CAAC;gCACvB+G,6LAAWtI,iBAAAA,EAAeO;gCAC1BgI,cAAchI;gCACda;gCACAC;gCACAC;gCACAkH,yBAAyB,CAAChD;gCAC1BjB;gCACAC;gCACAC;gCAEAgE,aAAavD,WAAWuD,WAAW;gCACnCC,gBACExD,WAAWG,YAAY,CAACqD,cAAc,IAAI;gCAC5CC,cAAcjE,kBAAkBkE,OAAO;gCACvCC,QAAQ3D,WAAW2D,MAAM;gCACzBC,kBAAkB5D,WAAW6D,MAAM;gCACnCC,aAAazD,QAAQL,WAAWG,YAAY,CAAC2D,WAAW;gCACxDC,mBAAmB1D,QACjBL,WAAWG,YAAY,CAAC4D,iBAAiB;gCAE3CC,aAAa,EAAA,CAAEhE,mBAAAA,WAAWiE,IAAI,KAAA,OAAA,KAAA,IAAfjE,iBAAiBkE,OAAO;gCACvCC,aAAanE,WAAWmE,WAAW;gCAEnC7F;gCACA8F,UAAUpE,WAAWoE,QAAQ;gCAC7BC,eAAerE,WAAWW,GAAG,CAAC0D,aAAa,IAAI;gCAC/CC,kBAAkB,EAAA,CAAEtE,+BAAAA,WAAWG,YAAY,CAACQ,GAAG,KAAA,OAAA,KAAA,IAA3BX,6BAA6BuE,SAAS;gCAC1DC,yBACExE,WAAWG,YAAY,CAACqE,uBAAuB;gCACjDC,oBACEzE,WAAWG,YAAY,CAACsE,kBAAkB;gCAC5C,mEAAmE;gCACnE,gEAAgE;gCAChEC,eACEC,OAAOC,IAAI,CAAC1C,qBAAqB2C,MAAM,GAAG,IACtC3C,sBACAa;gCAEN7C;gCAEAC,cAAc;oCACZ2E,qBACE9E,WAAWG,YAAY,CAAC2E,mBAAmB,IAC1C,EAAE;gCACP;gCAEAlF;gCACAC;gCACAC;gCACAiF,YAAY,EAAEhF,uBAAAA,OAAAA,KAAAA,IAAAA,oBAAqBgF,YAAY;gCAE/CjE,mBACEA,qBAAsBV,CAAAA,kBAAkBE,cAAa;gCAEvD0B;gCACA,uFAAuF;gCACvF,8DAA8D;gCAC9D,SAAS;gCACTgD,gBACE5E,kBAAkBI,2MACd7F,YAAAA,EAAU;oCACR,iEAAiE;oCACjE,UAAU;oCACVsC,UAAU6D,oBACN9F,mNAAAA,EAAkBoE,oBAClBA;oCACJJ,OAAOG;gCACT,KACA6C;gCAENtC;gCAEAuF,YAAYpK,+KAAAA,EAAekD,KAAK;gCAChCmH,mKAAKrK,iBAAAA,EAAekD,KAAK;gCACzBoH,KAAKvI,YAAYsE,KAAK;gCAEtB,8CAA8C;gCAC9C9D,SAAS,GAAGR,YAAYY,UAAU,CAAC,CAAC,EAAEZ,YAAYQ,OAAO,EAAE;gCAE3DgI,iBAAiB,EAAA,CACfpF,gCAAAA,WAAWG,YAAY,CAACQ,GAAG,KAAA,OAAA,KAAA,IAA3BX,8BAA6BqF,cAAc;gCAC7CC,4KAAczK,iBAAAA,EAAekD,KAAK;4BACpC;wBACF,GACCwH,IAAI,CAAC,CAACC;4BACL,MAAM,EAAEC,QAAQ,EAAE,GAAGD;4BAErB,IAAIE,eACFD,SAASC,YAAY;4BAEvB,IAAI,gBAAgBD,YAAYA,SAASE,UAAU,EAAE;gCACnD,OAAO;oCACLC,OAAO;oCACPF;gCACF;4BACF;4BAEA,uBAAuB;4BACvB,IAAID,SAASI,UAAU,EAAE;gCACvB,OAAO;oCACLD,OAAO;wCACL9I,MAAM7B,uLAAAA,CAAgB6K,QAAQ;wCAC9BC,OAAON,SAASO,QAAQ,IAAIP,SAASQ,UAAU;oCACjD;oCACAP;gCACF;4BACF;4BAEA,OAAO;gCACLE,OAAO;oCACL9I,2KAAM7B,kBAAAA,CAAgB8B,KAAK;oCAC3BmJ,MAAMV;oCACNQ,UAAUR,aAAaC,QAAQ,CAACO,QAAQ;oCACxCrE,SAAS6D,aAAaC,QAAQ,CAAC9D,OAAO;oCACtCwE,QAAQX,aAAaC,QAAQ,CAAC/G,UAAU;gCAC1C;gCACAgH;4BACF;wBACF,GACCU,OAAO,CAAC;4BACP,IAAI,CAAChE,MAAM;4BAEXA,KAAKiE,aAAa,CAAC;gCACjB,oBAAoBrI,IAAIU,UAAU;gCAClC,YAAY;4BACd;4BAEA,MAAM4H,qBAAqB1E,OAAO2E,qBAAqB;4BACvD,iEAAiE;4BACjE,IAAI,CAACD,oBAAoB;gCACvB;4BACF;4BAEA,IACEA,mBAAmBE,GAAG,CAAC,0LACvBhM,iBAAAA,CAAeiM,aAAa,EAC5B;gCACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEL,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;gCAE1E;4BACF;4BAEA,MAAMI,QAAQN,mBAAmBE,GAAG,CAAC;4BACrC,IAAII,OAAO;gCACT,MAAMC,OAAO,GAAG9E,OAAO,CAAC,EAAE6E,OAAO;gCAEjCxE,KAAKiE,aAAa,CAAC;oCACjB,cAAcO;oCACd,cAAcA;oCACd,kBAAkBC;gCACpB;gCACAzE,KAAK0E,UAAU,CAACD;4BAClB,OAAO;gCACLzE,KAAK0E,UAAU,CAAC,GAAG/E,OAAO,CAAC,EAAEhE,IAAIgJ,GAAG,EAAE;4BACxC;wBACF;oBACJ,EAAE,OAAO7B,KAAc;wBACrB,uDAAuD;wBACvD,gDAAgD;wBAChD,IAAI5C,sBAAAA,OAAAA,KAAAA,IAAAA,mBAAoB0E,OAAO,EAAE;4BAC/B,MAAMpK,YAAYqK,cAAc,CAC9BlJ,KACAmH,KACA;gCACEgC,YAAY;gCACZC,WAAWhJ;gCACXiJ,WAAW;gCACXC,mBAAkBtM,4LAAAA,EAAoB;oCACpCuM,cAAchH;oCACdZ;gCACF;4BACF,GACAK;wBAEJ;wBACA,MAAMmF;oBACR;gBACF;gBAEA,oDAAoD;gBACpD,qBAAqB;gBACrB,IAAI5C,oBAAoB;oBACtBzB,gBAAgB;gBAClB;gBAEA,IAAIA,eAAe;oBACjB,MAAM0G,mBAAmB,MAAM3K,YAAY4K,gBAAgB,CAACzJ,KAAKyI,GAAG,CAClE5J,YAAYsE,KAAK,GACb,OACAtB,SACE,CAAC,CAAC,EAAEA,SAASzB,SAAS,GACtBA,SACN,OAAO,EACLmE,oBAAoBmF,6BAA6B,IAAI,EACtD;wBACC,IAAI,CAAC7K,YAAYsE,KAAK,EAAE;4BACtB,gLAAOtF,uBAAAA,EAAqB6L;wBAC9B;wBACA,OAAOlF;oBACT,GACA;wBACEmF,WAAWnN,oKAAAA,CAAUwC,KAAK;wBAC1B2F,YAAY;wBACZiF,mBAAmB;wBACnBjI,sBAAsB;wBACtBkI,kBAAkB,MAAMhL,YAAYiL,mBAAmB,CACrD9J,KACAiC,YACAR;wBAEFZ,WAAWX,IAAIW,SAAS;oBAC1B;oBAEF,IAAI2I,kBAAkB;wBACpB,sEAAsE;wBACtE,iCAAiC;wBACjC,OAAOA,iBAAiB7B,YAAY;wBACpC6B,iBAAiBO,MAAM,GAAG;wBAC1B,OAAOP;oBACT;gBACF;gBAEA,IACE,CAAC1M,+KAAAA,EAAekD,KAAK,kBACrB2B,wBACAC,2BACA,CAAC2C,oBACD;oBACAtE,IAAIU,UAAU,GAAG;oBACjB,+CAA+C;oBAC/CV,IAAI+J,SAAS,CAAC,kBAAkB;oBAChC/J,IAAIW,GAAG,CAAC;oBACR,OAAO;gBACT;gBAEA,IACEkC,iBACAyB,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,4BAAAA,mBAAoBsD,KAAK,KAAA,OAAA,KAAA,IAAzBtD,0BAA2BxF,IAAI,0KAAK7B,mBAAAA,CAAgB8B,KAAK,EACzD;oBACA,OAAO;wBACL6I,OAAO;4BACL9I,2KAAM7B,kBAAAA,CAAgB8B,KAAK;4BAC3BmJ,MAAM,IAAIvK,qKAAAA,CACRqM,OAAOC,IAAI,CAAC3F,mBAAmBsD,KAAK,CAACM,IAAI,GACzC;gCACEgC,aAAa;gCACbzC,UAAU;oCACR/G,YAAY4D,mBAAmBsD,KAAK,CAACO,MAAM;oCAC3CxE,SAASW,mBAAmBsD,KAAK,CAACjE,OAAO;gCAC3C;4BACF;4BAEFqE,UAAU,CAAC;4BACXG,QAAQ7D,mBAAmBsD,KAAK,CAACO,MAAM;4BACvCxE,SAASW,mBAAmBsD,KAAK,CAACjE,OAAO;wBAC3C;wBACA+D,cAAc;4BAAEyC,YAAY;4BAAGC,QAAQrF;wBAAU;oBACnD;gBACF;gBACA,OAAOR;YACT;YAEA,MAAM8F,SAAS,MAAMzL,YAAYuF,cAAc,CAAC;gBAC9CvB;gBACA7C;gBACAiC;gBACA0H,mKAAWnN,YAAAA,CAAUwC,KAAK;gBAC1B2C;gBACAC;gBACAf,WAAWX,IAAIW,SAAS;gBACxByD,mBAAmBA;gBACnB7C;YACF;YAEA,oDAAoD;YACpD,uDAAuD;YACvD,qBAAqB;YACrB,IAAIqB,iBAAiB,CAAA,CAACwH,UAAAA,OAAAA,KAAAA,IAAAA,OAAQP,MAAM,GAAE;gBACpCjH,gBAAgB;YAClB;YAEA,yCAAyC;YACzC,IAAI,CAACwH,QAAQ;gBACX;YACF;YAEA,IAAI/H,kBAAkB,+JAACzF,iBAAAA,EAAekD,KAAK,gBAAgB;gBACzDC,IAAI+J,SAAS,CACX,kBACArI,uBACI,gBACA2I,OAAOP,MAAM,GACX,SACAO,OAAOrB,OAAO,GACZ,UACA;YAEZ;YAEA,IAAItB;YAEJ,IAAI,CAACpF,kBAAkBO,eAAe;gBACpC,IAAI,CAAC7C,IAAIsK,SAAS,CAAC,kBAAkB;oBACnC5C,eAAe;wBAAEyC,YAAY;wBAAGC,QAAQrF;oBAAU;gBACpD;YACF,OAAO,IAAIhC,WAAW;gBACpB,MAAMwH,qBAAqB1N,+KAAAA,EAAekD,KAAK;gBAE/C2H,eAAe;oBACbyC,YACE,OAAOI,uBAAuB,cAAc,IAAIA;oBAClDH,QAAQrF;gBACV;YACF,OAAO,IAAI/B,WAAW;gBACpB0E,eAAe;oBAAEyC,YAAY;oBAAGC,QAAQrF;gBAAU;YACpD,OAAO,IAAIsF,OAAO3C,YAAY,EAAE;gBAC9B,wEAAwE;gBACxE,oBAAoB;gBACpB,IAAI,OAAO2C,OAAO3C,YAAY,CAACyC,UAAU,KAAK,UAAU;wBAQ5CE;oBAPV,IAAIA,OAAO3C,YAAY,CAACyC,UAAU,GAAG,GAAG;wBACtC,MAAM,OAAA,cAEL,CAFK,IAAIK,MACR,CAAC,2CAA2C,EAAEH,OAAO3C,YAAY,CAACyC,UAAU,CAAC,IAAI,CAAC,GAD9E,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBACAzC,eAAe;wBACbyC,YAAYE,OAAO3C,YAAY,CAACyC,UAAU;wBAC1CC,QAAQC,CAAAA,CAAAA,uBAAAA,OAAO3C,YAAY,KAAA,OAAA,KAAA,IAAnB2C,qBAAqBD,MAAM,KAAIpI,WAAWyI,UAAU;oBAC9D;gBACF,OAAO;oBACL,oBAAoB;oBACpB/C,eAAe;wBACbyC,6JAAY1M,iBAAAA;wBACZ2M,QAAQrF;oBACV;gBACF;YACF;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAI2C,gBAAgB,CAAC1H,IAAIsK,SAAS,CAAC,kBAAkB;gBACnDtK,IAAI+J,SAAS,CAAC,uLAAiBzM,wBAAAA,EAAsBoK;YACvD;YAEA,sBAAsB;YACtB,IAAI,CAAC2C,OAAOzC,KAAK,EAAE;oBAQfyC;gBAPF,oDAAoD;gBACpD,qDAAqD;gBACrD,4DAA4D;gBAC5D,2BAA2B;8KAC3BzN,iBAAAA,EACEmD,KACA,sBAAA,CACAsK,wBAAAA,OAAO3C,YAAY,KAAA,OAAA,KAAA,IAAnB2C,sBAAqBF,UAAU;gBAGjCnK,IAAIU,UAAU,GAAG;gBAEjB,IAAIoC,mBAAmB;oBACrB9C,IAAIW,GAAG,CAAC;oBACR;gBACF;gBACA,4DAA4D;gBAC5D,IAAIoB,uBAAAA,OAAAA,KAAAA,IAAAA,oBAAqB2I,SAAS,EAAE;oBAClC,MAAM3I,oBAAoB2I,SAAS,CAAC3K,KAAKC,KAAKkB,WAAW;gBAC3D,OAAO;oBACLlB,IAAIW,GAAG,CAAC;gBACV;gBACA;YACF;YAEA,IAAI0J,OAAOzC,KAAK,CAAC9I,IAAI,0KAAK7B,kBAAAA,CAAgB6K,QAAQ,EAAE;gBAClD,IAAIhF,mBAAmB;oBACrB9C,IAAI+J,SAAS,CAAC,gBAAgB;oBAC9B/J,IAAIW,GAAG,CAACgK,KAAKC,SAAS,CAACP,OAAOzC,KAAK,CAACG,KAAK;oBACzC;gBACF,OAAO;oBACL,MAAM8C,iBAAiB,CAAC7C;wBACtB,MAAM8C,WAAW;4BACfC,aAAa/C,SAASgD,SAAS,CAACC,YAAY;4BAC5CvK,YAAYsH,SAASgD,SAAS,CAACE,mBAAmB;4BAClD9E,UAAU4B,SAASgD,SAAS,CAACG,sBAAsB;wBACrD;wBACA,MAAMzK,2KAAalD,oBAAAA,EAAkBsN;wBACrC,MAAM,EAAE1E,QAAQ,EAAE,GAAGpE;wBAErB,IACEoE,YACA0E,SAAS1E,QAAQ,KAAK,SACtB0E,SAASC,WAAW,CAACK,UAAU,CAAC,MAChC;4BACAN,SAASC,WAAW,GAAG,GAAG3E,WAAW0E,SAASC,WAAW,EAAE;wBAC7D;wBAEA,IAAID,SAASC,WAAW,CAACK,UAAU,CAAC,MAAM;4BACxCN,SAASC,WAAW,OAAGxN,kLAAAA,EACrBuN,SAASC,WAAW;wBAExB;wBAEA/K,IAAIU,UAAU,GAAGA;wBACjBV,IAAI+J,SAAS,CAAC,YAAYe,SAASC,WAAW;wBAC9C,IAAIrK,kMAAe5C,qBAAAA,CAAmBuN,iBAAiB,EAAE;4BACvDrL,IAAI+J,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEe,SAASC,WAAW,EAAE;wBAC1D;wBACA/K,IAAIW,GAAG,CAACmK,SAASC,WAAW;oBAC9B;oBACA,MAAMF,eAAeR,OAAOzC,KAAK,CAACG,KAAK;oBACvC,OAAO;gBACT;YACF;YAEA,IAAIsC,OAAOzC,KAAK,CAAC9I,IAAI,0KAAK7B,kBAAAA,CAAgB8B,KAAK,EAAE;gBAC/C,MAAM,OAAA,cAEL,CAFK,IAAIyL,MACR,CAAC,0DAA0D,CAAC,GADxD,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,oDAAoD;YACpD,IAAI5L,YAAYsE,KAAK,EAAE;gBACrBlD,IAAI+J,SAAS,CAAC,iBAAiB;YACjC;YAEA,oCAAoC;YACpC,IAAItI,aAAa;gBACfzB,IAAI+J,SAAS,CACX,iBACA;YAEJ;YAEA,0DAA0D;YAC1D,2BAA2B;YAC3B,kKACElN,iBAAAA,EAAekD,KAAK,wBACnBkD,6KACCpG,iBAAAA,EAAekD,KAAK,kBACpBC,IAAIU,UAAU,KAAK,KACrB;gBACA,OAAO;YACT;YAEA,oKAAMhD,mBAAAA,EAAiB;gBACrBqC;gBACAC;gBACA,6DAA6D;gBAC7D,UAAU;gBACVqK,QACEvH,qBAAqB,CAACG,eAAe,CAACD,YAClC,+JAAIrF,UAAAA,CACFqM,OAAOC,IAAI,CAACU,KAAKC,SAAS,CAACP,OAAOzC,KAAK,CAACI,QAAQ,IAChD;oBACEkC,aAAa;oBACbzC,UAAU4C,OAAOzC,KAAK,CAACM,IAAI,CAACT,QAAQ;gBACtC,KAEF4C,OAAOzC,KAAK,CAACM,IAAI;gBACvBoD,eAAetJ,WAAWsJ,aAAa;gBACvCC,iBAAiBvJ,WAAWuJ,eAAe;gBAC3C7D,cAAc9I,YAAYsE,KAAK,GAAG6B,YAAY2C;gBAC9C8D,MAAM1I,oBAAoB,SAAS;YACrC;QACF;QAEA,oDAAoD;QACpD,yDAAyD;QACzD,IAAIe,YAAY;YACd,MAAMM;QACR,OAAO;YACL,MAAMP,OAAO6H,qBAAqB,CAAC1L,IAAI4D,OAAO,EAAE,IAC9CC,OAAO8H,KAAK,CACVlP,qLAAAA,CAAeiM,aAAa,EAC5B;oBACEkD,UAAU,GAAG5H,OAAO,CAAC,EAAEhE,IAAIgJ,GAAG,EAAE;oBAChCjK,uKAAMpC,WAAAA,CAASkP,MAAM;oBACrBC,YAAY;wBACV,eAAe9H;wBACf,eAAehE,IAAIgJ,GAAG;oBACxB;gBACF,GACA5E;QAGN;IACF,EAAE,OAAO+C,KAAK;QACZ,IAAI,CAAEA,CAAAA,4PAAerJ,mBAAc,GAAI;YACrC,MAAMe,YAAYqK,cAAc,CAC9BlJ,KACAmH,KACA;gBACEgC,YAAY;gBACZC,WAAWhJ;gBACXiJ,WAAW;gBACXC,yLAAkBtM,sBAAAA,EAAoB;oBACpCuM,cAAchH;oBACdZ;gBACF;YACF,GACAK;QAEJ;QACA,mDAAmD;QACnD,MAAMmF;IACR;AACF", "ignoreList": [0], "debugId": null}}]}