{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Breadcrumb\",\n);\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Footer\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Breadcrumb\",\n);\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Footer\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uCACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/privacy/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON><PERSON> } from \"@/components/Header\"\nimport { Shield, Lock, Eye, Trash2, Server, Clock, Mail, FileText } from \"lucide-react\"\n\nexport default function PrivacyPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header currentPath=\"/privacy\" />\n\n      <main className=\"py-20\">\n        <div className=\"container mx-auto px-4 max-w-4xl\">\n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <div className=\"w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\n              <Shield className=\"w-8 h-8 text-green-600\" />\n            </div>\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              Privacy Policy\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Your privacy is important to us. This policy explains how we collect, use, and protect your information.\n            </p>\n            <div className=\"flex items-center justify-center space-x-2 mt-6 text-sm text-gray-500\">\n              <Clock className=\"w-4 h-4\" />\n              <span>Last updated: January 2025</span>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"prose prose-lg max-w-none\">\n            \n            {/* Section 1 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n                  <FileText className=\"w-4 h-4 text-blue-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">1. Information We Collect</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  We collect minimal information to provide our compression services:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li><strong>Files you upload:</strong> Images and videos for compression processing</li>\n                  <li><strong>Technical data:</strong> IP address, browser type, and usage statistics</li>\n                  <li><strong>Optional data:</strong> Email address if you contact support</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  We do not require account registration and do not collect personal information unless voluntarily provided.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 2 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center\">\n                  <Lock className=\"w-4 h-4 text-green-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">2. How We Use Your Information</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  Your information is used solely for:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li>Processing and compressing your uploaded files</li>\n                  <li>Providing technical support when requested</li>\n                  <li>Improving our service performance and reliability</li>\n                  <li>Ensuring security and preventing abuse</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  We never use your files for training AI models, marketing purposes, or any other commercial activities.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 3 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center\">\n                  <Server className=\"w-4 h-4 text-purple-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">3. Data Storage and Security</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  We implement industry-standard security measures:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li><strong>Encryption:</strong> All file transfers use HTTPS/TLS encryption</li>\n                  <li><strong>Secure servers:</strong> Files are processed on secure, monitored servers</li>\n                  <li><strong>Access control:</strong> Strict access controls limit who can access data</li>\n                  <li><strong>Regular audits:</strong> Security practices are regularly reviewed and updated</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  Our servers are located in secure data centers with physical and digital protection measures.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 4 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center\">\n                  <Trash2 className=\"w-4 h-4 text-red-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">4. Data Retention and Deletion</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  We prioritize data minimization and automatic deletion:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li><strong>Automatic deletion:</strong> All uploaded files are deleted within 1 hour of processing</li>\n                  <li><strong>No permanent storage:</strong> We do not keep copies of your files</li>\n                  <li><strong>Temporary processing:</strong> Files exist only during active compression</li>\n                  <li><strong>Log retention:</strong> Technical logs are kept for 30 days for security purposes</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  You can request immediate deletion of your files by contacting our support team.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 5 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\">\n                  <Eye className=\"w-4 h-4 text-orange-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">5. Third-Party Sharing</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  We do not share your data with third parties, except:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li><strong>Legal requirements:</strong> When required by law or legal process</li>\n                  <li><strong>Service providers:</strong> Essential infrastructure providers (hosting, CDN) under strict agreements</li>\n                  <li><strong>Security threats:</strong> To prevent fraud or security breaches</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  We never sell, rent, or trade your personal information or files to third parties for commercial purposes.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 6 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center\">\n                  <Shield className=\"w-4 h-4 text-indigo-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">6. Your Rights</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  You have the following rights regarding your data:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li><strong>Access:</strong> Request information about data we have about you</li>\n                  <li><strong>Deletion:</strong> Request immediate deletion of your files</li>\n                  <li><strong>Correction:</strong> Correct any inaccurate information</li>\n                  <li><strong>Portability:</strong> Receive your data in a portable format</li>\n                  <li><strong>Objection:</strong> Object to certain data processing activities</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  To exercise these rights, please contact us using the information provided below.\n                </p>\n              </div>\n            </div>\n\n            {/* Contact */}\n            <div className=\"bg-green-50 rounded-xl p-8 text-center\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Privacy Questions or Concerns?</h3>\n              <p className=\"text-gray-600 mb-6\">\n                If you have any questions about this Privacy Policy or our data practices, we're here to help.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <a \n                  href=\"mailto:<EMAIL>\" \n                  className=\"inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  <Mail className=\"w-4 h-4 mr-2\" />\n                  Contact Privacy Team\n                </a>\n                <a \n                  href=\"/terms\" \n                  className=\"inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors\"\n                >\n                  <FileText className=\"w-4 h-4 mr-2\" />\n                  View Terms of Service\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;gBAAC,aAAY;;;;;;0BAEpB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA0B;;;;;;;sEACtC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAwB;;;;;;;sEACpC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAuB;;;;;;;;;;;;;8DAErC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAoB;;;;;;;sEAChC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAwB;;;;;;;sEACpC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAwB;;;;;;;sEACpC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAwB;;;;;;;;;;;;;8DAEtC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA4B;;;;;;;sEACxC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA8B;;;;;;;sEAC1C,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA8B;;;;;;;sEAC1C,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAuB;;;;;;;;;;;;;8DAErC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA4B;;;;;;;sEACxC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA2B;;;;;;;sEACvC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAA0B;;;;;;;;;;;;;8DAExC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAgB;;;;;;;sEAC5B,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAkB;;;;;;;sEAC9B,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAoB;;;;;;;sEAChC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAqB;;;;;;;sEACjC,8OAAC;;8EAAG,8OAAC;8EAAO;;;;;;gEAAmB;;;;;;;;;;;;;8DAEjC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,8OAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}