{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Breadcrumb\",\n);\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Footer\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Breadcrumb\",\n);\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Footer\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uCACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport {\n  ArrowRight,\n  Image as ImageIcon,\n  Video,\n  Zap,\n  Shield,\n  Download,\n  Star,\n  Upload,\n  Settings,\n  CheckCircle,\n  Users,\n  Globe,\n  Smartphone,\n  Monitor,\n  Camera,\n  Play,\n  FileImage,\n  Layers,\n  Award\n} from \"lucide-react\"\nimport { <PERSON><PERSON>, Footer } from \"@/components/Header\"\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header currentPath=\"/\" />\n\n      <main>\n      {/* Hero Section - 模块一 */}\n      <section className=\"relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n        <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n        <div className=\"container mx-auto px-4 py-20\">\n          <div className=\"text-center max-w-4xl mx-auto\">\n            <h1 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Free Online Image & Video Compressor\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mt-2\">\n                Reduce File Size Instantly\n              </span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed\">\n              Experience the best image compressor and video compressor available. Compress images and compress videos with our simple, efficient, and secure tool, ensuring your privacy is always protected.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n              <Link\n                href=\"/compress\"\n                className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\"\n              >\n                Compress Files Now\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </Link>\n              <button className=\"inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200\">\n                <Play className=\"mr-2 w-5 h-5\" />\n                Watch Demo\n              </button>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n              <div className=\"text-center p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200\">\n                <div className=\"text-4xl font-bold text-blue-600 mb-2\">10M+</div>\n                <div className=\"text-gray-600 font-medium\">Files Compressed</div>\n              </div>\n              <div className=\"text-center p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200\">\n                <div className=\"text-4xl font-bold text-purple-600 mb-2\">90%</div>\n                <div className=\"text-gray-600 font-medium\">Average Size Reduction</div>\n              </div>\n              <div className=\"text-center p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200\">\n                <div className=\"text-4xl font-bold text-green-600 mb-2\">100%</div>\n                <div className=\"text-gray-600 font-medium\">Privacy Protected</div>\n              </div>\n            </div>\n\n            {/* Hero Image Placeholder */}\n            <div className=\"relative max-w-4xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl p-8 border-2 border-dashed border-blue-300\">\n                <div className=\"flex items-center justify-center space-x-8\">\n                  <div className=\"text-center\">\n                    <div className=\"w-24 h-24 bg-red-100 rounded-xl flex items-center justify-center mb-4 mx-auto\">\n                      <FileImage className=\"w-12 h-12 text-red-500\" />\n                    </div>\n                    <p className=\"text-sm text-gray-600\">Large File<br/>5.2 MB</p>\n                  </div>\n                  <ArrowRight className=\"w-8 h-8 text-blue-600 animate-pulse\" />\n                  <div className=\"text-center\">\n                    <div className=\"w-24 h-24 bg-green-100 rounded-xl flex items-center justify-center mb-4 mx-auto\">\n                      <FileImage className=\"w-12 h-12 text-green-500\" />\n                    </div>\n                    <p className=\"text-sm text-gray-600\">Compressed<br/>520 KB</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Product Advantages Section - 模块二 */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">\n              Why Our Image & Video Compressor Stands Out\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Discover the power of our image compressor and video compressor. We offer batch compress images capabilities, saving you hours of manual work. Efficiency and simplicity are built into our core.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16\">\n            {/* First Advantage */}\n            <div className=\"order-2 lg:order-1\">\n              <div className=\"bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8\">\n                <div className=\"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6\">\n                  <Shield className=\"w-8 h-8 text-blue-600\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  High-Efficiency Image Compressor with Free, Private Processing\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Unlike competitors that limit you, we offer free, secure local processing for your compressed images. Our system is designed to compress JPEG and compress PNG files with unmatched speed, and all processing happens in your browser for maximum privacy.\n                </p>\n                <div className=\"mt-6 flex items-center space-x-4\">\n                  <div className=\"flex items-center text-sm text-green-600\">\n                    <CheckCircle className=\"w-4 h-4 mr-2\" />\n                    100% Private\n                  </div>\n                  <div className=\"flex items-center text-sm text-blue-600\">\n                    <Zap className=\"w-4 h-4 mr-2\" />\n                    Lightning Fast\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"order-1 lg:order-2\">\n              <div className=\"bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-12 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <Monitor className=\"w-24 h-24 text-gray-600 mx-auto mb-4\" />\n                  <p className=\"text-gray-600\">Browser-based Processing</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            {/* Second Advantage */}\n            <div className=\"bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-12 flex items-center justify-center\">\n              <div className=\"text-center\">\n                <Layers className=\"w-24 h-24 text-gray-600 mx-auto mb-4\" />\n                <p className=\"text-gray-600\">Advanced Compression</p>\n              </div>\n            </div>\n            <div>\n              <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8\">\n                <div className=\"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mb-6\">\n                  <Settings className=\"w-8 h-8 text-purple-600\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  More Than Compression: Advanced Format Conversion and Batch Processing\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Our image compressor goes beyond simple compression. Convert image formats, compress videos to smaller sizes, or use our advanced algorithms to reduce image file size by up to 90%. It's a comprehensive tool for perfect results.\n                </p>\n                <div className=\"mt-6\">\n                  <Link\n                    href=\"/compress\"\n                    className=\"inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors\"\n                  >\n                    Try for Free Now\n                    <ArrowRight className=\"ml-2 w-4 h-4\" />\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Use Cases Section - 模块三 */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">\n              Real-World Applications of Our Compression Tools\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              See how our image compressor and video compressor help creators and businesses. It's the ultimate tool to reduce file size and optimize your visual content for web and mobile.\n            </p>\n          </div>\n\n          <div className=\"space-y-20\">\n            {/* Use Case 1 */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              <div className=\"bg-gradient-to-br from-blue-100 to-cyan-100 rounded-2xl p-12 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <Globe className=\"w-24 h-24 text-blue-600 mx-auto mb-4\" />\n                  <div className=\"text-sm text-blue-600 font-medium\">Web Optimization</div>\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                  Perfect for Web Developers: Optimize Images for Faster Loading\n                </h3>\n                <p className=\"text-gray-600 text-lg leading-relaxed mb-6\">\n                  With over 10 million images compressed, our image compressor is the go-to choice for web optimization. This powerful tool ensures faster page loading by reducing image file size without quality loss, helping you improve SEO rankings effortlessly.\n                </p>\n                <div className=\"flex items-center space-x-6 mb-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-600\">60%</div>\n                    <div className=\"text-sm text-gray-600\">Faster Loading</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-blue-600\">90%</div>\n                    <div className=\"text-sm text-gray-600\">Size Reduction</div>\n                  </div>\n                </div>\n                <Link\n                  href=\"/compress\"\n                  className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Compress Images Now\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Link>\n              </div>\n            </div>\n\n            {/* Use Case 2 */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              <div className=\"order-2 lg:order-1\">\n                <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                  Social Media Ready: Compress Videos for Easy Sharing\n                </h3>\n                <p className=\"text-gray-600 text-lg leading-relaxed mb-6\">\n                  90% of social media posts perform better with optimized media. Our video compressor provides perfect compression for Instagram, TikTok, and YouTube, reducing video file size by up to 80% while maintaining visual quality.\n                </p>\n                <div className=\"flex items-center space-x-6 mb-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-purple-600\">80%</div>\n                    <div className=\"text-sm text-gray-600\">Size Reduction</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-pink-600\">3x</div>\n                    <div className=\"text-sm text-gray-600\">Faster Upload</div>\n                  </div>\n                </div>\n                <Link\n                  href=\"/compress\"\n                  className=\"inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors\"\n                >\n                  Compress Your Video\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Link>\n              </div>\n              <div className=\"order-1 lg:order-2\">\n                <div className=\"bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-12 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <Smartphone className=\"w-24 h-24 text-purple-600 mx-auto mb-4\" />\n                    <div className=\"text-sm text-purple-600 font-medium\">Social Media</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Use Case 3 */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              <div className=\"bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl p-12 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <Layers className=\"w-24 h-24 text-green-600 mx-auto mb-4\" />\n                  <div className=\"text-sm text-green-600 font-medium\">Batch Processing</div>\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                  Storage Saver: Batch Compress Images for Cloud Storage\n                </h3>\n                <p className=\"text-gray-600 text-lg leading-relaxed mb-6\">\n                  Don't let large files eat up your storage space. Our batch image compressor can process hundreds of photos at once, helping you compress images efficiently and save up to 90% storage space.\n                </p>\n                <div className=\"flex items-center space-x-6 mb-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-600\">100+</div>\n                    <div className=\"text-sm text-gray-600\">Files at Once</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-emerald-600\">90%</div>\n                    <div className=\"text-sm text-gray-600\">Storage Saved</div>\n                  </div>\n                </div>\n                <Link\n                  href=\"/compress\"\n                  className=\"inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  Start Batch Compression\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Link>\n              </div>\n            </div>\n\n            {/* Use Case 4 */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              <div className=\"order-2 lg:order-1\">\n                <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                  Professional Quality: Lossless Compression for Photography\n                </h3>\n                <p className=\"text-gray-600 text-lg leading-relaxed mb-6\">\n                  Achieve professional results with our advanced compression algorithms. Whether you need to compress JPEG for web use or compress PNG with transparency, our tool maintains image quality while significantly reducing file size.\n                </p>\n                <div className=\"flex items-center space-x-6 mb-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-orange-600\">0%</div>\n                    <div className=\"text-sm text-gray-600\">Quality Loss</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-red-600\">85%</div>\n                    <div className=\"text-sm text-gray-600\">Size Reduction</div>\n                  </div>\n                </div>\n                <Link\n                  href=\"/compress\"\n                  className=\"inline-flex items-center px-6 py-3 bg-orange-600 text-white font-semibold rounded-lg hover:bg-orange-700 transition-colors\"\n                >\n                  Compress Photos\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Link>\n              </div>\n              <div className=\"order-1 lg:order-2\">\n                <div className=\"bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl p-12 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <Camera className=\"w-24 h-24 text-orange-600 mx-auto mb-4\" />\n                    <div className=\"text-sm text-orange-600 font-medium\">Photography</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* How-To Guide Section - 模块四 */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">\n              How to Use Our Image & Video Compressor\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              In just three simple steps, use our image compressor to get perfectly optimized files. It's the most efficient image compression tool free of charge you'll find online.\n            </p>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {/* Step 1 */}\n              <div className=\"text-center group\">\n                <div className=\"relative mb-8\">\n                  <div className=\"w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                    <Upload className=\"w-12 h-12 text-blue-600\" />\n                  </div>\n                  <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                    1\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                  Step 1: Upload Your Files\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Simply drag and drop your images or videos, or select them from your device. Our tool supports various formats including JPEG, PNG, MP4, GIF and can compress images in batch for maximum efficiency.\n                </p>\n              </div>\n\n              {/* Step 2 */}\n              <div className=\"text-center group\">\n                <div className=\"relative mb-8\">\n                  <div className=\"w-24 h-24 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                    <Settings className=\"w-12 h-12 text-purple-600\" />\n                  </div>\n                  <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                    2\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                  Step 2: Choose Compression Settings\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Select your desired compression level and output format. Our image compressor offers customizable quality settings to balance file size reduction with visual quality according to your needs.\n                </p>\n              </div>\n\n              {/* Step 3 */}\n              <div className=\"text-center group\">\n                <div className=\"relative mb-8\">\n                  <div className=\"w-24 h-24 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                    <Download className=\"w-12 h-12 text-green-600\" />\n                  </div>\n                  <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                    3\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                  Step 3: Download Your Compressed Files\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Our compression engine processes your files in seconds. Your optimized images and videos are ready for download, with significant file size reduction achieved. It's that simple and fast.\n                </p>\n              </div>\n            </div>\n\n            {/* CTA */}\n            <div className=\"text-center mt-12\">\n              <Link\n                href=\"/compress\"\n                className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\"\n              >\n                Start Compressing Now for Free\n                <ArrowRight className=\"ml-2 w-6 h-6\" />\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section - 模块五 */}\n      <section className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">\n              What Our Users Are Saying\n            </h2>\n            <div className=\"flex items-center justify-center space-x-1 mb-4\">\n              {[...Array(5)].map((_, i) => (\n                <Star key={i} className=\"w-6 h-6 text-yellow-400 fill-current\" />\n              ))}\n              <span className=\"ml-2 text-gray-600 font-medium\">4.9/5 from 10,000+ users</span>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Testimonial 1 */}\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"flex items-center space-x-1 mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                \"This is the best image compressor I've ever used. It reduced my photo file sizes by 85% without any visible quality loss. My website loads 60% faster now!\"\n              </p>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <Users className=\"w-5 h-5 text-blue-600\" />\n                </div>\n                <div>\n                  <div className=\"font-semibold text-gray-900\">Sarah Chen</div>\n                  <div className=\"text-sm text-gray-500\">Web Developer</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Testimonial 2 */}\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"flex items-center space-x-1 mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                \"I needed a reliable video compressor for my YouTube channel, and CompressHub delivered perfectly. The batch processing feature saved me hours of work.\"\n              </p>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center\">\n                  <Video className=\"w-5 h-5 text-purple-600\" />\n                </div>\n                <div>\n                  <div className=\"font-semibold text-gray-900\">Mike Rodriguez</div>\n                  <div className=\"text-sm text-gray-500\">Content Creator</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Testimonial 3 */}\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"flex items-center space-x-1 mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                \"The ability to compress images while maintaining transparency saved my design projects! This tool is incredibly efficient and user-friendly.\"\n              </p>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                  <ImageIcon className=\"w-5 h-5 text-green-600\" />\n                </div>\n                <div>\n                  <div className=\"font-semibold text-gray-900\">Emma Thompson</div>\n                  <div className=\"text-sm text-gray-500\">Graphic Designer</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Testimonial 4 */}\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"flex items-center space-x-1 mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                \"As an e-commerce business owner, this image compressor is essential for my product photos. Professional results with lightning-fast processing.\"\n              </p>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center\">\n                  <Award className=\"w-5 h-5 text-orange-600\" />\n                </div>\n                <div>\n                  <div className=\"font-semibold text-gray-900\">David Kim</div>\n                  <div className=\"text-sm text-gray-500\">Business Owner</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Section - 模块六 */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">\n              Frequently Asked Questions\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Get answers to common questions about our image and video compression tools.\n            </p>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"space-y-8\">\n              {/* FAQ 1 */}\n              <div className=\"bg-gray-50 rounded-2xl p-8\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                  How does the free image compressor work while ensuring privacy?\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Our image compressor processes all files locally in your browser, ensuring complete privacy. No files are uploaded to servers, and all compression happens on your device for maximum security. Your images never leave your computer.\n                </p>\n              </div>\n\n              {/* FAQ 2 */}\n              <div className=\"bg-gray-50 rounded-2xl p-8\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                  Can I compress images without losing quality?\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Yes, our advanced image compressor algorithm uses smart compression techniques to reduce file size while maintaining visual quality. You can achieve up to 90% size reduction with minimal quality impact, and you have full control over the quality settings.\n                </p>\n              </div>\n\n              {/* FAQ 3 */}\n              <div className=\"bg-gray-50 rounded-2xl p-8\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                  What file formats does your compressor support?\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Our tool supports all major formats: compress JPEG, compress PNG, compress GIF, MP4 video compression, and many more including WebP, BMP, TIFF, AVI, MOV, and WebM. You can also convert between formats during compression.\n                </p>\n              </div>\n\n              {/* FAQ 4 */}\n              <div className=\"bg-gray-50 rounded-2xl p-8\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                  How much can I reduce my file sizes?\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Typically, you can reduce image file size by 70-90% and video file size by 60-80% depending on the original format and quality settings. Our compressor is optimized for maximum efficiency while preserving visual quality.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Final CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-600\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Ready to Compress Your Files?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Join millions of users who trust our compression tools. Start optimizing your images and videos today.\n          </p>\n          <Link\n            href=\"/compress\"\n            className=\"inline-flex items-center px-8 py-4 bg-white text-blue-600 font-bold text-lg rounded-xl hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\"\n          >\n            Get Started for Free\n            <ArrowRight className=\"ml-2 w-6 h-6\" />\n          </Link>\n        </div>\n      </section>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;gBAAC,aAAY;;;;;;0BAEpB,8OAAC;;kCAED,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAoD;8DAEhE,8OAAC;oDAAK,WAAU;8DAAwF;;;;;;;;;;;;sDAI1G,8OAAC;4CAAE,WAAU;sDAA+D;;;;;;sDAK5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;wDACX;sEAEC,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;8DAExB,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAMrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAA4B;;;;;;;;;;;;8DAE7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA0C;;;;;;sEACzD,8OAAC;4DAAI,WAAU;sEAA4B;;;;;;;;;;;;8DAE7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;sDAK/C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;8EAEvB,8OAAC;oEAAE,WAAU;;wEAAwB;sFAAU,8OAAC;;;;;wEAAI;;;;;;;;;;;;;sEAEtD,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;8EAEvB,8OAAC;oEAAE,WAAU;;wEAAwB;sFAAU,8OAAC;;;;;wEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUlE,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEAGtD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;kEAG7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAG1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDAMxC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMrC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;sDAGjC,8OAAC;sDACC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEAGtD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;kEAG7C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;gEACX;8EAEC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAI,WAAU;0EAAoC;;;;;;;;;;;;;;;;;8DAGvD,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEAGtD,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAG1D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAoC;;;;;;sFACnD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAmC;;;;;;sFAClD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAG3C,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;gEACX;8EAEC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAM5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEAGtD,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAG1D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAqC;;;;;;sFACpD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAmC;;;;;;sFAClD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAG3C,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;gEACX;8EAEC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;8DAG1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;oEAAI,WAAU;8EAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO7D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAAI,WAAU;0EAAqC;;;;;;;;;;;;;;;;;8DAGxD,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEAGtD,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAG1D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAoC;;;;;;sFACnD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAsC;;;;;;sFACrD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAG3C,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;gEACX;8EAEC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAM5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEAGtD,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAG1D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAqC;;;;;;sFACpD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAkC;;;;;;sFACjD,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAG3C,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;gEACX;8EAEC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;8DAG1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAI,WAAU;8EAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnE,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAEpB,8OAAC;oEAAI,WAAU;8EAA0H;;;;;;;;;;;;sEAI3I,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEAGrD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAM/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,8OAAC;oEAAI,WAAU;8EAA4H;;;;;;;;;;;;sEAI7I,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEAGrD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAM/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,8OAAC;oEAAI,WAAU;8EAA2H;;;;;;;;;;;;sEAI5I,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEAGrD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAOjD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAI,WAAU;;gDACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;8DAEb,8OAAC;oDAAK,WAAU;8DAAiC;;;;;;;;;;;;;;;;;;8CAIrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAS;gEAAC,WAAU;;;;;;;;;;;sEAEvB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAM/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAM/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAM/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEAGrD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CAGnD,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}