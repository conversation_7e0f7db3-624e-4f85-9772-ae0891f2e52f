{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Breadcrumb\",\n);\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Footer\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Breadcrumb\",\n);\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Footer\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uCACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/terms/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON><PERSON> } from \"@/components/Header\"\nimport { FileText, Shield, AlertCircle, Scale, Globe, Clock } from \"lucide-react\"\n\nexport default function TermsPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header currentPath=\"/terms\" />\n\n      <main className=\"py-20\">\n        <div className=\"container mx-auto px-4 max-w-4xl\">\n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <div className=\"w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\n              <FileText className=\"w-8 h-8 text-blue-600\" />\n            </div>\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              Terms of Service\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Please read these terms carefully before using our image and video compression services.\n            </p>\n            <div className=\"flex items-center justify-center space-x-2 mt-6 text-sm text-gray-500\">\n              <Clock className=\"w-4 h-4\" />\n              <span>Last updated: January 2025</span>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"prose prose-lg max-w-none\">\n            \n            {/* Section 1 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n                  <Scale className=\"w-4 h-4 text-blue-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">1. Acceptance of Terms</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  By accessing and using CompressHub's image and video compression services, you accept and agree to be bound by the terms and provision of this agreement.\n                </p>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  If you do not agree to abide by the above, please do not use this service. We reserve the right to change these terms at any time without prior notice.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 2 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center\">\n                  <Shield className=\"w-4 h-4 text-green-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">2. Service Description</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  CompressHub provides online image and video compression services that allow users to reduce file sizes while maintaining quality. Our services include:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li>Image compression for JPEG, PNG, WebP, and other formats</li>\n                  <li>Video compression for MP4, AVI, MOV, and other formats</li>\n                  <li>Batch processing capabilities</li>\n                  <li>Format conversion services</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  We strive to provide reliable service but do not guarantee uninterrupted availability or error-free operation.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 3 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center\">\n                  <Globe className=\"w-4 h-4 text-purple-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">3. User Responsibilities</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  Users are responsible for ensuring they have the right to compress and process the files they upload. You agree not to:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li>Upload copyrighted material without proper authorization</li>\n                  <li>Upload illegal, harmful, or offensive content</li>\n                  <li>Use the service for any unlawful purposes</li>\n                  <li>Attempt to reverse engineer or compromise our systems</li>\n                  <li>Upload files containing malware or viruses</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  We reserve the right to refuse service or terminate accounts that violate these terms.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 4 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\">\n                  <AlertCircle className=\"w-4 h-4 text-orange-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">4. Privacy and Data Protection</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  We take your privacy seriously. Files uploaded to our service are:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li>Processed securely using industry-standard encryption</li>\n                  <li>Automatically deleted from our servers after processing</li>\n                  <li>Never shared with third parties</li>\n                  <li>Not used for any purpose other than compression</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  For more details, please review our Privacy Policy.\n                </p>\n              </div>\n            </div>\n\n            {/* Section 5 */}\n            <div className=\"mb-12\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center\">\n                  <Shield className=\"w-4 h-4 text-red-600\" />\n                </div>\n                <h2 className=\"text-2xl font-bold text-gray-900 m-0\">5. Limitation of Liability</h2>\n              </div>\n              <div className=\"bg-gray-50 rounded-xl p-6\">\n                <p className=\"text-gray-700 leading-relaxed mb-4\">\n                  CompressHub provides this service \"as is\" without any warranties. We are not liable for:\n                </p>\n                <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-4\">\n                  <li>Any loss of data or files during processing</li>\n                  <li>Service interruptions or downtime</li>\n                  <li>Quality issues with compressed files</li>\n                  <li>Any indirect or consequential damages</li>\n                </ul>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  Users should always keep backups of their original files before processing.\n                </p>\n              </div>\n            </div>\n\n            {/* Contact */}\n            <div className=\"bg-blue-50 rounded-xl p-8 text-center\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Questions about our Terms?</h3>\n              <p className=\"text-gray-600 mb-6\">\n                If you have any questions about these Terms of Service, please contact us.\n              </p>\n              <a \n                href=\"mailto:<EMAIL>\" \n                className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Contact Support\n              </a>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;gBAAC,aAAY;;;;;;0BAEpB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}