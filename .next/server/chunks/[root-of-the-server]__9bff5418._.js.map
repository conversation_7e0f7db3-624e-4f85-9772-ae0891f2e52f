{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/api/compress-video/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport ffmpeg from 'fluent-ffmpeg'\nimport { promises as fs } from 'fs'\nimport path from 'path'\nimport { v4 as uuidv4 } from 'uuid'\n\n// 设置FFmpeg路径（如果需要）\n// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n    const options = JSON.parse(formData.get('options') as string || '{}')\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 })\n    }\n\n    // 验证文件类型\n    if (!file.type.includes('video')) {\n      return NextResponse.json({ error: 'File must be a video' }, { status: 400 })\n    }\n\n    // 创建临时目录\n    const tempDir = path.join(process.cwd(), 'temp')\n    await fs.mkdir(tempDir, { recursive: true })\n\n    // 生成唯一文件名\n    const fileId = uuidv4()\n    const inputExt = file.name.split('.').pop() || 'mp4'\n    const outputExt = options.outputFormat || 'mp4'\n    const inputPath = path.join(tempDir, `${fileId}_input.${inputExt}`)\n    const outputPath = path.join(tempDir, `${fileId}_output.${outputExt}`)\n\n    // 保存上传的文件\n    const buffer = Buffer.from(await file.arrayBuffer())\n    await fs.writeFile(inputPath, buffer)\n\n    // 获取原始文件大小\n    const originalSize = buffer.length\n\n    try {\n      // 执行FFmpeg压缩\n      await new Promise<void>((resolve, reject) => {\n        let command = ffmpeg(inputPath)\n\n        // 视频编解码器设置\n        const codec = options.codec || 'libx264'\n        command = command.videoCodec(codec)\n\n        // 音频编解码器\n        command = command.audioCodec('aac').audioBitrate('128k')\n\n        // 质量控制 - 使用CRF (Constant Rate Factor)\n        let crf = 23 // 默认值，平衡质量和大小\n        if (options.quality) {\n          // quality: 0.1-1.0 -> CRF: 51-18 (数值越小质量越好)\n          crf = Math.round(51 - (options.quality * 33))\n          crf = Math.max(18, Math.min(51, crf)) // 限制在18-51范围内\n        }\n        command = command.outputOptions(['-crf', crf.toString()])\n\n        // 预设 - 平衡编码速度和压缩效率\n        command = command.outputOptions(['-preset', 'medium'])\n\n        // 尺寸控制\n        if (options.maxWidth || options.maxHeight) {\n          const width = options.maxWidth || -1\n          const height = options.maxHeight || -1\n          command = command.size(`${width}x${height}`)\n        }\n\n        // 帧率控制\n        if (options.fps) {\n          command = command.fps(options.fps)\n        }\n\n        // 比特率控制（可选，CRF优先）\n        if (options.bitrate) {\n          command = command.videoBitrate(options.bitrate)\n        }\n\n        // 移除元数据\n        if (options.removeMetadata) {\n          command = command.outputOptions(['-map_metadata', '-1'])\n        }\n\n        // 针对不同格式的优化\n        if (outputExt === 'mp4') {\n          // MP4优化：快速启动\n          command = command.outputOptions(['-movflags', '+faststart'])\n        } else if (outputExt === 'webm') {\n          // WebM优化\n          command = command.videoCodec('libvpx-vp9').audioCodec('libopus')\n        }\n\n        // 两遍编码（可选，用于更好的质量控制）\n        if (options.quality && options.quality > 0.8) {\n          command = command.outputOptions(['-pass', '1', '-f', 'null'])\n        }\n\n        command\n          .output(outputPath)\n          .on('start', (commandLine) => {\n            console.log('FFmpeg command:', commandLine)\n          })\n          .on('progress', (progress) => {\n            console.log('Processing: ' + (progress.percent || 0) + '% done')\n          })\n          .on('end', () => {\n            console.log('Video compression finished')\n            resolve()\n          })\n          .on('error', (err) => {\n            console.error('FFmpeg error:', err)\n            reject(err)\n          })\n          .run()\n      })\n\n      // 读取压缩后的文件\n      const compressedBuffer = await fs.readFile(outputPath)\n      const compressedSize = compressedBuffer.length\n      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100\n\n      // 清理临时文件\n      try {\n        await fs.unlink(inputPath)\n        await fs.unlink(outputPath)\n      } catch (cleanupError) {\n        console.warn('Failed to cleanup temp files:', cleanupError)\n      }\n\n      // 返回压缩结果\n      return new NextResponse(compressedBuffer, {\n        status: 200,\n        headers: {\n          'Content-Type': `video/${outputExt}`,\n          'Content-Length': compressedSize.toString(),\n          'X-Original-Size': originalSize.toString(),\n          'X-Compressed-Size': compressedSize.toString(),\n          'X-Compression-Ratio': compressionRatio.toFixed(2),\n        },\n      })\n\n    } catch (ffmpegError) {\n      // 清理临时文件\n      try {\n        await fs.unlink(inputPath)\n        await fs.unlink(outputPath)\n      } catch (cleanupError) {\n        console.warn('Failed to cleanup temp files after error:', cleanupError)\n      }\n\n      console.error('FFmpeg processing failed:', ffmpegError)\n      return NextResponse.json(\n        { error: 'Video processing failed', details: ffmpegError },\n        { status: 500 }\n      )\n    }\n\n  } catch (error) {\n    console.error('API error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error', details: error },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,UAAU,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,cAAwB;QAEhE,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuB,GAAG;gBAAE,QAAQ;YAAI;QAC5E;QAEA,SAAS;QACT,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QACzC,MAAM,6FAAA,CAAA,WAAE,CAAC,KAAK,CAAC,SAAS;YAAE,WAAW;QAAK;QAE1C,UAAU;QACV,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM;QAC/C,MAAM,YAAY,QAAQ,YAAY,IAAI;QAC1C,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,OAAO,EAAE,UAAU;QAClE,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,QAAQ,EAAE,WAAW;QAErE,UAAU;QACV,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QACjD,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,WAAW;QAE9B,WAAW;QACX,MAAM,eAAe,OAAO,MAAM;QAElC,IAAI;YACF,aAAa;YACb,MAAM,IAAI,QAAc,CAAC,SAAS;gBAChC,IAAI,UAAU,CAAA,GAAA,2IAAA,CAAA,UAAM,AAAD,EAAE;gBAErB,WAAW;gBACX,MAAM,QAAQ,QAAQ,KAAK,IAAI;gBAC/B,UAAU,QAAQ,UAAU,CAAC;gBAE7B,SAAS;gBACT,UAAU,QAAQ,UAAU,CAAC,OAAO,YAAY,CAAC;gBAEjD,sCAAsC;gBACtC,IAAI,MAAM,GAAG,cAAc;;gBAC3B,IAAI,QAAQ,OAAO,EAAE;oBACnB,4CAA4C;oBAC5C,MAAM,KAAK,KAAK,CAAC,KAAM,QAAQ,OAAO,GAAG;oBACzC,MAAM,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,OAAM,cAAc;gBACtD;gBACA,UAAU,QAAQ,aAAa,CAAC;oBAAC;oBAAQ,IAAI,QAAQ;iBAAG;gBAExD,mBAAmB;gBACnB,UAAU,QAAQ,aAAa,CAAC;oBAAC;oBAAW;iBAAS;gBAErD,OAAO;gBACP,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;oBACzC,MAAM,QAAQ,QAAQ,QAAQ,IAAI,CAAC;oBACnC,MAAM,SAAS,QAAQ,SAAS,IAAI,CAAC;oBACrC,UAAU,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE,QAAQ;gBAC7C;gBAEA,OAAO;gBACP,IAAI,QAAQ,GAAG,EAAE;oBACf,UAAU,QAAQ,GAAG,CAAC,QAAQ,GAAG;gBACnC;gBAEA,kBAAkB;gBAClB,IAAI,QAAQ,OAAO,EAAE;oBACnB,UAAU,QAAQ,YAAY,CAAC,QAAQ,OAAO;gBAChD;gBAEA,QAAQ;gBACR,IAAI,QAAQ,cAAc,EAAE;oBAC1B,UAAU,QAAQ,aAAa,CAAC;wBAAC;wBAAiB;qBAAK;gBACzD;gBAEA,YAAY;gBACZ,IAAI,cAAc,OAAO;oBACvB,aAAa;oBACb,UAAU,QAAQ,aAAa,CAAC;wBAAC;wBAAa;qBAAa;gBAC7D,OAAO,IAAI,cAAc,QAAQ;oBAC/B,SAAS;oBACT,UAAU,QAAQ,UAAU,CAAC,cAAc,UAAU,CAAC;gBACxD;gBAEA,qBAAqB;gBACrB,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,GAAG,KAAK;oBAC5C,UAAU,QAAQ,aAAa,CAAC;wBAAC;wBAAS;wBAAK;wBAAM;qBAAO;gBAC9D;gBAEA,QACG,MAAM,CAAC,YACP,EAAE,CAAC,SAAS,CAAC;oBACZ,QAAQ,GAAG,CAAC,mBAAmB;gBACjC,GACC,EAAE,CAAC,YAAY,CAAC;oBACf,QAAQ,GAAG,CAAC,iBAAiB,CAAC,SAAS,OAAO,IAAI,CAAC,IAAI;gBACzD,GACC,EAAE,CAAC,OAAO;oBACT,QAAQ,GAAG,CAAC;oBACZ;gBACF,GACC,EAAE,CAAC,SAAS,CAAC;oBACZ,QAAQ,KAAK,CAAC,iBAAiB;oBAC/B,OAAO;gBACT,GACC,GAAG;YACR;YAEA,WAAW;YACX,MAAM,mBAAmB,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC;YAC3C,MAAM,iBAAiB,iBAAiB,MAAM;YAC9C,MAAM,mBAAmB,AAAC,CAAC,eAAe,cAAc,IAAI,eAAgB;YAE5E,SAAS;YACT,IAAI;gBACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;gBAChB,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;YAClB,EAAE,OAAO,cAAc;gBACrB,QAAQ,IAAI,CAAC,iCAAiC;YAChD;YAEA,SAAS;YACT,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,kBAAkB;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB,CAAC,MAAM,EAAE,WAAW;oBACpC,kBAAkB,eAAe,QAAQ;oBACzC,mBAAmB,aAAa,QAAQ;oBACxC,qBAAqB,eAAe,QAAQ;oBAC5C,uBAAuB,iBAAiB,OAAO,CAAC;gBAClD;YACF;QAEF,EAAE,OAAO,aAAa;YACpB,SAAS;YACT,IAAI;gBACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;gBAChB,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;YAClB,EAAE,OAAO,cAAc;gBACrB,QAAQ,IAAI,CAAC,6CAA6C;YAC5D;YAEA,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA2B,SAAS;YAAY,GACzD;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}