module.exports = {

"[project]/.next-internal/server/app/api/compress-gif/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/app/api/compress-gif/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fluent$2d$ffmpeg$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fluent-ffmpeg/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
;
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        const options = JSON.parse(formData.get('options') || '{}');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // 验证文件类型
        if (!file.type.includes('gif')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be a GIF'
            }, {
                status: 400
            });
        }
        // 创建临时目录
        const tempDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'temp');
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].mkdir(tempDir, {
            recursive: true
        });
        // 生成唯一文件名
        const fileId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const inputPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(tempDir, `${fileId}_input.gif`);
        const outputPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(tempDir, `${fileId}_output.gif`);
        // 保存上传的文件
        const buffer = Buffer.from(await file.arrayBuffer());
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].writeFile(inputPath, buffer);
        // 获取原始文件大小
        const originalSize = buffer.length;
        try {
            // 执行FFmpeg压缩 - 专门针对GIF优化
            await new Promise((resolve, reject)=>{
                // 第一步：生成调色板
                const paletteFile = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(tempDir, `${fileId}_palette.png`);
                // 构建调色板生成命令
                let paletteCommand = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fluent$2d$ffmpeg$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(inputPath);
                // 帧率控制
                const frameRate = options.frameRate || 10;
                let paletteFilter = `fps=${frameRate}`;
                // 尺寸控制
                if (options.maxWidth || options.maxHeight) {
                    const width = options.maxWidth || -1;
                    const height = options.maxHeight || -1;
                    paletteFilter += `,scale=${width}:${height}:flags=lanczos`;
                }
                // 调色板生成 - 根据质量调整颜色数量，更激进的压缩
                let maxColors = 256;
                if (options.quality < 0.2) {
                    maxColors = 32; // 极低质量，极少颜色
                } else if (options.quality < 0.4) {
                    maxColors = 64; // 低质量，少颜色
                } else if (options.quality < 0.6) {
                    maxColors = 96; // 中低质量
                } else if (options.quality < 0.8) {
                    maxColors = 128; // 中等质量
                } else {
                    maxColors = 192; // 高质量，减少一些颜色以获得压缩
                }
                paletteFilter += `,palettegen=max_colors=${maxColors}:reserve_transparent=1`;
                paletteCommand.complexFilter(paletteFilter).output(paletteFile).on('end', ()=>{
                    console.log('Palette generation finished');
                    // 第二步：使用调色板压缩GIF
                    let finalCommand = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fluent$2d$ffmpeg$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])().input(inputPath).input(paletteFile);
                    // 构建最终压缩过滤器
                    let finalFilter = `fps=${frameRate}`;
                    if (options.maxWidth || options.maxHeight) {
                        const width = options.maxWidth || -1;
                        const height = options.maxHeight || -1;
                        finalFilter += `,scale=${width}:${height}:flags=lanczos`;
                    }
                    finalFilter += '[x];[x][1:v]paletteuse';
                    // 根据质量调整抖动和优化参数
                    if (options.quality < 0.3) {
                        // 极低质量：强抖动 + 差异模式 + 新帧检测
                        finalFilter += '=dither=bayer:bayer_scale=5:diff_mode=rectangle:new=1';
                    } else if (options.quality < 0.6) {
                        // 中低质量：中等抖动 + 差异模式
                        finalFilter += '=dither=bayer:bayer_scale=3:diff_mode=rectangle';
                    } else {
                        // 高质量：轻微抖动
                        finalFilter += '=dither=bayer:bayer_scale=1';
                    }
                    const outputOptions = [
                        '-y',
                        '-loop',
                        '0'
                    ];
                    // 根据质量添加额外的压缩选项
                    if (options.quality < 0.4) {
                        // 低质量：启用更激进的压缩
                        outputOptions.push('-compression_level', '9'); // 最高压缩级别
                        outputOptions.push('-preset', 'veryslow'); // 最慢但最高效的预设
                    } else if (options.quality < 0.7) {
                        // 中等质量：平衡压缩
                        outputOptions.push('-compression_level', '6');
                        outputOptions.push('-preset', 'slow');
                    } else {
                        // 高质量：轻度压缩
                        outputOptions.push('-compression_level', '3');
                        outputOptions.push('-preset', 'medium');
                    }
                    // 移除元数据
                    if (options.removeMetadata) {
                        outputOptions.push('-map_metadata', '-1');
                    }
                    // 优化文件大小
                    outputOptions.push('-optimize', '1'); // 启用优化
                    outputOptions.push('-fflags', '+bitexact'); // 确保一致的输出
                    finalCommand.complexFilter(finalFilter).outputOptions(outputOptions).output(outputPath).on('start', (commandLine)=>{
                        console.log('Final FFmpeg command:', commandLine);
                    }).on('progress', (progress)=>{
                        console.log('Final processing: ' + (progress.percent || 0) + '% done');
                    }).on('end', async ()=>{
                        console.log('GIF compression finished');
                        // 清理调色板文件
                        try {
                            await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(paletteFile);
                        } catch (e) {
                            console.warn('Failed to cleanup palette file:', e);
                        }
                        resolve();
                    }).on('error', async (err)=>{
                        console.error('Final FFmpeg error:', err);
                        // 清理调色板文件
                        try {
                            await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(paletteFile);
                        } catch (e) {
                            console.warn('Failed to cleanup palette file after error:', e);
                        }
                        reject(err);
                    }).run();
                }).on('error', (err)=>{
                    console.error('Palette generation error:', err);
                    reject(err);
                }).run();
            });
            // 读取压缩后的文件
            const compressedBuffer = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].readFile(outputPath);
            const compressedSize = compressedBuffer.length;
            const compressionRatio = (originalSize - compressedSize) / originalSize * 100;
            // 清理临时文件
            try {
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(inputPath);
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(outputPath);
            } catch (cleanupError) {
                console.warn('Failed to cleanup temp files:', cleanupError);
            }
            // 返回压缩结果
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](compressedBuffer, {
                status: 200,
                headers: {
                    'Content-Type': 'image/gif',
                    'Content-Length': compressedSize.toString(),
                    'X-Original-Size': originalSize.toString(),
                    'X-Compressed-Size': compressedSize.toString(),
                    'X-Compression-Ratio': compressionRatio.toFixed(2)
                }
            });
        } catch (ffmpegError) {
            // 清理临时文件
            try {
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(inputPath);
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].unlink(outputPath);
            } catch (cleanupError) {
                console.warn('Failed to cleanup temp files after error:', cleanupError);
            }
            console.error('FFmpeg GIF processing failed:', ffmpegError);
            // 提供更详细的错误信息
            let errorMessage = 'GIF processing failed';
            let errorDetails = ffmpegError;
            if (ffmpegError instanceof Error) {
                if (ffmpegError.message.includes('Invalid data found')) {
                    errorMessage = 'Invalid GIF file';
                    errorDetails = 'The uploaded file appears to be corrupted or not a valid GIF.';
                } else if (ffmpegError.message.includes('Permission denied')) {
                    errorMessage = 'Server permission error';
                    errorDetails = 'Server does not have permission to process the file. Please try again later.';
                } else if (ffmpegError.message.includes('No such file')) {
                    errorMessage = 'File processing error';
                    errorDetails = 'Temporary file was not created properly. Please try again.';
                } else {
                    errorDetails = ffmpegError.message;
                }
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: errorMessage,
                details: errorDetails,
                timestamp: new Date().toISOString()
            }, {
                status: 500
            });
        }
    } catch (error) {
        console.error('API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            details: error
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4adaffd4._.js.map