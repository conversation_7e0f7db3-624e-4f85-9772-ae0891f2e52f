{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/api/compress-gif/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport ffmpeg from 'fluent-ffmpeg'\nimport { promises as fs } from 'fs'\nimport path from 'path'\nimport { v4 as uuidv4 } from 'uuid'\n\n// 设置FFmpeg路径（如果需要）\n// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n    const options = JSON.parse(formData.get('options') as string || '{}')\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 })\n    }\n\n    // 验证文件类型\n    if (!file.type.includes('gif')) {\n      return NextResponse.json({ error: 'File must be a GIF' }, { status: 400 })\n    }\n\n    // 创建临时目录\n    const tempDir = path.join(process.cwd(), 'temp')\n    await fs.mkdir(tempDir, { recursive: true })\n\n    // 生成唯一文件名\n    const fileId = uuidv4()\n    const inputPath = path.join(tempDir, `${fileId}_input.gif`)\n    const outputPath = path.join(tempDir, `${fileId}_output.gif`)\n\n    // 保存上传的文件\n    const buffer = Buffer.from(await file.arrayBuffer())\n    await fs.writeFile(inputPath, buffer)\n\n    // 获取原始文件大小\n    const originalSize = buffer.length\n\n    try {\n      // 执行FFmpeg压缩 - 专门针对GIF优化\n      await new Promise<void>((resolve, reject) => {\n        // 第一步：生成调色板\n        const paletteFile = path.join(tempDir, `${fileId}_palette.png`)\n\n        // 构建调色板生成命令\n        let paletteCommand = ffmpeg(inputPath)\n\n        // 帧率控制\n        const frameRate = options.frameRate || 10\n        let paletteFilter = `fps=${frameRate}`\n\n        // 尺寸控制\n        if (options.maxWidth || options.maxHeight) {\n          const width = options.maxWidth || -1\n          const height = options.maxHeight || -1\n          paletteFilter += `,scale=${width}:${height}:flags=lanczos`\n        }\n\n        // 调色板生成 - 根据质量调整颜色数量\n        let maxColors = 256\n        if (options.quality < 0.3) {\n          maxColors = 64  // 低质量，少颜色\n        } else if (options.quality < 0.7) {\n          maxColors = 128 // 中等质量\n        } else {\n          maxColors = 256 // 高质量，全颜色\n        }\n\n        paletteFilter += `,palettegen=max_colors=${maxColors}:reserve_transparent=1`\n\n        paletteCommand\n          .complexFilter(paletteFilter)\n          .output(paletteFile)\n          .on('end', () => {\n            console.log('Palette generation finished')\n\n            // 第二步：使用调色板压缩GIF\n            let finalCommand = ffmpeg()\n              .input(inputPath)\n              .input(paletteFile)\n\n            // 构建最终压缩过滤器\n            let finalFilter = `fps=${frameRate}`\n\n            if (options.maxWidth || options.maxHeight) {\n              const width = options.maxWidth || -1\n              const height = options.maxHeight || -1\n              finalFilter += `,scale=${width}:${height}:flags=lanczos`\n            }\n\n            finalFilter += '[x];[x][1:v]paletteuse'\n\n            // 根据质量调整抖动\n            if (options.quality < 0.5) {\n              finalFilter += '=dither=bayer:bayer_scale=5:diff_mode=rectangle'\n            } else {\n              finalFilter += '=dither=bayer:bayer_scale=2'\n            }\n\n            const outputOptions = ['-y', '-loop', '0']\n\n            // 移除元数据\n            if (options.removeMetadata) {\n              outputOptions.push('-map_metadata', '-1')\n            }\n\n            finalCommand\n              .complexFilter(finalFilter)\n              .outputOptions(outputOptions)\n              .output(outputPath)\n              .on('start', (commandLine) => {\n                console.log('Final FFmpeg command:', commandLine)\n              })\n              .on('progress', (progress) => {\n                console.log('Final processing: ' + (progress.percent || 0) + '% done')\n              })\n              .on('end', async () => {\n                console.log('GIF compression finished')\n                // 清理调色板文件\n                try {\n                  await fs.unlink(paletteFile)\n                } catch (e) {\n                  console.warn('Failed to cleanup palette file:', e)\n                }\n                resolve()\n              })\n              .on('error', async (err) => {\n                console.error('Final FFmpeg error:', err)\n                // 清理调色板文件\n                try {\n                  await fs.unlink(paletteFile)\n                } catch (e) {\n                  console.warn('Failed to cleanup palette file after error:', e)\n                }\n                reject(err)\n              })\n              .run()\n          })\n          .on('error', (err) => {\n            console.error('Palette generation error:', err)\n            reject(err)\n          })\n          .run()\n      })\n\n      // 读取压缩后的文件\n      const compressedBuffer = await fs.readFile(outputPath)\n      const compressedSize = compressedBuffer.length\n      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100\n\n      // 清理临时文件\n      try {\n        await fs.unlink(inputPath)\n        await fs.unlink(outputPath)\n      } catch (cleanupError) {\n        console.warn('Failed to cleanup temp files:', cleanupError)\n      }\n\n      // 返回压缩结果\n      return new NextResponse(compressedBuffer, {\n        status: 200,\n        headers: {\n          'Content-Type': 'image/gif',\n          'Content-Length': compressedSize.toString(),\n          'X-Original-Size': originalSize.toString(),\n          'X-Compressed-Size': compressedSize.toString(),\n          'X-Compression-Ratio': compressionRatio.toFixed(2),\n        },\n      })\n\n    } catch (ffmpegError) {\n      // 清理临时文件\n      try {\n        await fs.unlink(inputPath)\n        await fs.unlink(outputPath)\n      } catch (cleanupError) {\n        console.warn('Failed to cleanup temp files after error:', cleanupError)\n      }\n\n      console.error('FFmpeg processing failed:', ffmpegError)\n      return NextResponse.json(\n        { error: 'Video processing failed', details: ffmpegError },\n        { status: 500 }\n      )\n    }\n\n  } catch (error) {\n    console.error('API error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error', details: error },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,UAAU,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,cAAwB;QAEhE,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,SAAS;QACT,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QACzC,MAAM,6FAAA,CAAA,WAAE,CAAC,KAAK,CAAC,SAAS;YAAE,WAAW;QAAK;QAE1C,UAAU;QACV,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,UAAU,CAAC;QAC1D,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,WAAW,CAAC;QAE5D,UAAU;QACV,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QACjD,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,WAAW;QAE9B,WAAW;QACX,MAAM,eAAe,OAAO,MAAM;QAElC,IAAI;YACF,yBAAyB;YACzB,MAAM,IAAI,QAAc,CAAC,SAAS;gBAChC,YAAY;gBACZ,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,YAAY,CAAC;gBAE9D,YAAY;gBACZ,IAAI,iBAAiB,CAAA,GAAA,2IAAA,CAAA,UAAM,AAAD,EAAE;gBAE5B,OAAO;gBACP,MAAM,YAAY,QAAQ,SAAS,IAAI;gBACvC,IAAI,gBAAgB,CAAC,IAAI,EAAE,WAAW;gBAEtC,OAAO;gBACP,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;oBACzC,MAAM,QAAQ,QAAQ,QAAQ,IAAI,CAAC;oBACnC,MAAM,SAAS,QAAQ,SAAS,IAAI,CAAC;oBACrC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,OAAO,cAAc,CAAC;gBAC5D;gBAEA,qBAAqB;gBACrB,IAAI,YAAY;gBAChB,IAAI,QAAQ,OAAO,GAAG,KAAK;oBACzB,YAAY,IAAI,UAAU;gBAC5B,OAAO,IAAI,QAAQ,OAAO,GAAG,KAAK;oBAChC,YAAY,KAAI,OAAO;gBACzB,OAAO;oBACL,YAAY,KAAI,UAAU;gBAC5B;gBAEA,iBAAiB,CAAC,uBAAuB,EAAE,UAAU,sBAAsB,CAAC;gBAE5E,eACG,aAAa,CAAC,eACd,MAAM,CAAC,aACP,EAAE,CAAC,OAAO;oBACT,QAAQ,GAAG,CAAC;oBAEZ,iBAAiB;oBACjB,IAAI,eAAe,CAAA,GAAA,2IAAA,CAAA,UAAM,AAAD,IACrB,KAAK,CAAC,WACN,KAAK,CAAC;oBAET,YAAY;oBACZ,IAAI,cAAc,CAAC,IAAI,EAAE,WAAW;oBAEpC,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;wBACzC,MAAM,QAAQ,QAAQ,QAAQ,IAAI,CAAC;wBACnC,MAAM,SAAS,QAAQ,SAAS,IAAI,CAAC;wBACrC,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,OAAO,cAAc,CAAC;oBAC1D;oBAEA,eAAe;oBAEf,WAAW;oBACX,IAAI,QAAQ,OAAO,GAAG,KAAK;wBACzB,eAAe;oBACjB,OAAO;wBACL,eAAe;oBACjB;oBAEA,MAAM,gBAAgB;wBAAC;wBAAM;wBAAS;qBAAI;oBAE1C,QAAQ;oBACR,IAAI,QAAQ,cAAc,EAAE;wBAC1B,cAAc,IAAI,CAAC,iBAAiB;oBACtC;oBAEA,aACG,aAAa,CAAC,aACd,aAAa,CAAC,eACd,MAAM,CAAC,YACP,EAAE,CAAC,SAAS,CAAC;wBACZ,QAAQ,GAAG,CAAC,yBAAyB;oBACvC,GACC,EAAE,CAAC,YAAY,CAAC;wBACf,QAAQ,GAAG,CAAC,uBAAuB,CAAC,SAAS,OAAO,IAAI,CAAC,IAAI;oBAC/D,GACC,EAAE,CAAC,OAAO;wBACT,QAAQ,GAAG,CAAC;wBACZ,UAAU;wBACV,IAAI;4BACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;wBAClB,EAAE,OAAO,GAAG;4BACV,QAAQ,IAAI,CAAC,mCAAmC;wBAClD;wBACA;oBACF,GACC,EAAE,CAAC,SAAS,OAAO;wBAClB,QAAQ,KAAK,CAAC,uBAAuB;wBACrC,UAAU;wBACV,IAAI;4BACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;wBAClB,EAAE,OAAO,GAAG;4BACV,QAAQ,IAAI,CAAC,+CAA+C;wBAC9D;wBACA,OAAO;oBACT,GACC,GAAG;gBACR,GACC,EAAE,CAAC,SAAS,CAAC;oBACZ,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,OAAO;gBACT,GACC,GAAG;YACR;YAEA,WAAW;YACX,MAAM,mBAAmB,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC;YAC3C,MAAM,iBAAiB,iBAAiB,MAAM;YAC9C,MAAM,mBAAmB,AAAC,CAAC,eAAe,cAAc,IAAI,eAAgB;YAE5E,SAAS;YACT,IAAI;gBACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;gBAChB,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;YAClB,EAAE,OAAO,cAAc;gBACrB,QAAQ,IAAI,CAAC,iCAAiC;YAChD;YAEA,SAAS;YACT,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,kBAAkB;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,kBAAkB,eAAe,QAAQ;oBACzC,mBAAmB,aAAa,QAAQ;oBACxC,qBAAqB,eAAe,QAAQ;oBAC5C,uBAAuB,iBAAiB,OAAO,CAAC;gBAClD;YACF;QAEF,EAAE,OAAO,aAAa;YACpB,SAAS;YACT,IAAI;gBACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;gBAChB,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;YAClB,EAAE,OAAO,cAAc;gBACrB,QAAQ,IAAI,CAAC,6CAA6C;YAC5D;YAEA,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA2B,SAAS;YAAY,GACzD;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}