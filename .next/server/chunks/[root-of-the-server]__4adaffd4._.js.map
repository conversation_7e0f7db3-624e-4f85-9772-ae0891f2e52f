{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/api/compress-gif/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport ffmpeg from 'fluent-ffmpeg'\nimport { promises as fs } from 'fs'\nimport path from 'path'\nimport { v4 as uuidv4 } from 'uuid'\n\n// 设置FFmpeg路径（如果需要）\n// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n    const options = JSON.parse(formData.get('options') as string || '{}')\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 })\n    }\n\n    // 验证文件类型\n    if (!file.type.includes('gif')) {\n      return NextResponse.json({ error: 'File must be a GIF' }, { status: 400 })\n    }\n\n    // 创建临时目录\n    const tempDir = path.join(process.cwd(), 'temp')\n    await fs.mkdir(tempDir, { recursive: true })\n\n    // 生成唯一文件名\n    const fileId = uuidv4()\n    const inputPath = path.join(tempDir, `${fileId}_input.gif`)\n    const outputPath = path.join(tempDir, `${fileId}_output.gif`)\n\n    // 保存上传的文件\n    const buffer = Buffer.from(await file.arrayBuffer())\n    await fs.writeFile(inputPath, buffer)\n\n    // 获取原始文件大小\n    const originalSize = buffer.length\n\n    try {\n      // 执行FFmpeg压缩 - 使用简化的方法\n      await new Promise<void>((resolve, reject) => {\n        let command = ffmpeg(inputPath)\n\n        // 基本的GIF优化参数\n        const outputOptions = ['-y'] // 覆盖输出文件\n\n        // 帧率控制\n        const frameRate = options.frameRate || 10\n        command = command.fps(frameRate)\n\n        // 尺寸控制\n        if (options.maxWidth || options.maxHeight) {\n          const width = options.maxWidth || -1\n          const height = options.maxHeight || -1\n          command = command.size(`${width}x${height}`)\n        }\n\n        // 质量控制 - 通过比特率\n        if (options.quality && options.quality < 0.5) {\n          // 低质量 - 更激进的压缩\n          outputOptions.push('-b:v', '200k')\n        } else if (options.quality && options.quality < 0.8) {\n          // 中等质量\n          outputOptions.push('-b:v', '500k')\n        } else {\n          // 高质量\n          outputOptions.push('-b:v', '1000k')\n        }\n\n        // 循环设置\n        outputOptions.push('-loop', '0')\n\n        // 移除元数据\n        if (options.removeMetadata) {\n          outputOptions.push('-map_metadata', '-1')\n        }\n\n        command\n          .outputOptions(outputOptions)\n          .output(outputPath)\n          .on('start', (commandLine) => {\n            console.log('FFmpeg command:', commandLine)\n          })\n          .on('progress', (progress) => {\n            console.log('Processing: ' + (progress.percent || 0) + '% done')\n          })\n          .on('end', () => {\n            console.log('FFmpeg processing finished')\n            resolve()\n          })\n          .on('error', (err) => {\n            console.error('FFmpeg error:', err)\n            reject(err)\n          })\n          .run()\n      })\n\n      // 读取压缩后的文件\n      const compressedBuffer = await fs.readFile(outputPath)\n      const compressedSize = compressedBuffer.length\n      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100\n\n      // 清理临时文件\n      try {\n        await fs.unlink(inputPath)\n        await fs.unlink(outputPath)\n      } catch (cleanupError) {\n        console.warn('Failed to cleanup temp files:', cleanupError)\n      }\n\n      // 返回压缩结果\n      return new NextResponse(compressedBuffer, {\n        status: 200,\n        headers: {\n          'Content-Type': 'image/gif',\n          'Content-Length': compressedSize.toString(),\n          'X-Original-Size': originalSize.toString(),\n          'X-Compressed-Size': compressedSize.toString(),\n          'X-Compression-Ratio': compressionRatio.toFixed(2),\n        },\n      })\n\n    } catch (ffmpegError) {\n      // 清理临时文件\n      try {\n        await fs.unlink(inputPath)\n        await fs.unlink(outputPath)\n      } catch (cleanupError) {\n        console.warn('Failed to cleanup temp files after error:', cleanupError)\n      }\n\n      console.error('FFmpeg processing failed:', ffmpegError)\n      return NextResponse.json(\n        { error: 'Video processing failed', details: ffmpegError },\n        { status: 500 }\n      )\n    }\n\n  } catch (error) {\n    console.error('API error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error', details: error },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,UAAU,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,cAAwB;QAEhE,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,SAAS;QACT,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QACzC,MAAM,6FAAA,CAAA,WAAE,CAAC,KAAK,CAAC,SAAS;YAAE,WAAW;QAAK;QAE1C,UAAU;QACV,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,UAAU,CAAC;QAC1D,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,WAAW,CAAC;QAE5D,UAAU;QACV,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QACjD,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,WAAW;QAE9B,WAAW;QACX,MAAM,eAAe,OAAO,MAAM;QAElC,IAAI;YACF,uBAAuB;YACvB,MAAM,IAAI,QAAc,CAAC,SAAS;gBAChC,IAAI,UAAU,CAAA,GAAA,2IAAA,CAAA,UAAM,AAAD,EAAE;gBAErB,aAAa;gBACb,MAAM,gBAAgB;oBAAC;iBAAK,CAAC,SAAS;;gBAEtC,OAAO;gBACP,MAAM,YAAY,QAAQ,SAAS,IAAI;gBACvC,UAAU,QAAQ,GAAG,CAAC;gBAEtB,OAAO;gBACP,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;oBACzC,MAAM,QAAQ,QAAQ,QAAQ,IAAI,CAAC;oBACnC,MAAM,SAAS,QAAQ,SAAS,IAAI,CAAC;oBACrC,UAAU,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE,QAAQ;gBAC7C;gBAEA,eAAe;gBACf,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,GAAG,KAAK;oBAC5C,eAAe;oBACf,cAAc,IAAI,CAAC,QAAQ;gBAC7B,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,GAAG,KAAK;oBACnD,OAAO;oBACP,cAAc,IAAI,CAAC,QAAQ;gBAC7B,OAAO;oBACL,MAAM;oBACN,cAAc,IAAI,CAAC,QAAQ;gBAC7B;gBAEA,OAAO;gBACP,cAAc,IAAI,CAAC,SAAS;gBAE5B,QAAQ;gBACR,IAAI,QAAQ,cAAc,EAAE;oBAC1B,cAAc,IAAI,CAAC,iBAAiB;gBACtC;gBAEA,QACG,aAAa,CAAC,eACd,MAAM,CAAC,YACP,EAAE,CAAC,SAAS,CAAC;oBACZ,QAAQ,GAAG,CAAC,mBAAmB;gBACjC,GACC,EAAE,CAAC,YAAY,CAAC;oBACf,QAAQ,GAAG,CAAC,iBAAiB,CAAC,SAAS,OAAO,IAAI,CAAC,IAAI;gBACzD,GACC,EAAE,CAAC,OAAO;oBACT,QAAQ,GAAG,CAAC;oBACZ;gBACF,GACC,EAAE,CAAC,SAAS,CAAC;oBACZ,QAAQ,KAAK,CAAC,iBAAiB;oBAC/B,OAAO;gBACT,GACC,GAAG;YACR;YAEA,WAAW;YACX,MAAM,mBAAmB,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC;YAC3C,MAAM,iBAAiB,iBAAiB,MAAM;YAC9C,MAAM,mBAAmB,AAAC,CAAC,eAAe,cAAc,IAAI,eAAgB;YAE5E,SAAS;YACT,IAAI;gBACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;gBAChB,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;YAClB,EAAE,OAAO,cAAc;gBACrB,QAAQ,IAAI,CAAC,iCAAiC;YAChD;YAEA,SAAS;YACT,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,kBAAkB;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,kBAAkB,eAAe,QAAQ;oBACzC,mBAAmB,aAAa,QAAQ;oBACxC,qBAAqB,eAAe,QAAQ;oBAC5C,uBAAuB,iBAAiB,OAAO,CAAC;gBAClD;YACF;QAEF,EAAE,OAAO,aAAa;YACpB,SAAS;YACT,IAAI;gBACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;gBAChB,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;YAClB,EAAE,OAAO,cAAc;gBACrB,QAAQ,IAAI,CAAC,6CAA6C;YAC5D;YAEA,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA2B,SAAS;YAAY,GACzD;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}