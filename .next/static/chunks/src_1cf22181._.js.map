{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Zap, Menu, X, Home, Info, DollarSign, ImageIcon, FileImage, Video } from 'lucide-react'\n\ninterface HeaderProps {\n  currentPath?: string\n}\n\nexport function Header({ currentPath = '/' }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: Home },\n    {\n      name: 'Compress',\n      href: '/compress',\n      icon: Zap,\n      submenu: [\n        { name: 'Images', href: '/image-compress', icon: ImageIcon, description: 'PNG, JPEG, WebP' },\n        { name: 'GIF Animation', href: '/gif-compress', icon: FileImage, description: 'Animated GIFs' },\n        { name: 'Videos', href: '/video-compress', icon: Video, description: 'MP4, AVI, MOV' },\n      ]\n    },\n    { name: 'About', href: '/about', icon: Info },\n    { name: 'Pricing', href: '/pricing', icon: DollarSign },\n  ]\n\n  const isActive = (href: string) => {\n    if (href === '/' && currentPath === '/') return true\n    if (href !== '/' && currentPath.startsWith(href)) return true\n    // Special handling for compress pages\n    if (href === '/compress' && (\n      currentPath.startsWith('/image-compress') ||\n      currentPath.startsWith('/gif-compress') ||\n      currentPath.startsWith('/video-compress')\n    )) return true\n    return false\n  }\n\n  return (\n    <header className=\"bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n              <Zap className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                {item.submenu ? (\n                  <>\n                    <Link\n                      href={item.href}\n                      className={`\n                        flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                        ${isActive(item.href)\n                          ? 'text-blue-600 bg-blue-50'\n                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                        }\n                      `}\n                    >\n                      <item.icon className=\"w-4 h-4\" />\n                      <span>{item.name}</span>\n                    </Link>\n\n                    {/* Dropdown Menu */}\n                    <div className=\"absolute top-full left-0 pt-2 w-64 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200\">\n                        <div className=\"p-2\">\n                          {item.submenu.map((subItem) => (\n                            <Link\n                              key={subItem.name}\n                              href={subItem.href}\n                              className={`\n                                flex items-center space-x-3 px-3 py-3 rounded-lg text-sm transition-colors\n                                ${currentPath === subItem.href\n                                  ? 'text-blue-600 bg-blue-50'\n                                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'\n                                }\n                              `}\n                            >\n                              <subItem.icon className=\"w-5 h-5 flex-shrink-0\" />\n                              <div>\n                                <div className=\"font-medium\">{subItem.name}</div>\n                                <div className=\"text-xs text-gray-500\">{subItem.description}</div>\n                              </div>\n                            </Link>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className={`\n                      flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                      ${isActive(item.href)\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    <item.icon className=\"w-4 h-4\" />\n                    <span>{item.name}</span>\n                  </Link>\n                )}\n              </div>\n            ))}\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"w-6 h-6\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-2\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  <Link\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`\n                      flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-colors\n                      ${isActive(item.href)\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    <item.icon className=\"w-5 h-5\" />\n                    <span>{item.name}</span>\n                  </Link>\n\n                  {/* Mobile Submenu */}\n                  {item.submenu && (\n                    <div className=\"ml-8 mt-2 space-y-1\">\n                      {item.submenu.map((subItem) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          onClick={() => setIsMobileMenuOpen(false)}\n                          className={`\n                            flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors\n                            ${currentPath === subItem.href\n                              ? 'text-blue-600 bg-blue-50'\n                              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                            }\n                          `}\n                        >\n                          <subItem.icon className=\"w-4 h-4 flex-shrink-0\" />\n                          <div>\n                            <div className=\"font-medium\">{subItem.name}</div>\n                            <div className=\"text-xs text-gray-400\">{subItem.description}</div>\n                          </div>\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n\n// Breadcrumb component for better navigation\ninterface BreadcrumbProps {\n  items: Array<{\n    label: string\n    href?: string\n  }>\n}\n\nexport function Breadcrumb({ items }: BreadcrumbProps) {\n  return (\n    <nav className=\"flex items-center space-x-2 text-sm text-gray-600 mb-6\">\n      {items.map((item, index) => (\n        <div key={index} className=\"flex items-center space-x-2\">\n          {index > 0 && (\n            <span className=\"text-gray-400\">/</span>\n          )}\n          {item.href ? (\n            <Link \n              href={item.href}\n              className=\"hover:text-gray-900 transition-colors\"\n            >\n              {item.label}\n            </Link>\n          ) : (\n            <span className=\"text-gray-900 font-medium\">{item.label}</span>\n          )}\n        </div>\n      ))}\n    </nav>\n  )\n}\n\n// Footer component\nexport function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerSections = [\n    {\n      title: 'Tools',\n      links: [\n        { name: 'Image Compressor', href: '/compress?type=image' },\n        { name: 'Video Compressor', href: '/compress?type=video' },\n        { name: 'GIF Optimizer', href: '/compress?type=gif' },\n        { name: 'Batch Processor', href: '/compress?mode=batch' },\n      ]\n    },\n    {\n      title: 'Support',\n      links: [\n        { name: 'Help Center', href: '/help' },\n        { name: 'Contact Us', href: '/contact' },\n        { name: 'FAQ', href: '/faq' },\n        { name: 'API Documentation', href: '/docs' },\n      ]\n    },\n    {\n      title: 'Company',\n      links: [\n        { name: 'About Us', href: '/about' },\n        { name: 'Privacy Policy', href: '/privacy' },\n        { name: 'Terms of Service', href: '/terms' },\n        { name: 'Blog', href: '/blog' },\n      ]\n    }\n  ]\n\n  return (\n    <footer className=\"bg-gray-50 border-t border-gray-200\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div>\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n            </div>\n            <p className=\"text-gray-600 text-sm leading-relaxed\">\n              The fastest and most secure way to compress your images and videos. \n              All processing happens locally in your browser.\n            </p>\n          </div>\n\n          {/* Footer sections */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"font-semibold text-gray-900 mb-4\">{section.title}</h4>\n              <ul className=\"space-y-2\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <Link \n                      href={link.href}\n                      className=\"text-gray-600 hover:text-gray-900 text-sm transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"border-t border-gray-200 mt-8 pt-8 text-center text-gray-600 text-sm\">\n          <p>&copy; {currentYear} CompressHub. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAkC;QAAlC,EAAE,cAAc,GAAG,EAAe,GAAlC;;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,sMAAA,CAAA,OAAI;QAAC;QACtC;YACE,MAAM;YACN,MAAM;YACN,MAAM,mMAAA,CAAA,MAAG;YACT,SAAS;gBACP;oBAAE,MAAM;oBAAU,MAAM;oBAAmB,MAAM,2MAAA,CAAA,YAAS;oBAAE,aAAa;gBAAkB;gBAC3F;oBAAE,MAAM;oBAAiB,MAAM;oBAAiB,MAAM,mNAAA,CAAA,YAAS;oBAAE,aAAa;gBAAgB;gBAC9F;oBAAE,MAAM;oBAAU,MAAM;oBAAmB,MAAM,uMAAA,CAAA,QAAK;oBAAE,aAAa;gBAAgB;aACtF;QACH;QACA;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC5C;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,qNAAA,CAAA,aAAU;QAAC;KACvD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,OAAO,gBAAgB,KAAK,OAAO;QAChD,IAAI,SAAS,OAAO,YAAY,UAAU,CAAC,OAAO,OAAO;QACzD,sCAAsC;QACtC,IAAI,SAAS,eAAe,CAC1B,YAAY,UAAU,CAAC,sBACvB,YAAY,UAAU,CAAC,oBACvB,YAAY,UAAU,CAAC,kBACzB,GAAG,OAAO;QACV,OAAO;IACT;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAAoB,WAAU;8CAC5B,KAAK,OAAO,iBACX;;0DACE,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,AAAC,6IAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;kEAGH,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,6LAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;0DAIlB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC,+JAAA,CAAA,UAAI;gEAEH,MAAM,QAAQ,IAAI;gEAClB,WAAW,AAAC,iJAKT,OAHC,gBAAgB,QAAQ,IAAI,GAC1B,6BACA,sDACH;;kFAGH,6LAAC,QAAQ,IAAI;wEAAC,WAAU;;;;;;kFACxB,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAe,QAAQ,IAAI;;;;;;0FAC1C,6LAAC;gFAAI,WAAU;0FAAyB,QAAQ,WAAW;;;;;;;;;;;;;+DAbxD,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;qEAsB7B,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,AAAC,yIAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;0DAGH,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;mCAxDZ,KAAK,IAAI;;;;;;;;;;sCAgEvB,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;;kDACC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,AAAC,yIAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;0DAGH,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;oCAIjB,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS,IAAM,oBAAoB;gDACnC,WAAW,AAAC,yIAKT,OAHC,gBAAgB,QAAQ,IAAI,GAC1B,6BACA,sDACH;;kEAGH,6LAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;kEACxB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,QAAQ,IAAI;;;;;;0EAC1C,6LAAC;gEAAI,WAAU;0EAAyB,QAAQ,WAAW;;;;;;;;;;;;;+CAdxD,QAAQ,IAAI;;;;;;;;;;;+BArBjB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDnC;GAhLgB;KAAA;AA0LT,SAAS,WAAW,KAA0B;QAA1B,EAAE,KAAK,EAAmB,GAA1B;IACzB,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAAgB,WAAU;;oBACxB,QAAQ,mBACP,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;oBAEjC,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,KAAK,IAAI;wBACf,WAAU;kCAET,KAAK,KAAK;;;;;6CAGb,6LAAC;wBAAK,WAAU;kCAA6B,KAAK,KAAK;;;;;;;eAZjD;;;;;;;;;;AAkBlB;MAtBgB;AAyBT,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAAqB;gBACpD;oBAAE,MAAM;oBAAmB,MAAM;gBAAuB;aACzD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAe,MAAM;gBAAQ;gBACrC;oBAAE,MAAM;oBAAc,MAAM;gBAAW;gBACvC;oBAAE,MAAM;oBAAO,MAAM;gBAAO;gBAC5B;oBAAE,MAAM;oBAAqB,MAAM;gBAAQ;aAC5C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAS;gBACnC;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAoB,MAAM;gBAAS;gBAC3C;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAC/B;QACH;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;wBAOtD,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,QAAQ,KAAK;;;;;;kDAC/D,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAkB3B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;4BAAE;4BAAQ;4BAAY;;;;;;;;;;;;;;;;;;;;;;;AAKjC;MA7EgB", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { Zap, Github, Twitter, Mail, Heart } from 'lucide-react'\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerLinks = {\n    product: [\n      { name: 'Image Compression', href: '/image-compress' },\n      { name: 'GIF Compression', href: '/gif-compress' },\n      { name: 'Video Compression', href: '/video-compress' },\n      { name: 'Batch Processing', href: '/compress' },\n    ],\n    company: [\n      { name: 'About', href: '/about' },\n      { name: 'Pricing', href: '/pricing' },\n      { name: 'Contact', href: '/contact' },\n      { name: 'Blog', href: '/blog' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'API Documentation', href: '/docs' },\n      { name: 'Status', href: '/status' },\n      { name: 'Feedback', href: '/feedback' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n    ]\n  }\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold\">CompressHub</span>\n            </Link>\n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Professional file compression tools for images, GIFs, and videos. \n              Reduce file sizes while maintaining quality with our advanced algorithms.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://github.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\"\n              >\n                <Github className=\"w-5 h-5\" />\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\"\n              >\n                <Twitter className=\"w-5 h-5\" />\n              </a>\n              <a\n                href=\"mailto:<EMAIL>\"\n                className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\"\n              >\n                <Mail className=\"w-5 h-5\" />\n              </a>\n            </div>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 mt-12 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-4 mb-4 md:mb-0\">\n              <p className=\"text-gray-400 text-sm\">\n                © {currentYear} CompressHub. All rights reserved.\n              </p>\n              <div className=\"hidden md:flex items-center space-x-4\">\n                {footerLinks.legal.map((link) => (\n                  <Link\n                    key={link.name}\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n            \n            <div className=\"flex items-center text-gray-400 text-sm\">\n              <span>Made with</span>\n              <Heart className=\"w-4 h-4 mx-1 text-red-500\" />\n              <span>for better web performance</span>\n            </div>\n          </div>\n\n          {/* Mobile Legal Links */}\n          <div className=\"md:hidden mt-4 flex flex-wrap justify-center gap-4\">\n            {footerLinks.legal.map((link) => (\n              <Link\n                key={link.name}\n                href={link.href}\n                className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n              >\n                {link.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAqB,MAAM;YAAkB;YACrD;gBAAE,MAAM;gBAAmB,MAAM;YAAgB;YACjD;gBAAE,MAAM;gBAAqB,MAAM;YAAkB;YACrD;gBAAE,MAAM;gBAAoB,MAAM;YAAY;SAC/C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAqB,MAAM;YAAQ;YAC3C;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDAAwB;gDAChC;gDAAY;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;mDAJL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAUtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;sCACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9B;KAxKgB", "debugId": null}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/AnimatedIcons.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Upload, Settings, Download, Zap, FileImage, Video, Image as ImageIcon } from 'lucide-react'\n\n// Animated Upload Icon\nexport function AnimatedUpload({ isActive = false }: { isActive?: boolean }) {\n  return (\n    <motion.div\n      animate={isActive ? {\n        scale: [1, 1.1, 1],\n        rotate: [0, 5, -5, 0]\n      } : {}}\n      transition={{\n        duration: 2,\n        repeat: isActive ? Infinity : 0,\n        ease: \"easeInOut\"\n      }}\n    >\n      <Upload className=\"w-8 h-8\" />\n    </motion.div>\n  )\n}\n\n// Animated Processing Icon\nexport function AnimatedProcessing({ isActive = false }: { isActive?: boolean }) {\n  return (\n    <motion.div\n      animate={isActive ? {\n        rotate: 360\n      } : {}}\n      transition={{\n        duration: 2,\n        repeat: isActive ? Infinity : 0,\n        ease: \"linear\"\n      }}\n    >\n      <Settings className=\"w-8 h-8\" />\n    </motion.div>\n  )\n}\n\n// Animated Download Icon\nexport function AnimatedDownload({ isActive = false }: { isActive?: boolean }) {\n  return (\n    <motion.div\n      animate={isActive ? {\n        y: [0, -5, 0]\n      } : {}}\n      transition={{\n        duration: 1,\n        repeat: isActive ? Infinity : 0,\n        ease: \"easeInOut\"\n      }}\n    >\n      <Download className=\"w-8 h-8\" />\n    </motion.div>\n  )\n}\n\n// Animated Compression Visualization\nexport function CompressionAnimation({ isActive = false }: { isActive?: boolean }) {\n  return (\n    <div className=\"flex items-center space-x-4\">\n      {/* Large File */}\n      <motion.div\n        animate={isActive ? {\n          scale: [1, 0.8, 1]\n        } : {}}\n        transition={{\n          duration: 2,\n          repeat: isActive ? Infinity : 0,\n          ease: \"easeInOut\"\n        }}\n        className=\"relative\"\n      >\n        <div className=\"w-16 h-16 bg-red-100 rounded-xl flex items-center justify-center\">\n          <FileImage className=\"w-8 h-8 text-red-500\" />\n        </div>\n        <div className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full\">\n          5MB\n        </div>\n      </motion.div>\n\n      {/* Arrow with pulse */}\n      <motion.div\n        animate={isActive ? {\n          x: [0, 10, 0],\n          opacity: [0.5, 1, 0.5]\n        } : {}}\n        transition={{\n          duration: 1.5,\n          repeat: isActive ? Infinity : 0,\n          ease: \"easeInOut\"\n        }}\n      >\n        <Zap className=\"w-6 h-6 text-blue-500\" />\n      </motion.div>\n\n      {/* Compressed File */}\n      <motion.div\n        animate={isActive ? {\n          scale: [1, 1.1, 1]\n        } : {}}\n        transition={{\n          duration: 2,\n          repeat: isActive ? Infinity : 0,\n          ease: \"easeInOut\",\n          delay: 0.5\n        }}\n        className=\"relative\"\n      >\n        <div className=\"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center\">\n          <FileImage className=\"w-8 h-8 text-green-500\" />\n        </div>\n        <div className=\"absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\">\n          500KB\n        </div>\n      </motion.div>\n    </div>\n  )\n}\n\n// Floating File Icons\nexport function FloatingIcons() {\n  const icons = [\n    { Icon: ImageIcon, color: 'text-blue-500', delay: 0, startX: 20, endX: 80 },\n    { Icon: Video, color: 'text-purple-500', delay: 0.5, startX: 60, endX: 30 },\n    { Icon: FileImage, color: 'text-pink-500', delay: 1, startX: 10, endX: 90 }\n  ]\n\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {icons.map(({ Icon, color, delay, startX, endX }, index) => (\n        <motion.div\n          key={index}\n          className={`absolute ${color} opacity-20`}\n          initial={{ y: 100, x: startX, opacity: 0 }}\n          animate={{\n            y: -100,\n            x: endX,\n            opacity: [0, 0.3, 0],\n            rotate: [0, 180, 360]\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            delay: delay,\n            ease: \"linear\"\n          }}\n          style={{\n            left: `${startX}%`,\n          }}\n        >\n          <Icon className=\"w-8 h-8\" />\n        </motion.div>\n      ))}\n    </div>\n  )\n}\n\n// Progress Ring Animation\nexport function ProgressRing({ progress = 0, size = 120 }: { progress?: number; size?: number }) {\n  const radius = (size - 20) / 2\n  const circumference = radius * 2 * Math.PI\n  const strokeDasharray = `${circumference} ${circumference}`\n  const strokeDashoffset = circumference - (progress / 100) * circumference\n\n  return (\n    <div className=\"relative\" style={{ width: size, height: size }}>\n      <svg\n        className=\"transform -rotate-90\"\n        width={size}\n        height={size}\n      >\n        {/* Background circle */}\n        <circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke=\"currentColor\"\n          strokeWidth=\"8\"\n          fill=\"transparent\"\n          className=\"text-gray-200\"\n        />\n        {/* Progress circle */}\n        <motion.circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke=\"url(#gradient)\"\n          strokeWidth=\"8\"\n          fill=\"transparent\"\n          strokeDasharray={strokeDasharray}\n          initial={{ strokeDashoffset: circumference }}\n          animate={{ strokeDashoffset }}\n          transition={{ duration: 0.5, ease: \"easeInOut\" }}\n          strokeLinecap=\"round\"\n        />\n        <defs>\n          <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n            <stop offset=\"0%\" stopColor=\"#3B82F6\" />\n            <stop offset=\"100%\" stopColor=\"#8B5CF6\" />\n          </linearGradient>\n        </defs>\n      </svg>\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <span className=\"text-2xl font-bold text-gray-900\">{Math.round(progress)}%</span>\n      </div>\n    </div>\n  )\n}\n\n// Pulse Animation for buttons\nexport function PulseButton({ children, isActive = false, ...props }: any) {\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      animate={isActive ? {\n        boxShadow: [\n          \"0 0 0 0 rgba(59, 130, 246, 0.7)\",\n          \"0 0 0 10px rgba(59, 130, 246, 0)\",\n          \"0 0 0 0 rgba(59, 130, 246, 0)\"\n        ]\n      } : {}}\n      transition={{\n        duration: 2,\n        repeat: isActive ? Infinity : 0,\n        ease: \"easeInOut\"\n      }}\n      {...props}\n    >\n      {children}\n    </motion.button>\n  )\n}\n\n// Slide in animation for cards\nexport function SlideInCard({ children, delay = 0, ...props }: any) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay }}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAMO,SAAS,eAAe,KAA4C;QAA5C,EAAE,WAAW,KAAK,EAA0B,GAA5C;IAC7B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS,WAAW;YAClB,OAAO;gBAAC;gBAAG;gBAAK;aAAE;YAClB,QAAQ;gBAAC;gBAAG;gBAAG,CAAC;gBAAG;aAAE;QACvB,IAAI,CAAC;QACL,YAAY;YACV,UAAU;YACV,QAAQ,WAAW,WAAW;YAC9B,MAAM;QACR;kBAEA,cAAA,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;;;;;;AAGxB;KAhBgB;AAmBT,SAAS,mBAAmB,KAA4C;QAA5C,EAAE,WAAW,KAAK,EAA0B,GAA5C;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS,WAAW;YAClB,QAAQ;QACV,IAAI,CAAC;QACL,YAAY;YACV,UAAU;YACV,QAAQ,WAAW,WAAW;YAC9B,MAAM;QACR;kBAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;;;;;;AAG1B;MAfgB;AAkBT,SAAS,iBAAiB,KAA4C;QAA5C,EAAE,WAAW,KAAK,EAA0B,GAA5C;IAC/B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS,WAAW;YAClB,GAAG;gBAAC;gBAAG,CAAC;gBAAG;aAAE;QACf,IAAI,CAAC;QACL,YAAY;YACV,UAAU;YACV,QAAQ,WAAW,WAAW;YAC9B,MAAM;QACR;kBAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;;;;;;AAG1B;MAfgB;AAkBT,SAAS,qBAAqB,KAA4C;QAA5C,EAAE,WAAW,KAAK,EAA0B,GAA5C;IACnC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,WAAW;oBAClB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB,IAAI,CAAC;gBACL,YAAY;oBACV,UAAU;oBACV,QAAQ,WAAW,WAAW;oBAC9B,MAAM;gBACR;gBACA,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;wBAAI,WAAU;kCAAgF;;;;;;;;;;;;0BAMjG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,WAAW;oBAClB,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB,IAAI,CAAC;gBACL,YAAY;oBACV,UAAU;oBACV,QAAQ,WAAW,WAAW;oBAC9B,MAAM;gBACR;0BAEA,cAAA,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;;;;;;0BAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,WAAW;oBAClB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB,IAAI,CAAC;gBACL,YAAY;oBACV,UAAU;oBACV,QAAQ,WAAW,WAAW;oBAC9B,MAAM;oBACN,OAAO;gBACT;gBACA,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;wBAAI,WAAU;kCAAkF;;;;;;;;;;;;;;;;;;AAMzG;MA5DgB;AA+DT,SAAS;IACd,MAAM,QAAQ;QACZ;YAAE,MAAM,uMAAA,CAAA,QAAS;YAAE,OAAO;YAAiB,OAAO;YAAG,QAAQ;YAAI,MAAM;QAAG;QAC1E;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;YAAmB,OAAO;YAAK,QAAQ;YAAI,MAAM;QAAG;QAC1E;YAAE,MAAM,mNAAA,CAAA,YAAS;YAAE,OAAO;YAAiB,OAAO;YAAG,QAAQ;YAAI,MAAM;QAAG;KAC3E;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,QAAuC;gBAAtC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;iCAC9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAW,AAAC,YAAiB,OAAN,OAAM;gBAC7B,SAAS;oBAAE,GAAG;oBAAK,GAAG;oBAAQ,SAAS;gBAAE;gBACzC,SAAS;oBACP,GAAG,CAAC;oBACJ,GAAG;oBACH,SAAS;wBAAC;wBAAG;wBAAK;qBAAE;oBACpB,QAAQ;wBAAC;wBAAG;wBAAK;qBAAI;gBACvB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,MAAM;gBACR;gBACA,OAAO;oBACL,MAAM,AAAC,GAAS,OAAP,QAAO;gBAClB;0BAEA,cAAA,6LAAC;oBAAK,WAAU;;;;;;eAnBX;;;;;;;;;;;AAwBf;MAnCgB;AAsCT,SAAS,aAAa,KAAkE;QAAlE,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,EAAwC,GAAlE;IAC3B,MAAM,SAAS,CAAC,OAAO,EAAE,IAAI;IAC7B,MAAM,gBAAgB,SAAS,IAAI,KAAK,EAAE;IAC1C,MAAM,kBAAkB,AAAC,GAAmB,OAAjB,eAAc,KAAiB,OAAd;IAC5C,MAAM,mBAAmB,gBAAgB,AAAC,WAAW,MAAO;IAE5D,qBACE,6LAAC;QAAI,WAAU;QAAW,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAK;;0BAC3D,6LAAC;gBACC,WAAU;gBACV,OAAO;gBACP,QAAQ;;kCAGR,6LAAC;wBACC,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAO;wBACP,aAAY;wBACZ,MAAK;wBACL,WAAU;;;;;;kCAGZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAO;wBACP,aAAY;wBACZ,MAAK;wBACL,iBAAiB;wBACjB,SAAS;4BAAE,kBAAkB;wBAAc;wBAC3C,SAAS;4BAAE;wBAAiB;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;wBAC/C,eAAc;;;;;;kCAEhB,6LAAC;kCACC,cAAA,6LAAC;4BAAe,IAAG;4BAAW,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CACzD,6LAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,6LAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAIpC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;;wBAAoC,KAAK,KAAK,CAAC;wBAAU;;;;;;;;;;;;;;;;;;AAIjF;MAjDgB;AAoDT,SAAS,YAAY,KAA6C;QAA7C,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,GAAG,OAAY,GAA7C;IAC1B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS,WAAW;YAClB,WAAW;gBACT;gBACA;gBACA;aACD;QACH,IAAI,CAAC;QACL,YAAY;YACV,UAAU;YACV,QAAQ,WAAW,WAAW;YAC9B,MAAM;QACR;QACC,GAAG,KAAK;kBAER;;;;;;AAGP;MAtBgB;AAyBT,SAAS,YAAY,KAAsC;QAAtC,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,GAAG,OAAY,GAAtC;IAC1B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK;QAAM;QAClC,GAAG,KAAK;kBAER;;;;;;AAGP;MAXgB", "debugId": null}}, {"offset": {"line": 1605, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/compress/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { Header } from '@/components/Header'\nimport { Footer } from '@/components/Footer'\nimport { FloatingIcons } from '@/components/AnimatedIcons'\nimport { \n  ImageIcon, \n  FileImage, \n  Video, \n  ArrowRight,\n  Zap,\n  Sparkles,\n  Play,\n  Layers,\n  Clock,\n  Palette,\n  Film,\n  Monitor\n} from 'lucide-react'\n\nexport default function CompressPage() {\n  const compressionTypes = [\n    {\n      title: 'Image Compression',\n      description: 'Compress PNG, JPEG, and WebP images while preserving quality and transparency',\n      href: '/image-compress',\n      icon: ImageIcon,\n      gradient: 'from-blue-500 to-indigo-600',\n      bgGradient: 'from-blue-50 to-indigo-50',\n      features: ['PNG, JPEG, WebP', 'Transparency Preserved', 'Batch Processing', 'Up to 90% Reduction'],\n      stats: 'Up to 90% size reduction',\n      color: 'blue'\n    },\n    {\n      title: 'GIF Animation',\n      description: 'Compress animated GIF files while maintaining smooth animations and frame timing',\n      href: '/gif-compress',\n      icon: FileImage,\n      gradient: 'from-purple-500 to-pink-600',\n      bgGradient: 'from-purple-50 to-pink-50',\n      features: ['Animation Preserved', 'Smart Palette', 'Frame Rate Control', 'Up to 80% Reduction'],\n      stats: 'Up to 80% size reduction',\n      color: 'purple'\n    },\n    {\n      title: 'Video Compression',\n      description: 'Professional video encoding with advanced codec support and quality optimization',\n      href: '/video-compress',\n      icon: Video,\n      gradient: 'from-green-500 to-emerald-600',\n      bgGradient: 'from-green-50 to-emerald-50',\n      features: ['Multiple Formats', 'Quality Control', 'Mobile Ready', 'Fast Processing'],\n      stats: 'Professional encoding',\n      color: 'green'\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden\">\n      <FloatingIcons />\n      <Header currentPath=\"/compress\" />\n\n      <main className=\"container mx-auto px-4 py-12\">\n        {/* Hero Section */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <div className=\"flex items-center justify-center mb-8\">\n            <div className=\"relative\">\n              <div className=\"w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl\">\n                <Zap className=\"w-12 h-12 text-white\" />\n              </div>\n              <div className=\"absolute -top-3 -right-3 w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse\">\n                <Sparkles className=\"w-5 h-5 text-yellow-800\" />\n              </div>\n            </div>\n          </div>\n          \n          <h1 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Choose Your <span className=\"text-blue-600\">Compression</span> Type\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-8\">\n            Select the perfect compression tool for your needs. Each specialized page offers \n            optimized settings and processing for different file types.\n          </p>\n          \n          <div className=\"flex flex-wrap items-center justify-center gap-8 text-sm text-gray-500\">\n            <div className=\"flex items-center\">\n              <Zap className=\"w-4 h-4 mr-2 text-blue-500\" />\n              Server-Side Processing\n            </div>\n            <div className=\"flex items-center\">\n              <Layers className=\"w-4 h-4 mr-2 text-green-500\" />\n              Batch Operations\n            </div>\n            <div className=\"flex items-center\">\n              <Play className=\"w-4 h-4 mr-2 text-purple-500\" />\n              Animation Support\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Compression Types Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {compressionTypes.map((type, index) => (\n            <motion.div\n              key={type.title}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <Link href={type.href}>\n                <div className={`\n                  relative group bg-gradient-to-br ${type.bgGradient} p-8 rounded-2xl \n                  border border-gray-200 hover:border-gray-300 transition-all duration-300\n                  hover:shadow-xl hover:scale-105 cursor-pointer overflow-hidden\n                `}>\n                  {/* Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-5\">\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-transparent via-white to-transparent\"></div>\n                  </div>\n                  \n                  {/* Icon */}\n                  <div className={`\n                    w-16 h-16 bg-gradient-to-br ${type.gradient} rounded-2xl \n                    flex items-center justify-center mb-6 shadow-lg\n                    group-hover:scale-110 transition-transform duration-300\n                  `}>\n                    <type.icon className=\"w-8 h-8 text-white\" />\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">\n                    {type.title}\n                  </h3>\n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {type.description}\n                  </p>\n\n                  {/* Features */}\n                  <div className=\"space-y-2 mb-6\">\n                    {type.features.map((feature, idx) => (\n                      <div key={idx} className=\"flex items-center text-sm text-gray-600\">\n                        <div className={`w-2 h-2 bg-${type.color}-500 rounded-full mr-3`}></div>\n                        {feature}\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Stats */}\n                  <div className={`\n                    inline-flex items-center px-3 py-1 rounded-full text-sm font-medium\n                    bg-${type.color}-100 text-${type.color}-700 mb-4\n                  `}>\n                    {type.stats}\n                  </div>\n\n                  {/* Action */}\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium text-gray-500\">\n                      Get Started\n                    </span>\n                    <ArrowRight className={`\n                      w-5 h-5 text-${type.color}-500 \n                      group-hover:translate-x-1 transition-transform duration-300\n                    `} />\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Features Comparison */}\n        <motion.div\n          className=\"bg-white rounded-2xl shadow-lg p-8 mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 text-center mb-8\">\n            Why Choose Specialized Compression?\n          </h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n                <ImageIcon className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Optimized Algorithms</h3>\n              <p className=\"text-gray-600\">\n                Each page uses specialized compression algorithms tailored for specific file types\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n                <Palette className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Smart Settings</h3>\n              <p className=\"text-gray-600\">\n                Customized compression settings and options for maximum efficiency\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n                <Monitor className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Better Results</h3>\n              <p className=\"text-gray-600\">\n                Achieve better compression ratios while maintaining quality\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Quick Stats */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-3 gap-6\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n        >\n          <div className=\"bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white text-center\">\n            <div className=\"text-3xl font-bold mb-2\">90%</div>\n            <div className=\"text-blue-100\">Average Image Compression</div>\n          </div>\n          \n          <div className=\"bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white text-center\">\n            <div className=\"text-3xl font-bold mb-2\">80%</div>\n            <div className=\"text-purple-100\">GIF Size Reduction</div>\n          </div>\n          \n          <div className=\"bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white text-center\">\n            <div className=\"text-3xl font-bold mb-2\">75%</div>\n            <div className=\"text-green-100\">Video Compression Ratio</div>\n          </div>\n        </motion.div>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAsBe,SAAS;IACtB,MAAM,mBAAmB;QACvB;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM,2MAAA,CAAA,YAAS;YACf,UAAU;YACV,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAA0B;gBAAoB;aAAsB;YAClG,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,UAAU;YACV,YAAY;YACZ,UAAU;gBAAC;gBAAuB;gBAAiB;gBAAsB;aAAsB;YAC/F,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,UAAU;YACV,YAAY;YACZ,UAAU;gBAAC;gBAAoB;gBAAmB;gBAAgB;aAAkB;YACpF,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sIAAA,CAAA,gBAAa;;;;;0BACd,6LAAC,+HAAA,CAAA,SAAM;gBAAC,aAAY;;;;;;0BAEpB,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCAAG,WAAU;;oCAAoD;kDACpD,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAkB;;;;;;;0CAEhE,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAgC;;;;;;;kDAGpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiC;;;;;;;;;;;;;;;;;;;kCAOvD,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;8CACnB,cAAA,6LAAC;wCAAI,WAAW,AAAC,wDACoC,OAAhB,KAAK,UAAU,EAAC;;0DAKnD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;0DAIjB,6LAAC;gDAAI,WAAW,AAAC,qDAC6B,OAAd,KAAK,QAAQ,EAAC;0DAI5C,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAIvB,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAInB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC3B,6LAAC;wDAAc,WAAU;;0EACvB,6LAAC;gEAAI,WAAW,AAAC,cAAwB,OAAX,KAAK,KAAK,EAAC;;;;;;4DACxC;;uDAFO;;;;;;;;;;0DAQd,6LAAC;gDAAI,WAAW,AAAC,qHAEa,OAAvB,KAAK,KAAK,EAAC,cAAuB,OAAX,KAAK,KAAK,EAAC;0DAEtC,KAAK,KAAK;;;;;;0DAIb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEAGpD,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAW,AAAC,wCACI,OAAX,KAAK,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;+BAzD7B,KAAK,KAAK;;;;;;;;;;kCAoErB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAQnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAGjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAkB;;;;;;;;;;;;0CAGnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAKtC,6LAAC,+HAAA,CAAA,SAAM;;;;;;;;;;;AAGb;KAnOwB", "debugId": null}}]}