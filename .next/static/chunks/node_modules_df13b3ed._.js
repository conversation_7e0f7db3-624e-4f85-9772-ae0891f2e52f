(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return type.displayName || "Context";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler"), REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        react_stack_bottom_frame: function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/browser-image-compression/dist/browser-image-compression.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Browser Image Compression
 * v2.0.2
 * by Donald <<EMAIL>>
 * https://github.com/Donaldcwl/browser-image-compression
 */ __turbopack_context__.s({
    "default": ()=>imageCompression
});
function _mergeNamespaces(e, t1) {
    return t1.forEach(function(t1) {
        t1 && "string" != typeof t1 && !Array.isArray(t1) && Object.keys(t1).forEach(function(r) {
            if ("default" !== r && !(r in e)) {
                var i = Object.getOwnPropertyDescriptor(t1, r);
                Object.defineProperty(e, r, i.get ? i : {
                    enumerable: !0,
                    get: function() {
                        return t1[r];
                    }
                });
            }
        });
    }), Object.freeze(e);
}
function copyExifWithoutOrientation(e, t1) {
    return new Promise(function(r, i) {
        let o;
        return getApp1Segment(e).then(function(e) {
            try {
                return o = e, r(new Blob([
                    t1.slice(0, 2),
                    o,
                    t1.slice(2)
                ], {
                    type: "image/jpeg"
                }));
            } catch (e) {
                return i(e);
            }
        }, i);
    });
}
const getApp1Segment = (e)=>new Promise((t1, r)=>{
        const i = new FileReader;
        i.addEventListener("load", (param)=>{
            let { target: { result: e } } = param;
            const i = new DataView(e);
            let o = 0;
            if (65496 !== i.getUint16(o)) return r("not a valid JPEG");
            for(o += 2;;){
                const a = i.getUint16(o);
                if (65498 === a) break;
                const s = i.getUint16(o + 2);
                if (65505 === a && 1165519206 === i.getUint32(o + 4)) {
                    const a = o + 10;
                    let f;
                    switch(i.getUint16(a)){
                        case 18761:
                            f = !0;
                            break;
                        case 19789:
                            f = !1;
                            break;
                        default:
                            return r("TIFF header contains invalid endian");
                    }
                    if (42 !== i.getUint16(a + 2, f)) return r("TIFF header contains invalid version");
                    const l = i.getUint32(a + 4, f), c = a + l + 2 + 12 * i.getUint16(a + l, f);
                    for(let e = a + l + 2; e < c; e += 12){
                        if (274 == i.getUint16(e, f)) {
                            if (3 !== i.getUint16(e + 2, f)) return r("Orientation data type is invalid");
                            if (1 !== i.getUint32(e + 4, f)) return r("Orientation data count is invalid");
                            i.setUint16(e + 8, 1, f);
                            break;
                        }
                    }
                    return t1(e.slice(o, o + 2 + s));
                }
                o += 2 + s;
            }
            return t1(new Blob);
        }), i.readAsArrayBuffer(e);
    });
var e = {}, t1 = {
    get exports () {
        return e;
    },
    set exports (t){
        e = t;
    }
};
!function(e) {
    var r, i, UZIP = {};
    t1.exports = UZIP, UZIP.parse = function(e, t1) {
        for(var r = UZIP.bin.readUshort, i = UZIP.bin.readUint, o = 0, a = {}, s = new Uint8Array(e), f = s.length - 4; 101010256 != i(s, f);)f--;
        o = f;
        o += 4;
        var l = r(s, o += 4);
        r(s, o += 2);
        var c = i(s, o += 2), u = i(s, o += 4);
        o += 4, o = u;
        for(var h = 0; h < l; h++){
            i(s, o), o += 4, o += 4, o += 4, i(s, o += 4);
            c = i(s, o += 4);
            var d = i(s, o += 4), A = r(s, o += 4), g = r(s, o + 2), p = r(s, o + 4);
            o += 6;
            var m = i(s, o += 8);
            o += 4, o += A + g + p, UZIP._readLocal(s, m, a, c, d, t1);
        }
        return a;
    }, UZIP._readLocal = function(e, t1, r, i, o, a) {
        var s = UZIP.bin.readUshort, f = UZIP.bin.readUint;
        f(e, t1), s(e, t1 += 4), s(e, t1 += 2);
        var l = s(e, t1 += 2);
        f(e, t1 += 2), f(e, t1 += 4), t1 += 4;
        var c = s(e, t1 += 8), u = s(e, t1 += 2);
        t1 += 2;
        var h = UZIP.bin.readUTF8(e, t1, c);
        if (t1 += c, t1 += u, a) r[h] = {
            size: o,
            csize: i
        };
        else {
            var d = new Uint8Array(e.buffer, t1);
            if (0 == l) r[h] = new Uint8Array(d.buffer.slice(t1, t1 + i));
            else {
                if (8 != l) throw "unknown compression method: " + l;
                var A = new Uint8Array(o);
                UZIP.inflateRaw(d, A), r[h] = A;
            }
        }
    }, UZIP.inflateRaw = function(e, t1) {
        return UZIP.F.inflate(e, t1);
    }, UZIP.inflate = function(e, t1) {
        return e[0], e[1], UZIP.inflateRaw(new Uint8Array(e.buffer, e.byteOffset + 2, e.length - 6), t1);
    }, UZIP.deflate = function(e, t1) {
        null == t1 && (t1 = {
            level: 6
        });
        var r = 0, i = new Uint8Array(50 + Math.floor(1.1 * e.length));
        i[r] = 120, i[r + 1] = 156, r += 2, r = UZIP.F.deflateRaw(e, i, r, t1.level);
        var o = UZIP.adler(e, 0, e.length);
        return i[r + 0] = o >>> 24 & 255, i[r + 1] = o >>> 16 & 255, i[r + 2] = o >>> 8 & 255, i[r + 3] = o >>> 0 & 255, new Uint8Array(i.buffer, 0, r + 4);
    }, UZIP.deflateRaw = function(e, t1) {
        null == t1 && (t1 = {
            level: 6
        });
        var r = new Uint8Array(50 + Math.floor(1.1 * e.length)), i = UZIP.F.deflateRaw(e, r, i, t1.level);
        return new Uint8Array(r.buffer, 0, i);
    }, UZIP.encode = function(e, t1) {
        null == t1 && (t1 = !1);
        var r = 0, i = UZIP.bin.writeUint, o = UZIP.bin.writeUshort, a = {};
        for(var s in e){
            var f = !UZIP._noNeed(s) && !t1, l = e[s], c = UZIP.crc.crc(l, 0, l.length);
            a[s] = {
                cpr: f,
                usize: l.length,
                crc: c,
                file: f ? UZIP.deflateRaw(l) : l
            };
        }
        for(var s in a)r += a[s].file.length + 30 + 46 + 2 * UZIP.bin.sizeUTF8(s);
        r += 22;
        var u = new Uint8Array(r), h = 0, d = [];
        for(var s in a){
            var A = a[s];
            d.push(h), h = UZIP._writeHeader(u, h, s, A, 0);
        }
        var g = 0, p = h;
        for(var s in a){
            A = a[s];
            d.push(h), h = UZIP._writeHeader(u, h, s, A, 1, d[g++]);
        }
        var m = h - p;
        return i(u, h, 101010256), h += 4, o(u, h += 4, g), o(u, h += 2, g), i(u, h += 2, m), i(u, h += 4, p), h += 4, h += 2, u.buffer;
    }, UZIP._noNeed = function(e) {
        var t1 = e.split(".").pop().toLowerCase();
        return -1 != "png,jpg,jpeg,zip".indexOf(t1);
    }, UZIP._writeHeader = function(e, t1, r, i, o, a) {
        var s = UZIP.bin.writeUint, f = UZIP.bin.writeUshort, l = i.file;
        return s(e, t1, 0 == o ? 67324752 : 33639248), t1 += 4, 1 == o && (t1 += 2), f(e, t1, 20), f(e, t1 += 2, 0), f(e, t1 += 2, i.cpr ? 8 : 0), s(e, t1 += 2, 0), s(e, t1 += 4, i.crc), s(e, t1 += 4, l.length), s(e, t1 += 4, i.usize), f(e, t1 += 4, UZIP.bin.sizeUTF8(r)), f(e, t1 += 2, 0), t1 += 2, 1 == o && (t1 += 2, t1 += 2, s(e, t1 += 6, a), t1 += 4), t1 += UZIP.bin.writeUTF8(e, t1, r), 0 == o && (e.set(l, t1), t1 += l.length), t1;
    }, UZIP.crc = {
        table: function() {
            for(var e = new Uint32Array(256), t1 = 0; t1 < 256; t1++){
                for(var r = t1, i = 0; i < 8; i++)1 & r ? r = 3988292384 ^ r >>> 1 : r >>>= 1;
                e[t1] = r;
            }
            return e;
        }(),
        update: function(e, t1, r, i) {
            for(var o = 0; o < i; o++)e = UZIP.crc.table[255 & (e ^ t1[r + o])] ^ e >>> 8;
            return e;
        },
        crc: function(e, t1, r) {
            return 4294967295 ^ UZIP.crc.update(4294967295, e, t1, r);
        }
    }, UZIP.adler = function(e, t1, r) {
        for(var i = 1, o = 0, a = t1, s = t1 + r; a < s;){
            for(var f = Math.min(a + 5552, s); a < f;)o += i += e[a++];
            i %= 65521, o %= 65521;
        }
        return o << 16 | i;
    }, UZIP.bin = {
        readUshort: function(e, t1) {
            return e[t1] | e[t1 + 1] << 8;
        },
        writeUshort: function(e, t1, r) {
            e[t1] = 255 & r, e[t1 + 1] = r >> 8 & 255;
        },
        readUint: function(e, t1) {
            return 16777216 * e[t1 + 3] + (e[t1 + 2] << 16 | e[t1 + 1] << 8 | e[t1]);
        },
        writeUint: function(e, t1, r) {
            e[t1] = 255 & r, e[t1 + 1] = r >> 8 & 255, e[t1 + 2] = r >> 16 & 255, e[t1 + 3] = r >> 24 & 255;
        },
        readASCII: function(e, t1, r) {
            for(var i = "", o = 0; o < r; o++)i += String.fromCharCode(e[t1 + o]);
            return i;
        },
        writeASCII: function(e, t1, r) {
            for(var i = 0; i < r.length; i++)e[t1 + i] = r.charCodeAt(i);
        },
        pad: function(e) {
            return e.length < 2 ? "0" + e : e;
        },
        readUTF8: function(e, t1, r) {
            for(var i, o = "", a = 0; a < r; a++)o += "%" + UZIP.bin.pad(e[t1 + a].toString(16));
            try {
                i = decodeURIComponent(o);
            } catch (i) {
                return UZIP.bin.readASCII(e, t1, r);
            }
            return i;
        },
        writeUTF8: function(e, t1, r) {
            for(var i = r.length, o = 0, a = 0; a < i; a++){
                var s = r.charCodeAt(a);
                if (0 == (4294967168 & s)) e[t1 + o] = s, o++;
                else if (0 == (4294965248 & s)) e[t1 + o] = 192 | s >> 6, e[t1 + o + 1] = 128 | s >> 0 & 63, o += 2;
                else if (0 == (4294901760 & s)) e[t1 + o] = 224 | s >> 12, e[t1 + o + 1] = 128 | s >> 6 & 63, e[t1 + o + 2] = 128 | s >> 0 & 63, o += 3;
                else {
                    if (0 != (4292870144 & s)) throw "e";
                    e[t1 + o] = 240 | s >> 18, e[t1 + o + 1] = 128 | s >> 12 & 63, e[t1 + o + 2] = 128 | s >> 6 & 63, e[t1 + o + 3] = 128 | s >> 0 & 63, o += 4;
                }
            }
            return o;
        },
        sizeUTF8: function(e) {
            for(var t1 = e.length, r = 0, i = 0; i < t1; i++){
                var o = e.charCodeAt(i);
                if (0 == (4294967168 & o)) r++;
                else if (0 == (4294965248 & o)) r += 2;
                else if (0 == (4294901760 & o)) r += 3;
                else {
                    if (0 != (4292870144 & o)) throw "e";
                    r += 4;
                }
            }
            return r;
        }
    }, UZIP.F = {}, UZIP.F.deflateRaw = function(e, t1, r, i) {
        var o = [
            [
                0,
                0,
                0,
                0,
                0
            ],
            [
                4,
                4,
                8,
                4,
                0
            ],
            [
                4,
                5,
                16,
                8,
                0
            ],
            [
                4,
                6,
                16,
                16,
                0
            ],
            [
                4,
                10,
                16,
                32,
                0
            ],
            [
                8,
                16,
                32,
                32,
                0
            ],
            [
                8,
                16,
                128,
                128,
                0
            ],
            [
                8,
                32,
                128,
                256,
                0
            ],
            [
                32,
                128,
                258,
                1024,
                1
            ],
            [
                32,
                258,
                258,
                4096,
                1
            ]
        ][i], a = UZIP.F.U, s = UZIP.F._goodIndex;
        UZIP.F._hash;
        var f = UZIP.F._putsE, l = 0, c = r << 3, u = 0, h = e.length;
        if (0 == i) {
            for(; l < h;){
                f(t1, c, l + (_ = Math.min(65535, h - l)) == h ? 1 : 0), c = UZIP.F._copyExact(e, l, _, t1, c + 8), l += _;
            }
            return c >>> 3;
        }
        var d = a.lits, A = a.strt, g = a.prev, p = 0, m = 0, w = 0, v = 0, b = 0, y = 0;
        for(h > 2 && (A[y = UZIP.F._hash(e, 0)] = 0), l = 0; l < h; l++){
            if (b = y, l + 1 < h - 2) {
                y = UZIP.F._hash(e, l + 1);
                var E = l + 1 & 32767;
                g[E] = A[y], A[y] = E;
            }
            if (u <= l) {
                (p > 14e3 || m > 26697) && h - l > 100 && (u < l && (d[p] = l - u, p += 2, u = l), c = UZIP.F._writeBlock(l == h - 1 || u == h ? 1 : 0, d, p, v, e, w, l - w, t1, c), p = m = v = 0, w = l);
                var F = 0;
                l < h - 2 && (F = UZIP.F._bestMatch(e, l, g, b, Math.min(o[2], h - l), o[3]));
                var _ = F >>> 16, B = 65535 & F;
                if (0 != F) {
                    B = 65535 & F;
                    var U = s(_ = F >>> 16, a.of0);
                    a.lhst[257 + U]++;
                    var C = s(B, a.df0);
                    a.dhst[C]++, v += a.exb[U] + a.dxb[C], d[p] = _ << 23 | l - u, d[p + 1] = B << 16 | U << 8 | C, p += 2, u = l + _;
                } else a.lhst[e[l]]++;
                m++;
            }
        }
        for(w == l && 0 != e.length || (u < l && (d[p] = l - u, p += 2, u = l), c = UZIP.F._writeBlock(1, d, p, v, e, w, l - w, t1, c), p = 0, m = 0, p = m = v = 0, w = l); 0 != (7 & c);)c++;
        return c >>> 3;
    }, UZIP.F._bestMatch = function(e, t1, r, i, o, a) {
        var s = 32767 & t1, f = r[s], l = s - f + 32768 & 32767;
        if (f == s || i != UZIP.F._hash(e, t1 - l)) return 0;
        for(var c = 0, u = 0, h = Math.min(32767, t1); l <= h && 0 != --a && f != s;){
            if (0 == c || e[t1 + c] == e[t1 + c - l]) {
                var d = UZIP.F._howLong(e, t1, l);
                if (d > c) {
                    if (u = l, (c = d) >= o) break;
                    l + 2 < d && (d = l + 2);
                    for(var A = 0, g = 0; g < d - 2; g++){
                        var p = t1 - l + g + 32768 & 32767, m = p - r[p] + 32768 & 32767;
                        m > A && (A = m, f = p);
                    }
                }
            }
            l += (s = f) - (f = r[s]) + 32768 & 32767;
        }
        return c << 16 | u;
    }, UZIP.F._howLong = function(e, t1, r) {
        if (e[t1] != e[t1 - r] || e[t1 + 1] != e[t1 + 1 - r] || e[t1 + 2] != e[t1 + 2 - r]) return 0;
        var i = t1, o = Math.min(e.length, t1 + 258);
        for(t1 += 3; t1 < o && e[t1] == e[t1 - r];)t1++;
        return t1 - i;
    }, UZIP.F._hash = function(e, t1) {
        return (e[t1] << 8 | e[t1 + 1]) + (e[t1 + 2] << 4) & 65535;
    }, UZIP.saved = 0, UZIP.F._writeBlock = function(e, t1, r, i, o, a, s, f, l) {
        var c, u, h, d, A, g, p, m, w, v = UZIP.F.U, b = UZIP.F._putsF, y = UZIP.F._putsE;
        v.lhst[256]++, u = (c = UZIP.F.getTrees())[0], h = c[1], d = c[2], A = c[3], g = c[4], p = c[5], m = c[6], w = c[7];
        var E = 32 + (0 == (l + 3 & 7) ? 0 : 8 - (l + 3 & 7)) + (s << 3), F = i + UZIP.F.contSize(v.fltree, v.lhst) + UZIP.F.contSize(v.fdtree, v.dhst), _ = i + UZIP.F.contSize(v.ltree, v.lhst) + UZIP.F.contSize(v.dtree, v.dhst);
        _ += 14 + 3 * p + UZIP.F.contSize(v.itree, v.ihst) + (2 * v.ihst[16] + 3 * v.ihst[17] + 7 * v.ihst[18]);
        for(var B = 0; B < 286; B++)v.lhst[B] = 0;
        for(B = 0; B < 30; B++)v.dhst[B] = 0;
        for(B = 0; B < 19; B++)v.ihst[B] = 0;
        var U = E < F && E < _ ? 0 : F < _ ? 1 : 2;
        if (b(f, l, e), b(f, l + 1, U), l += 3, 0 == U) {
            for(; 0 != (7 & l);)l++;
            l = UZIP.F._copyExact(o, a, s, f, l);
        } else {
            var C, I;
            if (1 == U && (C = v.fltree, I = v.fdtree), 2 == U) {
                UZIP.F.makeCodes(v.ltree, u), UZIP.F.revCodes(v.ltree, u), UZIP.F.makeCodes(v.dtree, h), UZIP.F.revCodes(v.dtree, h), UZIP.F.makeCodes(v.itree, d), UZIP.F.revCodes(v.itree, d), C = v.ltree, I = v.dtree, y(f, l, A - 257), y(f, l += 5, g - 1), y(f, l += 5, p - 4), l += 4;
                for(var Q = 0; Q < p; Q++)y(f, l + 3 * Q, v.itree[1 + (v.ordr[Q] << 1)]);
                l += 3 * p, l = UZIP.F._codeTiny(m, v.itree, f, l), l = UZIP.F._codeTiny(w, v.itree, f, l);
            }
            for(var M = a, x = 0; x < r; x += 2){
                for(var S = t1[x], R = S >>> 23, T = M + (8388607 & S); M < T;)l = UZIP.F._writeLit(o[M++], C, f, l);
                if (0 != R) {
                    var O = t1[x + 1], P = O >> 16, H = O >> 8 & 255, L = 255 & O;
                    y(f, l = UZIP.F._writeLit(257 + H, C, f, l), R - v.of0[H]), l += v.exb[H], b(f, l = UZIP.F._writeLit(L, I, f, l), P - v.df0[L]), l += v.dxb[L], M += R;
                }
            }
            l = UZIP.F._writeLit(256, C, f, l);
        }
        return l;
    }, UZIP.F._copyExact = function(e, t1, r, i, o) {
        var a = o >>> 3;
        return i[a] = r, i[a + 1] = r >>> 8, i[a + 2] = 255 - i[a], i[a + 3] = 255 - i[a + 1], a += 4, i.set(new Uint8Array(e.buffer, t1, r), a), o + (r + 4 << 3);
    }, UZIP.F.getTrees = function() {
        for(var e = UZIP.F.U, t1 = UZIP.F._hufTree(e.lhst, e.ltree, 15), r = UZIP.F._hufTree(e.dhst, e.dtree, 15), i = [], o = UZIP.F._lenCodes(e.ltree, i), a = [], s = UZIP.F._lenCodes(e.dtree, a), f = 0; f < i.length; f += 2)e.ihst[i[f]]++;
        for(f = 0; f < a.length; f += 2)e.ihst[a[f]]++;
        for(var l = UZIP.F._hufTree(e.ihst, e.itree, 7), c = 19; c > 4 && 0 == e.itree[1 + (e.ordr[c - 1] << 1)];)c--;
        return [
            t1,
            r,
            l,
            o,
            s,
            c,
            i,
            a
        ];
    }, UZIP.F.getSecond = function(e) {
        for(var t1 = [], r = 0; r < e.length; r += 2)t1.push(e[r + 1]);
        return t1;
    }, UZIP.F.nonZero = function(e) {
        for(var t1 = "", r = 0; r < e.length; r += 2)0 != e[r + 1] && (t1 += (r >> 1) + ",");
        return t1;
    }, UZIP.F.contSize = function(e, t1) {
        for(var r = 0, i = 0; i < t1.length; i++)r += t1[i] * e[1 + (i << 1)];
        return r;
    }, UZIP.F._codeTiny = function(e, t1, r, i) {
        for(var o = 0; o < e.length; o += 2){
            var a = e[o], s = e[o + 1];
            i = UZIP.F._writeLit(a, t1, r, i);
            var f = 16 == a ? 2 : 17 == a ? 3 : 7;
            a > 15 && (UZIP.F._putsE(r, i, s, f), i += f);
        }
        return i;
    }, UZIP.F._lenCodes = function(e, t1) {
        for(var r = e.length; 2 != r && 0 == e[r - 1];)r -= 2;
        for(var i = 0; i < r; i += 2){
            var o = e[i + 1], a = i + 3 < r ? e[i + 3] : -1, s = i + 5 < r ? e[i + 5] : -1, f = 0 == i ? -1 : e[i - 1];
            if (0 == o && a == o && s == o) {
                for(var l = i + 5; l + 2 < r && e[l + 2] == o;)l += 2;
                (c = Math.min(l + 1 - i >>> 1, 138)) < 11 ? t1.push(17, c - 3) : t1.push(18, c - 11), i += 2 * c - 2;
            } else if (o == f && a == o && s == o) {
                for(l = i + 5; l + 2 < r && e[l + 2] == o;)l += 2;
                var c = Math.min(l + 1 - i >>> 1, 6);
                t1.push(16, c - 3), i += 2 * c - 2;
            } else t1.push(o, 0);
        }
        return r >>> 1;
    }, UZIP.F._hufTree = function(e, t1, r) {
        var i = [], o = e.length, a = t1.length, s = 0;
        for(s = 0; s < a; s += 2)t1[s] = 0, t1[s + 1] = 0;
        for(s = 0; s < o; s++)0 != e[s] && i.push({
            lit: s,
            f: e[s]
        });
        var f = i.length, l = i.slice(0);
        if (0 == f) return 0;
        if (1 == f) {
            var c = i[0].lit;
            l = 0 == c ? 1 : 0;
            return t1[1 + (c << 1)] = 1, t1[1 + (l << 1)] = 1, 1;
        }
        i.sort(function(e, t1) {
            return e.f - t1.f;
        });
        var u = i[0], h = i[1], d = 0, A = 1, g = 2;
        for(i[0] = {
            lit: -1,
            f: u.f + h.f,
            l: u,
            r: h,
            d: 0
        }; A != f - 1;)u = d != A && (g == f || i[d].f < i[g].f) ? i[d++] : i[g++], h = d != A && (g == f || i[d].f < i[g].f) ? i[d++] : i[g++], i[A++] = {
            lit: -1,
            f: u.f + h.f,
            l: u,
            r: h
        };
        var p = UZIP.F.setDepth(i[A - 1], 0);
        for(p > r && (UZIP.F.restrictDepth(l, r, p), p = r), s = 0; s < f; s++)t1[1 + (l[s].lit << 1)] = l[s].d;
        return p;
    }, UZIP.F.setDepth = function(e, t1) {
        return -1 != e.lit ? (e.d = t1, t1) : Math.max(UZIP.F.setDepth(e.l, t1 + 1), UZIP.F.setDepth(e.r, t1 + 1));
    }, UZIP.F.restrictDepth = function(e, t1, r) {
        var i = 0, o = 1 << r - t1, a = 0;
        for(e.sort(function(e, t1) {
            return t1.d == e.d ? e.f - t1.f : t1.d - e.d;
        }), i = 0; i < e.length && e[i].d > t1; i++){
            var s = e[i].d;
            e[i].d = t1, a += o - (1 << r - s);
        }
        for(a >>>= r - t1; a > 0;){
            (s = e[i].d) < t1 ? (e[i].d++, a -= 1 << t1 - s - 1) : i++;
        }
        for(; i >= 0; i--)e[i].d == t1 && a < 0 && (e[i].d--, a++);
        0 != a && console.log("debt left");
    }, UZIP.F._goodIndex = function(e, t1) {
        var r = 0;
        return t1[16 | r] <= e && (r |= 16), t1[8 | r] <= e && (r |= 8), t1[4 | r] <= e && (r |= 4), t1[2 | r] <= e && (r |= 2), t1[1 | r] <= e && (r |= 1), r;
    }, UZIP.F._writeLit = function(e, t1, r, i) {
        return UZIP.F._putsF(r, i, t1[e << 1]), i + t1[1 + (e << 1)];
    }, UZIP.F.inflate = function(e, t1) {
        var r = Uint8Array;
        if (3 == e[0] && 0 == e[1]) return t1 || new r(0);
        var i = UZIP.F, o = i._bitsF, a = i._bitsE, s = i._decodeTiny, f = i.makeCodes, l = i.codes2map, c = i._get17, u = i.U, h = null == t1;
        h && (t1 = new r(e.length >>> 2 << 3));
        for(var d, A, g = 0, p = 0, m = 0, w = 0, v = 0, b = 0, y = 0, E = 0, F = 0; 0 == g;)if (g = o(e, F, 1), p = o(e, F + 1, 2), F += 3, 0 != p) {
            if (h && (t1 = UZIP.F._check(t1, E + (1 << 17))), 1 == p && (d = u.flmap, A = u.fdmap, b = 511, y = 31), 2 == p) {
                m = a(e, F, 5) + 257, w = a(e, F + 5, 5) + 1, v = a(e, F + 10, 4) + 4, F += 14;
                for(var _ = 0; _ < 38; _ += 2)u.itree[_] = 0, u.itree[_ + 1] = 0;
                var B = 1;
                for(_ = 0; _ < v; _++){
                    var U = a(e, F + 3 * _, 3);
                    u.itree[1 + (u.ordr[_] << 1)] = U, U > B && (B = U);
                }
                F += 3 * v, f(u.itree, B), l(u.itree, B, u.imap), d = u.lmap, A = u.dmap, F = s(u.imap, (1 << B) - 1, m + w, e, F, u.ttree);
                var C = i._copyOut(u.ttree, 0, m, u.ltree);
                b = (1 << C) - 1;
                var I = i._copyOut(u.ttree, m, w, u.dtree);
                y = (1 << I) - 1, f(u.ltree, C), l(u.ltree, C, d), f(u.dtree, I), l(u.dtree, I, A);
            }
            for(;;){
                var Q = d[c(e, F) & b];
                F += 15 & Q;
                var M = Q >>> 4;
                if (M >>> 8 == 0) t1[E++] = M;
                else {
                    if (256 == M) break;
                    var x = E + M - 254;
                    if (M > 264) {
                        var S = u.ldef[M - 257];
                        x = E + (S >>> 3) + a(e, F, 7 & S), F += 7 & S;
                    }
                    var R = A[c(e, F) & y];
                    F += 15 & R;
                    var T = R >>> 4, O = u.ddef[T], P = (O >>> 4) + o(e, F, 15 & O);
                    for(F += 15 & O, h && (t1 = UZIP.F._check(t1, E + (1 << 17))); E < x;)t1[E] = t1[E++ - P], t1[E] = t1[E++ - P], t1[E] = t1[E++ - P], t1[E] = t1[E++ - P];
                    E = x;
                }
            }
        } else {
            0 != (7 & F) && (F += 8 - (7 & F));
            var H = 4 + (F >>> 3), L = e[H - 4] | e[H - 3] << 8;
            h && (t1 = UZIP.F._check(t1, E + L)), t1.set(new r(e.buffer, e.byteOffset + H, L), E), F = H + L << 3, E += L;
        }
        return t1.length == E ? t1 : t1.slice(0, E);
    }, UZIP.F._check = function(e, t1) {
        var r = e.length;
        if (t1 <= r) return e;
        var i = new Uint8Array(Math.max(r << 1, t1));
        return i.set(e, 0), i;
    }, UZIP.F._decodeTiny = function(e, t1, r, i, o, a) {
        for(var s = UZIP.F._bitsE, f = UZIP.F._get17, l = 0; l < r;){
            var c = e[f(i, o) & t1];
            o += 15 & c;
            var u = c >>> 4;
            if (u <= 15) a[l] = u, l++;
            else {
                var h = 0, d = 0;
                16 == u ? (d = 3 + s(i, o, 2), o += 2, h = a[l - 1]) : 17 == u ? (d = 3 + s(i, o, 3), o += 3) : 18 == u && (d = 11 + s(i, o, 7), o += 7);
                for(var A = l + d; l < A;)a[l] = h, l++;
            }
        }
        return o;
    }, UZIP.F._copyOut = function(e, t1, r, i) {
        for(var o = 0, a = 0, s = i.length >>> 1; a < r;){
            var f = e[a + t1];
            i[a << 1] = 0, i[1 + (a << 1)] = f, f > o && (o = f), a++;
        }
        for(; a < s;)i[a << 1] = 0, i[1 + (a << 1)] = 0, a++;
        return o;
    }, UZIP.F.makeCodes = function(e, t1) {
        for(var r, i, o, a, s = UZIP.F.U, f = e.length, l = s.bl_count, c = 0; c <= t1; c++)l[c] = 0;
        for(c = 1; c < f; c += 2)l[e[c]]++;
        var u = s.next_code;
        for(r = 0, l[0] = 0, i = 1; i <= t1; i++)r = r + l[i - 1] << 1, u[i] = r;
        for(o = 0; o < f; o += 2)0 != (a = e[o + 1]) && (e[o] = u[a], u[a]++);
    }, UZIP.F.codes2map = function(e, t1, r) {
        for(var i = e.length, o = UZIP.F.U.rev15, a = 0; a < i; a += 2)if (0 != e[a + 1]) for(var s = a >> 1, f = e[a + 1], l = s << 4 | f, c = t1 - f, u = e[a] << c, h = u + (1 << c); u != h;){
            r[o[u] >>> 15 - t1] = l, u++;
        }
    }, UZIP.F.revCodes = function(e, t1) {
        for(var r = UZIP.F.U.rev15, i = 15 - t1, o = 0; o < e.length; o += 2){
            var a = e[o] << t1 - e[o + 1];
            e[o] = r[a] >>> i;
        }
    }, UZIP.F._putsE = function(e, t1, r) {
        r <<= 7 & t1;
        var i = t1 >>> 3;
        e[i] |= r, e[i + 1] |= r >>> 8;
    }, UZIP.F._putsF = function(e, t1, r) {
        r <<= 7 & t1;
        var i = t1 >>> 3;
        e[i] |= r, e[i + 1] |= r >>> 8, e[i + 2] |= r >>> 16;
    }, UZIP.F._bitsE = function(e, t1, r) {
        return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8) >>> (7 & t1) & (1 << r) - 1;
    }, UZIP.F._bitsF = function(e, t1, r) {
        return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8 | e[2 + (t1 >>> 3)] << 16) >>> (7 & t1) & (1 << r) - 1;
    }, UZIP.F._get17 = function(e, t1) {
        return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8 | e[2 + (t1 >>> 3)] << 16) >>> (7 & t1);
    }, UZIP.F._get25 = function(e, t1) {
        return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8 | e[2 + (t1 >>> 3)] << 16 | e[3 + (t1 >>> 3)] << 24) >>> (7 & t1);
    }, UZIP.F.U = (r = Uint16Array, i = Uint32Array, {
        next_code: new r(16),
        bl_count: new r(16),
        ordr: [
            16,
            17,
            18,
            0,
            8,
            7,
            9,
            6,
            10,
            5,
            11,
            4,
            12,
            3,
            13,
            2,
            14,
            1,
            15
        ],
        of0: [
            3,
            4,
            5,
            6,
            7,
            8,
            9,
            10,
            11,
            13,
            15,
            17,
            19,
            23,
            27,
            31,
            35,
            43,
            51,
            59,
            67,
            83,
            99,
            115,
            131,
            163,
            195,
            227,
            258,
            999,
            999,
            999
        ],
        exb: [
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            1,
            1,
            1,
            1,
            2,
            2,
            2,
            2,
            3,
            3,
            3,
            3,
            4,
            4,
            4,
            4,
            5,
            5,
            5,
            5,
            0,
            0,
            0,
            0
        ],
        ldef: new r(32),
        df0: [
            1,
            2,
            3,
            4,
            5,
            7,
            9,
            13,
            17,
            25,
            33,
            49,
            65,
            97,
            129,
            193,
            257,
            385,
            513,
            769,
            1025,
            1537,
            2049,
            3073,
            4097,
            6145,
            8193,
            12289,
            16385,
            24577,
            65535,
            65535
        ],
        dxb: [
            0,
            0,
            0,
            0,
            1,
            1,
            2,
            2,
            3,
            3,
            4,
            4,
            5,
            5,
            6,
            6,
            7,
            7,
            8,
            8,
            9,
            9,
            10,
            10,
            11,
            11,
            12,
            12,
            13,
            13,
            0,
            0
        ],
        ddef: new i(32),
        flmap: new r(512),
        fltree: [],
        fdmap: new r(32),
        fdtree: [],
        lmap: new r(32768),
        ltree: [],
        ttree: [],
        dmap: new r(32768),
        dtree: [],
        imap: new r(512),
        itree: [],
        rev15: new r(32768),
        lhst: new i(286),
        dhst: new i(30),
        ihst: new i(19),
        lits: new i(15e3),
        strt: new r(65536),
        prev: new r(32768)
    }), function() {
        for(var e = UZIP.F.U, t1 = 0; t1 < 32768; t1++){
            var r = t1;
            r = (4278255360 & (r = (4042322160 & (r = (3435973836 & (r = (2863311530 & r) >>> 1 | (1431655765 & r) << 1)) >>> 2 | (858993459 & r) << 2)) >>> 4 | (252645135 & r) << 4)) >>> 8 | (16711935 & r) << 8, e.rev15[t1] = (r >>> 16 | r << 16) >>> 17;
        }
        function pushV(e, t1, r) {
            for(; 0 != t1--;)e.push(0, r);
        }
        for(t1 = 0; t1 < 32; t1++)e.ldef[t1] = e.of0[t1] << 3 | e.exb[t1], e.ddef[t1] = e.df0[t1] << 4 | e.dxb[t1];
        pushV(e.fltree, 144, 8), pushV(e.fltree, 112, 9), pushV(e.fltree, 24, 7), pushV(e.fltree, 8, 8), UZIP.F.makeCodes(e.fltree, 9), UZIP.F.codes2map(e.fltree, 9, e.flmap), UZIP.F.revCodes(e.fltree, 9), pushV(e.fdtree, 32, 5), UZIP.F.makeCodes(e.fdtree, 5), UZIP.F.codes2map(e.fdtree, 5, e.fdmap), UZIP.F.revCodes(e.fdtree, 5), pushV(e.itree, 19, 0), pushV(e.ltree, 286, 0), pushV(e.dtree, 30, 0), pushV(e.ttree, 320, 0);
    }();
}();
var UZIP = _mergeNamespaces({
    __proto__: null,
    default: e
}, [
    e
]);
const UPNG = function() {
    var e = {
        nextZero (e, t1) {
            for(; 0 != e[t1];)t1++;
            return t1;
        },
        readUshort: (e, t1)=>e[t1] << 8 | e[t1 + 1],
        writeUshort (e, t1, r) {
            e[t1] = r >> 8 & 255, e[t1 + 1] = 255 & r;
        },
        readUint: (e, t1)=>16777216 * e[t1] + (e[t1 + 1] << 16 | e[t1 + 2] << 8 | e[t1 + 3]),
        writeUint (e, t1, r) {
            e[t1] = r >> 24 & 255, e[t1 + 1] = r >> 16 & 255, e[t1 + 2] = r >> 8 & 255, e[t1 + 3] = 255 & r;
        },
        readASCII (e, t1, r) {
            let i = "";
            for(let o = 0; o < r; o++)i += String.fromCharCode(e[t1 + o]);
            return i;
        },
        writeASCII (e, t1, r) {
            for(let i = 0; i < r.length; i++)e[t1 + i] = r.charCodeAt(i);
        },
        readBytes (e, t1, r) {
            const i = [];
            for(let o = 0; o < r; o++)i.push(e[t1 + o]);
            return i;
        },
        pad: (e)=>e.length < 2 ? "0".concat(e) : e,
        readUTF8 (t1, r, i) {
            let o, a = "";
            for(let o = 0; o < i; o++)a += "%".concat(e.pad(t1[r + o].toString(16)));
            try {
                o = decodeURIComponent(a);
            } catch (o) {
                return e.readASCII(t1, r, i);
            }
            return o;
        }
    };
    function decodeImage(t1, r, i, o) {
        const a = r * i, s = _getBPP(o), f = Math.ceil(r * s / 8), l = new Uint8Array(4 * a), c = new Uint32Array(l.buffer), { ctype: u } = o, { depth: h } = o, d = e.readUshort;
        if (6 == u) {
            const e = a << 2;
            if (8 == h) for(var A = 0; A < e; A += 4)l[A] = t1[A], l[A + 1] = t1[A + 1], l[A + 2] = t1[A + 2], l[A + 3] = t1[A + 3];
            if (16 == h) for(A = 0; A < e; A++)l[A] = t1[A << 1];
        } else if (2 == u) {
            const e = o.tabs.tRNS;
            if (null == e) {
                if (8 == h) for(A = 0; A < a; A++){
                    var g = 3 * A;
                    c[A] = 255 << 24 | t1[g + 2] << 16 | t1[g + 1] << 8 | t1[g];
                }
                if (16 == h) for(A = 0; A < a; A++){
                    g = 6 * A;
                    c[A] = 255 << 24 | t1[g + 4] << 16 | t1[g + 2] << 8 | t1[g];
                }
            } else {
                var p = e[0];
                const r = e[1], i = e[2];
                if (8 == h) for(A = 0; A < a; A++){
                    var m = A << 2;
                    g = 3 * A;
                    c[A] = 255 << 24 | t1[g + 2] << 16 | t1[g + 1] << 8 | t1[g], t1[g] == p && t1[g + 1] == r && t1[g + 2] == i && (l[m + 3] = 0);
                }
                if (16 == h) for(A = 0; A < a; A++){
                    m = A << 2, g = 6 * A;
                    c[A] = 255 << 24 | t1[g + 4] << 16 | t1[g + 2] << 8 | t1[g], d(t1, g) == p && d(t1, g + 2) == r && d(t1, g + 4) == i && (l[m + 3] = 0);
                }
            }
        } else if (3 == u) {
            const e = o.tabs.PLTE, s = o.tabs.tRNS, c = s ? s.length : 0;
            if (1 == h) for(var w = 0; w < i; w++){
                var v = w * f, b = w * r;
                for(A = 0; A < r; A++){
                    m = b + A << 2;
                    var y = 3 * (E = t1[v + (A >> 3)] >> 7 - ((7 & A) << 0) & 1);
                    l[m] = e[y], l[m + 1] = e[y + 1], l[m + 2] = e[y + 2], l[m + 3] = E < c ? s[E] : 255;
                }
            }
            if (2 == h) for(w = 0; w < i; w++)for(v = w * f, b = w * r, A = 0; A < r; A++){
                m = b + A << 2, y = 3 * (E = t1[v + (A >> 2)] >> 6 - ((3 & A) << 1) & 3);
                l[m] = e[y], l[m + 1] = e[y + 1], l[m + 2] = e[y + 2], l[m + 3] = E < c ? s[E] : 255;
            }
            if (4 == h) for(w = 0; w < i; w++)for(v = w * f, b = w * r, A = 0; A < r; A++){
                m = b + A << 2, y = 3 * (E = t1[v + (A >> 1)] >> 4 - ((1 & A) << 2) & 15);
                l[m] = e[y], l[m + 1] = e[y + 1], l[m + 2] = e[y + 2], l[m + 3] = E < c ? s[E] : 255;
            }
            if (8 == h) for(A = 0; A < a; A++){
                var E;
                m = A << 2, y = 3 * (E = t1[A]);
                l[m] = e[y], l[m + 1] = e[y + 1], l[m + 2] = e[y + 2], l[m + 3] = E < c ? s[E] : 255;
            }
        } else if (4 == u) {
            if (8 == h) for(A = 0; A < a; A++){
                m = A << 2;
                var F = t1[_ = A << 1];
                l[m] = F, l[m + 1] = F, l[m + 2] = F, l[m + 3] = t1[_ + 1];
            }
            if (16 == h) for(A = 0; A < a; A++){
                var _;
                m = A << 2, F = t1[_ = A << 2];
                l[m] = F, l[m + 1] = F, l[m + 2] = F, l[m + 3] = t1[_ + 2];
            }
        } else if (0 == u) for(p = o.tabs.tRNS ? o.tabs.tRNS : -1, w = 0; w < i; w++){
            const e = w * f, i = w * r;
            if (1 == h) for(var B = 0; B < r; B++){
                var U = (F = 255 * (t1[e + (B >>> 3)] >>> 7 - (7 & B) & 1)) == 255 * p ? 0 : 255;
                c[i + B] = U << 24 | F << 16 | F << 8 | F;
            }
            else if (2 == h) for(B = 0; B < r; B++){
                U = (F = 85 * (t1[e + (B >>> 2)] >>> 6 - ((3 & B) << 1) & 3)) == 85 * p ? 0 : 255;
                c[i + B] = U << 24 | F << 16 | F << 8 | F;
            }
            else if (4 == h) for(B = 0; B < r; B++){
                U = (F = 17 * (t1[e + (B >>> 1)] >>> 4 - ((1 & B) << 2) & 15)) == 17 * p ? 0 : 255;
                c[i + B] = U << 24 | F << 16 | F << 8 | F;
            }
            else if (8 == h) for(B = 0; B < r; B++){
                U = (F = t1[e + B]) == p ? 0 : 255;
                c[i + B] = U << 24 | F << 16 | F << 8 | F;
            }
            else if (16 == h) for(B = 0; B < r; B++){
                F = t1[e + (B << 1)], U = d(t1, e + (B << 1)) == p ? 0 : 255;
                c[i + B] = U << 24 | F << 16 | F << 8 | F;
            }
        }
        return l;
    }
    function _decompress(e, r, i, o) {
        const a = _getBPP(e), s = Math.ceil(i * a / 8), f = new Uint8Array((s + 1 + e.interlace) * o);
        return r = e.tabs.CgBI ? t1(r, f) : _inflate(r, f), 0 == e.interlace ? r = _filterZero(r, e, 0, i, o) : 1 == e.interlace && (r = function _readInterlace(e, t1) {
            const r = t1.width, i = t1.height, o = _getBPP(t1), a = o >> 3, s = Math.ceil(r * o / 8), f = new Uint8Array(i * s);
            let l = 0;
            const c = [
                0,
                0,
                4,
                0,
                2,
                0,
                1
            ], u = [
                0,
                4,
                0,
                2,
                0,
                1,
                0
            ], h = [
                8,
                8,
                8,
                4,
                4,
                2,
                2
            ], d = [
                8,
                8,
                4,
                4,
                2,
                2,
                1
            ];
            let A = 0;
            for(; A < 7;){
                const p = h[A], m = d[A];
                let w = 0, v = 0, b = c[A];
                for(; b < i;)b += p, v++;
                let y = u[A];
                for(; y < r;)y += m, w++;
                const E = Math.ceil(w * o / 8);
                _filterZero(e, t1, l, w, v);
                let F = 0, _ = c[A];
                for(; _ < i;){
                    let t1 = u[A], i = l + F * E << 3;
                    for(; t1 < r;){
                        var g;
                        if (1 == o) g = (g = e[i >> 3]) >> 7 - (7 & i) & 1, f[_ * s + (t1 >> 3)] |= g << 7 - ((7 & t1) << 0);
                        if (2 == o) g = (g = e[i >> 3]) >> 6 - (7 & i) & 3, f[_ * s + (t1 >> 2)] |= g << 6 - ((3 & t1) << 1);
                        if (4 == o) g = (g = e[i >> 3]) >> 4 - (7 & i) & 15, f[_ * s + (t1 >> 1)] |= g << 4 - ((1 & t1) << 2);
                        if (o >= 8) {
                            const r = _ * s + t1 * a;
                            for(let t1 = 0; t1 < a; t1++)f[r + t1] = e[(i >> 3) + t1];
                        }
                        i += o, t1 += m;
                    }
                    F++, _ += p;
                }
                w * v != 0 && (l += v * (1 + E)), A += 1;
            }
            return f;
        }(r, e)), r;
    }
    function _inflate(e, r) {
        return t1(new Uint8Array(e.buffer, 2, e.length - 6), r);
    }
    var t1 = function() {
        const e = {
            H: {}
        };
        return e.H.N = function(t1, r) {
            const i = Uint8Array;
            let o, a, s = 0, f = 0, l = 0, c = 0, u = 0, h = 0, d = 0, A = 0, g = 0;
            if (3 == t1[0] && 0 == t1[1]) return r || new i(0);
            const p = e.H, m = p.b, w = p.e, v = p.R, b = p.n, y = p.A, E = p.Z, F = p.m, _ = null == r;
            for(_ && (r = new i(t1.length >>> 2 << 5)); 0 == s;)if (s = m(t1, g, 1), f = m(t1, g + 1, 2), g += 3, 0 != f) {
                if (_ && (r = e.H.W(r, A + (1 << 17))), 1 == f && (o = F.J, a = F.h, h = 511, d = 31), 2 == f) {
                    l = w(t1, g, 5) + 257, c = w(t1, g + 5, 5) + 1, u = w(t1, g + 10, 4) + 4, g += 14;
                    let e = 1;
                    for(var B = 0; B < 38; B += 2)F.Q[B] = 0, F.Q[B + 1] = 0;
                    for(B = 0; B < u; B++){
                        const r = w(t1, g + 3 * B, 3);
                        F.Q[1 + (F.X[B] << 1)] = r, r > e && (e = r);
                    }
                    g += 3 * u, b(F.Q, e), y(F.Q, e, F.u), o = F.w, a = F.d, g = v(F.u, (1 << e) - 1, l + c, t1, g, F.v);
                    const r = p.V(F.v, 0, l, F.C);
                    h = (1 << r) - 1;
                    const i = p.V(F.v, l, c, F.D);
                    d = (1 << i) - 1, b(F.C, r), y(F.C, r, o), b(F.D, i), y(F.D, i, a);
                }
                for(;;){
                    const e = o[E(t1, g) & h];
                    g += 15 & e;
                    const i = e >>> 4;
                    if (i >>> 8 == 0) r[A++] = i;
                    else {
                        if (256 == i) break;
                        {
                            let e = A + i - 254;
                            if (i > 264) {
                                const r = F.q[i - 257];
                                e = A + (r >>> 3) + w(t1, g, 7 & r), g += 7 & r;
                            }
                            const o = a[E(t1, g) & d];
                            g += 15 & o;
                            const s = o >>> 4, f = F.c[s], l = (f >>> 4) + m(t1, g, 15 & f);
                            for(g += 15 & f; A < e;)r[A] = r[A++ - l], r[A] = r[A++ - l], r[A] = r[A++ - l], r[A] = r[A++ - l];
                            A = e;
                        }
                    }
                }
            } else {
                0 != (7 & g) && (g += 8 - (7 & g));
                const o = 4 + (g >>> 3), a = t1[o - 4] | t1[o - 3] << 8;
                _ && (r = e.H.W(r, A + a)), r.set(new i(t1.buffer, t1.byteOffset + o, a), A), g = o + a << 3, A += a;
            }
            return r.length == A ? r : r.slice(0, A);
        }, e.H.W = function(e, t1) {
            const r = e.length;
            if (t1 <= r) return e;
            const i = new Uint8Array(r << 1);
            return i.set(e, 0), i;
        }, e.H.R = function(t1, r, i, o, a, s) {
            const f = e.H.e, l = e.H.Z;
            let c = 0;
            for(; c < i;){
                const e = t1[l(o, a) & r];
                a += 15 & e;
                const i = e >>> 4;
                if (i <= 15) s[c] = i, c++;
                else {
                    let e = 0, t1 = 0;
                    16 == i ? (t1 = 3 + f(o, a, 2), a += 2, e = s[c - 1]) : 17 == i ? (t1 = 3 + f(o, a, 3), a += 3) : 18 == i && (t1 = 11 + f(o, a, 7), a += 7);
                    const r = c + t1;
                    for(; c < r;)s[c] = e, c++;
                }
            }
            return a;
        }, e.H.V = function(e, t1, r, i) {
            let o = 0, a = 0;
            const s = i.length >>> 1;
            for(; a < r;){
                const r = e[a + t1];
                i[a << 1] = 0, i[1 + (a << 1)] = r, r > o && (o = r), a++;
            }
            for(; a < s;)i[a << 1] = 0, i[1 + (a << 1)] = 0, a++;
            return o;
        }, e.H.n = function(t1, r) {
            const i = e.H.m, o = t1.length;
            let a, s, f;
            let l;
            const c = i.j;
            for(var u = 0; u <= r; u++)c[u] = 0;
            for(u = 1; u < o; u += 2)c[t1[u]]++;
            const h = i.K;
            for(a = 0, c[0] = 0, s = 1; s <= r; s++)a = a + c[s - 1] << 1, h[s] = a;
            for(f = 0; f < o; f += 2)l = t1[f + 1], 0 != l && (t1[f] = h[l], h[l]++);
        }, e.H.A = function(t1, r, i) {
            const o = t1.length, a = e.H.m.r;
            for(let e = 0; e < o; e += 2)if (0 != t1[e + 1]) {
                const o = e >> 1, s = t1[e + 1], f = o << 4 | s, l = r - s;
                let c = t1[e] << l;
                const u = c + (1 << l);
                for(; c != u;){
                    i[a[c] >>> 15 - r] = f, c++;
                }
            }
        }, e.H.l = function(t1, r) {
            const i = e.H.m.r, o = 15 - r;
            for(let e = 0; e < t1.length; e += 2){
                const a = t1[e] << r - t1[e + 1];
                t1[e] = i[a] >>> o;
            }
        }, e.H.M = function(e, t1, r) {
            r <<= 7 & t1;
            const i = t1 >>> 3;
            e[i] |= r, e[i + 1] |= r >>> 8;
        }, e.H.I = function(e, t1, r) {
            r <<= 7 & t1;
            const i = t1 >>> 3;
            e[i] |= r, e[i + 1] |= r >>> 8, e[i + 2] |= r >>> 16;
        }, e.H.e = function(e, t1, r) {
            return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8) >>> (7 & t1) & (1 << r) - 1;
        }, e.H.b = function(e, t1, r) {
            return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8 | e[2 + (t1 >>> 3)] << 16) >>> (7 & t1) & (1 << r) - 1;
        }, e.H.Z = function(e, t1) {
            return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8 | e[2 + (t1 >>> 3)] << 16) >>> (7 & t1);
        }, e.H.i = function(e, t1) {
            return (e[t1 >>> 3] | e[1 + (t1 >>> 3)] << 8 | e[2 + (t1 >>> 3)] << 16 | e[3 + (t1 >>> 3)] << 24) >>> (7 & t1);
        }, e.H.m = function() {
            const e = Uint16Array, t1 = Uint32Array;
            return {
                K: new e(16),
                j: new e(16),
                X: [
                    16,
                    17,
                    18,
                    0,
                    8,
                    7,
                    9,
                    6,
                    10,
                    5,
                    11,
                    4,
                    12,
                    3,
                    13,
                    2,
                    14,
                    1,
                    15
                ],
                S: [
                    3,
                    4,
                    5,
                    6,
                    7,
                    8,
                    9,
                    10,
                    11,
                    13,
                    15,
                    17,
                    19,
                    23,
                    27,
                    31,
                    35,
                    43,
                    51,
                    59,
                    67,
                    83,
                    99,
                    115,
                    131,
                    163,
                    195,
                    227,
                    258,
                    999,
                    999,
                    999
                ],
                T: [
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    1,
                    1,
                    1,
                    1,
                    2,
                    2,
                    2,
                    2,
                    3,
                    3,
                    3,
                    3,
                    4,
                    4,
                    4,
                    4,
                    5,
                    5,
                    5,
                    5,
                    0,
                    0,
                    0,
                    0
                ],
                q: new e(32),
                p: [
                    1,
                    2,
                    3,
                    4,
                    5,
                    7,
                    9,
                    13,
                    17,
                    25,
                    33,
                    49,
                    65,
                    97,
                    129,
                    193,
                    257,
                    385,
                    513,
                    769,
                    1025,
                    1537,
                    2049,
                    3073,
                    4097,
                    6145,
                    8193,
                    12289,
                    16385,
                    24577,
                    65535,
                    65535
                ],
                z: [
                    0,
                    0,
                    0,
                    0,
                    1,
                    1,
                    2,
                    2,
                    3,
                    3,
                    4,
                    4,
                    5,
                    5,
                    6,
                    6,
                    7,
                    7,
                    8,
                    8,
                    9,
                    9,
                    10,
                    10,
                    11,
                    11,
                    12,
                    12,
                    13,
                    13,
                    0,
                    0
                ],
                c: new t1(32),
                J: new e(512),
                _: [],
                h: new e(32),
                $: [],
                w: new e(32768),
                C: [],
                v: [],
                d: new e(32768),
                D: [],
                u: new e(512),
                Q: [],
                r: new e(32768),
                s: new t1(286),
                Y: new t1(30),
                a: new t1(19),
                t: new t1(15e3),
                k: new e(65536),
                g: new e(32768)
            };
        }(), function() {
            const t1 = e.H.m;
            for(var r = 0; r < 32768; r++){
                let e = r;
                e = (2863311530 & e) >>> 1 | (1431655765 & e) << 1, e = (3435973836 & e) >>> 2 | (858993459 & e) << 2, e = (4042322160 & e) >>> 4 | (252645135 & e) << 4, e = (4278255360 & e) >>> 8 | (16711935 & e) << 8, t1.r[r] = (e >>> 16 | e << 16) >>> 17;
            }
            function n(e, t1, r) {
                for(; 0 != t1--;)e.push(0, r);
            }
            for(r = 0; r < 32; r++)t1.q[r] = t1.S[r] << 3 | t1.T[r], t1.c[r] = t1.p[r] << 4 | t1.z[r];
            n(t1._, 144, 8), n(t1._, 112, 9), n(t1._, 24, 7), n(t1._, 8, 8), e.H.n(t1._, 9), e.H.A(t1._, 9, t1.J), e.H.l(t1._, 9), n(t1.$, 32, 5), e.H.n(t1.$, 5), e.H.A(t1.$, 5, t1.h), e.H.l(t1.$, 5), n(t1.Q, 19, 0), n(t1.C, 286, 0), n(t1.D, 30, 0), n(t1.v, 320, 0);
        }(), e.H.N;
    }();
    function _getBPP(e) {
        return [
            1,
            null,
            3,
            1,
            2,
            null,
            4
        ][e.ctype] * e.depth;
    }
    function _filterZero(e, t1, r, i, o) {
        let a = _getBPP(t1);
        const s = Math.ceil(i * a / 8);
        let f, l;
        a = Math.ceil(a / 8);
        let c = e[r], u = 0;
        if (c > 1 && (e[r] = [
            0,
            0,
            1
        ][c - 2]), 3 == c) for(u = a; u < s; u++)e[u + 1] = e[u + 1] + (e[u + 1 - a] >>> 1) & 255;
        for(let t1 = 0; t1 < o; t1++)if (f = r + t1 * s, l = f + t1 + 1, c = e[l - 1], u = 0, 0 == c) for(; u < s; u++)e[f + u] = e[l + u];
        else if (1 == c) {
            for(; u < a; u++)e[f + u] = e[l + u];
            for(; u < s; u++)e[f + u] = e[l + u] + e[f + u - a];
        } else if (2 == c) for(; u < s; u++)e[f + u] = e[l + u] + e[f + u - s];
        else if (3 == c) {
            for(; u < a; u++)e[f + u] = e[l + u] + (e[f + u - s] >>> 1);
            for(; u < s; u++)e[f + u] = e[l + u] + (e[f + u - s] + e[f + u - a] >>> 1);
        } else {
            for(; u < a; u++)e[f + u] = e[l + u] + _paeth(0, e[f + u - s], 0);
            for(; u < s; u++)e[f + u] = e[l + u] + _paeth(e[f + u - a], e[f + u - s], e[f + u - a - s]);
        }
        return e;
    }
    function _paeth(e, t1, r) {
        const i = e + t1 - r, o = i - e, a = i - t1, s = i - r;
        return o * o <= a * a && o * o <= s * s ? e : a * a <= s * s ? t1 : r;
    }
    function _IHDR(t1, r, i) {
        i.width = e.readUint(t1, r), r += 4, i.height = e.readUint(t1, r), r += 4, i.depth = t1[r], r++, i.ctype = t1[r], r++, i.compress = t1[r], r++, i.filter = t1[r], r++, i.interlace = t1[r], r++;
    }
    function _copyTile(e, t1, r, i, o, a, s, f, l) {
        const c = Math.min(t1, o), u = Math.min(r, a);
        let h = 0, d = 0;
        for(let r = 0; r < u; r++)for(let a = 0; a < c; a++)if (s >= 0 && f >= 0 ? (h = r * t1 + a << 2, d = (f + r) * o + s + a << 2) : (h = (-f + r) * t1 - s + a << 2, d = r * o + a << 2), 0 == l) i[d] = e[h], i[d + 1] = e[h + 1], i[d + 2] = e[h + 2], i[d + 3] = e[h + 3];
        else if (1 == l) {
            var A = e[h + 3] * (1 / 255), g = e[h] * A, p = e[h + 1] * A, m = e[h + 2] * A, w = i[d + 3] * (1 / 255), v = i[d] * w, b = i[d + 1] * w, y = i[d + 2] * w;
            const t1 = 1 - A, r = A + w * t1, o = 0 == r ? 0 : 1 / r;
            i[d + 3] = 255 * r, i[d + 0] = (g + v * t1) * o, i[d + 1] = (p + b * t1) * o, i[d + 2] = (m + y * t1) * o;
        } else if (2 == l) {
            A = e[h + 3], g = e[h], p = e[h + 1], m = e[h + 2], w = i[d + 3], v = i[d], b = i[d + 1], y = i[d + 2];
            A == w && g == v && p == b && m == y ? (i[d] = 0, i[d + 1] = 0, i[d + 2] = 0, i[d + 3] = 0) : (i[d] = g, i[d + 1] = p, i[d + 2] = m, i[d + 3] = A);
        } else if (3 == l) {
            A = e[h + 3], g = e[h], p = e[h + 1], m = e[h + 2], w = i[d + 3], v = i[d], b = i[d + 1], y = i[d + 2];
            if (A == w && g == v && p == b && m == y) continue;
            if (A < 220 && w > 20) return !1;
        }
        return !0;
    }
    return {
        decode: function decode(r) {
            const i = new Uint8Array(r);
            let o = 8;
            const a = e, s = a.readUshort, f = a.readUint, l = {
                tabs: {},
                frames: []
            }, c = new Uint8Array(i.length);
            let u, h = 0, d = 0;
            const A = [
                137,
                80,
                78,
                71,
                13,
                10,
                26,
                10
            ];
            for(var g = 0; g < 8; g++)if (i[g] != A[g]) throw "The input is not a PNG file!";
            for(; o < i.length;){
                const e = a.readUint(i, o);
                o += 4;
                const r = a.readASCII(i, o, 4);
                if (o += 4, "IHDR" == r) _IHDR(i, o, l);
                else if ("iCCP" == r) {
                    for(var p = o; 0 != i[p];)p++;
                    a.readASCII(i, o, p - o), i[p + 1];
                    const s = i.slice(p + 2, o + e);
                    let f = null;
                    try {
                        f = _inflate(s);
                    } catch (e) {
                        f = t1(s);
                    }
                    l.tabs[r] = f;
                } else if ("CgBI" == r) l.tabs[r] = i.slice(o, o + 4);
                else if ("IDAT" == r) {
                    for(g = 0; g < e; g++)c[h + g] = i[o + g];
                    h += e;
                } else if ("acTL" == r) l.tabs[r] = {
                    num_frames: f(i, o),
                    num_plays: f(i, o + 4)
                }, u = new Uint8Array(i.length);
                else if ("fcTL" == r) {
                    if (0 != d) (E = l.frames[l.frames.length - 1]).data = _decompress(l, u.slice(0, d), E.rect.width, E.rect.height), d = 0;
                    const e = {
                        x: f(i, o + 12),
                        y: f(i, o + 16),
                        width: f(i, o + 4),
                        height: f(i, o + 8)
                    };
                    let t1 = s(i, o + 22);
                    t1 = s(i, o + 20) / (0 == t1 ? 100 : t1);
                    const r = {
                        rect: e,
                        delay: Math.round(1e3 * t1),
                        dispose: i[o + 24],
                        blend: i[o + 25]
                    };
                    l.frames.push(r);
                } else if ("fdAT" == r) {
                    for(g = 0; g < e - 4; g++)u[d + g] = i[o + g + 4];
                    d += e - 4;
                } else if ("pHYs" == r) l.tabs[r] = [
                    a.readUint(i, o),
                    a.readUint(i, o + 4),
                    i[o + 8]
                ];
                else if ("cHRM" == r) {
                    l.tabs[r] = [];
                    for(g = 0; g < 8; g++)l.tabs[r].push(a.readUint(i, o + 4 * g));
                } else if ("tEXt" == r || "zTXt" == r) {
                    null == l.tabs[r] && (l.tabs[r] = {});
                    var m = a.nextZero(i, o), w = a.readASCII(i, o, m - o), v = o + e - m - 1;
                    if ("tEXt" == r) y = a.readASCII(i, m + 1, v);
                    else {
                        var b = _inflate(i.slice(m + 2, m + 2 + v));
                        y = a.readUTF8(b, 0, b.length);
                    }
                    l.tabs[r][w] = y;
                } else if ("iTXt" == r) {
                    null == l.tabs[r] && (l.tabs[r] = {});
                    m = 0, p = o;
                    m = a.nextZero(i, p);
                    w = a.readASCII(i, p, m - p);
                    const t1 = i[p = m + 1];
                    var y;
                    i[p + 1], p += 2, m = a.nextZero(i, p), a.readASCII(i, p, m - p), p = m + 1, m = a.nextZero(i, p), a.readUTF8(i, p, m - p);
                    v = e - ((p = m + 1) - o);
                    if (0 == t1) y = a.readUTF8(i, p, v);
                    else {
                        b = _inflate(i.slice(p, p + v));
                        y = a.readUTF8(b, 0, b.length);
                    }
                    l.tabs[r][w] = y;
                } else if ("PLTE" == r) l.tabs[r] = a.readBytes(i, o, e);
                else if ("hIST" == r) {
                    const e = l.tabs.PLTE.length / 3;
                    l.tabs[r] = [];
                    for(g = 0; g < e; g++)l.tabs[r].push(s(i, o + 2 * g));
                } else if ("tRNS" == r) 3 == l.ctype ? l.tabs[r] = a.readBytes(i, o, e) : 0 == l.ctype ? l.tabs[r] = s(i, o) : 2 == l.ctype && (l.tabs[r] = [
                    s(i, o),
                    s(i, o + 2),
                    s(i, o + 4)
                ]);
                else if ("gAMA" == r) l.tabs[r] = a.readUint(i, o) / 1e5;
                else if ("sRGB" == r) l.tabs[r] = i[o];
                else if ("bKGD" == r) 0 == l.ctype || 4 == l.ctype ? l.tabs[r] = [
                    s(i, o)
                ] : 2 == l.ctype || 6 == l.ctype ? l.tabs[r] = [
                    s(i, o),
                    s(i, o + 2),
                    s(i, o + 4)
                ] : 3 == l.ctype && (l.tabs[r] = i[o]);
                else if ("IEND" == r) break;
                o += e, a.readUint(i, o), o += 4;
            }
            var E;
            return 0 != d && ((E = l.frames[l.frames.length - 1]).data = _decompress(l, u.slice(0, d), E.rect.width, E.rect.height)), l.data = _decompress(l, c, l.width, l.height), delete l.compress, delete l.interlace, delete l.filter, l;
        },
        toRGBA8: function toRGBA8(e) {
            const t1 = e.width, r = e.height;
            if (null == e.tabs.acTL) return [
                decodeImage(e.data, t1, r, e).buffer
            ];
            const i = [];
            null == e.frames[0].data && (e.frames[0].data = e.data);
            const o = t1 * r * 4, a = new Uint8Array(o), s = new Uint8Array(o), f = new Uint8Array(o);
            for(let c = 0; c < e.frames.length; c++){
                const u = e.frames[c], h = u.rect.x, d = u.rect.y, A = u.rect.width, g = u.rect.height, p = decodeImage(u.data, A, g, e);
                if (0 != c) for(var l = 0; l < o; l++)f[l] = a[l];
                if (0 == u.blend ? _copyTile(p, A, g, a, t1, r, h, d, 0) : 1 == u.blend && _copyTile(p, A, g, a, t1, r, h, d, 1), i.push(a.buffer.slice(0)), 0 == u.dispose) ;
                else if (1 == u.dispose) _copyTile(s, A, g, a, t1, r, h, d, 0);
                else if (2 == u.dispose) for(l = 0; l < o; l++)a[l] = f[l];
            }
            return i;
        },
        _paeth: _paeth,
        _copyTile: _copyTile,
        _bin: e
    };
}();
!function() {
    const { _copyTile: e } = UPNG, { _bin: t1 } = UPNG, r = UPNG._paeth;
    var i = {
        table: function() {
            const e = new Uint32Array(256);
            for(let t1 = 0; t1 < 256; t1++){
                let r = t1;
                for(let e = 0; e < 8; e++)1 & r ? r = 3988292384 ^ r >>> 1 : r >>>= 1;
                e[t1] = r;
            }
            return e;
        }(),
        update (e, t1, r, o) {
            for(let a = 0; a < o; a++)e = i.table[255 & (e ^ t1[r + a])] ^ e >>> 8;
            return e;
        },
        crc: (e, t1, r)=>4294967295 ^ i.update(4294967295, e, t1, r)
    };
    function addErr(e, t1, r, i) {
        t1[r] += e[0] * i >> 4, t1[r + 1] += e[1] * i >> 4, t1[r + 2] += e[2] * i >> 4, t1[r + 3] += e[3] * i >> 4;
    }
    function N(e) {
        return Math.max(0, Math.min(255, e));
    }
    function D(e, t1) {
        const r = e[0] - t1[0], i = e[1] - t1[1], o = e[2] - t1[2], a = e[3] - t1[3];
        return r * r + i * i + o * o + a * a;
    }
    function dither(e, t1, r, i, o, a, s) {
        null == s && (s = 1);
        const f = i.length, l = [];
        for(var c = 0; c < f; c++){
            const e = i[c];
            l.push([
                e >>> 0 & 255,
                e >>> 8 & 255,
                e >>> 16 & 255,
                e >>> 24 & 255
            ]);
        }
        for(c = 0; c < f; c++){
            let e = 4294967295;
            for(var u = 0, h = 0; h < f; h++){
                var d = D(l[c], l[h]);
                h != c && d < e && (e = d, u = h);
            }
        }
        const A = new Uint32Array(o.buffer), g = new Int16Array(t1 * r * 4), p = [
            0,
            8,
            2,
            10,
            12,
            4,
            14,
            6,
            3,
            11,
            1,
            9,
            15,
            7,
            13,
            5
        ];
        for(c = 0; c < p.length; c++)p[c] = 255 * ((p[c] + .5) / 16 - .5);
        for(let o = 0; o < r; o++)for(let w = 0; w < t1; w++){
            var m;
            c = 4 * (o * t1 + w);
            if (2 != s) m = [
                N(e[c] + g[c]),
                N(e[c + 1] + g[c + 1]),
                N(e[c + 2] + g[c + 2]),
                N(e[c + 3] + g[c + 3])
            ];
            else {
                d = p[4 * (3 & o) + (3 & w)];
                m = [
                    N(e[c] + d),
                    N(e[c + 1] + d),
                    N(e[c + 2] + d),
                    N(e[c + 3] + d)
                ];
            }
            u = 0;
            let v = 16777215;
            for(h = 0; h < f; h++){
                const e = D(m, l[h]);
                e < v && (v = e, u = h);
            }
            const b = l[u], y = [
                m[0] - b[0],
                m[1] - b[1],
                m[2] - b[2],
                m[3] - b[3]
            ];
            1 == s && (w != t1 - 1 && addErr(y, g, c + 4, 7), o != r - 1 && (0 != w && addErr(y, g, c + 4 * t1 - 4, 3), addErr(y, g, c + 4 * t1, 5), w != t1 - 1 && addErr(y, g, c + 4 * t1 + 4, 1))), a[c >> 2] = u, A[c >> 2] = i[u];
        }
    }
    function _main(e, r, o, a, s) {
        null == s && (s = {});
        const { crc: f } = i, l = t1.writeUint, c = t1.writeUshort, u = t1.writeASCII;
        let h = 8;
        const d = e.frames.length > 1;
        let A, g = !1, p = 33 + (d ? 20 : 0);
        if (null != s.sRGB && (p += 13), null != s.pHYs && (p += 21), null != s.iCCP && (A = pako.deflate(s.iCCP), p += 21 + A.length + 4), 3 == e.ctype) {
            for(var m = e.plte.length, w = 0; w < m; w++)e.plte[w] >>> 24 != 255 && (g = !0);
            p += 8 + 3 * m + 4 + (g ? 8 + 1 * m + 4 : 0);
        }
        for(var v = 0; v < e.frames.length; v++){
            d && (p += 38), p += (F = e.frames[v]).cimg.length + 12, 0 != v && (p += 4);
        }
        p += 12;
        const b = new Uint8Array(p), y = [
            137,
            80,
            78,
            71,
            13,
            10,
            26,
            10
        ];
        for(w = 0; w < 8; w++)b[w] = y[w];
        if (l(b, h, 13), h += 4, u(b, h, "IHDR"), h += 4, l(b, h, r), h += 4, l(b, h, o), h += 4, b[h] = e.depth, h++, b[h] = e.ctype, h++, b[h] = 0, h++, b[h] = 0, h++, b[h] = 0, h++, l(b, h, f(b, h - 17, 17)), h += 4, null != s.sRGB && (l(b, h, 1), h += 4, u(b, h, "sRGB"), h += 4, b[h] = s.sRGB, h++, l(b, h, f(b, h - 5, 5)), h += 4), null != s.iCCP) {
            const e = 13 + A.length;
            l(b, h, e), h += 4, u(b, h, "iCCP"), h += 4, u(b, h, "ICC profile"), h += 11, h += 2, b.set(A, h), h += A.length, l(b, h, f(b, h - (e + 4), e + 4)), h += 4;
        }
        if (null != s.pHYs && (l(b, h, 9), h += 4, u(b, h, "pHYs"), h += 4, l(b, h, s.pHYs[0]), h += 4, l(b, h, s.pHYs[1]), h += 4, b[h] = s.pHYs[2], h++, l(b, h, f(b, h - 13, 13)), h += 4), d && (l(b, h, 8), h += 4, u(b, h, "acTL"), h += 4, l(b, h, e.frames.length), h += 4, l(b, h, null != s.loop ? s.loop : 0), h += 4, l(b, h, f(b, h - 12, 12)), h += 4), 3 == e.ctype) {
            l(b, h, 3 * (m = e.plte.length)), h += 4, u(b, h, "PLTE"), h += 4;
            for(w = 0; w < m; w++){
                const t1 = 3 * w, r = e.plte[w], i = 255 & r, o = r >>> 8 & 255, a = r >>> 16 & 255;
                b[h + t1 + 0] = i, b[h + t1 + 1] = o, b[h + t1 + 2] = a;
            }
            if (h += 3 * m, l(b, h, f(b, h - 3 * m - 4, 3 * m + 4)), h += 4, g) {
                l(b, h, m), h += 4, u(b, h, "tRNS"), h += 4;
                for(w = 0; w < m; w++)b[h + w] = e.plte[w] >>> 24 & 255;
                h += m, l(b, h, f(b, h - m - 4, m + 4)), h += 4;
            }
        }
        let E = 0;
        for(v = 0; v < e.frames.length; v++){
            var F = e.frames[v];
            d && (l(b, h, 26), h += 4, u(b, h, "fcTL"), h += 4, l(b, h, E++), h += 4, l(b, h, F.rect.width), h += 4, l(b, h, F.rect.height), h += 4, l(b, h, F.rect.x), h += 4, l(b, h, F.rect.y), h += 4, c(b, h, a[v]), h += 2, c(b, h, 1e3), h += 2, b[h] = F.dispose, h++, b[h] = F.blend, h++, l(b, h, f(b, h - 30, 30)), h += 4);
            const t1 = F.cimg;
            l(b, h, (m = t1.length) + (0 == v ? 0 : 4)), h += 4;
            const r = h;
            u(b, h, 0 == v ? "IDAT" : "fdAT"), h += 4, 0 != v && (l(b, h, E++), h += 4), b.set(t1, h), h += m, l(b, h, f(b, r, h - r)), h += 4;
        }
        return l(b, h, 0), h += 4, u(b, h, "IEND"), h += 4, l(b, h, f(b, h - 4, 4)), h += 4, b.buffer;
    }
    function compressPNG(e, t1, r) {
        for(let i = 0; i < e.frames.length; i++){
            const o = e.frames[i];
            o.rect.width;
            const a = o.rect.height, s = new Uint8Array(a * o.bpl + a);
            o.cimg = _filterZero(o.img, a, o.bpp, o.bpl, s, t1, r);
        }
    }
    function compress(t1, r, i, o, a) {
        const s = a[0], f = a[1], l = a[2], c = a[3], u = a[4], h = a[5];
        let d = 6, A = 8, g = 255;
        for(var p = 0; p < t1.length; p++){
            const e = new Uint8Array(t1[p]);
            for(var m = e.length, w = 0; w < m; w += 4)g &= e[w + 3];
        }
        const v = 255 != g, b = function framize(t1, r, i, o, a, s) {
            const f = [];
            for(var l = 0; l < t1.length; l++){
                const h = new Uint8Array(t1[l]), A = new Uint32Array(h.buffer);
                var c;
                let g = 0, p = 0, m = r, w = i, v = o ? 1 : 0;
                if (0 != l) {
                    const b = s || o || 1 == l || 0 != f[l - 2].dispose ? 1 : 2;
                    let y = 0, E = 1e9;
                    for(let e = 0; e < b; e++){
                        var u = new Uint8Array(t1[l - 1 - e]);
                        const o = new Uint32Array(t1[l - 1 - e]);
                        let s = r, f = i, c = -1, h = -1;
                        for(let e = 0; e < i; e++)for(let t1 = 0; t1 < r; t1++){
                            A[d = e * r + t1] != o[d] && (t1 < s && (s = t1), t1 > c && (c = t1), e < f && (f = e), e > h && (h = e));
                        }
                        -1 == c && (s = f = c = h = 0), a && (1 == (1 & s) && s--, 1 == (1 & f) && f--);
                        const v = (c - s + 1) * (h - f + 1);
                        v < E && (E = v, y = e, g = s, p = f, m = c - s + 1, w = h - f + 1);
                    }
                    u = new Uint8Array(t1[l - 1 - y]);
                    1 == y && (f[l - 1].dispose = 2), c = new Uint8Array(m * w * 4), e(u, r, i, c, m, w, -g, -p, 0), v = e(h, r, i, c, m, w, -g, -p, 3) ? 1 : 0, 1 == v ? _prepareDiff(h, r, i, c, {
                        x: g,
                        y: p,
                        width: m,
                        height: w
                    }) : e(h, r, i, c, m, w, -g, -p, 0);
                } else c = h.slice(0);
                f.push({
                    rect: {
                        x: g,
                        y: p,
                        width: m,
                        height: w
                    },
                    img: c,
                    blend: v,
                    dispose: 0
                });
            }
            if (o) for(l = 0; l < f.length; l++){
                if (1 == (A = f[l]).blend) continue;
                const e = A.rect, o = f[l - 1].rect, s = Math.min(e.x, o.x), c = Math.min(e.y, o.y), u = {
                    x: s,
                    y: c,
                    width: Math.max(e.x + e.width, o.x + o.width) - s,
                    height: Math.max(e.y + e.height, o.y + o.height) - c
                };
                f[l - 1].dispose = 1, l - 1 != 0 && _updateFrame(t1, r, i, f, l - 1, u, a), _updateFrame(t1, r, i, f, l, u, a);
            }
            let h = 0;
            if (1 != t1.length) for(var d = 0; d < f.length; d++){
                var A;
                h += (A = f[d]).rect.width * A.rect.height;
            }
            return f;
        }(t1, r, i, s, f, l), y = {}, E = [], F = [];
        if (0 != o) {
            const e = [];
            for(w = 0; w < b.length; w++)e.push(b[w].img.buffer);
            const t1 = function concatRGBA(e) {
                let t1 = 0;
                for(var r = 0; r < e.length; r++)t1 += e[r].byteLength;
                const i = new Uint8Array(t1);
                let o = 0;
                for(r = 0; r < e.length; r++){
                    const t1 = new Uint8Array(e[r]), a = t1.length;
                    for(let e = 0; e < a; e += 4){
                        let r = t1[e], a = t1[e + 1], s = t1[e + 2];
                        const f = t1[e + 3];
                        0 == f && (r = a = s = 0), i[o + e] = r, i[o + e + 1] = a, i[o + e + 2] = s, i[o + e + 3] = f;
                    }
                    o += a;
                }
                return i.buffer;
            }(e), r = quantize(t1, o);
            for(w = 0; w < r.plte.length; w++)E.push(r.plte[w].est.rgba);
            let i = 0;
            for(w = 0; w < b.length; w++){
                const e = (B = b[w]).img.length;
                var _ = new Uint8Array(r.inds.buffer, i >> 2, e >> 2);
                F.push(_);
                const t1 = new Uint8Array(r.abuf, i, e);
                h && dither(B.img, B.rect.width, B.rect.height, E, t1, _), B.img.set(t1), i += e;
            }
        } else for(p = 0; p < b.length; p++){
            var B = b[p];
            const e = new Uint32Array(B.img.buffer);
            var U = B.rect.width;
            m = e.length, _ = new Uint8Array(m);
            F.push(_);
            for(w = 0; w < m; w++){
                const t1 = e[w];
                if (0 != w && t1 == e[w - 1]) _[w] = _[w - 1];
                else if (w > U && t1 == e[w - U]) _[w] = _[w - U];
                else {
                    let e = y[t1];
                    if (null == e && (y[t1] = e = E.length, E.push(t1), E.length >= 300)) break;
                    _[w] = e;
                }
            }
        }
        const C = E.length;
        C <= 256 && 0 == u && (A = C <= 2 ? 1 : C <= 4 ? 2 : C <= 16 ? 4 : 8, A = Math.max(A, c));
        for(p = 0; p < b.length; p++){
            (B = b[p]).rect.x, B.rect.y;
            U = B.rect.width;
            const e = B.rect.height;
            let t1 = B.img;
            new Uint32Array(t1.buffer);
            let r = 4 * U, i = 4;
            if (C <= 256 && 0 == u) {
                r = Math.ceil(A * U / 8);
                var I = new Uint8Array(r * e);
                const o = F[p];
                for(let t1 = 0; t1 < e; t1++){
                    w = t1 * r;
                    const e = t1 * U;
                    if (8 == A) for(var Q = 0; Q < U; Q++)I[w + Q] = o[e + Q];
                    else if (4 == A) for(Q = 0; Q < U; Q++)I[w + (Q >> 1)] |= o[e + Q] << 4 - 4 * (1 & Q);
                    else if (2 == A) for(Q = 0; Q < U; Q++)I[w + (Q >> 2)] |= o[e + Q] << 6 - 2 * (3 & Q);
                    else if (1 == A) for(Q = 0; Q < U; Q++)I[w + (Q >> 3)] |= o[e + Q] << 7 - 1 * (7 & Q);
                }
                t1 = I, d = 3, i = 1;
            } else if (0 == v && 1 == b.length) {
                I = new Uint8Array(U * e * 3);
                const o = U * e;
                for(w = 0; w < o; w++){
                    const e = 3 * w, r = 4 * w;
                    I[e] = t1[r], I[e + 1] = t1[r + 1], I[e + 2] = t1[r + 2];
                }
                t1 = I, d = 2, i = 3, r = 3 * U;
            }
            B.img = t1, B.bpl = r, B.bpp = i;
        }
        return {
            ctype: d,
            depth: A,
            plte: E,
            frames: b
        };
    }
    function _updateFrame(t1, r, i, o, a, s, f) {
        const l = Uint8Array, c = Uint32Array, u = new l(t1[a - 1]), h = new c(t1[a - 1]), d = a + 1 < t1.length ? new l(t1[a + 1]) : null, A = new l(t1[a]), g = new c(A.buffer);
        let p = r, m = i, w = -1, v = -1;
        for(let e = 0; e < s.height; e++)for(let t1 = 0; t1 < s.width; t1++){
            const i = s.x + t1, f = s.y + e, l = f * r + i, c = g[l];
            0 == c || 0 == o[a - 1].dispose && h[l] == c && (null == d || 0 != d[4 * l + 3]) || (i < p && (p = i), i > w && (w = i), f < m && (m = f), f > v && (v = f));
        }
        -1 == w && (p = m = w = v = 0), f && (1 == (1 & p) && p--, 1 == (1 & m) && m--), s = {
            x: p,
            y: m,
            width: w - p + 1,
            height: v - m + 1
        };
        const b = o[a];
        b.rect = s, b.blend = 1, b.img = new Uint8Array(s.width * s.height * 4), 0 == o[a - 1].dispose ? (e(u, r, i, b.img, s.width, s.height, -s.x, -s.y, 0), _prepareDiff(A, r, i, b.img, s)) : e(A, r, i, b.img, s.width, s.height, -s.x, -s.y, 0);
    }
    function _prepareDiff(t1, r, i, o, a) {
        e(t1, r, i, o, a.width, a.height, -a.x, -a.y, 2);
    }
    function _filterZero(e, t1, r, i, o, a, s) {
        const f = [];
        let l, c = [
            0,
            1,
            2,
            3,
            4
        ];
        -1 != a ? c = [
            a
        ] : (t1 * i > 5e5 || 1 == r) && (c = [
            0
        ]), s && (l = {
            level: 0
        });
        const u = UZIP;
        for(var h = 0; h < c.length; h++){
            for(let a = 0; a < t1; a++)_filterLine(o, e, a, i, r, c[h]);
            f.push(u.deflate(o, l));
        }
        let d, A = 1e9;
        for(h = 0; h < f.length; h++)f[h].length < A && (d = h, A = f[h].length);
        return f[d];
    }
    function _filterLine(e, t1, i, o, a, s) {
        const f = i * o;
        let l = f + i;
        if (e[l] = s, l++, 0 == s) if (o < 500) for(var c = 0; c < o; c++)e[l + c] = t1[f + c];
        else e.set(new Uint8Array(t1.buffer, f, o), l);
        else if (1 == s) {
            for(c = 0; c < a; c++)e[l + c] = t1[f + c];
            for(c = a; c < o; c++)e[l + c] = t1[f + c] - t1[f + c - a] + 256 & 255;
        } else if (0 == i) {
            for(c = 0; c < a; c++)e[l + c] = t1[f + c];
            if (2 == s) for(c = a; c < o; c++)e[l + c] = t1[f + c];
            if (3 == s) for(c = a; c < o; c++)e[l + c] = t1[f + c] - (t1[f + c - a] >> 1) + 256 & 255;
            if (4 == s) for(c = a; c < o; c++)e[l + c] = t1[f + c] - r(t1[f + c - a], 0, 0) + 256 & 255;
        } else {
            if (2 == s) for(c = 0; c < o; c++)e[l + c] = t1[f + c] + 256 - t1[f + c - o] & 255;
            if (3 == s) {
                for(c = 0; c < a; c++)e[l + c] = t1[f + c] + 256 - (t1[f + c - o] >> 1) & 255;
                for(c = a; c < o; c++)e[l + c] = t1[f + c] + 256 - (t1[f + c - o] + t1[f + c - a] >> 1) & 255;
            }
            if (4 == s) {
                for(c = 0; c < a; c++)e[l + c] = t1[f + c] + 256 - r(0, t1[f + c - o], 0) & 255;
                for(c = a; c < o; c++)e[l + c] = t1[f + c] + 256 - r(t1[f + c - a], t1[f + c - o], t1[f + c - a - o]) & 255;
            }
        }
    }
    function quantize(e, t1) {
        const r = new Uint8Array(e), i = r.slice(0), o = new Uint32Array(i.buffer), a = getKDtree(i, t1), s = a[0], f = a[1], l = r.length, c = new Uint8Array(l >> 2);
        let u;
        if (r.length < 2e7) for(var h = 0; h < l; h += 4){
            u = getNearest(s, d = r[h] * (1 / 255), A = r[h + 1] * (1 / 255), g = r[h + 2] * (1 / 255), p = r[h + 3] * (1 / 255)), c[h >> 2] = u.ind, o[h >> 2] = u.est.rgba;
        }
        else for(h = 0; h < l; h += 4){
            var d = r[h] * (1 / 255), A = r[h + 1] * (1 / 255), g = r[h + 2] * (1 / 255), p = r[h + 3] * (1 / 255);
            for(u = s; u.left;)u = planeDst(u.est, d, A, g, p) <= 0 ? u.left : u.right;
            c[h >> 2] = u.ind, o[h >> 2] = u.est.rgba;
        }
        return {
            abuf: i.buffer,
            inds: c,
            plte: f
        };
    }
    function getKDtree(e, t1, r) {
        null == r && (r = 1e-4);
        const i = new Uint32Array(e.buffer), o = {
            i0: 0,
            i1: e.length,
            bst: null,
            est: null,
            tdst: 0,
            left: null,
            right: null
        };
        o.bst = stats(e, o.i0, o.i1), o.est = estats(o.bst);
        const a = [
            o
        ];
        for(; a.length < t1;){
            let t1 = 0, o = 0;
            for(var s = 0; s < a.length; s++)a[s].est.L > t1 && (t1 = a[s].est.L, o = s);
            if (t1 < r) break;
            const f = a[o], l = splitPixels(e, i, f.i0, f.i1, f.est.e, f.est.eMq255);
            if (f.i0 >= l || f.i1 <= l) {
                f.est.L = 0;
                continue;
            }
            const c = {
                i0: f.i0,
                i1: l,
                bst: null,
                est: null,
                tdst: 0,
                left: null,
                right: null
            };
            c.bst = stats(e, c.i0, c.i1), c.est = estats(c.bst);
            const u = {
                i0: l,
                i1: f.i1,
                bst: null,
                est: null,
                tdst: 0,
                left: null,
                right: null
            };
            u.bst = {
                R: [],
                m: [],
                N: f.bst.N - c.bst.N
            };
            for(s = 0; s < 16; s++)u.bst.R[s] = f.bst.R[s] - c.bst.R[s];
            for(s = 0; s < 4; s++)u.bst.m[s] = f.bst.m[s] - c.bst.m[s];
            u.est = estats(u.bst), f.left = c, f.right = u, a[o] = c, a.push(u);
        }
        a.sort((e, t1)=>t1.bst.N - e.bst.N);
        for(s = 0; s < a.length; s++)a[s].ind = s;
        return [
            o,
            a
        ];
    }
    function getNearest(e, t1, r, i, o) {
        if (null == e.left) return e.tdst = function dist(e, t1, r, i, o) {
            const a = t1 - e[0], s = r - e[1], f = i - e[2], l = o - e[3];
            return a * a + s * s + f * f + l * l;
        }(e.est.q, t1, r, i, o), e;
        const a = planeDst(e.est, t1, r, i, o);
        let s = e.left, f = e.right;
        a > 0 && (s = e.right, f = e.left);
        const l = getNearest(s, t1, r, i, o);
        if (l.tdst <= a * a) return l;
        const c = getNearest(f, t1, r, i, o);
        return c.tdst < l.tdst ? c : l;
    }
    function planeDst(e, t1, r, i, o) {
        const { e: a } = e;
        return a[0] * t1 + a[1] * r + a[2] * i + a[3] * o - e.eMq;
    }
    function splitPixels(e, t1, r, i, o, a) {
        for(i -= 4; r < i;){
            for(; vecDot(e, r, o) <= a;)r += 4;
            for(; vecDot(e, i, o) > a;)i -= 4;
            if (r >= i) break;
            const s = t1[r >> 2];
            t1[r >> 2] = t1[i >> 2], t1[i >> 2] = s, r += 4, i -= 4;
        }
        for(; vecDot(e, r, o) > a;)r -= 4;
        return r + 4;
    }
    function vecDot(e, t1, r) {
        return e[t1] * r[0] + e[t1 + 1] * r[1] + e[t1 + 2] * r[2] + e[t1 + 3] * r[3];
    }
    function stats(e, t1, r) {
        const i = [
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0
        ], o = [
            0,
            0,
            0,
            0
        ], a = r - t1 >> 2;
        for(let a = t1; a < r; a += 4){
            const t1 = e[a] * (1 / 255), r = e[a + 1] * (1 / 255), s = e[a + 2] * (1 / 255), f = e[a + 3] * (1 / 255);
            o[0] += t1, o[1] += r, o[2] += s, o[3] += f, i[0] += t1 * t1, i[1] += t1 * r, i[2] += t1 * s, i[3] += t1 * f, i[5] += r * r, i[6] += r * s, i[7] += r * f, i[10] += s * s, i[11] += s * f, i[15] += f * f;
        }
        return i[4] = i[1], i[8] = i[2], i[9] = i[6], i[12] = i[3], i[13] = i[7], i[14] = i[11], {
            R: i,
            m: o,
            N: a
        };
    }
    function estats(e) {
        const { R: t1 } = e, { m: r } = e, { N: i } = e, a = r[0], s = r[1], f = r[2], l = r[3], c = 0 == i ? 0 : 1 / i, u = [
            t1[0] - a * a * c,
            t1[1] - a * s * c,
            t1[2] - a * f * c,
            t1[3] - a * l * c,
            t1[4] - s * a * c,
            t1[5] - s * s * c,
            t1[6] - s * f * c,
            t1[7] - s * l * c,
            t1[8] - f * a * c,
            t1[9] - f * s * c,
            t1[10] - f * f * c,
            t1[11] - f * l * c,
            t1[12] - l * a * c,
            t1[13] - l * s * c,
            t1[14] - l * f * c,
            t1[15] - l * l * c
        ], h = u, d = o;
        let A = [
            Math.random(),
            Math.random(),
            Math.random(),
            Math.random()
        ], g = 0, p = 0;
        if (0 != i) for(let e = 0; e < 16 && (A = d.multVec(h, A), p = Math.sqrt(d.dot(A, A)), A = d.sml(1 / p, A), !(0 != e && Math.abs(p - g) < 1e-9)); e++)g = p;
        const m = [
            a * c,
            s * c,
            f * c,
            l * c
        ];
        return {
            Cov: u,
            q: m,
            e: A,
            L: g,
            eMq255: d.dot(d.sml(255, m), A),
            eMq: d.dot(A, m),
            rgba: (Math.round(255 * m[3]) << 24 | Math.round(255 * m[2]) << 16 | Math.round(255 * m[1]) << 8 | Math.round(255 * m[0]) << 0) >>> 0
        };
    }
    var o = {
        multVec: (e, t1)=>[
                e[0] * t1[0] + e[1] * t1[1] + e[2] * t1[2] + e[3] * t1[3],
                e[4] * t1[0] + e[5] * t1[1] + e[6] * t1[2] + e[7] * t1[3],
                e[8] * t1[0] + e[9] * t1[1] + e[10] * t1[2] + e[11] * t1[3],
                e[12] * t1[0] + e[13] * t1[1] + e[14] * t1[2] + e[15] * t1[3]
            ],
        dot: (e, t1)=>e[0] * t1[0] + e[1] * t1[1] + e[2] * t1[2] + e[3] * t1[3],
        sml: (e, t1)=>[
                e * t1[0],
                e * t1[1],
                e * t1[2],
                e * t1[3]
            ]
    };
    UPNG.encode = function encode(e, t1, r, i, o, a, s) {
        null == i && (i = 0), null == s && (s = !1);
        const f = compress(e, t1, r, i, [
            !1,
            !1,
            !1,
            0,
            s,
            !1
        ]);
        return compressPNG(f, -1), _main(f, t1, r, o, a);
    }, UPNG.encodeLL = function encodeLL(e, t1, r, i, o, a, s, f) {
        const l = {
            ctype: 0 + (1 == i ? 0 : 2) + (0 == o ? 0 : 4),
            depth: a,
            frames: []
        }, c = (i + o) * a, u = c * t1;
        for(let i = 0; i < e.length; i++)l.frames.push({
            rect: {
                x: 0,
                y: 0,
                width: t1,
                height: r
            },
            img: new Uint8Array(e[i]),
            blend: 0,
            dispose: 1,
            bpp: Math.ceil(c / 8),
            bpl: Math.ceil(u / 8)
        });
        return compressPNG(l, 0, !0), _main(l, t1, r, s, f);
    }, UPNG.encode.compress = compress, UPNG.encode.dither = dither, UPNG.quantize = quantize, UPNG.quantize.getKDtree = getKDtree, UPNG.quantize.getNearest = getNearest;
}();
const r = {
    toArrayBuffer (e, t1) {
        const i = e.width, o = e.height, a = i << 2, s = e.getContext("2d").getImageData(0, 0, i, o), f = new Uint32Array(s.data.buffer), l = (32 * i + 31) / 32 << 2, c = l * o, u = 122 + c, h = new ArrayBuffer(u), d = new DataView(h), A = 1 << 20;
        let g, p, m, w, v = A, b = 0, y = 0, E = 0;
        function set16(e) {
            d.setUint16(y, e, !0), y += 2;
        }
        function set32(e) {
            d.setUint32(y, e, !0), y += 4;
        }
        function seek(e) {
            y += e;
        }
        set16(19778), set32(u), seek(4), set32(122), set32(108), set32(i), set32(-o >>> 0), set16(1), set16(32), set32(3), set32(c), set32(2835), set32(2835), seek(8), set32(16711680), set32(65280), set32(255), set32(4278190080), set32(1466527264), function convert() {
            for(; b < o && v > 0;){
                for(w = 122 + b * l, g = 0; g < a;)v--, p = f[E++], m = p >>> 24, d.setUint32(w + g, p << 8 | m), g += 4;
                b++;
            }
            E < f.length ? (v = A, setTimeout(convert, r._dly)) : t1(h);
        }();
    },
    toBlob (e, t1) {
        this.toArrayBuffer(e, (e)=>{
            t1(new Blob([
                e
            ], {
                type: "image/bmp"
            }));
        });
    },
    _dly: 9
};
var i = {
    CHROME: "CHROME",
    FIREFOX: "FIREFOX",
    DESKTOP_SAFARI: "DESKTOP_SAFARI",
    IE: "IE",
    IOS: "IOS",
    ETC: "ETC"
}, o = {
    [i.CHROME]: 16384,
    [i.FIREFOX]: 11180,
    [i.DESKTOP_SAFARI]: 16384,
    [i.IE]: 8192,
    [i.IOS]: 4096,
    [i.ETC]: 8192
};
const a = "undefined" != typeof window, s = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope, f = a && window.cordova && window.cordova.require && window.cordova.require("cordova/modulemapper"), CustomFile = (a || s) && (f && f.getOriginalSymbol(window, "File") || "undefined" != typeof File && File), CustomFileReader = (a || s) && (f && f.getOriginalSymbol(window, "FileReader") || "undefined" != typeof FileReader && FileReader);
function getFilefromDataUrl(e, t1) {
    let r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : Date.now();
    return new Promise((i)=>{
        const o = e.split(","), a = o[0].match(/:(.*?);/)[1], s = globalThis.atob(o[1]);
        let f = s.length;
        const l = new Uint8Array(f);
        for(; f--;)l[f] = s.charCodeAt(f);
        const c = new Blob([
            l
        ], {
            type: a
        });
        c.name = t1, c.lastModified = r, i(c);
    });
}
function getDataUrlFromFile(e) {
    return new Promise((t1, r)=>{
        const i = new CustomFileReader;
        i.onload = ()=>t1(i.result), i.onerror = (e)=>r(e), i.readAsDataURL(e);
    });
}
function loadImage(e) {
    return new Promise((t1, r)=>{
        const i = new Image;
        i.onload = ()=>t1(i), i.onerror = (e)=>r(e), i.src = e;
    });
}
function getBrowserName() {
    if (void 0 !== getBrowserName.cachedResult) return getBrowserName.cachedResult;
    let e = i.ETC;
    const { userAgent: t1 } = navigator;
    return /Chrom(e|ium)/i.test(t1) ? e = i.CHROME : /iP(ad|od|hone)/i.test(t1) && /WebKit/i.test(t1) ? e = i.IOS : /Safari/i.test(t1) ? e = i.DESKTOP_SAFARI : /Firefox/i.test(t1) ? e = i.FIREFOX : (/MSIE/i.test(t1) || !0 == !!document.documentMode) && (e = i.IE), getBrowserName.cachedResult = e, getBrowserName.cachedResult;
}
function approximateBelowMaximumCanvasSizeOfBrowser(e, t1) {
    const r = getBrowserName(), i = o[r];
    let a = e, s = t1, f = a * s;
    const l = a > s ? s / a : a / s;
    for(; f > i * i;){
        const e = (i + a) / 2, t1 = (i + s) / 2;
        e < t1 ? (s = t1, a = t1 * l) : (s = e * l, a = e), f = a * s;
    }
    return {
        width: a,
        height: s
    };
}
function getNewCanvasAndCtx(e, t1) {
    let r, i;
    try {
        if (r = new OffscreenCanvas(e, t1), i = r.getContext("2d"), null === i) throw new Error("getContext of OffscreenCanvas returns null");
    } catch (e) {
        r = document.createElement("canvas"), i = r.getContext("2d");
    }
    return r.width = e, r.height = t1, [
        r,
        i
    ];
}
function drawImageInCanvas(e, t1) {
    const { width: r, height: i } = approximateBelowMaximumCanvasSizeOfBrowser(e.width, e.height), [o, a] = getNewCanvasAndCtx(r, i);
    return t1 && /jpe?g/.test(t1) && (a.fillStyle = "white", a.fillRect(0, 0, o.width, o.height)), a.drawImage(e, 0, 0, o.width, o.height), o;
}
function isIOS() {
    return void 0 !== isIOS.cachedResult || (isIOS.cachedResult = [
        "iPad Simulator",
        "iPhone Simulator",
        "iPod Simulator",
        "iPad",
        "iPhone",
        "iPod"
    ].includes(navigator.platform) || navigator.userAgent.includes("Mac") && "undefined" != typeof document && "ontouchend" in document), isIOS.cachedResult;
}
function drawFileInCanvas(e) {
    let t1 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return new Promise(function(r, o) {
        let a, s;
        var $Try_2_Post = function() {
            try {
                return s = drawImageInCanvas(a, t1.fileType || e.type), r([
                    a,
                    s
                ]);
            } catch (e) {
                return o(e);
            }
        }, $Try_2_Catch = function(t1) {
            try {
                0;
                var $Try_3_Catch = function(e) {
                    try {
                        throw e;
                    } catch (e) {
                        return o(e);
                    }
                };
                try {
                    let t1;
                    return getDataUrlFromFile(e).then(function(e) {
                        try {
                            return t1 = e, loadImage(t1).then(function(e) {
                                try {
                                    return a = e, function() {
                                        try {
                                            return $Try_2_Post();
                                        } catch (e) {
                                            return o(e);
                                        }
                                    }();
                                } catch (e) {
                                    return $Try_3_Catch(e);
                                }
                            }, $Try_3_Catch);
                        } catch (e) {
                            return $Try_3_Catch(e);
                        }
                    }, $Try_3_Catch);
                } catch (e) {
                    $Try_3_Catch(e);
                }
            } catch (e) {
                return o(e);
            }
        };
        try {
            if (isIOS() || [
                i.DESKTOP_SAFARI,
                i.MOBILE_SAFARI
            ].includes(getBrowserName())) throw new Error("Skip createImageBitmap on IOS and Safari");
            return createImageBitmap(e).then(function(e) {
                try {
                    return a = e, $Try_2_Post();
                } catch (e) {
                    return $Try_2_Catch();
                }
            }, $Try_2_Catch);
        } catch (e) {
            $Try_2_Catch();
        }
    });
}
function canvasToFile(e, t1, i, o) {
    let a = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 1;
    return new Promise(function(s, f) {
        let l;
        if ("image/png" === t1) {
            let c, u, h;
            return c = e.getContext("2d"), { data: u } = c.getImageData(0, 0, e.width, e.height), h = UPNG.encode([
                u.buffer
            ], e.width, e.height, 4096 * a), l = new Blob([
                h
            ], {
                type: t1
            }), l.name = i, l.lastModified = o, $If_4.call(this);
        }
        {
            if ("image/bmp" === t1) return new Promise((t1)=>r.toBlob(e, t1)).then((function(e) {
                try {
                    return l = e, l.name = i, l.lastModified = o, $If_5.call(this);
                } catch (e) {
                    return f(e);
                }
            }).bind(this), f);
            {
                if ("function" == typeof OffscreenCanvas && e instanceof OffscreenCanvas) return e.convertToBlob({
                    type: t1,
                    quality: a
                }).then((function(e) {
                    try {
                        return l = e, l.name = i, l.lastModified = o, $If_6.call(this);
                    } catch (e) {
                        return f(e);
                    }
                }).bind(this), f);
                {
                    let d;
                    return d = e.toDataURL(t1, a), getFilefromDataUrl(d, i, o).then((function(e) {
                        try {
                            return l = e, $If_6.call(this);
                        } catch (e) {
                            return f(e);
                        }
                    }).bind(this), f);
                }
                //TURBOPACK unreachable
                ;
                function $If_6() {
                    return $If_5.call(this);
                }
            }
            //TURBOPACK unreachable
            ;
            function $If_5() {
                return $If_4.call(this);
            }
        }
        //TURBOPACK unreachable
        ;
        function $If_4() {
            return s(l);
        }
    });
}
function cleanupCanvasMemory(e) {
    e.width = 0, e.height = 0;
}
function isAutoOrientationInBrowser() {
    return new Promise(function(e, t1) {
        let r, i, o, a, s;
        return void 0 !== isAutoOrientationInBrowser.cachedResult ? e(isAutoOrientationInBrowser.cachedResult) : (r = "data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==", getFilefromDataUrl("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==", "test.jpg", Date.now()).then(function(r) {
            try {
                return i = r, drawFileInCanvas(i).then(function(r) {
                    try {
                        return o = r[1], canvasToFile(o, i.type, i.name, i.lastModified).then(function(r) {
                            try {
                                return a = r, cleanupCanvasMemory(o), drawFileInCanvas(a).then(function(r) {
                                    try {
                                        return s = r[0], isAutoOrientationInBrowser.cachedResult = 1 === s.width && 2 === s.height, e(isAutoOrientationInBrowser.cachedResult);
                                    } catch (e) {
                                        return t1(e);
                                    }
                                }, t1);
                            } catch (e) {
                                return t1(e);
                            }
                        }, t1);
                    } catch (e) {
                        return t1(e);
                    }
                }, t1);
            } catch (e) {
                return t1(e);
            }
        }, t1));
    });
}
function getExifOrientation(e) {
    return new Promise((t1, r)=>{
        const i = new CustomFileReader;
        i.onload = (e)=>{
            const r = new DataView(e.target.result);
            if (65496 != r.getUint16(0, !1)) return t1(-2);
            const i = r.byteLength;
            let o = 2;
            for(; o < i;){
                if (r.getUint16(o + 2, !1) <= 8) return t1(-1);
                const e = r.getUint16(o, !1);
                if (o += 2, 65505 == e) {
                    if (1165519206 != r.getUint32(o += 2, !1)) return t1(-1);
                    const e = 18761 == r.getUint16(o += 6, !1);
                    o += r.getUint32(o + 4, e);
                    const i = r.getUint16(o, e);
                    o += 2;
                    for(let a = 0; a < i; a++)if (274 == r.getUint16(o + 12 * a, e)) return t1(r.getUint16(o + 12 * a + 8, e));
                } else {
                    if (65280 != (65280 & e)) break;
                    o += r.getUint16(o, !1);
                }
            }
            return t1(-1);
        }, i.onerror = (e)=>r(e), i.readAsArrayBuffer(e);
    });
}
function handleMaxWidthOrHeight(e, t1) {
    const { width: r } = e, { height: i } = e, { maxWidthOrHeight: o } = t1;
    let a, s = e;
    return isFinite(o) && (r > o || i > o) && ([s, a] = getNewCanvasAndCtx(r, i), r > i ? (s.width = o, s.height = i / r * o) : (s.width = r / i * o, s.height = o), a.drawImage(e, 0, 0, s.width, s.height), cleanupCanvasMemory(e)), s;
}
function followExifOrientation(e, t1) {
    const { width: r } = e, { height: i } = e, [o, a] = getNewCanvasAndCtx(r, i);
    switch(t1 > 4 && t1 < 9 ? (o.width = i, o.height = r) : (o.width = r, o.height = i), t1){
        case 2:
            a.transform(-1, 0, 0, 1, r, 0);
            break;
        case 3:
            a.transform(-1, 0, 0, -1, r, i);
            break;
        case 4:
            a.transform(1, 0, 0, -1, 0, i);
            break;
        case 5:
            a.transform(0, 1, 1, 0, 0, 0);
            break;
        case 6:
            a.transform(0, 1, -1, 0, i, 0);
            break;
        case 7:
            a.transform(0, -1, -1, 0, i, r);
            break;
        case 8:
            a.transform(0, -1, 1, 0, 0, r);
    }
    return a.drawImage(e, 0, 0, r, i), cleanupCanvasMemory(e), o;
}
function compress(e, t1) {
    let r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
    return new Promise(function(i, o) {
        let a, s, f, l, c, u, h, d, A, g, p, m, w, v, b, y, E, F, _, B;
        function incProgress() {
            let e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;
            if (t1.signal && t1.signal.aborted) throw t1.signal.reason;
            a += e, t1.onProgress(Math.min(a, 100));
        }
        function setProgress(e) {
            if (t1.signal && t1.signal.aborted) throw t1.signal.reason;
            a = Math.min(Math.max(e, a), 100), t1.onProgress(a);
        }
        return a = r, s = t1.maxIteration || 10, f = 1024 * t1.maxSizeMB * 1024, incProgress(), drawFileInCanvas(e, t1).then((function(r) {
            try {
                return [, l] = r, incProgress(), c = handleMaxWidthOrHeight(l, t1), incProgress(), new Promise(function(r, i) {
                    var o;
                    if (!(o = t1.exifOrientation)) return getExifOrientation(e).then((function(e) {
                        try {
                            return o = e, $If_2.call(this);
                        } catch (e) {
                            return i(e);
                        }
                    }).bind(this), i);
                    function $If_2() {
                        return r(o);
                    }
                    return $If_2.call(this);
                }).then((function(r) {
                    try {
                        return u = r, incProgress(), isAutoOrientationInBrowser().then((function(r) {
                            try {
                                return h = r ? c : followExifOrientation(c, u), incProgress(), d = t1.initialQuality || 1, A = t1.fileType || e.type, canvasToFile(h, A, e.name, e.lastModified, d).then((function(r) {
                                    try {
                                        {
                                            if (g = r, incProgress(), p = g.size > f, m = g.size > e.size, !p && !m) return setProgress(100), i(g);
                                            var a;
                                            function $Loop_3() {
                                                if (s-- && (b > f || b > w)) {
                                                    let t1, r;
                                                    return t1 = B ? .95 * _.width : _.width, r = B ? .95 * _.height : _.height, [E, F] = getNewCanvasAndCtx(t1, r), F.drawImage(_, 0, 0, t1, r), d *= "image/png" === A ? .85 : .95, canvasToFile(E, A, e.name, e.lastModified, d).then(function(e) {
                                                        try {
                                                            return y = e, cleanupCanvasMemory(_), _ = E, b = y.size, setProgress(Math.min(99, Math.floor((v - b) / (v - f) * 100))), $Loop_3;
                                                        } catch (e) {
                                                            return o(e);
                                                        }
                                                    }, o);
                                                }
                                                return [
                                                    1
                                                ];
                                            }
                                            return w = e.size, v = g.size, b = v, _ = h, B = !t1.alwaysKeepResolution && p, (a = (function(e) {
                                                for(; e;){
                                                    if (e.then) return void e.then(a, o);
                                                    try {
                                                        if (e.pop) {
                                                            if (e.length) return e.pop() ? $Loop_3_exit.call(this) : e;
                                                            e = $Loop_3;
                                                        } else e = e.call(this);
                                                    } catch (e) {
                                                        return o(e);
                                                    }
                                                }
                                            }).bind(this))($Loop_3);
                                            //TURBOPACK unreachable
                                            ;
                                            function $Loop_3_exit() {
                                                return cleanupCanvasMemory(_), cleanupCanvasMemory(E), cleanupCanvasMemory(c), cleanupCanvasMemory(h), cleanupCanvasMemory(l), setProgress(100), i(y);
                                            }
                                        }
                                    } catch (u) {
                                        return o(u);
                                    }
                                }).bind(this), o);
                            } catch (e) {
                                return o(e);
                            }
                        }).bind(this), o);
                    } catch (e) {
                        return o(e);
                    }
                }).bind(this), o);
            } catch (e) {
                return o(e);
            }
        }).bind(this), o);
    });
}
const l = "\nlet scriptImported = false\nself.addEventListener('message', async (e) => {\n  const { file, id, imageCompressionLibUrl, options } = e.data\n  options.onProgress = (progress) => self.postMessage({ progress, id })\n  try {\n    if (!scriptImported) {\n      // console.log('[worker] importScripts', imageCompressionLibUrl)\n      self.importScripts(imageCompressionLibUrl)\n      scriptImported = true\n    }\n    // console.log('[worker] self', self)\n    const compressedFile = await imageCompression(file, options)\n    self.postMessage({ file: compressedFile, id })\n  } catch (e) {\n    // console.error('[worker] error', e)\n    self.postMessage({ error: e.message + '\\n' + e.stack, id })\n  }\n})\n";
let c;
function compressOnWebWorker(e, t1) {
    return new Promise((r, i)=>{
        c || (c = function createWorkerScriptURL(e) {
            const t1 = [];
            return "function" == typeof e ? t1.push("(".concat(e, ")()")) : t1.push(e), URL.createObjectURL(new Blob(t1));
        }(l));
        const o = new Worker(c);
        o.addEventListener("message", function handler(e) {
            if (t1.signal && t1.signal.aborted) o.terminate();
            else if (void 0 === e.data.progress) {
                if (e.data.error) return i(new Error(e.data.error)), void o.terminate();
                r(e.data.file), o.terminate();
            } else t1.onProgress(e.data.progress);
        }), o.addEventListener("error", i), t1.signal && t1.signal.addEventListener("abort", ()=>{
            i(t1.signal.reason), o.terminate();
        }), o.postMessage({
            file: e,
            imageCompressionLibUrl: t1.libURL,
            options: {
                ...t1,
                onProgress: void 0,
                signal: void 0
            }
        });
    });
}
function imageCompression(e, t1) {
    return new Promise(function(r, i) {
        let o, a, s, f, l, c;
        if (o = {
            ...t1
        }, s = 0, { onProgress: f } = o, o.maxSizeMB = o.maxSizeMB || Number.POSITIVE_INFINITY, l = "boolean" != typeof o.useWebWorker || o.useWebWorker, delete o.useWebWorker, o.onProgress = (e)=>{
            s = e, "function" == typeof f && f(s);
        }, !(e instanceof Blob || e instanceof CustomFile)) return i(new Error("The file given is not an instance of Blob or File"));
        if (!/^image/.test(e.type)) return i(new Error("The file given is not an image"));
        if (c = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope, !l || "function" != typeof Worker || c) return compress(e, o).then((function(e) {
            try {
                return a = e, $If_4.call(this);
            } catch (e) {
                return i(e);
            }
        }).bind(this), i);
        var u = (function() {
            try {
                return $If_4.call(this);
            } catch (e) {
                return i(e);
            }
        }).bind(this), $Try_1_Catch = function(t1) {
            try {
                return compress(e, o).then(function(e) {
                    try {
                        return a = e, u();
                    } catch (e) {
                        return i(e);
                    }
                }, i);
            } catch (e) {
                return i(e);
            }
        };
        try {
            return o.libURL = o.libURL || "https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js", compressOnWebWorker(e, o).then(function(e) {
                try {
                    return a = e, u();
                } catch (e) {
                    return $Try_1_Catch();
                }
            }, $Try_1_Catch);
        } catch (e) {
            $Try_1_Catch();
        }
        function $If_4() {
            try {
                a.name = e.name, a.lastModified = e.lastModified;
            } catch (e) {}
            try {
                o.preserveExif && "image/jpeg" === e.type && (!o.fileType || o.fileType && o.fileType === e.type) && (a = copyExifWithoutOrientation(e, a));
            } catch (e) {}
            return r(a);
        }
    });
}
imageCompression.getDataUrlFromFile = getDataUrlFromFile, imageCompression.getFilefromDataUrl = getFilefromDataUrl, imageCompression.loadImage = loadImage, imageCompression.drawImageInCanvas = drawImageInCanvas, imageCompression.drawFileInCanvas = drawFileInCanvas, imageCompression.canvasToFile = canvasToFile, imageCompression.getExifOrientation = getExifOrientation, imageCompression.handleMaxWidthOrHeight = handleMaxWidthOrHeight, imageCompression.followExifOrientation = followExifOrientation, imageCompression.cleanupCanvasMemory = cleanupCanvasMemory, imageCompression.isAutoOrientationInBrowser = isAutoOrientationInBrowser, imageCompression.approximateBelowMaximumCanvasSizeOfBrowser = approximateBelowMaximumCanvasSizeOfBrowser, imageCompression.copyExifWithoutOrientation = copyExifWithoutOrientation, imageCompression.getBrowserName = getBrowserName, imageCompression.version = "2.0.2";
;
 //# sourceMappingURL=browser-image-compression.mjs.map
}),
"[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "clsx": ()=>clsx,
    "default": ()=>__TURBOPACK__default__export__
});
function r(e) {
    var t, f, n = "";
    if ("string" == typeof e || "number" == typeof e) n += e;
    else if ("object" == typeof e) if (Array.isArray(e)) {
        var o = e.length;
        for(t = 0; t < o; t++)e[t] && (f = r(e[t])) && (n && (n += " "), n += f);
    } else for(f in e)e[f] && (n && (n += " "), n += f);
    return n;
}
function clsx() {
    for(var e, t, f = 0, n = "", o = arguments.length; f < o; f++)(e = arguments[f]) && (t = r(e)) && (n && (n += " "), n += t);
    return n;
}
const __TURBOPACK__default__export__ = clsx;
}),
"[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createTailwindMerge": ()=>createTailwindMerge,
    "extendTailwindMerge": ()=>extendTailwindMerge,
    "fromTheme": ()=>fromTheme,
    "getDefaultConfig": ()=>getDefaultConfig,
    "mergeConfigs": ()=>mergeConfigs,
    "twJoin": ()=>twJoin,
    "twMerge": ()=>twMerge,
    "validators": ()=>validators
});
const CLASS_PART_SEPARATOR = '-';
const createClassGroupUtils = (config)=>{
    const classMap = createClassMap(config);
    const { conflictingClassGroups, conflictingClassGroupModifiers } = config;
    const getClassGroupId = (className)=>{
        const classParts = className.split(CLASS_PART_SEPARATOR);
        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.
        if (classParts[0] === '' && classParts.length !== 1) {
            classParts.shift();
        }
        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);
    };
    const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier)=>{
        const conflicts = conflictingClassGroups[classGroupId] || [];
        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {
            return [
                ...conflicts,
                ...conflictingClassGroupModifiers[classGroupId]
            ];
        }
        return conflicts;
    };
    return {
        getClassGroupId,
        getConflictingClassGroupIds
    };
};
const getGroupRecursive = (classParts, classPartObject)=>{
    var _classPartObject_validators_find;
    if (classParts.length === 0) {
        return classPartObject.classGroupId;
    }
    const currentClassPart = classParts[0];
    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);
    const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;
    if (classGroupFromNextClassPart) {
        return classGroupFromNextClassPart;
    }
    if (classPartObject.validators.length === 0) {
        return undefined;
    }
    const classRest = classParts.join(CLASS_PART_SEPARATOR);
    return (_classPartObject_validators_find = classPartObject.validators.find((param)=>{
        let { validator } = param;
        return validator(classRest);
    })) === null || _classPartObject_validators_find === void 0 ? void 0 : _classPartObject_validators_find.classGroupId;
};
const arbitraryPropertyRegex = /^\[(.+)\]$/;
const getGroupIdForArbitraryProperty = (className)=>{
    if (arbitraryPropertyRegex.test(className)) {
        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];
        const property = arbitraryPropertyClassName === null || arbitraryPropertyClassName === void 0 ? void 0 : arbitraryPropertyClassName.substring(0, arbitraryPropertyClassName.indexOf(':'));
        if (property) {
            // I use two dots here because one dot is used as prefix for class groups in plugins
            return 'arbitrary..' + property;
        }
    }
};
/**
 * Exported for testing only
 */ const createClassMap = (config)=>{
    const { theme, classGroups } = config;
    const classMap = {
        nextPart: new Map(),
        validators: []
    };
    for(const classGroupId in classGroups){
        processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);
    }
    return classMap;
};
const processClassesRecursively = (classGroup, classPartObject, classGroupId, theme)=>{
    classGroup.forEach((classDefinition)=>{
        if (typeof classDefinition === 'string') {
            const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);
            classPartObjectToEdit.classGroupId = classGroupId;
            return;
        }
        if (typeof classDefinition === 'function') {
            if (isThemeGetter(classDefinition)) {
                processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);
                return;
            }
            classPartObject.validators.push({
                validator: classDefinition,
                classGroupId
            });
            return;
        }
        Object.entries(classDefinition).forEach((param)=>{
            let [key, classGroup] = param;
            processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);
        });
    });
};
const getPart = (classPartObject, path)=>{
    let currentClassPartObject = classPartObject;
    path.split(CLASS_PART_SEPARATOR).forEach((pathPart)=>{
        if (!currentClassPartObject.nextPart.has(pathPart)) {
            currentClassPartObject.nextPart.set(pathPart, {
                nextPart: new Map(),
                validators: []
            });
        }
        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);
    });
    return currentClassPartObject;
};
const isThemeGetter = (func)=>func.isThemeGetter;
// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance
const createLruCache = (maxCacheSize)=>{
    if (maxCacheSize < 1) {
        return {
            get: ()=>undefined,
            set: ()=>{}
        };
    }
    let cacheSize = 0;
    let cache = new Map();
    let previousCache = new Map();
    const update = (key, value)=>{
        cache.set(key, value);
        cacheSize++;
        if (cacheSize > maxCacheSize) {
            cacheSize = 0;
            previousCache = cache;
            cache = new Map();
        }
    };
    return {
        get (key) {
            let value = cache.get(key);
            if (value !== undefined) {
                return value;
            }
            if ((value = previousCache.get(key)) !== undefined) {
                update(key, value);
                return value;
            }
        },
        set (key, value) {
            if (cache.has(key)) {
                cache.set(key, value);
            } else {
                update(key, value);
            }
        }
    };
};
const IMPORTANT_MODIFIER = '!';
const MODIFIER_SEPARATOR = ':';
const MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;
const createParseClassName = (config)=>{
    const { prefix, experimentalParseClassName } = config;
    /**
   * Parse class name into parts.
   *
   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS
   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js
   */ let parseClassName = (className)=>{
        const modifiers = [];
        let bracketDepth = 0;
        let parenDepth = 0;
        let modifierStart = 0;
        let postfixModifierPosition;
        for(let index = 0; index < className.length; index++){
            let currentCharacter = className[index];
            if (bracketDepth === 0 && parenDepth === 0) {
                if (currentCharacter === MODIFIER_SEPARATOR) {
                    modifiers.push(className.slice(modifierStart, index));
                    modifierStart = index + MODIFIER_SEPARATOR_LENGTH;
                    continue;
                }
                if (currentCharacter === '/') {
                    postfixModifierPosition = index;
                    continue;
                }
            }
            if (currentCharacter === '[') {
                bracketDepth++;
            } else if (currentCharacter === ']') {
                bracketDepth--;
            } else if (currentCharacter === '(') {
                parenDepth++;
            } else if (currentCharacter === ')') {
                parenDepth--;
            }
        }
        const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);
        const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);
        const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;
        const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;
        return {
            modifiers,
            hasImportantModifier,
            baseClassName,
            maybePostfixModifierPosition
        };
    };
    if (prefix) {
        const fullPrefix = prefix + MODIFIER_SEPARATOR;
        const parseClassNameOriginal = parseClassName;
        parseClassName = (className)=>className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {
                isExternal: true,
                modifiers: [],
                hasImportantModifier: false,
                baseClassName: className,
                maybePostfixModifierPosition: undefined
            };
    }
    if (experimentalParseClassName) {
        const parseClassNameOriginal = parseClassName;
        parseClassName = (className)=>experimentalParseClassName({
                className,
                parseClassName: parseClassNameOriginal
            });
    }
    return parseClassName;
};
const stripImportantModifier = (baseClassName)=>{
    if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {
        return baseClassName.substring(0, baseClassName.length - 1);
    }
    /**
   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.
   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864
   */ if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {
        return baseClassName.substring(1);
    }
    return baseClassName;
};
/**
 * Sorts modifiers according to following schema:
 * - Predefined modifiers are sorted alphabetically
 * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it
 */ const createSortModifiers = (config)=>{
    const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map((modifier)=>[
            modifier,
            true
        ]));
    const sortModifiers = (modifiers)=>{
        if (modifiers.length <= 1) {
            return modifiers;
        }
        const sortedModifiers = [];
        let unsortedModifiers = [];
        modifiers.forEach((modifier)=>{
            const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];
            if (isPositionSensitive) {
                sortedModifiers.push(...unsortedModifiers.sort(), modifier);
                unsortedModifiers = [];
            } else {
                unsortedModifiers.push(modifier);
            }
        });
        sortedModifiers.push(...unsortedModifiers.sort());
        return sortedModifiers;
    };
    return sortModifiers;
};
const createConfigUtils = (config)=>({
        cache: createLruCache(config.cacheSize),
        parseClassName: createParseClassName(config),
        sortModifiers: createSortModifiers(config),
        ...createClassGroupUtils(config)
    });
const SPLIT_CLASSES_REGEX = /\s+/;
const mergeClassList = (classList, configUtils)=>{
    const { parseClassName, getClassGroupId, getConflictingClassGroupIds, sortModifiers } = configUtils;
    /**
   * Set of classGroupIds in following format:
   * `{importantModifier}{variantModifiers}{classGroupId}`
   * @example 'float'
   * @example 'hover:focus:bg-color'
   * @example 'md:!pr'
   */ const classGroupsInConflict = [];
    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);
    let result = '';
    for(let index = classNames.length - 1; index >= 0; index -= 1){
        const originalClassName = classNames[index];
        const { isExternal, modifiers, hasImportantModifier, baseClassName, maybePostfixModifierPosition } = parseClassName(originalClassName);
        if (isExternal) {
            result = originalClassName + (result.length > 0 ? ' ' + result : result);
            continue;
        }
        let hasPostfixModifier = !!maybePostfixModifierPosition;
        let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);
        if (!classGroupId) {
            if (!hasPostfixModifier) {
                // Not a Tailwind class
                result = originalClassName + (result.length > 0 ? ' ' + result : result);
                continue;
            }
            classGroupId = getClassGroupId(baseClassName);
            if (!classGroupId) {
                // Not a Tailwind class
                result = originalClassName + (result.length > 0 ? ' ' + result : result);
                continue;
            }
            hasPostfixModifier = false;
        }
        const variantModifier = sortModifiers(modifiers).join(':');
        const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;
        const classId = modifierId + classGroupId;
        if (classGroupsInConflict.includes(classId)) {
            continue;
        }
        classGroupsInConflict.push(classId);
        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);
        for(let i = 0; i < conflictGroups.length; ++i){
            const group = conflictGroups[i];
            classGroupsInConflict.push(modifierId + group);
        }
        // Tailwind class not in conflict
        result = originalClassName + (result.length > 0 ? ' ' + result : result);
    }
    return result;
};
/**
 * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.
 *
 * Specifically:
 * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js
 * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts
 *
 * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)
 */ function twJoin() {
    let index = 0;
    let argument;
    let resolvedValue;
    let string = '';
    while(index < arguments.length){
        if (argument = arguments[index++]) {
            if (resolvedValue = toValue(argument)) {
                string && (string += ' ');
                string += resolvedValue;
            }
        }
    }
    return string;
}
const toValue = (mix)=>{
    if (typeof mix === 'string') {
        return mix;
    }
    let resolvedValue;
    let string = '';
    for(let k = 0; k < mix.length; k++){
        if (mix[k]) {
            if (resolvedValue = toValue(mix[k])) {
                string && (string += ' ');
                string += resolvedValue;
            }
        }
    }
    return string;
};
function createTailwindMerge(createConfigFirst) {
    for(var _len = arguments.length, createConfigRest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
        createConfigRest[_key - 1] = arguments[_key];
    }
    let configUtils;
    let cacheGet;
    let cacheSet;
    let functionToCall = initTailwindMerge;
    function initTailwindMerge(classList) {
        const config = createConfigRest.reduce((previousConfig, createConfigCurrent)=>createConfigCurrent(previousConfig), createConfigFirst());
        configUtils = createConfigUtils(config);
        cacheGet = configUtils.cache.get;
        cacheSet = configUtils.cache.set;
        functionToCall = tailwindMerge;
        return tailwindMerge(classList);
    }
    function tailwindMerge(classList) {
        const cachedResult = cacheGet(classList);
        if (cachedResult) {
            return cachedResult;
        }
        const result = mergeClassList(classList, configUtils);
        cacheSet(classList, result);
        return result;
    }
    return function callTailwindMerge() {
        return functionToCall(twJoin.apply(null, arguments));
    };
}
const fromTheme = (key)=>{
    const themeGetter = (theme)=>theme[key] || [];
    themeGetter.isThemeGetter = true;
    return themeGetter;
};
const arbitraryValueRegex = /^\[(?:(\w[\w-]*):)?(.+)\]$/i;
const arbitraryVariableRegex = /^\((?:(\w[\w-]*):)?(.+)\)$/i;
const fractionRegex = /^\d+\/\d+$/;
const tshirtUnitRegex = /^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/;
const lengthUnitRegex = /\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/;
const colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/;
// Shadow always begins with x and y offset separated by underscore optionally prepended by inset
const shadowRegex = /^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;
const imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;
const isFraction = (value)=>fractionRegex.test(value);
const isNumber = (value)=>!!value && !Number.isNaN(Number(value));
const isInteger = (value)=>!!value && Number.isInteger(Number(value));
const isPercent = (value)=>value.endsWith('%') && isNumber(value.slice(0, -1));
const isTshirtSize = (value)=>tshirtUnitRegex.test(value);
const isAny = ()=>true;
const isLengthOnly = (value)=>// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.
    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.
    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.
    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value);
const isNever = ()=>false;
const isShadow = (value)=>shadowRegex.test(value);
const isImage = (value)=>imageRegex.test(value);
const isAnyNonArbitrary = (value)=>!isArbitraryValue(value) && !isArbitraryVariable(value);
const isArbitrarySize = (value)=>getIsArbitraryValue(value, isLabelSize, isNever);
const isArbitraryValue = (value)=>arbitraryValueRegex.test(value);
const isArbitraryLength = (value)=>getIsArbitraryValue(value, isLabelLength, isLengthOnly);
const isArbitraryNumber = (value)=>getIsArbitraryValue(value, isLabelNumber, isNumber);
const isArbitraryPosition = (value)=>getIsArbitraryValue(value, isLabelPosition, isNever);
const isArbitraryImage = (value)=>getIsArbitraryValue(value, isLabelImage, isImage);
const isArbitraryShadow = (value)=>getIsArbitraryValue(value, isLabelShadow, isShadow);
const isArbitraryVariable = (value)=>arbitraryVariableRegex.test(value);
const isArbitraryVariableLength = (value)=>getIsArbitraryVariable(value, isLabelLength);
const isArbitraryVariableFamilyName = (value)=>getIsArbitraryVariable(value, isLabelFamilyName);
const isArbitraryVariablePosition = (value)=>getIsArbitraryVariable(value, isLabelPosition);
const isArbitraryVariableSize = (value)=>getIsArbitraryVariable(value, isLabelSize);
const isArbitraryVariableImage = (value)=>getIsArbitraryVariable(value, isLabelImage);
const isArbitraryVariableShadow = (value)=>getIsArbitraryVariable(value, isLabelShadow, true);
// Helpers
const getIsArbitraryValue = (value, testLabel, testValue)=>{
    const result = arbitraryValueRegex.exec(value);
    if (result) {
        if (result[1]) {
            return testLabel(result[1]);
        }
        return testValue(result[2]);
    }
    return false;
};
const getIsArbitraryVariable = function(value, testLabel) {
    let shouldMatchNoLabel = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
    const result = arbitraryVariableRegex.exec(value);
    if (result) {
        if (result[1]) {
            return testLabel(result[1]);
        }
        return shouldMatchNoLabel;
    }
    return false;
};
// Labels
const isLabelPosition = (label)=>label === 'position' || label === 'percentage';
const isLabelImage = (label)=>label === 'image' || label === 'url';
const isLabelSize = (label)=>label === 'length' || label === 'size' || label === 'bg-size';
const isLabelLength = (label)=>label === 'length';
const isLabelNumber = (label)=>label === 'number';
const isLabelFamilyName = (label)=>label === 'family-name';
const isLabelShadow = (label)=>label === 'shadow';
const validators = /*#__PURE__*/ Object.defineProperty({
    __proto__: null,
    isAny,
    isAnyNonArbitrary,
    isArbitraryImage,
    isArbitraryLength,
    isArbitraryNumber,
    isArbitraryPosition,
    isArbitraryShadow,
    isArbitrarySize,
    isArbitraryValue,
    isArbitraryVariable,
    isArbitraryVariableFamilyName,
    isArbitraryVariableImage,
    isArbitraryVariableLength,
    isArbitraryVariablePosition,
    isArbitraryVariableShadow,
    isArbitraryVariableSize,
    isFraction,
    isInteger,
    isNumber,
    isPercent,
    isTshirtSize
}, Symbol.toStringTag, {
    value: 'Module'
});
const getDefaultConfig = ()=>{
    /**
   * Theme getters for theme variable namespaces
   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces
   */ /***/ const themeColor = fromTheme('color');
    const themeFont = fromTheme('font');
    const themeText = fromTheme('text');
    const themeFontWeight = fromTheme('font-weight');
    const themeTracking = fromTheme('tracking');
    const themeLeading = fromTheme('leading');
    const themeBreakpoint = fromTheme('breakpoint');
    const themeContainer = fromTheme('container');
    const themeSpacing = fromTheme('spacing');
    const themeRadius = fromTheme('radius');
    const themeShadow = fromTheme('shadow');
    const themeInsetShadow = fromTheme('inset-shadow');
    const themeTextShadow = fromTheme('text-shadow');
    const themeDropShadow = fromTheme('drop-shadow');
    const themeBlur = fromTheme('blur');
    const themePerspective = fromTheme('perspective');
    const themeAspect = fromTheme('aspect');
    const themeEase = fromTheme('ease');
    const themeAnimate = fromTheme('animate');
    /**
   * Helpers to avoid repeating the same scales
   *
   * We use functions that create a new array every time they're called instead of static arrays.
   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.
   */ /***/ const scaleBreak = ()=>[
            'auto',
            'avoid',
            'all',
            'avoid-page',
            'page',
            'left',
            'right',
            'column'
        ];
    const scalePosition = ()=>[
            'center',
            'top',
            'bottom',
            'left',
            'right',
            'top-left',
            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378
            'left-top',
            'top-right',
            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378
            'right-top',
            'bottom-right',
            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378
            'right-bottom',
            'bottom-left',
            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378
            'left-bottom'
        ];
    const scalePositionWithArbitrary = ()=>[
            ...scalePosition(),
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleOverflow = ()=>[
            'auto',
            'hidden',
            'clip',
            'visible',
            'scroll'
        ];
    const scaleOverscroll = ()=>[
            'auto',
            'contain',
            'none'
        ];
    const scaleUnambiguousSpacing = ()=>[
            isArbitraryVariable,
            isArbitraryValue,
            themeSpacing
        ];
    const scaleInset = ()=>[
            isFraction,
            'full',
            'auto',
            ...scaleUnambiguousSpacing()
        ];
    const scaleGridTemplateColsRows = ()=>[
            isInteger,
            'none',
            'subgrid',
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleGridColRowStartAndEnd = ()=>[
            'auto',
            {
                span: [
                    'full',
                    isInteger,
                    isArbitraryVariable,
                    isArbitraryValue
                ]
            },
            isInteger,
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleGridColRowStartOrEnd = ()=>[
            isInteger,
            'auto',
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleGridAutoColsRows = ()=>[
            'auto',
            'min',
            'max',
            'fr',
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleAlignPrimaryAxis = ()=>[
            'start',
            'end',
            'center',
            'between',
            'around',
            'evenly',
            'stretch',
            'baseline',
            'center-safe',
            'end-safe'
        ];
    const scaleAlignSecondaryAxis = ()=>[
            'start',
            'end',
            'center',
            'stretch',
            'center-safe',
            'end-safe'
        ];
    const scaleMargin = ()=>[
            'auto',
            ...scaleUnambiguousSpacing()
        ];
    const scaleSizing = ()=>[
            isFraction,
            'auto',
            'full',
            'dvw',
            'dvh',
            'lvw',
            'lvh',
            'svw',
            'svh',
            'min',
            'max',
            'fit',
            ...scaleUnambiguousSpacing()
        ];
    const scaleColor = ()=>[
            themeColor,
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleBgPosition = ()=>[
            ...scalePosition(),
            isArbitraryVariablePosition,
            isArbitraryPosition,
            {
                position: [
                    isArbitraryVariable,
                    isArbitraryValue
                ]
            }
        ];
    const scaleBgRepeat = ()=>[
            'no-repeat',
            {
                repeat: [
                    '',
                    'x',
                    'y',
                    'space',
                    'round'
                ]
            }
        ];
    const scaleBgSize = ()=>[
            'auto',
            'cover',
            'contain',
            isArbitraryVariableSize,
            isArbitrarySize,
            {
                size: [
                    isArbitraryVariable,
                    isArbitraryValue
                ]
            }
        ];
    const scaleGradientStopPosition = ()=>[
            isPercent,
            isArbitraryVariableLength,
            isArbitraryLength
        ];
    const scaleRadius = ()=>[
            // Deprecated since Tailwind CSS v4.0.0
            '',
            'none',
            'full',
            themeRadius,
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleBorderWidth = ()=>[
            '',
            isNumber,
            isArbitraryVariableLength,
            isArbitraryLength
        ];
    const scaleLineStyle = ()=>[
            'solid',
            'dashed',
            'dotted',
            'double'
        ];
    const scaleBlendMode = ()=>[
            'normal',
            'multiply',
            'screen',
            'overlay',
            'darken',
            'lighten',
            'color-dodge',
            'color-burn',
            'hard-light',
            'soft-light',
            'difference',
            'exclusion',
            'hue',
            'saturation',
            'color',
            'luminosity'
        ];
    const scaleMaskImagePosition = ()=>[
            isNumber,
            isPercent,
            isArbitraryVariablePosition,
            isArbitraryPosition
        ];
    const scaleBlur = ()=>[
            // Deprecated since Tailwind CSS v4.0.0
            '',
            'none',
            themeBlur,
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleRotate = ()=>[
            'none',
            isNumber,
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleScale = ()=>[
            'none',
            isNumber,
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleSkew = ()=>[
            isNumber,
            isArbitraryVariable,
            isArbitraryValue
        ];
    const scaleTranslate = ()=>[
            isFraction,
            'full',
            ...scaleUnambiguousSpacing()
        ];
    return {
        cacheSize: 500,
        theme: {
            animate: [
                'spin',
                'ping',
                'pulse',
                'bounce'
            ],
            aspect: [
                'video'
            ],
            blur: [
                isTshirtSize
            ],
            breakpoint: [
                isTshirtSize
            ],
            color: [
                isAny
            ],
            container: [
                isTshirtSize
            ],
            'drop-shadow': [
                isTshirtSize
            ],
            ease: [
                'in',
                'out',
                'in-out'
            ],
            font: [
                isAnyNonArbitrary
            ],
            'font-weight': [
                'thin',
                'extralight',
                'light',
                'normal',
                'medium',
                'semibold',
                'bold',
                'extrabold',
                'black'
            ],
            'inset-shadow': [
                isTshirtSize
            ],
            leading: [
                'none',
                'tight',
                'snug',
                'normal',
                'relaxed',
                'loose'
            ],
            perspective: [
                'dramatic',
                'near',
                'normal',
                'midrange',
                'distant',
                'none'
            ],
            radius: [
                isTshirtSize
            ],
            shadow: [
                isTshirtSize
            ],
            spacing: [
                'px',
                isNumber
            ],
            text: [
                isTshirtSize
            ],
            'text-shadow': [
                isTshirtSize
            ],
            tracking: [
                'tighter',
                'tight',
                'normal',
                'wide',
                'wider',
                'widest'
            ]
        },
        classGroups: {
            // --------------
            // --- Layout ---
            // --------------
            /**
       * Aspect Ratio
       * @see https://tailwindcss.com/docs/aspect-ratio
       */ aspect: [
                {
                    aspect: [
                        'auto',
                        'square',
                        isFraction,
                        isArbitraryValue,
                        isArbitraryVariable,
                        themeAspect
                    ]
                }
            ],
            /**
       * Container
       * @see https://tailwindcss.com/docs/container
       * @deprecated since Tailwind CSS v4.0.0
       */ container: [
                'container'
            ],
            /**
       * Columns
       * @see https://tailwindcss.com/docs/columns
       */ columns: [
                {
                    columns: [
                        isNumber,
                        isArbitraryValue,
                        isArbitraryVariable,
                        themeContainer
                    ]
                }
            ],
            /**
       * Break After
       * @see https://tailwindcss.com/docs/break-after
       */ 'break-after': [
                {
                    'break-after': scaleBreak()
                }
            ],
            /**
       * Break Before
       * @see https://tailwindcss.com/docs/break-before
       */ 'break-before': [
                {
                    'break-before': scaleBreak()
                }
            ],
            /**
       * Break Inside
       * @see https://tailwindcss.com/docs/break-inside
       */ 'break-inside': [
                {
                    'break-inside': [
                        'auto',
                        'avoid',
                        'avoid-page',
                        'avoid-column'
                    ]
                }
            ],
            /**
       * Box Decoration Break
       * @see https://tailwindcss.com/docs/box-decoration-break
       */ 'box-decoration': [
                {
                    'box-decoration': [
                        'slice',
                        'clone'
                    ]
                }
            ],
            /**
       * Box Sizing
       * @see https://tailwindcss.com/docs/box-sizing
       */ box: [
                {
                    box: [
                        'border',
                        'content'
                    ]
                }
            ],
            /**
       * Display
       * @see https://tailwindcss.com/docs/display
       */ display: [
                'block',
                'inline-block',
                'inline',
                'flex',
                'inline-flex',
                'table',
                'inline-table',
                'table-caption',
                'table-cell',
                'table-column',
                'table-column-group',
                'table-footer-group',
                'table-header-group',
                'table-row-group',
                'table-row',
                'flow-root',
                'grid',
                'inline-grid',
                'contents',
                'list-item',
                'hidden'
            ],
            /**
       * Screen Reader Only
       * @see https://tailwindcss.com/docs/display#screen-reader-only
       */ sr: [
                'sr-only',
                'not-sr-only'
            ],
            /**
       * Floats
       * @see https://tailwindcss.com/docs/float
       */ float: [
                {
                    float: [
                        'right',
                        'left',
                        'none',
                        'start',
                        'end'
                    ]
                }
            ],
            /**
       * Clear
       * @see https://tailwindcss.com/docs/clear
       */ clear: [
                {
                    clear: [
                        'left',
                        'right',
                        'both',
                        'none',
                        'start',
                        'end'
                    ]
                }
            ],
            /**
       * Isolation
       * @see https://tailwindcss.com/docs/isolation
       */ isolation: [
                'isolate',
                'isolation-auto'
            ],
            /**
       * Object Fit
       * @see https://tailwindcss.com/docs/object-fit
       */ 'object-fit': [
                {
                    object: [
                        'contain',
                        'cover',
                        'fill',
                        'none',
                        'scale-down'
                    ]
                }
            ],
            /**
       * Object Position
       * @see https://tailwindcss.com/docs/object-position
       */ 'object-position': [
                {
                    object: scalePositionWithArbitrary()
                }
            ],
            /**
       * Overflow
       * @see https://tailwindcss.com/docs/overflow
       */ overflow: [
                {
                    overflow: scaleOverflow()
                }
            ],
            /**
       * Overflow X
       * @see https://tailwindcss.com/docs/overflow
       */ 'overflow-x': [
                {
                    'overflow-x': scaleOverflow()
                }
            ],
            /**
       * Overflow Y
       * @see https://tailwindcss.com/docs/overflow
       */ 'overflow-y': [
                {
                    'overflow-y': scaleOverflow()
                }
            ],
            /**
       * Overscroll Behavior
       * @see https://tailwindcss.com/docs/overscroll-behavior
       */ overscroll: [
                {
                    overscroll: scaleOverscroll()
                }
            ],
            /**
       * Overscroll Behavior X
       * @see https://tailwindcss.com/docs/overscroll-behavior
       */ 'overscroll-x': [
                {
                    'overscroll-x': scaleOverscroll()
                }
            ],
            /**
       * Overscroll Behavior Y
       * @see https://tailwindcss.com/docs/overscroll-behavior
       */ 'overscroll-y': [
                {
                    'overscroll-y': scaleOverscroll()
                }
            ],
            /**
       * Position
       * @see https://tailwindcss.com/docs/position
       */ position: [
                'static',
                'fixed',
                'absolute',
                'relative',
                'sticky'
            ],
            /**
       * Top / Right / Bottom / Left
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ inset: [
                {
                    inset: scaleInset()
                }
            ],
            /**
       * Right / Left
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ 'inset-x': [
                {
                    'inset-x': scaleInset()
                }
            ],
            /**
       * Top / Bottom
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ 'inset-y': [
                {
                    'inset-y': scaleInset()
                }
            ],
            /**
       * Start
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ start: [
                {
                    start: scaleInset()
                }
            ],
            /**
       * End
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ end: [
                {
                    end: scaleInset()
                }
            ],
            /**
       * Top
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ top: [
                {
                    top: scaleInset()
                }
            ],
            /**
       * Right
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ right: [
                {
                    right: scaleInset()
                }
            ],
            /**
       * Bottom
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ bottom: [
                {
                    bottom: scaleInset()
                }
            ],
            /**
       * Left
       * @see https://tailwindcss.com/docs/top-right-bottom-left
       */ left: [
                {
                    left: scaleInset()
                }
            ],
            /**
       * Visibility
       * @see https://tailwindcss.com/docs/visibility
       */ visibility: [
                'visible',
                'invisible',
                'collapse'
            ],
            /**
       * Z-Index
       * @see https://tailwindcss.com/docs/z-index
       */ z: [
                {
                    z: [
                        isInteger,
                        'auto',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            // ------------------------
            // --- Flexbox and Grid ---
            // ------------------------
            /**
       * Flex Basis
       * @see https://tailwindcss.com/docs/flex-basis
       */ basis: [
                {
                    basis: [
                        isFraction,
                        'full',
                        'auto',
                        themeContainer,
                        ...scaleUnambiguousSpacing()
                    ]
                }
            ],
            /**
       * Flex Direction
       * @see https://tailwindcss.com/docs/flex-direction
       */ 'flex-direction': [
                {
                    flex: [
                        'row',
                        'row-reverse',
                        'col',
                        'col-reverse'
                    ]
                }
            ],
            /**
       * Flex Wrap
       * @see https://tailwindcss.com/docs/flex-wrap
       */ 'flex-wrap': [
                {
                    flex: [
                        'nowrap',
                        'wrap',
                        'wrap-reverse'
                    ]
                }
            ],
            /**
       * Flex
       * @see https://tailwindcss.com/docs/flex
       */ flex: [
                {
                    flex: [
                        isNumber,
                        isFraction,
                        'auto',
                        'initial',
                        'none',
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Flex Grow
       * @see https://tailwindcss.com/docs/flex-grow
       */ grow: [
                {
                    grow: [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Flex Shrink
       * @see https://tailwindcss.com/docs/flex-shrink
       */ shrink: [
                {
                    shrink: [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Order
       * @see https://tailwindcss.com/docs/order
       */ order: [
                {
                    order: [
                        isInteger,
                        'first',
                        'last',
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Grid Template Columns
       * @see https://tailwindcss.com/docs/grid-template-columns
       */ 'grid-cols': [
                {
                    'grid-cols': scaleGridTemplateColsRows()
                }
            ],
            /**
       * Grid Column Start / End
       * @see https://tailwindcss.com/docs/grid-column
       */ 'col-start-end': [
                {
                    col: scaleGridColRowStartAndEnd()
                }
            ],
            /**
       * Grid Column Start
       * @see https://tailwindcss.com/docs/grid-column
       */ 'col-start': [
                {
                    'col-start': scaleGridColRowStartOrEnd()
                }
            ],
            /**
       * Grid Column End
       * @see https://tailwindcss.com/docs/grid-column
       */ 'col-end': [
                {
                    'col-end': scaleGridColRowStartOrEnd()
                }
            ],
            /**
       * Grid Template Rows
       * @see https://tailwindcss.com/docs/grid-template-rows
       */ 'grid-rows': [
                {
                    'grid-rows': scaleGridTemplateColsRows()
                }
            ],
            /**
       * Grid Row Start / End
       * @see https://tailwindcss.com/docs/grid-row
       */ 'row-start-end': [
                {
                    row: scaleGridColRowStartAndEnd()
                }
            ],
            /**
       * Grid Row Start
       * @see https://tailwindcss.com/docs/grid-row
       */ 'row-start': [
                {
                    'row-start': scaleGridColRowStartOrEnd()
                }
            ],
            /**
       * Grid Row End
       * @see https://tailwindcss.com/docs/grid-row
       */ 'row-end': [
                {
                    'row-end': scaleGridColRowStartOrEnd()
                }
            ],
            /**
       * Grid Auto Flow
       * @see https://tailwindcss.com/docs/grid-auto-flow
       */ 'grid-flow': [
                {
                    'grid-flow': [
                        'row',
                        'col',
                        'dense',
                        'row-dense',
                        'col-dense'
                    ]
                }
            ],
            /**
       * Grid Auto Columns
       * @see https://tailwindcss.com/docs/grid-auto-columns
       */ 'auto-cols': [
                {
                    'auto-cols': scaleGridAutoColsRows()
                }
            ],
            /**
       * Grid Auto Rows
       * @see https://tailwindcss.com/docs/grid-auto-rows
       */ 'auto-rows': [
                {
                    'auto-rows': scaleGridAutoColsRows()
                }
            ],
            /**
       * Gap
       * @see https://tailwindcss.com/docs/gap
       */ gap: [
                {
                    gap: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Gap X
       * @see https://tailwindcss.com/docs/gap
       */ 'gap-x': [
                {
                    'gap-x': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Gap Y
       * @see https://tailwindcss.com/docs/gap
       */ 'gap-y': [
                {
                    'gap-y': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Justify Content
       * @see https://tailwindcss.com/docs/justify-content
       */ 'justify-content': [
                {
                    justify: [
                        ...scaleAlignPrimaryAxis(),
                        'normal'
                    ]
                }
            ],
            /**
       * Justify Items
       * @see https://tailwindcss.com/docs/justify-items
       */ 'justify-items': [
                {
                    'justify-items': [
                        ...scaleAlignSecondaryAxis(),
                        'normal'
                    ]
                }
            ],
            /**
       * Justify Self
       * @see https://tailwindcss.com/docs/justify-self
       */ 'justify-self': [
                {
                    'justify-self': [
                        'auto',
                        ...scaleAlignSecondaryAxis()
                    ]
                }
            ],
            /**
       * Align Content
       * @see https://tailwindcss.com/docs/align-content
       */ 'align-content': [
                {
                    content: [
                        'normal',
                        ...scaleAlignPrimaryAxis()
                    ]
                }
            ],
            /**
       * Align Items
       * @see https://tailwindcss.com/docs/align-items
       */ 'align-items': [
                {
                    items: [
                        ...scaleAlignSecondaryAxis(),
                        {
                            baseline: [
                                '',
                                'last'
                            ]
                        }
                    ]
                }
            ],
            /**
       * Align Self
       * @see https://tailwindcss.com/docs/align-self
       */ 'align-self': [
                {
                    self: [
                        'auto',
                        ...scaleAlignSecondaryAxis(),
                        {
                            baseline: [
                                '',
                                'last'
                            ]
                        }
                    ]
                }
            ],
            /**
       * Place Content
       * @see https://tailwindcss.com/docs/place-content
       */ 'place-content': [
                {
                    'place-content': scaleAlignPrimaryAxis()
                }
            ],
            /**
       * Place Items
       * @see https://tailwindcss.com/docs/place-items
       */ 'place-items': [
                {
                    'place-items': [
                        ...scaleAlignSecondaryAxis(),
                        'baseline'
                    ]
                }
            ],
            /**
       * Place Self
       * @see https://tailwindcss.com/docs/place-self
       */ 'place-self': [
                {
                    'place-self': [
                        'auto',
                        ...scaleAlignSecondaryAxis()
                    ]
                }
            ],
            // Spacing
            /**
       * Padding
       * @see https://tailwindcss.com/docs/padding
       */ p: [
                {
                    p: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding X
       * @see https://tailwindcss.com/docs/padding
       */ px: [
                {
                    px: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding Y
       * @see https://tailwindcss.com/docs/padding
       */ py: [
                {
                    py: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding Start
       * @see https://tailwindcss.com/docs/padding
       */ ps: [
                {
                    ps: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding End
       * @see https://tailwindcss.com/docs/padding
       */ pe: [
                {
                    pe: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding Top
       * @see https://tailwindcss.com/docs/padding
       */ pt: [
                {
                    pt: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding Right
       * @see https://tailwindcss.com/docs/padding
       */ pr: [
                {
                    pr: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding Bottom
       * @see https://tailwindcss.com/docs/padding
       */ pb: [
                {
                    pb: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Padding Left
       * @see https://tailwindcss.com/docs/padding
       */ pl: [
                {
                    pl: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Margin
       * @see https://tailwindcss.com/docs/margin
       */ m: [
                {
                    m: scaleMargin()
                }
            ],
            /**
       * Margin X
       * @see https://tailwindcss.com/docs/margin
       */ mx: [
                {
                    mx: scaleMargin()
                }
            ],
            /**
       * Margin Y
       * @see https://tailwindcss.com/docs/margin
       */ my: [
                {
                    my: scaleMargin()
                }
            ],
            /**
       * Margin Start
       * @see https://tailwindcss.com/docs/margin
       */ ms: [
                {
                    ms: scaleMargin()
                }
            ],
            /**
       * Margin End
       * @see https://tailwindcss.com/docs/margin
       */ me: [
                {
                    me: scaleMargin()
                }
            ],
            /**
       * Margin Top
       * @see https://tailwindcss.com/docs/margin
       */ mt: [
                {
                    mt: scaleMargin()
                }
            ],
            /**
       * Margin Right
       * @see https://tailwindcss.com/docs/margin
       */ mr: [
                {
                    mr: scaleMargin()
                }
            ],
            /**
       * Margin Bottom
       * @see https://tailwindcss.com/docs/margin
       */ mb: [
                {
                    mb: scaleMargin()
                }
            ],
            /**
       * Margin Left
       * @see https://tailwindcss.com/docs/margin
       */ ml: [
                {
                    ml: scaleMargin()
                }
            ],
            /**
       * Space Between X
       * @see https://tailwindcss.com/docs/margin#adding-space-between-children
       */ 'space-x': [
                {
                    'space-x': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Space Between X Reverse
       * @see https://tailwindcss.com/docs/margin#adding-space-between-children
       */ 'space-x-reverse': [
                'space-x-reverse'
            ],
            /**
       * Space Between Y
       * @see https://tailwindcss.com/docs/margin#adding-space-between-children
       */ 'space-y': [
                {
                    'space-y': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Space Between Y Reverse
       * @see https://tailwindcss.com/docs/margin#adding-space-between-children
       */ 'space-y-reverse': [
                'space-y-reverse'
            ],
            // --------------
            // --- Sizing ---
            // --------------
            /**
       * Size
       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height
       */ size: [
                {
                    size: scaleSizing()
                }
            ],
            /**
       * Width
       * @see https://tailwindcss.com/docs/width
       */ w: [
                {
                    w: [
                        themeContainer,
                        'screen',
                        ...scaleSizing()
                    ]
                }
            ],
            /**
       * Min-Width
       * @see https://tailwindcss.com/docs/min-width
       */ 'min-w': [
                {
                    'min-w': [
                        themeContainer,
                        'screen',
                        /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ 'none',
                        ...scaleSizing()
                    ]
                }
            ],
            /**
       * Max-Width
       * @see https://tailwindcss.com/docs/max-width
       */ 'max-w': [
                {
                    'max-w': [
                        themeContainer,
                        'screen',
                        'none',
                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ 'prose',
                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ {
                            screen: [
                                themeBreakpoint
                            ]
                        },
                        ...scaleSizing()
                    ]
                }
            ],
            /**
       * Height
       * @see https://tailwindcss.com/docs/height
       */ h: [
                {
                    h: [
                        'screen',
                        'lh',
                        ...scaleSizing()
                    ]
                }
            ],
            /**
       * Min-Height
       * @see https://tailwindcss.com/docs/min-height
       */ 'min-h': [
                {
                    'min-h': [
                        'screen',
                        'lh',
                        'none',
                        ...scaleSizing()
                    ]
                }
            ],
            /**
       * Max-Height
       * @see https://tailwindcss.com/docs/max-height
       */ 'max-h': [
                {
                    'max-h': [
                        'screen',
                        'lh',
                        ...scaleSizing()
                    ]
                }
            ],
            // ------------------
            // --- Typography ---
            // ------------------
            /**
       * Font Size
       * @see https://tailwindcss.com/docs/font-size
       */ 'font-size': [
                {
                    text: [
                        'base',
                        themeText,
                        isArbitraryVariableLength,
                        isArbitraryLength
                    ]
                }
            ],
            /**
       * Font Smoothing
       * @see https://tailwindcss.com/docs/font-smoothing
       */ 'font-smoothing': [
                'antialiased',
                'subpixel-antialiased'
            ],
            /**
       * Font Style
       * @see https://tailwindcss.com/docs/font-style
       */ 'font-style': [
                'italic',
                'not-italic'
            ],
            /**
       * Font Weight
       * @see https://tailwindcss.com/docs/font-weight
       */ 'font-weight': [
                {
                    font: [
                        themeFontWeight,
                        isArbitraryVariable,
                        isArbitraryNumber
                    ]
                }
            ],
            /**
       * Font Stretch
       * @see https://tailwindcss.com/docs/font-stretch
       */ 'font-stretch': [
                {
                    'font-stretch': [
                        'ultra-condensed',
                        'extra-condensed',
                        'condensed',
                        'semi-condensed',
                        'normal',
                        'semi-expanded',
                        'expanded',
                        'extra-expanded',
                        'ultra-expanded',
                        isPercent,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Font Family
       * @see https://tailwindcss.com/docs/font-family
       */ 'font-family': [
                {
                    font: [
                        isArbitraryVariableFamilyName,
                        isArbitraryValue,
                        themeFont
                    ]
                }
            ],
            /**
       * Font Variant Numeric
       * @see https://tailwindcss.com/docs/font-variant-numeric
       */ 'fvn-normal': [
                'normal-nums'
            ],
            /**
       * Font Variant Numeric
       * @see https://tailwindcss.com/docs/font-variant-numeric
       */ 'fvn-ordinal': [
                'ordinal'
            ],
            /**
       * Font Variant Numeric
       * @see https://tailwindcss.com/docs/font-variant-numeric
       */ 'fvn-slashed-zero': [
                'slashed-zero'
            ],
            /**
       * Font Variant Numeric
       * @see https://tailwindcss.com/docs/font-variant-numeric
       */ 'fvn-figure': [
                'lining-nums',
                'oldstyle-nums'
            ],
            /**
       * Font Variant Numeric
       * @see https://tailwindcss.com/docs/font-variant-numeric
       */ 'fvn-spacing': [
                'proportional-nums',
                'tabular-nums'
            ],
            /**
       * Font Variant Numeric
       * @see https://tailwindcss.com/docs/font-variant-numeric
       */ 'fvn-fraction': [
                'diagonal-fractions',
                'stacked-fractions'
            ],
            /**
       * Letter Spacing
       * @see https://tailwindcss.com/docs/letter-spacing
       */ tracking: [
                {
                    tracking: [
                        themeTracking,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Line Clamp
       * @see https://tailwindcss.com/docs/line-clamp
       */ 'line-clamp': [
                {
                    'line-clamp': [
                        isNumber,
                        'none',
                        isArbitraryVariable,
                        isArbitraryNumber
                    ]
                }
            ],
            /**
       * Line Height
       * @see https://tailwindcss.com/docs/line-height
       */ leading: [
                {
                    leading: [
                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ themeLeading,
                        ...scaleUnambiguousSpacing()
                    ]
                }
            ],
            /**
       * List Style Image
       * @see https://tailwindcss.com/docs/list-style-image
       */ 'list-image': [
                {
                    'list-image': [
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * List Style Position
       * @see https://tailwindcss.com/docs/list-style-position
       */ 'list-style-position': [
                {
                    list: [
                        'inside',
                        'outside'
                    ]
                }
            ],
            /**
       * List Style Type
       * @see https://tailwindcss.com/docs/list-style-type
       */ 'list-style-type': [
                {
                    list: [
                        'disc',
                        'decimal',
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Text Alignment
       * @see https://tailwindcss.com/docs/text-align
       */ 'text-alignment': [
                {
                    text: [
                        'left',
                        'center',
                        'right',
                        'justify',
                        'start',
                        'end'
                    ]
                }
            ],
            /**
       * Placeholder Color
       * @deprecated since Tailwind CSS v3.0.0
       * @see https://v3.tailwindcss.com/docs/placeholder-color
       */ 'placeholder-color': [
                {
                    placeholder: scaleColor()
                }
            ],
            /**
       * Text Color
       * @see https://tailwindcss.com/docs/text-color
       */ 'text-color': [
                {
                    text: scaleColor()
                }
            ],
            /**
       * Text Decoration
       * @see https://tailwindcss.com/docs/text-decoration
       */ 'text-decoration': [
                'underline',
                'overline',
                'line-through',
                'no-underline'
            ],
            /**
       * Text Decoration Style
       * @see https://tailwindcss.com/docs/text-decoration-style
       */ 'text-decoration-style': [
                {
                    decoration: [
                        ...scaleLineStyle(),
                        'wavy'
                    ]
                }
            ],
            /**
       * Text Decoration Thickness
       * @see https://tailwindcss.com/docs/text-decoration-thickness
       */ 'text-decoration-thickness': [
                {
                    decoration: [
                        isNumber,
                        'from-font',
                        'auto',
                        isArbitraryVariable,
                        isArbitraryLength
                    ]
                }
            ],
            /**
       * Text Decoration Color
       * @see https://tailwindcss.com/docs/text-decoration-color
       */ 'text-decoration-color': [
                {
                    decoration: scaleColor()
                }
            ],
            /**
       * Text Underline Offset
       * @see https://tailwindcss.com/docs/text-underline-offset
       */ 'underline-offset': [
                {
                    'underline-offset': [
                        isNumber,
                        'auto',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Text Transform
       * @see https://tailwindcss.com/docs/text-transform
       */ 'text-transform': [
                'uppercase',
                'lowercase',
                'capitalize',
                'normal-case'
            ],
            /**
       * Text Overflow
       * @see https://tailwindcss.com/docs/text-overflow
       */ 'text-overflow': [
                'truncate',
                'text-ellipsis',
                'text-clip'
            ],
            /**
       * Text Wrap
       * @see https://tailwindcss.com/docs/text-wrap
       */ 'text-wrap': [
                {
                    text: [
                        'wrap',
                        'nowrap',
                        'balance',
                        'pretty'
                    ]
                }
            ],
            /**
       * Text Indent
       * @see https://tailwindcss.com/docs/text-indent
       */ indent: [
                {
                    indent: scaleUnambiguousSpacing()
                }
            ],
            /**
       * Vertical Alignment
       * @see https://tailwindcss.com/docs/vertical-align
       */ 'vertical-align': [
                {
                    align: [
                        'baseline',
                        'top',
                        'middle',
                        'bottom',
                        'text-top',
                        'text-bottom',
                        'sub',
                        'super',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Whitespace
       * @see https://tailwindcss.com/docs/whitespace
       */ whitespace: [
                {
                    whitespace: [
                        'normal',
                        'nowrap',
                        'pre',
                        'pre-line',
                        'pre-wrap',
                        'break-spaces'
                    ]
                }
            ],
            /**
       * Word Break
       * @see https://tailwindcss.com/docs/word-break
       */ break: [
                {
                    break: [
                        'normal',
                        'words',
                        'all',
                        'keep'
                    ]
                }
            ],
            /**
       * Overflow Wrap
       * @see https://tailwindcss.com/docs/overflow-wrap
       */ wrap: [
                {
                    wrap: [
                        'break-word',
                        'anywhere',
                        'normal'
                    ]
                }
            ],
            /**
       * Hyphens
       * @see https://tailwindcss.com/docs/hyphens
       */ hyphens: [
                {
                    hyphens: [
                        'none',
                        'manual',
                        'auto'
                    ]
                }
            ],
            /**
       * Content
       * @see https://tailwindcss.com/docs/content
       */ content: [
                {
                    content: [
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            // -------------------
            // --- Backgrounds ---
            // -------------------
            /**
       * Background Attachment
       * @see https://tailwindcss.com/docs/background-attachment
       */ 'bg-attachment': [
                {
                    bg: [
                        'fixed',
                        'local',
                        'scroll'
                    ]
                }
            ],
            /**
       * Background Clip
       * @see https://tailwindcss.com/docs/background-clip
       */ 'bg-clip': [
                {
                    'bg-clip': [
                        'border',
                        'padding',
                        'content',
                        'text'
                    ]
                }
            ],
            /**
       * Background Origin
       * @see https://tailwindcss.com/docs/background-origin
       */ 'bg-origin': [
                {
                    'bg-origin': [
                        'border',
                        'padding',
                        'content'
                    ]
                }
            ],
            /**
       * Background Position
       * @see https://tailwindcss.com/docs/background-position
       */ 'bg-position': [
                {
                    bg: scaleBgPosition()
                }
            ],
            /**
       * Background Repeat
       * @see https://tailwindcss.com/docs/background-repeat
       */ 'bg-repeat': [
                {
                    bg: scaleBgRepeat()
                }
            ],
            /**
       * Background Size
       * @see https://tailwindcss.com/docs/background-size
       */ 'bg-size': [
                {
                    bg: scaleBgSize()
                }
            ],
            /**
       * Background Image
       * @see https://tailwindcss.com/docs/background-image
       */ 'bg-image': [
                {
                    bg: [
                        'none',
                        {
                            linear: [
                                {
                                    to: [
                                        't',
                                        'tr',
                                        'r',
                                        'br',
                                        'b',
                                        'bl',
                                        'l',
                                        'tl'
                                    ]
                                },
                                isInteger,
                                isArbitraryVariable,
                                isArbitraryValue
                            ],
                            radial: [
                                '',
                                isArbitraryVariable,
                                isArbitraryValue
                            ],
                            conic: [
                                isInteger,
                                isArbitraryVariable,
                                isArbitraryValue
                            ]
                        },
                        isArbitraryVariableImage,
                        isArbitraryImage
                    ]
                }
            ],
            /**
       * Background Color
       * @see https://tailwindcss.com/docs/background-color
       */ 'bg-color': [
                {
                    bg: scaleColor()
                }
            ],
            /**
       * Gradient Color Stops From Position
       * @see https://tailwindcss.com/docs/gradient-color-stops
       */ 'gradient-from-pos': [
                {
                    from: scaleGradientStopPosition()
                }
            ],
            /**
       * Gradient Color Stops Via Position
       * @see https://tailwindcss.com/docs/gradient-color-stops
       */ 'gradient-via-pos': [
                {
                    via: scaleGradientStopPosition()
                }
            ],
            /**
       * Gradient Color Stops To Position
       * @see https://tailwindcss.com/docs/gradient-color-stops
       */ 'gradient-to-pos': [
                {
                    to: scaleGradientStopPosition()
                }
            ],
            /**
       * Gradient Color Stops From
       * @see https://tailwindcss.com/docs/gradient-color-stops
       */ 'gradient-from': [
                {
                    from: scaleColor()
                }
            ],
            /**
       * Gradient Color Stops Via
       * @see https://tailwindcss.com/docs/gradient-color-stops
       */ 'gradient-via': [
                {
                    via: scaleColor()
                }
            ],
            /**
       * Gradient Color Stops To
       * @see https://tailwindcss.com/docs/gradient-color-stops
       */ 'gradient-to': [
                {
                    to: scaleColor()
                }
            ],
            // ---------------
            // --- Borders ---
            // ---------------
            /**
       * Border Radius
       * @see https://tailwindcss.com/docs/border-radius
       */ rounded: [
                {
                    rounded: scaleRadius()
                }
            ],
            /**
       * Border Radius Start
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-s': [
                {
                    'rounded-s': scaleRadius()
                }
            ],
            /**
       * Border Radius End
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-e': [
                {
                    'rounded-e': scaleRadius()
                }
            ],
            /**
       * Border Radius Top
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-t': [
                {
                    'rounded-t': scaleRadius()
                }
            ],
            /**
       * Border Radius Right
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-r': [
                {
                    'rounded-r': scaleRadius()
                }
            ],
            /**
       * Border Radius Bottom
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-b': [
                {
                    'rounded-b': scaleRadius()
                }
            ],
            /**
       * Border Radius Left
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-l': [
                {
                    'rounded-l': scaleRadius()
                }
            ],
            /**
       * Border Radius Start Start
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-ss': [
                {
                    'rounded-ss': scaleRadius()
                }
            ],
            /**
       * Border Radius Start End
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-se': [
                {
                    'rounded-se': scaleRadius()
                }
            ],
            /**
       * Border Radius End End
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-ee': [
                {
                    'rounded-ee': scaleRadius()
                }
            ],
            /**
       * Border Radius End Start
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-es': [
                {
                    'rounded-es': scaleRadius()
                }
            ],
            /**
       * Border Radius Top Left
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-tl': [
                {
                    'rounded-tl': scaleRadius()
                }
            ],
            /**
       * Border Radius Top Right
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-tr': [
                {
                    'rounded-tr': scaleRadius()
                }
            ],
            /**
       * Border Radius Bottom Right
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-br': [
                {
                    'rounded-br': scaleRadius()
                }
            ],
            /**
       * Border Radius Bottom Left
       * @see https://tailwindcss.com/docs/border-radius
       */ 'rounded-bl': [
                {
                    'rounded-bl': scaleRadius()
                }
            ],
            /**
       * Border Width
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w': [
                {
                    border: scaleBorderWidth()
                }
            ],
            /**
       * Border Width X
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-x': [
                {
                    'border-x': scaleBorderWidth()
                }
            ],
            /**
       * Border Width Y
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-y': [
                {
                    'border-y': scaleBorderWidth()
                }
            ],
            /**
       * Border Width Start
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-s': [
                {
                    'border-s': scaleBorderWidth()
                }
            ],
            /**
       * Border Width End
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-e': [
                {
                    'border-e': scaleBorderWidth()
                }
            ],
            /**
       * Border Width Top
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-t': [
                {
                    'border-t': scaleBorderWidth()
                }
            ],
            /**
       * Border Width Right
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-r': [
                {
                    'border-r': scaleBorderWidth()
                }
            ],
            /**
       * Border Width Bottom
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-b': [
                {
                    'border-b': scaleBorderWidth()
                }
            ],
            /**
       * Border Width Left
       * @see https://tailwindcss.com/docs/border-width
       */ 'border-w-l': [
                {
                    'border-l': scaleBorderWidth()
                }
            ],
            /**
       * Divide Width X
       * @see https://tailwindcss.com/docs/border-width#between-children
       */ 'divide-x': [
                {
                    'divide-x': scaleBorderWidth()
                }
            ],
            /**
       * Divide Width X Reverse
       * @see https://tailwindcss.com/docs/border-width#between-children
       */ 'divide-x-reverse': [
                'divide-x-reverse'
            ],
            /**
       * Divide Width Y
       * @see https://tailwindcss.com/docs/border-width#between-children
       */ 'divide-y': [
                {
                    'divide-y': scaleBorderWidth()
                }
            ],
            /**
       * Divide Width Y Reverse
       * @see https://tailwindcss.com/docs/border-width#between-children
       */ 'divide-y-reverse': [
                'divide-y-reverse'
            ],
            /**
       * Border Style
       * @see https://tailwindcss.com/docs/border-style
       */ 'border-style': [
                {
                    border: [
                        ...scaleLineStyle(),
                        'hidden',
                        'none'
                    ]
                }
            ],
            /**
       * Divide Style
       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style
       */ 'divide-style': [
                {
                    divide: [
                        ...scaleLineStyle(),
                        'hidden',
                        'none'
                    ]
                }
            ],
            /**
       * Border Color
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color': [
                {
                    border: scaleColor()
                }
            ],
            /**
       * Border Color X
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-x': [
                {
                    'border-x': scaleColor()
                }
            ],
            /**
       * Border Color Y
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-y': [
                {
                    'border-y': scaleColor()
                }
            ],
            /**
       * Border Color S
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-s': [
                {
                    'border-s': scaleColor()
                }
            ],
            /**
       * Border Color E
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-e': [
                {
                    'border-e': scaleColor()
                }
            ],
            /**
       * Border Color Top
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-t': [
                {
                    'border-t': scaleColor()
                }
            ],
            /**
       * Border Color Right
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-r': [
                {
                    'border-r': scaleColor()
                }
            ],
            /**
       * Border Color Bottom
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-b': [
                {
                    'border-b': scaleColor()
                }
            ],
            /**
       * Border Color Left
       * @see https://tailwindcss.com/docs/border-color
       */ 'border-color-l': [
                {
                    'border-l': scaleColor()
                }
            ],
            /**
       * Divide Color
       * @see https://tailwindcss.com/docs/divide-color
       */ 'divide-color': [
                {
                    divide: scaleColor()
                }
            ],
            /**
       * Outline Style
       * @see https://tailwindcss.com/docs/outline-style
       */ 'outline-style': [
                {
                    outline: [
                        ...scaleLineStyle(),
                        'none',
                        'hidden'
                    ]
                }
            ],
            /**
       * Outline Offset
       * @see https://tailwindcss.com/docs/outline-offset
       */ 'outline-offset': [
                {
                    'outline-offset': [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Outline Width
       * @see https://tailwindcss.com/docs/outline-width
       */ 'outline-w': [
                {
                    outline: [
                        '',
                        isNumber,
                        isArbitraryVariableLength,
                        isArbitraryLength
                    ]
                }
            ],
            /**
       * Outline Color
       * @see https://tailwindcss.com/docs/outline-color
       */ 'outline-color': [
                {
                    outline: scaleColor()
                }
            ],
            // ---------------
            // --- Effects ---
            // ---------------
            /**
       * Box Shadow
       * @see https://tailwindcss.com/docs/box-shadow
       */ shadow: [
                {
                    shadow: [
                        // Deprecated since Tailwind CSS v4.0.0
                        '',
                        'none',
                        themeShadow,
                        isArbitraryVariableShadow,
                        isArbitraryShadow
                    ]
                }
            ],
            /**
       * Box Shadow Color
       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color
       */ 'shadow-color': [
                {
                    shadow: scaleColor()
                }
            ],
            /**
       * Inset Box Shadow
       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow
       */ 'inset-shadow': [
                {
                    'inset-shadow': [
                        'none',
                        themeInsetShadow,
                        isArbitraryVariableShadow,
                        isArbitraryShadow
                    ]
                }
            ],
            /**
       * Inset Box Shadow Color
       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color
       */ 'inset-shadow-color': [
                {
                    'inset-shadow': scaleColor()
                }
            ],
            /**
       * Ring Width
       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring
       */ 'ring-w': [
                {
                    ring: scaleBorderWidth()
                }
            ],
            /**
       * Ring Width Inset
       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings
       * @deprecated since Tailwind CSS v4.0.0
       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158
       */ 'ring-w-inset': [
                'ring-inset'
            ],
            /**
       * Ring Color
       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color
       */ 'ring-color': [
                {
                    ring: scaleColor()
                }
            ],
            /**
       * Ring Offset Width
       * @see https://v3.tailwindcss.com/docs/ring-offset-width
       * @deprecated since Tailwind CSS v4.0.0
       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158
       */ 'ring-offset-w': [
                {
                    'ring-offset': [
                        isNumber,
                        isArbitraryLength
                    ]
                }
            ],
            /**
       * Ring Offset Color
       * @see https://v3.tailwindcss.com/docs/ring-offset-color
       * @deprecated since Tailwind CSS v4.0.0
       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158
       */ 'ring-offset-color': [
                {
                    'ring-offset': scaleColor()
                }
            ],
            /**
       * Inset Ring Width
       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring
       */ 'inset-ring-w': [
                {
                    'inset-ring': scaleBorderWidth()
                }
            ],
            /**
       * Inset Ring Color
       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color
       */ 'inset-ring-color': [
                {
                    'inset-ring': scaleColor()
                }
            ],
            /**
       * Text Shadow
       * @see https://tailwindcss.com/docs/text-shadow
       */ 'text-shadow': [
                {
                    'text-shadow': [
                        'none',
                        themeTextShadow,
                        isArbitraryVariableShadow,
                        isArbitraryShadow
                    ]
                }
            ],
            /**
       * Text Shadow Color
       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color
       */ 'text-shadow-color': [
                {
                    'text-shadow': scaleColor()
                }
            ],
            /**
       * Opacity
       * @see https://tailwindcss.com/docs/opacity
       */ opacity: [
                {
                    opacity: [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Mix Blend Mode
       * @see https://tailwindcss.com/docs/mix-blend-mode
       */ 'mix-blend': [
                {
                    'mix-blend': [
                        ...scaleBlendMode(),
                        'plus-darker',
                        'plus-lighter'
                    ]
                }
            ],
            /**
       * Background Blend Mode
       * @see https://tailwindcss.com/docs/background-blend-mode
       */ 'bg-blend': [
                {
                    'bg-blend': scaleBlendMode()
                }
            ],
            /**
       * Mask Clip
       * @see https://tailwindcss.com/docs/mask-clip
       */ 'mask-clip': [
                {
                    'mask-clip': [
                        'border',
                        'padding',
                        'content',
                        'fill',
                        'stroke',
                        'view'
                    ]
                },
                'mask-no-clip'
            ],
            /**
       * Mask Composite
       * @see https://tailwindcss.com/docs/mask-composite
       */ 'mask-composite': [
                {
                    mask: [
                        'add',
                        'subtract',
                        'intersect',
                        'exclude'
                    ]
                }
            ],
            /**
       * Mask Image
       * @see https://tailwindcss.com/docs/mask-image
       */ 'mask-image-linear-pos': [
                {
                    'mask-linear': [
                        isNumber
                    ]
                }
            ],
            'mask-image-linear-from-pos': [
                {
                    'mask-linear-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-linear-to-pos': [
                {
                    'mask-linear-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-linear-from-color': [
                {
                    'mask-linear-from': scaleColor()
                }
            ],
            'mask-image-linear-to-color': [
                {
                    'mask-linear-to': scaleColor()
                }
            ],
            'mask-image-t-from-pos': [
                {
                    'mask-t-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-t-to-pos': [
                {
                    'mask-t-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-t-from-color': [
                {
                    'mask-t-from': scaleColor()
                }
            ],
            'mask-image-t-to-color': [
                {
                    'mask-t-to': scaleColor()
                }
            ],
            'mask-image-r-from-pos': [
                {
                    'mask-r-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-r-to-pos': [
                {
                    'mask-r-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-r-from-color': [
                {
                    'mask-r-from': scaleColor()
                }
            ],
            'mask-image-r-to-color': [
                {
                    'mask-r-to': scaleColor()
                }
            ],
            'mask-image-b-from-pos': [
                {
                    'mask-b-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-b-to-pos': [
                {
                    'mask-b-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-b-from-color': [
                {
                    'mask-b-from': scaleColor()
                }
            ],
            'mask-image-b-to-color': [
                {
                    'mask-b-to': scaleColor()
                }
            ],
            'mask-image-l-from-pos': [
                {
                    'mask-l-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-l-to-pos': [
                {
                    'mask-l-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-l-from-color': [
                {
                    'mask-l-from': scaleColor()
                }
            ],
            'mask-image-l-to-color': [
                {
                    'mask-l-to': scaleColor()
                }
            ],
            'mask-image-x-from-pos': [
                {
                    'mask-x-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-x-to-pos': [
                {
                    'mask-x-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-x-from-color': [
                {
                    'mask-x-from': scaleColor()
                }
            ],
            'mask-image-x-to-color': [
                {
                    'mask-x-to': scaleColor()
                }
            ],
            'mask-image-y-from-pos': [
                {
                    'mask-y-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-y-to-pos': [
                {
                    'mask-y-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-y-from-color': [
                {
                    'mask-y-from': scaleColor()
                }
            ],
            'mask-image-y-to-color': [
                {
                    'mask-y-to': scaleColor()
                }
            ],
            'mask-image-radial': [
                {
                    'mask-radial': [
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            'mask-image-radial-from-pos': [
                {
                    'mask-radial-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-radial-to-pos': [
                {
                    'mask-radial-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-radial-from-color': [
                {
                    'mask-radial-from': scaleColor()
                }
            ],
            'mask-image-radial-to-color': [
                {
                    'mask-radial-to': scaleColor()
                }
            ],
            'mask-image-radial-shape': [
                {
                    'mask-radial': [
                        'circle',
                        'ellipse'
                    ]
                }
            ],
            'mask-image-radial-size': [
                {
                    'mask-radial': [
                        {
                            closest: [
                                'side',
                                'corner'
                            ],
                            farthest: [
                                'side',
                                'corner'
                            ]
                        }
                    ]
                }
            ],
            'mask-image-radial-pos': [
                {
                    'mask-radial-at': scalePosition()
                }
            ],
            'mask-image-conic-pos': [
                {
                    'mask-conic': [
                        isNumber
                    ]
                }
            ],
            'mask-image-conic-from-pos': [
                {
                    'mask-conic-from': scaleMaskImagePosition()
                }
            ],
            'mask-image-conic-to-pos': [
                {
                    'mask-conic-to': scaleMaskImagePosition()
                }
            ],
            'mask-image-conic-from-color': [
                {
                    'mask-conic-from': scaleColor()
                }
            ],
            'mask-image-conic-to-color': [
                {
                    'mask-conic-to': scaleColor()
                }
            ],
            /**
       * Mask Mode
       * @see https://tailwindcss.com/docs/mask-mode
       */ 'mask-mode': [
                {
                    mask: [
                        'alpha',
                        'luminance',
                        'match'
                    ]
                }
            ],
            /**
       * Mask Origin
       * @see https://tailwindcss.com/docs/mask-origin
       */ 'mask-origin': [
                {
                    'mask-origin': [
                        'border',
                        'padding',
                        'content',
                        'fill',
                        'stroke',
                        'view'
                    ]
                }
            ],
            /**
       * Mask Position
       * @see https://tailwindcss.com/docs/mask-position
       */ 'mask-position': [
                {
                    mask: scaleBgPosition()
                }
            ],
            /**
       * Mask Repeat
       * @see https://tailwindcss.com/docs/mask-repeat
       */ 'mask-repeat': [
                {
                    mask: scaleBgRepeat()
                }
            ],
            /**
       * Mask Size
       * @see https://tailwindcss.com/docs/mask-size
       */ 'mask-size': [
                {
                    mask: scaleBgSize()
                }
            ],
            /**
       * Mask Type
       * @see https://tailwindcss.com/docs/mask-type
       */ 'mask-type': [
                {
                    'mask-type': [
                        'alpha',
                        'luminance'
                    ]
                }
            ],
            /**
       * Mask Image
       * @see https://tailwindcss.com/docs/mask-image
       */ 'mask-image': [
                {
                    mask: [
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            // ---------------
            // --- Filters ---
            // ---------------
            /**
       * Filter
       * @see https://tailwindcss.com/docs/filter
       */ filter: [
                {
                    filter: [
                        // Deprecated since Tailwind CSS v3.0.0
                        '',
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Blur
       * @see https://tailwindcss.com/docs/blur
       */ blur: [
                {
                    blur: scaleBlur()
                }
            ],
            /**
       * Brightness
       * @see https://tailwindcss.com/docs/brightness
       */ brightness: [
                {
                    brightness: [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Contrast
       * @see https://tailwindcss.com/docs/contrast
       */ contrast: [
                {
                    contrast: [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Drop Shadow
       * @see https://tailwindcss.com/docs/drop-shadow
       */ 'drop-shadow': [
                {
                    'drop-shadow': [
                        // Deprecated since Tailwind CSS v4.0.0
                        '',
                        'none',
                        themeDropShadow,
                        isArbitraryVariableShadow,
                        isArbitraryShadow
                    ]
                }
            ],
            /**
       * Drop Shadow Color
       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color
       */ 'drop-shadow-color': [
                {
                    'drop-shadow': scaleColor()
                }
            ],
            /**
       * Grayscale
       * @see https://tailwindcss.com/docs/grayscale
       */ grayscale: [
                {
                    grayscale: [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Hue Rotate
       * @see https://tailwindcss.com/docs/hue-rotate
       */ 'hue-rotate': [
                {
                    'hue-rotate': [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Invert
       * @see https://tailwindcss.com/docs/invert
       */ invert: [
                {
                    invert: [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Saturate
       * @see https://tailwindcss.com/docs/saturate
       */ saturate: [
                {
                    saturate: [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Sepia
       * @see https://tailwindcss.com/docs/sepia
       */ sepia: [
                {
                    sepia: [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Filter
       * @see https://tailwindcss.com/docs/backdrop-filter
       */ 'backdrop-filter': [
                {
                    'backdrop-filter': [
                        // Deprecated since Tailwind CSS v3.0.0
                        '',
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Blur
       * @see https://tailwindcss.com/docs/backdrop-blur
       */ 'backdrop-blur': [
                {
                    'backdrop-blur': scaleBlur()
                }
            ],
            /**
       * Backdrop Brightness
       * @see https://tailwindcss.com/docs/backdrop-brightness
       */ 'backdrop-brightness': [
                {
                    'backdrop-brightness': [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Contrast
       * @see https://tailwindcss.com/docs/backdrop-contrast
       */ 'backdrop-contrast': [
                {
                    'backdrop-contrast': [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Grayscale
       * @see https://tailwindcss.com/docs/backdrop-grayscale
       */ 'backdrop-grayscale': [
                {
                    'backdrop-grayscale': [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Hue Rotate
       * @see https://tailwindcss.com/docs/backdrop-hue-rotate
       */ 'backdrop-hue-rotate': [
                {
                    'backdrop-hue-rotate': [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Invert
       * @see https://tailwindcss.com/docs/backdrop-invert
       */ 'backdrop-invert': [
                {
                    'backdrop-invert': [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Opacity
       * @see https://tailwindcss.com/docs/backdrop-opacity
       */ 'backdrop-opacity': [
                {
                    'backdrop-opacity': [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Saturate
       * @see https://tailwindcss.com/docs/backdrop-saturate
       */ 'backdrop-saturate': [
                {
                    'backdrop-saturate': [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Backdrop Sepia
       * @see https://tailwindcss.com/docs/backdrop-sepia
       */ 'backdrop-sepia': [
                {
                    'backdrop-sepia': [
                        '',
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            // --------------
            // --- Tables ---
            // --------------
            /**
       * Border Collapse
       * @see https://tailwindcss.com/docs/border-collapse
       */ 'border-collapse': [
                {
                    border: [
                        'collapse',
                        'separate'
                    ]
                }
            ],
            /**
       * Border Spacing
       * @see https://tailwindcss.com/docs/border-spacing
       */ 'border-spacing': [
                {
                    'border-spacing': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Border Spacing X
       * @see https://tailwindcss.com/docs/border-spacing
       */ 'border-spacing-x': [
                {
                    'border-spacing-x': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Border Spacing Y
       * @see https://tailwindcss.com/docs/border-spacing
       */ 'border-spacing-y': [
                {
                    'border-spacing-y': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Table Layout
       * @see https://tailwindcss.com/docs/table-layout
       */ 'table-layout': [
                {
                    table: [
                        'auto',
                        'fixed'
                    ]
                }
            ],
            /**
       * Caption Side
       * @see https://tailwindcss.com/docs/caption-side
       */ caption: [
                {
                    caption: [
                        'top',
                        'bottom'
                    ]
                }
            ],
            // ---------------------------------
            // --- Transitions and Animation ---
            // ---------------------------------
            /**
       * Transition Property
       * @see https://tailwindcss.com/docs/transition-property
       */ transition: [
                {
                    transition: [
                        '',
                        'all',
                        'colors',
                        'opacity',
                        'shadow',
                        'transform',
                        'none',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Transition Behavior
       * @see https://tailwindcss.com/docs/transition-behavior
       */ 'transition-behavior': [
                {
                    transition: [
                        'normal',
                        'discrete'
                    ]
                }
            ],
            /**
       * Transition Duration
       * @see https://tailwindcss.com/docs/transition-duration
       */ duration: [
                {
                    duration: [
                        isNumber,
                        'initial',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Transition Timing Function
       * @see https://tailwindcss.com/docs/transition-timing-function
       */ ease: [
                {
                    ease: [
                        'linear',
                        'initial',
                        themeEase,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Transition Delay
       * @see https://tailwindcss.com/docs/transition-delay
       */ delay: [
                {
                    delay: [
                        isNumber,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Animation
       * @see https://tailwindcss.com/docs/animation
       */ animate: [
                {
                    animate: [
                        'none',
                        themeAnimate,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            // ------------------
            // --- Transforms ---
            // ------------------
            /**
       * Backface Visibility
       * @see https://tailwindcss.com/docs/backface-visibility
       */ backface: [
                {
                    backface: [
                        'hidden',
                        'visible'
                    ]
                }
            ],
            /**
       * Perspective
       * @see https://tailwindcss.com/docs/perspective
       */ perspective: [
                {
                    perspective: [
                        themePerspective,
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Perspective Origin
       * @see https://tailwindcss.com/docs/perspective-origin
       */ 'perspective-origin': [
                {
                    'perspective-origin': scalePositionWithArbitrary()
                }
            ],
            /**
       * Rotate
       * @see https://tailwindcss.com/docs/rotate
       */ rotate: [
                {
                    rotate: scaleRotate()
                }
            ],
            /**
       * Rotate X
       * @see https://tailwindcss.com/docs/rotate
       */ 'rotate-x': [
                {
                    'rotate-x': scaleRotate()
                }
            ],
            /**
       * Rotate Y
       * @see https://tailwindcss.com/docs/rotate
       */ 'rotate-y': [
                {
                    'rotate-y': scaleRotate()
                }
            ],
            /**
       * Rotate Z
       * @see https://tailwindcss.com/docs/rotate
       */ 'rotate-z': [
                {
                    'rotate-z': scaleRotate()
                }
            ],
            /**
       * Scale
       * @see https://tailwindcss.com/docs/scale
       */ scale: [
                {
                    scale: scaleScale()
                }
            ],
            /**
       * Scale X
       * @see https://tailwindcss.com/docs/scale
       */ 'scale-x': [
                {
                    'scale-x': scaleScale()
                }
            ],
            /**
       * Scale Y
       * @see https://tailwindcss.com/docs/scale
       */ 'scale-y': [
                {
                    'scale-y': scaleScale()
                }
            ],
            /**
       * Scale Z
       * @see https://tailwindcss.com/docs/scale
       */ 'scale-z': [
                {
                    'scale-z': scaleScale()
                }
            ],
            /**
       * Scale 3D
       * @see https://tailwindcss.com/docs/scale
       */ 'scale-3d': [
                'scale-3d'
            ],
            /**
       * Skew
       * @see https://tailwindcss.com/docs/skew
       */ skew: [
                {
                    skew: scaleSkew()
                }
            ],
            /**
       * Skew X
       * @see https://tailwindcss.com/docs/skew
       */ 'skew-x': [
                {
                    'skew-x': scaleSkew()
                }
            ],
            /**
       * Skew Y
       * @see https://tailwindcss.com/docs/skew
       */ 'skew-y': [
                {
                    'skew-y': scaleSkew()
                }
            ],
            /**
       * Transform
       * @see https://tailwindcss.com/docs/transform
       */ transform: [
                {
                    transform: [
                        isArbitraryVariable,
                        isArbitraryValue,
                        '',
                        'none',
                        'gpu',
                        'cpu'
                    ]
                }
            ],
            /**
       * Transform Origin
       * @see https://tailwindcss.com/docs/transform-origin
       */ 'transform-origin': [
                {
                    origin: scalePositionWithArbitrary()
                }
            ],
            /**
       * Transform Style
       * @see https://tailwindcss.com/docs/transform-style
       */ 'transform-style': [
                {
                    transform: [
                        '3d',
                        'flat'
                    ]
                }
            ],
            /**
       * Translate
       * @see https://tailwindcss.com/docs/translate
       */ translate: [
                {
                    translate: scaleTranslate()
                }
            ],
            /**
       * Translate X
       * @see https://tailwindcss.com/docs/translate
       */ 'translate-x': [
                {
                    'translate-x': scaleTranslate()
                }
            ],
            /**
       * Translate Y
       * @see https://tailwindcss.com/docs/translate
       */ 'translate-y': [
                {
                    'translate-y': scaleTranslate()
                }
            ],
            /**
       * Translate Z
       * @see https://tailwindcss.com/docs/translate
       */ 'translate-z': [
                {
                    'translate-z': scaleTranslate()
                }
            ],
            /**
       * Translate None
       * @see https://tailwindcss.com/docs/translate
       */ 'translate-none': [
                'translate-none'
            ],
            // ---------------------
            // --- Interactivity ---
            // ---------------------
            /**
       * Accent Color
       * @see https://tailwindcss.com/docs/accent-color
       */ accent: [
                {
                    accent: scaleColor()
                }
            ],
            /**
       * Appearance
       * @see https://tailwindcss.com/docs/appearance
       */ appearance: [
                {
                    appearance: [
                        'none',
                        'auto'
                    ]
                }
            ],
            /**
       * Caret Color
       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities
       */ 'caret-color': [
                {
                    caret: scaleColor()
                }
            ],
            /**
       * Color Scheme
       * @see https://tailwindcss.com/docs/color-scheme
       */ 'color-scheme': [
                {
                    scheme: [
                        'normal',
                        'dark',
                        'light',
                        'light-dark',
                        'only-dark',
                        'only-light'
                    ]
                }
            ],
            /**
       * Cursor
       * @see https://tailwindcss.com/docs/cursor
       */ cursor: [
                {
                    cursor: [
                        'auto',
                        'default',
                        'pointer',
                        'wait',
                        'text',
                        'move',
                        'help',
                        'not-allowed',
                        'none',
                        'context-menu',
                        'progress',
                        'cell',
                        'crosshair',
                        'vertical-text',
                        'alias',
                        'copy',
                        'no-drop',
                        'grab',
                        'grabbing',
                        'all-scroll',
                        'col-resize',
                        'row-resize',
                        'n-resize',
                        'e-resize',
                        's-resize',
                        'w-resize',
                        'ne-resize',
                        'nw-resize',
                        'se-resize',
                        'sw-resize',
                        'ew-resize',
                        'ns-resize',
                        'nesw-resize',
                        'nwse-resize',
                        'zoom-in',
                        'zoom-out',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            /**
       * Field Sizing
       * @see https://tailwindcss.com/docs/field-sizing
       */ 'field-sizing': [
                {
                    'field-sizing': [
                        'fixed',
                        'content'
                    ]
                }
            ],
            /**
       * Pointer Events
       * @see https://tailwindcss.com/docs/pointer-events
       */ 'pointer-events': [
                {
                    'pointer-events': [
                        'auto',
                        'none'
                    ]
                }
            ],
            /**
       * Resize
       * @see https://tailwindcss.com/docs/resize
       */ resize: [
                {
                    resize: [
                        'none',
                        '',
                        'y',
                        'x'
                    ]
                }
            ],
            /**
       * Scroll Behavior
       * @see https://tailwindcss.com/docs/scroll-behavior
       */ 'scroll-behavior': [
                {
                    scroll: [
                        'auto',
                        'smooth'
                    ]
                }
            ],
            /**
       * Scroll Margin
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-m': [
                {
                    'scroll-m': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin X
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-mx': [
                {
                    'scroll-mx': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin Y
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-my': [
                {
                    'scroll-my': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin Start
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-ms': [
                {
                    'scroll-ms': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin End
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-me': [
                {
                    'scroll-me': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin Top
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-mt': [
                {
                    'scroll-mt': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin Right
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-mr': [
                {
                    'scroll-mr': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin Bottom
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-mb': [
                {
                    'scroll-mb': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Margin Left
       * @see https://tailwindcss.com/docs/scroll-margin
       */ 'scroll-ml': [
                {
                    'scroll-ml': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-p': [
                {
                    'scroll-p': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding X
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-px': [
                {
                    'scroll-px': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding Y
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-py': [
                {
                    'scroll-py': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding Start
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-ps': [
                {
                    'scroll-ps': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding End
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-pe': [
                {
                    'scroll-pe': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding Top
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-pt': [
                {
                    'scroll-pt': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding Right
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-pr': [
                {
                    'scroll-pr': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding Bottom
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-pb': [
                {
                    'scroll-pb': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Padding Left
       * @see https://tailwindcss.com/docs/scroll-padding
       */ 'scroll-pl': [
                {
                    'scroll-pl': scaleUnambiguousSpacing()
                }
            ],
            /**
       * Scroll Snap Align
       * @see https://tailwindcss.com/docs/scroll-snap-align
       */ 'snap-align': [
                {
                    snap: [
                        'start',
                        'end',
                        'center',
                        'align-none'
                    ]
                }
            ],
            /**
       * Scroll Snap Stop
       * @see https://tailwindcss.com/docs/scroll-snap-stop
       */ 'snap-stop': [
                {
                    snap: [
                        'normal',
                        'always'
                    ]
                }
            ],
            /**
       * Scroll Snap Type
       * @see https://tailwindcss.com/docs/scroll-snap-type
       */ 'snap-type': [
                {
                    snap: [
                        'none',
                        'x',
                        'y',
                        'both'
                    ]
                }
            ],
            /**
       * Scroll Snap Type Strictness
       * @see https://tailwindcss.com/docs/scroll-snap-type
       */ 'snap-strictness': [
                {
                    snap: [
                        'mandatory',
                        'proximity'
                    ]
                }
            ],
            /**
       * Touch Action
       * @see https://tailwindcss.com/docs/touch-action
       */ touch: [
                {
                    touch: [
                        'auto',
                        'none',
                        'manipulation'
                    ]
                }
            ],
            /**
       * Touch Action X
       * @see https://tailwindcss.com/docs/touch-action
       */ 'touch-x': [
                {
                    'touch-pan': [
                        'x',
                        'left',
                        'right'
                    ]
                }
            ],
            /**
       * Touch Action Y
       * @see https://tailwindcss.com/docs/touch-action
       */ 'touch-y': [
                {
                    'touch-pan': [
                        'y',
                        'up',
                        'down'
                    ]
                }
            ],
            /**
       * Touch Action Pinch Zoom
       * @see https://tailwindcss.com/docs/touch-action
       */ 'touch-pz': [
                'touch-pinch-zoom'
            ],
            /**
       * User Select
       * @see https://tailwindcss.com/docs/user-select
       */ select: [
                {
                    select: [
                        'none',
                        'text',
                        'all',
                        'auto'
                    ]
                }
            ],
            /**
       * Will Change
       * @see https://tailwindcss.com/docs/will-change
       */ 'will-change': [
                {
                    'will-change': [
                        'auto',
                        'scroll',
                        'contents',
                        'transform',
                        isArbitraryVariable,
                        isArbitraryValue
                    ]
                }
            ],
            // -----------
            // --- SVG ---
            // -----------
            /**
       * Fill
       * @see https://tailwindcss.com/docs/fill
       */ fill: [
                {
                    fill: [
                        'none',
                        ...scaleColor()
                    ]
                }
            ],
            /**
       * Stroke Width
       * @see https://tailwindcss.com/docs/stroke-width
       */ 'stroke-w': [
                {
                    stroke: [
                        isNumber,
                        isArbitraryVariableLength,
                        isArbitraryLength,
                        isArbitraryNumber
                    ]
                }
            ],
            /**
       * Stroke
       * @see https://tailwindcss.com/docs/stroke
       */ stroke: [
                {
                    stroke: [
                        'none',
                        ...scaleColor()
                    ]
                }
            ],
            // ---------------------
            // --- Accessibility ---
            // ---------------------
            /**
       * Forced Color Adjust
       * @see https://tailwindcss.com/docs/forced-color-adjust
       */ 'forced-color-adjust': [
                {
                    'forced-color-adjust': [
                        'auto',
                        'none'
                    ]
                }
            ]
        },
        conflictingClassGroups: {
            overflow: [
                'overflow-x',
                'overflow-y'
            ],
            overscroll: [
                'overscroll-x',
                'overscroll-y'
            ],
            inset: [
                'inset-x',
                'inset-y',
                'start',
                'end',
                'top',
                'right',
                'bottom',
                'left'
            ],
            'inset-x': [
                'right',
                'left'
            ],
            'inset-y': [
                'top',
                'bottom'
            ],
            flex: [
                'basis',
                'grow',
                'shrink'
            ],
            gap: [
                'gap-x',
                'gap-y'
            ],
            p: [
                'px',
                'py',
                'ps',
                'pe',
                'pt',
                'pr',
                'pb',
                'pl'
            ],
            px: [
                'pr',
                'pl'
            ],
            py: [
                'pt',
                'pb'
            ],
            m: [
                'mx',
                'my',
                'ms',
                'me',
                'mt',
                'mr',
                'mb',
                'ml'
            ],
            mx: [
                'mr',
                'ml'
            ],
            my: [
                'mt',
                'mb'
            ],
            size: [
                'w',
                'h'
            ],
            'font-size': [
                'leading'
            ],
            'fvn-normal': [
                'fvn-ordinal',
                'fvn-slashed-zero',
                'fvn-figure',
                'fvn-spacing',
                'fvn-fraction'
            ],
            'fvn-ordinal': [
                'fvn-normal'
            ],
            'fvn-slashed-zero': [
                'fvn-normal'
            ],
            'fvn-figure': [
                'fvn-normal'
            ],
            'fvn-spacing': [
                'fvn-normal'
            ],
            'fvn-fraction': [
                'fvn-normal'
            ],
            'line-clamp': [
                'display',
                'overflow'
            ],
            rounded: [
                'rounded-s',
                'rounded-e',
                'rounded-t',
                'rounded-r',
                'rounded-b',
                'rounded-l',
                'rounded-ss',
                'rounded-se',
                'rounded-ee',
                'rounded-es',
                'rounded-tl',
                'rounded-tr',
                'rounded-br',
                'rounded-bl'
            ],
            'rounded-s': [
                'rounded-ss',
                'rounded-es'
            ],
            'rounded-e': [
                'rounded-se',
                'rounded-ee'
            ],
            'rounded-t': [
                'rounded-tl',
                'rounded-tr'
            ],
            'rounded-r': [
                'rounded-tr',
                'rounded-br'
            ],
            'rounded-b': [
                'rounded-br',
                'rounded-bl'
            ],
            'rounded-l': [
                'rounded-tl',
                'rounded-bl'
            ],
            'border-spacing': [
                'border-spacing-x',
                'border-spacing-y'
            ],
            'border-w': [
                'border-w-x',
                'border-w-y',
                'border-w-s',
                'border-w-e',
                'border-w-t',
                'border-w-r',
                'border-w-b',
                'border-w-l'
            ],
            'border-w-x': [
                'border-w-r',
                'border-w-l'
            ],
            'border-w-y': [
                'border-w-t',
                'border-w-b'
            ],
            'border-color': [
                'border-color-x',
                'border-color-y',
                'border-color-s',
                'border-color-e',
                'border-color-t',
                'border-color-r',
                'border-color-b',
                'border-color-l'
            ],
            'border-color-x': [
                'border-color-r',
                'border-color-l'
            ],
            'border-color-y': [
                'border-color-t',
                'border-color-b'
            ],
            translate: [
                'translate-x',
                'translate-y',
                'translate-none'
            ],
            'translate-none': [
                'translate',
                'translate-x',
                'translate-y',
                'translate-z'
            ],
            'scroll-m': [
                'scroll-mx',
                'scroll-my',
                'scroll-ms',
                'scroll-me',
                'scroll-mt',
                'scroll-mr',
                'scroll-mb',
                'scroll-ml'
            ],
            'scroll-mx': [
                'scroll-mr',
                'scroll-ml'
            ],
            'scroll-my': [
                'scroll-mt',
                'scroll-mb'
            ],
            'scroll-p': [
                'scroll-px',
                'scroll-py',
                'scroll-ps',
                'scroll-pe',
                'scroll-pt',
                'scroll-pr',
                'scroll-pb',
                'scroll-pl'
            ],
            'scroll-px': [
                'scroll-pr',
                'scroll-pl'
            ],
            'scroll-py': [
                'scroll-pt',
                'scroll-pb'
            ],
            touch: [
                'touch-x',
                'touch-y',
                'touch-pz'
            ],
            'touch-x': [
                'touch'
            ],
            'touch-y': [
                'touch'
            ],
            'touch-pz': [
                'touch'
            ]
        },
        conflictingClassGroupModifiers: {
            'font-size': [
                'leading'
            ]
        },
        orderSensitiveModifiers: [
            '*',
            '**',
            'after',
            'backdrop',
            'before',
            'details-content',
            'file',
            'first-letter',
            'first-line',
            'marker',
            'placeholder',
            'selection'
        ]
    };
};
/**
 * @param baseConfig Config where other config will be merged into. This object will be mutated.
 * @param configExtension Partial config to merge into the `baseConfig`.
 */ const mergeConfigs = (baseConfig, param)=>{
    let { cacheSize, prefix, experimentalParseClassName, extend = {}, override = {} } = param;
    overrideProperty(baseConfig, 'cacheSize', cacheSize);
    overrideProperty(baseConfig, 'prefix', prefix);
    overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);
    overrideConfigProperties(baseConfig.theme, override.theme);
    overrideConfigProperties(baseConfig.classGroups, override.classGroups);
    overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);
    overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);
    overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);
    mergeConfigProperties(baseConfig.theme, extend.theme);
    mergeConfigProperties(baseConfig.classGroups, extend.classGroups);
    mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);
    mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);
    mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');
    return baseConfig;
};
const overrideProperty = (baseObject, overrideKey, overrideValue)=>{
    if (overrideValue !== undefined) {
        baseObject[overrideKey] = overrideValue;
    }
};
const overrideConfigProperties = (baseObject, overrideObject)=>{
    if (overrideObject) {
        for(const key in overrideObject){
            overrideProperty(baseObject, key, overrideObject[key]);
        }
    }
};
const mergeConfigProperties = (baseObject, mergeObject)=>{
    if (mergeObject) {
        for(const key in mergeObject){
            mergeArrayProperties(baseObject, mergeObject, key);
        }
    }
};
const mergeArrayProperties = (baseObject, mergeObject, key)=>{
    const mergeValue = mergeObject[key];
    if (mergeValue !== undefined) {
        baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;
    }
};
const extendTailwindMerge = function(configExtension) {
    for(var _len = arguments.length, createConfig = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
        createConfig[_key - 1] = arguments[_key];
    }
    return typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(()=>mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);
};
const twMerge = /*#__PURE__*/ createTailwindMerge(getDefaultConfig);
;
 //# sourceMappingURL=bundle-mjs.mjs.map
}),
}]);

//# sourceMappingURL=node_modules_df13b3ed._.js.map