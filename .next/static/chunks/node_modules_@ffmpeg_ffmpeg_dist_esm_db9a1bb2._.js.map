{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/%40ffmpeg/ffmpeg/dist/esm/const.js"], "sourcesContent": ["export const MIME_TYPE_JAVASCRIPT = \"text/javascript\";\nexport const MIME_TYPE_WASM = \"application/wasm\";\nexport const CORE_VERSION = \"0.12.9\";\nexport const CORE_URL = `https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/umd/ffmpeg-core.js`;\nexport var FFMessageType;\n(function (FFMessageType) {\n    FFMessageType[\"LOAD\"] = \"LOAD\";\n    FFMessageType[\"EXEC\"] = \"EXEC\";\n    FFMessageType[\"FFPROBE\"] = \"FFPROBE\";\n    FFMessageType[\"WRITE_FILE\"] = \"WRITE_FILE\";\n    FFMessageType[\"READ_FILE\"] = \"READ_FILE\";\n    FFMessageType[\"DELETE_FILE\"] = \"DELETE_FILE\";\n    FFMessageType[\"RENAME\"] = \"RENAME\";\n    FFMessageType[\"CREATE_DIR\"] = \"CREATE_DIR\";\n    FFMessageType[\"LIST_DIR\"] = \"LIST_DIR\";\n    FFMessageType[\"DELETE_DIR\"] = \"DELETE_DIR\";\n    FFMessageType[\"ERROR\"] = \"ERROR\";\n    FFMessageType[\"DOWNLOAD\"] = \"DOWNLOAD\";\n    FFMessageType[\"PROGRESS\"] = \"PROGRESS\";\n    FFMessageType[\"LOG\"] = \"LOG\";\n    FFMessageType[\"MOUNT\"] = \"MOUNT\";\n    FFMessageType[\"UNMOUNT\"] = \"UNMOUNT\";\n})(FFMessageType || (FFMessageType = {}));\n"], "names": [], "mappings": ";;;;;;;AAAO,MAAM,uBAAuB;AAC7B,MAAM,iBAAiB;AACvB,MAAM,eAAe;AACrB,MAAM,WAAW,AAAC,kCAA8C,OAAb,cAAa;AAChE,IAAI;AACX,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,OAAO,GAAG;IACxB,aAAa,CAAC,OAAO,GAAG;IACxB,aAAa,CAAC,UAAU,GAAG;IAC3B,aAAa,CAAC,aAAa,GAAG;IAC9B,aAAa,CAAC,YAAY,GAAG;IAC7B,aAAa,CAAC,cAAc,GAAG;IAC/B,aAAa,CAAC,SAAS,GAAG;IAC1B,aAAa,CAAC,aAAa,GAAG;IAC9B,aAAa,CAAC,WAAW,GAAG;IAC5B,aAAa,CAAC,aAAa,GAAG;IAC9B,aAAa,CAAC,QAAQ,GAAG;IACzB,aAAa,CAAC,WAAW,GAAG;IAC5B,aAAa,CAAC,WAAW,GAAG;IAC5B,aAAa,CAAC,MAAM,GAAG;IACvB,aAAa,CAAC,QAAQ,GAAG;IACzB,aAAa,CAAC,UAAU,GAAG;AAC/B,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/%40ffmpeg/ffmpeg/dist/esm/errors.js"], "sourcesContent": ["export const ERROR_UNKNOWN_MESSAGE_TYPE = new Error(\"unknown message type\");\nexport const ERROR_NOT_LOADED = new Error(\"ffmpeg is not loaded, call `await ffmpeg.load()` first\");\nexport const ERROR_TERMINATED = new Error(\"called FFmpeg.terminate()\");\nexport const ERROR_IMPORT_FAILURE = new Error(\"failed to import ffmpeg-core.js\");\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,6BAA6B,IAAI,MAAM;AAC7C,MAAM,mBAAmB,IAAI,MAAM;AACnC,MAAM,mBAAmB,IAAI,MAAM;AACnC,MAAM,uBAAuB,IAAI,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/node_modules/%40ffmpeg/ffmpeg/dist/esm/worker.js"], "sourcesContent": ["/// <reference no-default-lib=\"true\" />\n/// <reference lib=\"esnext\" />\n/// <reference lib=\"webworker\" />\nimport { CORE_URL, FFMessageType } from \"./const.js\";\nimport { ERROR_UNKNOWN_MESSAGE_TYPE, ERROR_NOT_LOADED, ERROR_IMPORT_FAILURE, } from \"./errors.js\";\nlet ffmpeg;\nconst load = async ({ coreURL: _coreURL, wasmURL: _wasmURL, workerURL: _workerURL, }) => {\n    const first = !ffmpeg;\n    try {\n        if (!_coreURL)\n            _coreURL = CORE_URL;\n        // when web worker type is `classic`.\n        importScripts(_coreURL);\n    }\n    catch {\n        if (!_coreURL || _coreURL === CORE_URL)\n            _coreURL = CORE_URL.replace('/umd/', '/esm/');\n        // when web worker type is `module`.\n        self.createFFmpegCore = (await import(\n        /* @vite-ignore */ _coreURL)).default;\n        if (!self.createFFmpegCore) {\n            throw ERROR_IMPORT_FAILURE;\n        }\n    }\n    const coreURL = _coreURL;\n    const wasmURL = _wasmURL ? _wasmURL : _coreURL.replace(/.js$/g, \".wasm\");\n    const workerURL = _workerURL\n        ? _workerURL\n        : _coreURL.replace(/.js$/g, \".worker.js\");\n    ffmpeg = await self.createFFmpegCore({\n        // Fix `Overload resolution failed.` when using multi-threaded ffmpeg-core.\n        // Encoded wasmURL and workerURL in the URL as a hack to fix locateFile issue.\n        mainScriptUrlOrBlob: `${coreURL}#${btoa(JSON.stringify({ wasmURL, workerURL }))}`,\n    });\n    ffmpeg.setLogger((data) => self.postMessage({ type: FFMessageType.LOG, data }));\n    ffmpeg.setProgress((data) => self.postMessage({\n        type: FFMessageType.PROGRESS,\n        data,\n    }));\n    return first;\n};\nconst exec = ({ args, timeout = -1 }) => {\n    ffmpeg.setTimeout(timeout);\n    ffmpeg.exec(...args);\n    const ret = ffmpeg.ret;\n    ffmpeg.reset();\n    return ret;\n};\nconst ffprobe = ({ args, timeout = -1 }) => {\n    ffmpeg.setTimeout(timeout);\n    ffmpeg.ffprobe(...args);\n    const ret = ffmpeg.ret;\n    ffmpeg.reset();\n    return ret;\n};\nconst writeFile = ({ path, data }) => {\n    ffmpeg.FS.writeFile(path, data);\n    return true;\n};\nconst readFile = ({ path, encoding }) => ffmpeg.FS.readFile(path, { encoding });\n// TODO: check if deletion works.\nconst deleteFile = ({ path }) => {\n    ffmpeg.FS.unlink(path);\n    return true;\n};\nconst rename = ({ oldPath, newPath }) => {\n    ffmpeg.FS.rename(oldPath, newPath);\n    return true;\n};\n// TODO: check if creation works.\nconst createDir = ({ path }) => {\n    ffmpeg.FS.mkdir(path);\n    return true;\n};\nconst listDir = ({ path }) => {\n    const names = ffmpeg.FS.readdir(path);\n    const nodes = [];\n    for (const name of names) {\n        const stat = ffmpeg.FS.stat(`${path}/${name}`);\n        const isDir = ffmpeg.FS.isDir(stat.mode);\n        nodes.push({ name, isDir });\n    }\n    return nodes;\n};\n// TODO: check if deletion works.\nconst deleteDir = ({ path }) => {\n    ffmpeg.FS.rmdir(path);\n    return true;\n};\nconst mount = ({ fsType, options, mountPoint }) => {\n    const str = fsType;\n    const fs = ffmpeg.FS.filesystems[str];\n    if (!fs)\n        return false;\n    ffmpeg.FS.mount(fs, options, mountPoint);\n    return true;\n};\nconst unmount = ({ mountPoint }) => {\n    ffmpeg.FS.unmount(mountPoint);\n    return true;\n};\nself.onmessage = async ({ data: { id, type, data: _data }, }) => {\n    const trans = [];\n    let data;\n    try {\n        if (type !== FFMessageType.LOAD && !ffmpeg)\n            throw ERROR_NOT_LOADED; // eslint-disable-line\n        switch (type) {\n            case FFMessageType.LOAD:\n                data = await load(_data);\n                break;\n            case FFMessageType.EXEC:\n                data = exec(_data);\n                break;\n            case FFMessageType.FFPROBE:\n                data = ffprobe(_data);\n                break;\n            case FFMessageType.WRITE_FILE:\n                data = writeFile(_data);\n                break;\n            case FFMessageType.READ_FILE:\n                data = readFile(_data);\n                break;\n            case FFMessageType.DELETE_FILE:\n                data = deleteFile(_data);\n                break;\n            case FFMessageType.RENAME:\n                data = rename(_data);\n                break;\n            case FFMessageType.CREATE_DIR:\n                data = createDir(_data);\n                break;\n            case FFMessageType.LIST_DIR:\n                data = listDir(_data);\n                break;\n            case FFMessageType.DELETE_DIR:\n                data = deleteDir(_data);\n                break;\n            case FFMessageType.MOUNT:\n                data = mount(_data);\n                break;\n            case FFMessageType.UNMOUNT:\n                data = unmount(_data);\n                break;\n            default:\n                throw ERROR_UNKNOWN_MESSAGE_TYPE;\n        }\n    }\n    catch (e) {\n        self.postMessage({\n            id,\n            type: FFMessageType.ERROR,\n            data: e.toString(),\n        });\n        return;\n    }\n    if (data instanceof Uint8Array) {\n        trans.push(data.buffer);\n    }\n    self.postMessage({ id, type, data }, trans);\n};\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,8BAA8B;AAC9B,iCAAiC;;AACjC;AACA;;;AACA,IAAI;AACJ,MAAM,OAAO;QAAO,EAAE,SAAS,QAAQ,EAAE,SAAS,QAAQ,EAAE,WAAW,UAAU,EAAG;IAChF,MAAM,QAAQ,CAAC;IACf,IAAI;QACA,IAAI,CAAC,UACD,WAAW,6JAAA,CAAA,WAAQ;QACvB,qCAAqC;QACrC,cAAc;IAClB,EACA,UAAM;QACF,IAAI,CAAC,YAAY,aAAa,6JAAA,CAAA,WAAQ,EAClC,WAAW,6JAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SAAS;QACzC,oCAAoC;QACpC,KAAK,gBAAgB,GAAG,CAAC;;;;UACG,EAAE,OAAO;QACrC,IAAI,CAAC,KAAK,gBAAgB,EAAE;YACxB,MAAM,8JAAA,CAAA,uBAAoB;QAC9B;IACJ;IACA,MAAM,UAAU;IAChB,MAAM,UAAU,WAAW,WAAW,SAAS,OAAO,CAAC,SAAS;IAChE,MAAM,YAAY,aACZ,aACA,SAAS,OAAO,CAAC,SAAS;IAChC,SAAS,MAAM,KAAK,gBAAgB,CAAC;QACjC,2EAA2E;QAC3E,8EAA8E;QAC9E,qBAAqB,AAAC,GAAa,OAAX,SAAQ,KAAgD,OAA7C,KAAK,KAAK,SAAS,CAAC;YAAE;YAAS;QAAU;IAChF;IACA,OAAO,SAAS,CAAC,CAAC,OAAS,KAAK,WAAW,CAAC;YAAE,MAAM,6JAAA,CAAA,gBAAa,CAAC,GAAG;YAAE;QAAK;IAC5E,OAAO,WAAW,CAAC,CAAC,OAAS,KAAK,WAAW,CAAC;YAC1C,MAAM,6JAAA,CAAA,gBAAa,CAAC,QAAQ;YAC5B;QACJ;IACA,OAAO;AACX;AACA,MAAM,OAAO;QAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE;IAChC,OAAO,UAAU,CAAC;IAClB,OAAO,IAAI,IAAI;IACf,MAAM,MAAM,OAAO,GAAG;IACtB,OAAO,KAAK;IACZ,OAAO;AACX;AACA,MAAM,UAAU;QAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE;IACnC,OAAO,UAAU,CAAC;IAClB,OAAO,OAAO,IAAI;IAClB,MAAM,MAAM,OAAO,GAAG;IACtB,OAAO,KAAK;IACZ,OAAO;AACX;AACA,MAAM,YAAY;QAAC,EAAE,IAAI,EAAE,IAAI,EAAE;IAC7B,OAAO,EAAE,CAAC,SAAS,CAAC,MAAM;IAC1B,OAAO;AACX;AACA,MAAM,WAAW;QAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;WAAK,OAAO,EAAE,CAAC,QAAQ,CAAC,MAAM;QAAE;IAAS;;AAC7E,iCAAiC;AACjC,MAAM,aAAa;QAAC,EAAE,IAAI,EAAE;IACxB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,OAAO;AACX;AACA,MAAM,SAAS;QAAC,EAAE,OAAO,EAAE,OAAO,EAAE;IAChC,OAAO,EAAE,CAAC,MAAM,CAAC,SAAS;IAC1B,OAAO;AACX;AACA,iCAAiC;AACjC,MAAM,YAAY;QAAC,EAAE,IAAI,EAAE;IACvB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,OAAO;AACX;AACA,MAAM,UAAU;QAAC,EAAE,IAAI,EAAE;IACrB,MAAM,QAAQ,OAAO,EAAE,CAAC,OAAO,CAAC;IAChC,MAAM,QAAQ,EAAE;IAChB,KAAK,MAAM,QAAQ,MAAO;QACtB,MAAM,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,AAAC,GAAU,OAAR,MAAK,KAAQ,OAAL;QACvC,MAAM,QAAQ,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI;QACvC,MAAM,IAAI,CAAC;YAAE;YAAM;QAAM;IAC7B;IACA,OAAO;AACX;AACA,iCAAiC;AACjC,MAAM,YAAY;QAAC,EAAE,IAAI,EAAE;IACvB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,OAAO;AACX;AACA,MAAM,QAAQ;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE;IAC1C,MAAM,MAAM;IACZ,MAAM,KAAK,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI;IACrC,IAAI,CAAC,IACD,OAAO;IACX,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,SAAS;IAC7B,OAAO;AACX;AACA,MAAM,UAAU;QAAC,EAAE,UAAU,EAAE;IAC3B,OAAO,EAAE,CAAC,OAAO,CAAC;IAClB,OAAO;AACX;AACA,KAAK,SAAS,GAAG;QAAO,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,KAAK,EAAE,EAAG;IACxD,MAAM,QAAQ,EAAE;IAChB,IAAI;IACJ,IAAI;QACA,IAAI,SAAS,6JAAA,CAAA,gBAAa,CAAC,IAAI,IAAI,CAAC,QAChC,MAAM,8JAAA,CAAA,mBAAgB,EAAE,sBAAsB;QAClD,OAAQ;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,IAAI;gBACnB,OAAO,MAAM,KAAK;gBAClB;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,IAAI;gBACnB,OAAO,KAAK;gBACZ;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,OAAO;gBACtB,OAAO,QAAQ;gBACf;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,UAAU;gBACzB,OAAO,UAAU;gBACjB;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,SAAS;gBACxB,OAAO,SAAS;gBAChB;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,WAAW;gBAC1B,OAAO,WAAW;gBAClB;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,MAAM;gBACrB,OAAO,OAAO;gBACd;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,UAAU;gBACzB,OAAO,UAAU;gBACjB;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,QAAQ;gBACvB,OAAO,QAAQ;gBACf;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,UAAU;gBACzB,OAAO,UAAU;gBACjB;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,KAAK;gBACpB,OAAO,MAAM;gBACb;YACJ,KAAK,6JAAA,CAAA,gBAAa,CAAC,OAAO;gBACtB,OAAO,QAAQ;gBACf;YACJ;gBACI,MAAM,8JAAA,CAAA,6BAA0B;QACxC;IACJ,EACA,OAAO,GAAG;QACN,KAAK,WAAW,CAAC;YACb;YACA,MAAM,6JAAA,CAAA,gBAAa,CAAC,KAAK;YACzB,MAAM,EAAE,QAAQ;QACpB;QACA;IACJ;IACA,IAAI,gBAAgB,YAAY;QAC5B,MAAM,IAAI,CAAC,KAAK,MAAM;IAC1B;IACA,KAAK,WAAW,CAAC;QAAE;QAAI;QAAM;IAAK,GAAG;AACzC", "ignoreList": [0], "debugId": null}}]}