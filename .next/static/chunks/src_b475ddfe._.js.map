{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function formatDuration(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const secs = Math.floor(seconds % 60)\n  \n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n  }\n  return `${minutes}:${secs.toString().padStart(2, '0')}`\n}\n\nexport function getFileExtension(filename: string): string {\n  return filename.split('.').pop()?.toLowerCase() || ''\n}\n\nexport function isImageFile(file: File): boolean {\n  return file.type.startsWith('image/')\n}\n\nexport function isVideoFile(file: File): boolean {\n  return file.type.startsWith('video/')\n}\n\nexport function isGifFile(file: File): boolean {\n  return file.type === 'image/gif'\n}\n\nexport function getSupportedImageFormats(): string[] {\n  return ['jpeg', 'jpg', 'png', 'webp', 'gif', 'bmp', 'tiff']\n}\n\nexport function getSupportedVideoFormats(): string[] {\n  return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp']\n}\n\nexport function generateUniqueId(): string {\n  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36)\n}\n\nexport function downloadFile(blob: Blob, filename: string): void {\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n\nexport function createImagePreview(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader()\n    reader.onload = (e) => resolve(e.target?.result as string)\n    reader.onerror = reject\n    reader.readAsDataURL(file)\n  })\n}\n\nexport function createVideoPreview(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const video = document.createElement('video')\n    const canvas = document.createElement('canvas')\n    const ctx = canvas.getContext('2d')\n    \n    video.onloadedmetadata = () => {\n      canvas.width = video.videoWidth\n      canvas.height = video.videoHeight\n      video.currentTime = 1 // Get frame at 1 second\n    }\n    \n    video.onseeked = () => {\n      if (ctx) {\n        ctx.drawImage(video, 0, 0)\n        resolve(canvas.toDataURL())\n      } else {\n        reject(new Error('Could not get canvas context'))\n      }\n    }\n    \n    video.onerror = reject\n    video.src = URL.createObjectURL(file)\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAElC,IAAI,QAAQ,GAAG;QACb,OAAO,AAAC,GAAW,OAAT,OAAM,KAA0C,OAAvC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACxF;IACA,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;AACnD;AAEO,SAAS,iBAAiB,QAAgB;QACxC;IAAP,OAAO,EAAA,sBAAA,SAAS,KAAK,CAAC,KAAK,GAAG,gBAAvB,0CAAA,oBAA2B,WAAW,OAAM;AACrD;AAEO,SAAS,YAAY,IAAU;IACpC,OAAO,KAAK,IAAI,CAAC,UAAU,CAAC;AAC9B;AAEO,SAAS,YAAY,IAAU;IACpC,OAAO,KAAK,IAAI,CAAC,UAAU,CAAC;AAC9B;AAEO,SAAS,UAAU,IAAU;IAClC,OAAO,KAAK,IAAI,KAAK;AACvB;AAEO,SAAS;IACd,OAAO;QAAC;QAAQ;QAAO;QAAO;QAAQ;QAAO;QAAO;KAAO;AAC7D;AAEO,SAAS;IACd,OAAO;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAQ;QAAO;KAAM;AAClE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,aAAa,IAAU,EAAE,QAAgB;IACvD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAEO,SAAS,mBAAmB,IAAU;IAC3C,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;gBAAc;mBAAR,SAAQ,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;;QAC/C,OAAO,OAAO,GAAG;QACjB,OAAO,aAAa,CAAC;IACvB;AACF;AAEO,SAAS,mBAAmB,IAAU;IAC3C,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,MAAM,gBAAgB,GAAG;YACvB,OAAO,KAAK,GAAG,MAAM,UAAU;YAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;YACjC,MAAM,WAAW,GAAG,GAAE,wBAAwB;QAChD;QAEA,MAAM,QAAQ,GAAG;YACf,IAAI,KAAK;gBACP,IAAI,SAAS,CAAC,OAAO,GAAG;gBACxB,QAAQ,OAAO,SAAS;YAC1B,OAAO;gBACL,OAAO,IAAI,MAAM;YACnB;QACF;QAEA,MAAM,OAAO,GAAG;QAChB,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC;IAClC;AACF", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/services/imageCompression.ts"], "sourcesContent": ["import imageCompression from 'browser-image-compression'\nimport { CompressionResult, ImageCompressionOptions } from '@/types'\n\nexport class ImageCompressionService {\n  static async compressImage(\n    file: File,\n    options: ImageCompressionOptions,\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    try {\n      // Determine output format\n      let outputFormat = options.outputFormat || file.type.replace('image/', '')\n\n      // Handle special cases\n      if (outputFormat === 'jpg') outputFormat = 'jpeg'\n\n      const compressionOptions = {\n        maxSizeMB: 10, // Maximum file size in MB\n        maxWidthOrHeight: Math.max(options.maxWidth || 1920, options.maxHeight || 1080),\n        useWebWorker: true,\n        fileType: `image/${outputFormat}`,\n        initialQuality: options.quality,\n        alwaysKeepResolution: !options.maintainAspectRatio,\n        preserveExif: !options.removeMetadata,\n        onProgress: (progress: number) => {\n          onProgress?.(progress)\n        }\n      }\n\n      // Handle transparency preservation for PNG\n      if (options.preserveTransparency && (file.type === 'image/png' || outputFormat === 'png')) {\n        compressionOptions.fileType = 'image/png'\n        outputFormat = 'png'\n      }\n\n      // For WebP, ensure proper handling\n      if (outputFormat === 'webp') {\n        compressionOptions.fileType = 'image/webp'\n      }\n\n      console.log('Compression options:', compressionOptions)\n\n      const compressedFile = await imageCompression(file, compressionOptions)\n\n      const compressionRatio = ((file.size - compressedFile.size) / file.size) * 100\n\n      return {\n        success: true,\n        originalSize: file.size,\n        compressedSize: compressedFile.size,\n        compressionRatio,\n        blob: compressedFile\n      }\n    } catch (error) {\n      console.error('Image compression failed:', error)\n      return {\n        success: false,\n        originalSize: file.size,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      }\n    }\n  }\n\n  static async convertFormat(\n    file: File,\n    targetFormat: string,\n    quality: number = 0.8\n  ): Promise<CompressionResult> {\n    try {\n      const canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      const img = new Image()\n\n      return new Promise((resolve) => {\n        img.onload = () => {\n          canvas.width = img.width\n          canvas.height = img.height\n          \n          if (ctx) {\n            // Handle transparency for PNG\n            if (targetFormat === 'png') {\n              ctx.clearRect(0, 0, canvas.width, canvas.height)\n            } else {\n              // Fill with white background for JPEG/WebP\n              ctx.fillStyle = '#FFFFFF'\n              ctx.fillRect(0, 0, canvas.width, canvas.height)\n            }\n            \n            ctx.drawImage(img, 0, 0)\n            \n            canvas.toBlob((blob) => {\n              if (blob) {\n                const compressionRatio = ((file.size - blob.size) / file.size) * 100\n                resolve({\n                  success: true,\n                  originalSize: file.size,\n                  compressedSize: blob.size,\n                  compressionRatio,\n                  blob\n                })\n              } else {\n                resolve({\n                  success: false,\n                  originalSize: file.size,\n                  compressedSize: 0,\n                  compressionRatio: 0,\n                  error: 'Failed to convert image format'\n                })\n              }\n            }, `image/${targetFormat}`, quality)\n          }\n        }\n\n        img.onerror = () => {\n          resolve({\n            success: false,\n            originalSize: file.size,\n            compressedSize: 0,\n            compressionRatio: 0,\n            error: 'Failed to load image'\n          })\n        }\n\n        img.src = URL.createObjectURL(file)\n      })\n    } catch (error) {\n      return {\n        success: false,\n        originalSize: file.size,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      }\n    }\n  }\n\n  static async resizeImage(\n    file: File,\n    maxWidth: number,\n    maxHeight: number,\n    maintainAspectRatio: boolean = true\n  ): Promise<CompressionResult> {\n    try {\n      const canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      const img = new Image()\n\n      return new Promise((resolve) => {\n        img.onload = () => {\n          let { width, height } = img\n\n          if (maintainAspectRatio) {\n            const aspectRatio = width / height\n            if (width > maxWidth) {\n              width = maxWidth\n              height = width / aspectRatio\n            }\n            if (height > maxHeight) {\n              height = maxHeight\n              width = height * aspectRatio\n            }\n          } else {\n            width = Math.min(width, maxWidth)\n            height = Math.min(height, maxHeight)\n          }\n\n          canvas.width = width\n          canvas.height = height\n\n          if (ctx) {\n            ctx.drawImage(img, 0, 0, width, height)\n            \n            canvas.toBlob((blob) => {\n              if (blob) {\n                const compressionRatio = ((file.size - blob.size) / file.size) * 100\n                resolve({\n                  success: true,\n                  originalSize: file.size,\n                  compressedSize: blob.size,\n                  compressionRatio,\n                  blob\n                })\n              } else {\n                resolve({\n                  success: false,\n                  originalSize: file.size,\n                  compressedSize: 0,\n                  compressionRatio: 0,\n                  error: 'Failed to resize image'\n                })\n              }\n            }, file.type, 0.9)\n          }\n        }\n\n        img.onerror = () => {\n          resolve({\n            success: false,\n            originalSize: file.size,\n            compressedSize: 0,\n            compressionRatio: 0,\n            error: 'Failed to load image'\n          })\n        }\n\n        img.src = URL.createObjectURL(file)\n      })\n    } catch (error) {\n      return {\n        success: false,\n        originalSize: file.size,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACX,aAAa,cACX,IAAU,EACV,OAAgC,EAChC,UAAuC,EACX;QAC5B,IAAI;YACF,0BAA0B;YAC1B,IAAI,eAAe,QAAQ,YAAY,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU;YAEvE,uBAAuB;YACvB,IAAI,iBAAiB,OAAO,eAAe;YAE3C,MAAM,qBAAqB;gBACzB,WAAW;gBACX,kBAAkB,KAAK,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,QAAQ,SAAS,IAAI;gBAC1E,cAAc;gBACd,UAAU,AAAC,SAAqB,OAAb;gBACnB,gBAAgB,QAAQ,OAAO;gBAC/B,sBAAsB,CAAC,QAAQ,mBAAmB;gBAClD,cAAc,CAAC,QAAQ,cAAc;gBACrC,YAAY,CAAC;oBACX,uBAAA,iCAAA,WAAa;gBACf;YACF;YAEA,2CAA2C;YAC3C,IAAI,QAAQ,oBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,eAAe,iBAAiB,KAAK,GAAG;gBACzF,mBAAmB,QAAQ,GAAG;gBAC9B,eAAe;YACjB;YAEA,mCAAmC;YACnC,IAAI,iBAAiB,QAAQ;gBAC3B,mBAAmB,QAAQ,GAAG;YAChC;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,MAAM,iBAAiB,MAAM,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE,MAAM;YAEpD,MAAM,mBAAmB,AAAC,CAAC,KAAK,IAAI,GAAG,eAAe,IAAI,IAAI,KAAK,IAAI,GAAI;YAE3E,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB,eAAe,IAAI;gBACnC;gBACA,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,cACX,IAAU,EACV,YAAoB,EAEQ;YAD5B,UAAA,iEAAkB;QAElB,IAAI;YACF,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,MAAM,IAAI;YAEhB,OAAO,IAAI,QAAQ,CAAC;gBAClB,IAAI,MAAM,GAAG;oBACX,OAAO,KAAK,GAAG,IAAI,KAAK;oBACxB,OAAO,MAAM,GAAG,IAAI,MAAM;oBAE1B,IAAI,KAAK;wBACP,8BAA8B;wBAC9B,IAAI,iBAAiB,OAAO;4BAC1B,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;wBACjD,OAAO;4BACL,2CAA2C;4BAC3C,IAAI,SAAS,GAAG;4BAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;wBAChD;wBAEA,IAAI,SAAS,CAAC,KAAK,GAAG;wBAEtB,OAAO,MAAM,CAAC,CAAC;4BACb,IAAI,MAAM;gCACR,MAAM,mBAAmB,AAAC,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,GAAI;gCACjE,QAAQ;oCACN,SAAS;oCACT,cAAc,KAAK,IAAI;oCACvB,gBAAgB,KAAK,IAAI;oCACzB;oCACA;gCACF;4BACF,OAAO;gCACL,QAAQ;oCACN,SAAS;oCACT,cAAc,KAAK,IAAI;oCACvB,gBAAgB;oCAChB,kBAAkB;oCAClB,OAAO;gCACT;4BACF;wBACF,GAAG,AAAC,SAAqB,OAAb,eAAgB;oBAC9B;gBACF;gBAEA,IAAI,OAAO,GAAG;oBACZ,QAAQ;wBACN,SAAS;wBACT,cAAc,KAAK,IAAI;wBACvB,gBAAgB;wBAChB,kBAAkB;wBAClB,OAAO;oBACT;gBACF;gBAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;YAChC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,YACX,IAAU,EACV,QAAgB,EAChB,SAAiB,EAEW;YAD5B,sBAAA,iEAA+B;QAE/B,IAAI;YACF,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,MAAM,IAAI;YAEhB,OAAO,IAAI,QAAQ,CAAC;gBAClB,IAAI,MAAM,GAAG;oBACX,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;oBAExB,IAAI,qBAAqB;wBACvB,MAAM,cAAc,QAAQ;wBAC5B,IAAI,QAAQ,UAAU;4BACpB,QAAQ;4BACR,SAAS,QAAQ;wBACnB;wBACA,IAAI,SAAS,WAAW;4BACtB,SAAS;4BACT,QAAQ,SAAS;wBACnB;oBACF,OAAO;wBACL,QAAQ,KAAK,GAAG,CAAC,OAAO;wBACxB,SAAS,KAAK,GAAG,CAAC,QAAQ;oBAC5B;oBAEA,OAAO,KAAK,GAAG;oBACf,OAAO,MAAM,GAAG;oBAEhB,IAAI,KAAK;wBACP,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;wBAEhC,OAAO,MAAM,CAAC,CAAC;4BACb,IAAI,MAAM;gCACR,MAAM,mBAAmB,AAAC,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,GAAI;gCACjE,QAAQ;oCACN,SAAS;oCACT,cAAc,KAAK,IAAI;oCACvB,gBAAgB,KAAK,IAAI;oCACzB;oCACA;gCACF;4BACF,OAAO;gCACL,QAAQ;oCACN,SAAS;oCACT,cAAc,KAAK,IAAI;oCACvB,gBAAgB;oCAChB,kBAAkB;oCAClB,OAAO;gCACT;4BACF;wBACF,GAAG,KAAK,IAAI,EAAE;oBAChB;gBACF;gBAEA,IAAI,OAAO,GAAG;oBACZ,QAAQ;wBACN,SAAS;wBACT,cAAc,KAAK,IAAI;wBACvB,gBAAgB;wBAChB,kBAAkB;wBAClB,OAAO;oBACT;gBACF;gBAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;YAChC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/services/videoCompression.ts"], "sourcesContent": ["import { FFmpeg } from '@ffmpeg/ffmpeg'\nimport { fetchFile, toBlobURL } from '@ffmpeg/util'\nimport { CompressionResult, VideoCompressionOptions } from '@/types'\n\nexport class VideoCompressionService {\n  private static ffmpeg: FFmpeg | null = null\n  private static isLoaded = false\n\n  static async initialize(): Promise<void> {\n    if (this.isLoaded) return\n\n    try {\n      this.ffmpeg = new FFmpeg()\n\n      // Set up logging\n      this.ffmpeg.on('log', ({ message }) => {\n        console.log('FFmpeg:', message)\n      })\n\n      // Load FFmpeg with CDN URLs - using a more reliable CDN\n      const baseURL = 'https://unpkg.com/@ffmpeg/core-mt@0.12.6/dist/esm'\n      await this.ffmpeg.load({\n        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),\n        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),\n        workerURL: await toBlobURL(`${baseURL}/ffmpeg-core.worker.js`, 'text/javascript'),\n      })\n\n      this.isLoaded = true\n      console.log('FFmpeg initialized successfully')\n    } catch (error) {\n      console.error('Failed to initialize FFmpeg:', error)\n      // Fallback: try without multi-threading\n      try {\n        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd'\n        await this.ffmpeg!.load({\n          coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),\n          wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),\n        })\n        this.isLoaded = true\n        console.log('FFmpeg initialized successfully (fallback mode)')\n      } catch (fallbackError) {\n        console.error('FFmpeg fallback initialization failed:', fallbackError)\n        throw new Error('Failed to initialize video compression engine')\n      }\n    }\n  }\n\n  static async compressVideo(\n    file: File,\n    options: VideoCompressionOptions,\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    try {\n      // For now, we'll implement a basic video compression using HTML5 video and canvas\n      // This is a simplified approach - in production, FFmpeg.wasm would be better\n      return await this.compressVideoWithCanvas(file, options, onProgress)\n    } catch (error) {\n      console.error('Video compression failed:', error)\n      return {\n        success: false,\n        originalSize: file.size,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      }\n    }\n  }\n\n  private static async compressVideoWithCanvas(\n    file: File,\n    options: VideoCompressionOptions,\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    return new Promise((resolve) => {\n      // For now, we'll create a simple video compression by reducing quality\n      // This is a placeholder - real video compression would use FFmpeg.wasm\n\n      // Since we can't do real video compression without FFmpeg.wasm working properly,\n      // we'll return the original file with a warning\n      console.warn('Video compression is currently limited. Returning original file.')\n\n      // Create a copy of the original file with potentially different format\n      const outputFormat = options.outputFormat || 'mp4'\n      const blob = new Blob([file], { type: `video/${outputFormat}` })\n\n      // Simulate compression progress\n      let progress = 0\n      const interval = setInterval(() => {\n        progress += 10\n        onProgress?.(progress)\n        if (progress >= 100) {\n          clearInterval(interval)\n          resolve({\n            success: true,\n            originalSize: file.size,\n            compressedSize: blob.size,\n            compressionRatio: 0, // No actual compression for now\n            blob\n          })\n        }\n      }, 100)\n    })\n  }\n\n  private static calculateVideoDimensions(\n    originalWidth: number,\n    originalHeight: number,\n    maxWidth?: number,\n    maxHeight?: number,\n    maintainAspectRatio: boolean = true\n  ): { width: number; height: number } {\n    let width = originalWidth\n    let height = originalHeight\n\n    if (maxWidth && maxHeight) {\n      if (maintainAspectRatio) {\n        const aspectRatio = originalWidth / originalHeight\n        if (width > maxWidth) {\n          width = maxWidth\n          height = width / aspectRatio\n        }\n        if (height > maxHeight) {\n          height = maxHeight\n          width = height * aspectRatio\n        }\n      } else {\n        width = Math.min(width, maxWidth)\n        height = Math.min(height, maxHeight)\n      }\n    }\n\n    return { width: Math.round(width), height: Math.round(height) }\n  }\n\n  private static buildCompressionCommand(\n    inputFile: string,\n    outputFile: string,\n    options: VideoCompressionOptions\n  ): string[] {\n    const command = ['-i', inputFile]\n\n    // Video codec\n    if (options.codec) {\n      switch (options.codec) {\n        case 'h264':\n          command.push('-c:v', 'libx264')\n          break\n        case 'h265':\n          command.push('-c:v', 'libx265')\n          break\n        case 'vp8':\n          command.push('-c:v', 'libvpx')\n          break\n        case 'vp9':\n          command.push('-c:v', 'libvpx-vp9')\n          break\n      }\n    }\n\n    // Video quality (CRF - Constant Rate Factor)\n    // Lower values = better quality, higher file size\n    const crf = Math.round(options.quality * 51) // Convert 0-1 to 0-51\n    command.push('-crf', crf.toString())\n\n    // Video bitrate\n    if (options.bitrate) {\n      command.push('-b:v', options.bitrate)\n    }\n\n    // Frame rate\n    if (options.fps) {\n      command.push('-r', options.fps.toString())\n    }\n\n    // Resolution\n    if (options.maxWidth && options.maxHeight) {\n      const scale = options.maintainAspectRatio \n        ? `scale='min(${options.maxWidth},iw)':'min(${options.maxHeight},ih)':force_original_aspect_ratio=decrease`\n        : `scale=${options.maxWidth}:${options.maxHeight}`\n      command.push('-vf', scale)\n    }\n\n    // Audio codec\n    if (options.audioCodec) {\n      switch (options.audioCodec) {\n        case 'aac':\n          command.push('-c:a', 'aac')\n          break\n        case 'mp3':\n          command.push('-c:a', 'libmp3lame')\n          break\n        case 'opus':\n          command.push('-c:a', 'libopus')\n          break\n      }\n    }\n\n    // Audio bitrate\n    if (options.audioBitrate) {\n      command.push('-b:a', options.audioBitrate)\n    }\n\n    // Remove metadata if requested\n    if (options.removeMetadata) {\n      command.push('-map_metadata', '-1')\n    }\n\n    // Output format specific options\n    if (options.outputFormat === 'webm') {\n      command.push('-f', 'webm')\n    } else if (options.outputFormat === 'mp4') {\n      command.push('-movflags', '+faststart') // Optimize for web streaming\n    }\n\n    command.push(outputFile)\n    return command\n  }\n\n  static async convertFormat(\n    file: File,\n    targetFormat: string,\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    const options: VideoCompressionOptions = {\n      quality: 0.7,\n      outputFormat: targetFormat,\n      maintainAspectRatio: true,\n      removeMetadata: false\n    }\n\n    return this.compressVideo(file, options, onProgress)\n  }\n\n  static async extractThumbnail(file: File, timeInSeconds: number = 1): Promise<string> {\n    try {\n      await this.initialize()\n      \n      if (!this.ffmpeg) {\n        throw new Error('FFmpeg not initialized')\n      }\n\n      const inputFileName = `input.${file.name.split('.').pop()}`\n      const outputFileName = 'thumbnail.jpg'\n\n      await this.ffmpeg.writeFile(inputFileName, await fetchFile(file))\n\n      await this.ffmpeg.exec([\n        '-i', inputFileName,\n        '-ss', timeInSeconds.toString(),\n        '-vframes', '1',\n        '-q:v', '2',\n        outputFileName\n      ])\n\n      const data = await this.ffmpeg.readFile(outputFileName)\n      const blob = new Blob([data], { type: 'image/jpeg' })\n      \n      // Clean up\n      await this.ffmpeg.deleteFile(inputFileName)\n      await this.ffmpeg.deleteFile(outputFileName)\n\n      return URL.createObjectURL(blob)\n    } catch (error) {\n      console.error('Failed to extract thumbnail:', error)\n      throw error\n    }\n  }\n\n  static async getVideoInfo(file: File): Promise<{\n    duration: number\n    width: number\n    height: number\n    fps: number\n    bitrate: number\n  }> {\n    return new Promise((resolve, reject) => {\n      const video = document.createElement('video')\n      video.onloadedmetadata = () => {\n        resolve({\n          duration: video.duration,\n          width: video.videoWidth,\n          height: video.videoHeight,\n          fps: 30, // Default, actual FPS detection requires more complex analysis\n          bitrate: Math.round(file.size * 8 / video.duration) // Rough estimate\n        })\n      }\n      video.onerror = reject\n      video.src = URL.createObjectURL(file)\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;;AAGO,MAAM;IAIX,aAAa,aAA4B;QACvC,IAAI,IAAI,CAAC,QAAQ,EAAE;QAEnB,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,IAAI,+JAAA,CAAA,SAAM;YAExB,iBAAiB;YACjB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;oBAAC,EAAE,OAAO,EAAE;gBAChC,QAAQ,GAAG,CAAC,WAAW;YACzB;YAEA,wDAAwD;YACxD,MAAM,UAAU;YAChB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACrB,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,AAAC,GAAU,OAAR,SAAQ,oBAAkB;gBACtD,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,AAAC,GAAU,OAAR,SAAQ,sBAAoB;gBACxD,WAAW,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,AAAC,GAAU,OAAR,SAAQ,2BAAyB;YACjE;YAEA,IAAI,CAAC,QAAQ,GAAG;YAChB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wCAAwC;YACxC,IAAI;gBACF,MAAM,UAAU;gBAChB,MAAM,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC;oBACtB,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,AAAC,GAAU,OAAR,SAAQ,oBAAkB;oBACtD,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,AAAC,GAAU,OAAR,SAAQ,sBAAoB;gBAC1D;gBACA,IAAI,CAAC,QAAQ,GAAG;gBAChB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,aAAa,cACX,IAAU,EACV,OAAgC,EAChC,UAAuC,EACX;QAC5B,IAAI;YACF,kFAAkF;YAClF,6EAA6E;YAC7E,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,SAAS;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAqB,wBACnB,IAAU,EACV,OAAgC,EAChC,UAAuC,EACX;QAC5B,OAAO,IAAI,QAAQ,CAAC;YAClB,uEAAuE;YACvE,uEAAuE;YAEvE,iFAAiF;YACjF,gDAAgD;YAChD,QAAQ,IAAI,CAAC;YAEb,uEAAuE;YACvE,MAAM,eAAe,QAAQ,YAAY,IAAI;YAC7C,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAK,EAAE;gBAAE,MAAM,AAAC,SAAqB,OAAb;YAAe;YAE9D,gCAAgC;YAChC,IAAI,WAAW;YACf,MAAM,WAAW,YAAY;gBAC3B,YAAY;gBACZ,uBAAA,iCAAA,WAAa;gBACb,IAAI,YAAY,KAAK;oBACnB,cAAc;oBACd,QAAQ;wBACN,SAAS;wBACT,cAAc,KAAK,IAAI;wBACvB,gBAAgB,KAAK,IAAI;wBACzB,kBAAkB;wBAClB;oBACF;gBACF;YACF,GAAG;QACL;IACF;IAEA,OAAe,yBACb,aAAqB,EACrB,cAAsB,EACtB,QAAiB,EACjB,SAAkB,EAEiB;YADnC,sBAAA,iEAA+B;QAE/B,IAAI,QAAQ;QACZ,IAAI,SAAS;QAEb,IAAI,YAAY,WAAW;YACzB,IAAI,qBAAqB;gBACvB,MAAM,cAAc,gBAAgB;gBACpC,IAAI,QAAQ,UAAU;oBACpB,QAAQ;oBACR,SAAS,QAAQ;gBACnB;gBACA,IAAI,SAAS,WAAW;oBACtB,SAAS;oBACT,QAAQ,SAAS;gBACnB;YACF,OAAO;gBACL,QAAQ,KAAK,GAAG,CAAC,OAAO;gBACxB,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC5B;QACF;QAEA,OAAO;YAAE,OAAO,KAAK,KAAK,CAAC;YAAQ,QAAQ,KAAK,KAAK,CAAC;QAAQ;IAChE;IAEA,OAAe,wBACb,SAAiB,EACjB,UAAkB,EAClB,OAAgC,EACtB;QACV,MAAM,UAAU;YAAC;YAAM;SAAU;QAEjC,cAAc;QACd,IAAI,QAAQ,KAAK,EAAE;YACjB,OAAQ,QAAQ,KAAK;gBACnB,KAAK;oBACH,QAAQ,IAAI,CAAC,QAAQ;oBACrB;gBACF,KAAK;oBACH,QAAQ,IAAI,CAAC,QAAQ;oBACrB;gBACF,KAAK;oBACH,QAAQ,IAAI,CAAC,QAAQ;oBACrB;gBACF,KAAK;oBACH,QAAQ,IAAI,CAAC,QAAQ;oBACrB;YACJ;QACF;QAEA,6CAA6C;QAC7C,kDAAkD;QAClD,MAAM,MAAM,KAAK,KAAK,CAAC,QAAQ,OAAO,GAAG,IAAI,sBAAsB;;QACnE,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ;QAEjC,gBAAgB;QAChB,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,IAAI,CAAC,QAAQ,QAAQ,OAAO;QACtC;QAEA,aAAa;QACb,IAAI,QAAQ,GAAG,EAAE;YACf,QAAQ,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAC,QAAQ;QACzC;QAEA,aAAa;QACb,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;YACzC,MAAM,QAAQ,QAAQ,mBAAmB,GACrC,AAAC,cAA2C,OAA9B,QAAQ,QAAQ,EAAC,eAA+B,OAAlB,QAAQ,SAAS,EAAC,gDAC9D,AAAC,SAA4B,OAApB,QAAQ,QAAQ,EAAC,KAAqB,OAAlB,QAAQ,SAAS;YAClD,QAAQ,IAAI,CAAC,OAAO;QACtB;QAEA,cAAc;QACd,IAAI,QAAQ,UAAU,EAAE;YACtB,OAAQ,QAAQ,UAAU;gBACxB,KAAK;oBACH,QAAQ,IAAI,CAAC,QAAQ;oBACrB;gBACF,KAAK;oBACH,QAAQ,IAAI,CAAC,QAAQ;oBACrB;gBACF,KAAK;oBACH,QAAQ,IAAI,CAAC,QAAQ;oBACrB;YACJ;QACF;QAEA,gBAAgB;QAChB,IAAI,QAAQ,YAAY,EAAE;YACxB,QAAQ,IAAI,CAAC,QAAQ,QAAQ,YAAY;QAC3C;QAEA,+BAA+B;QAC/B,IAAI,QAAQ,cAAc,EAAE;YAC1B,QAAQ,IAAI,CAAC,iBAAiB;QAChC;QAEA,iCAAiC;QACjC,IAAI,QAAQ,YAAY,KAAK,QAAQ;YACnC,QAAQ,IAAI,CAAC,MAAM;QACrB,OAAO,IAAI,QAAQ,YAAY,KAAK,OAAO;YACzC,QAAQ,IAAI,CAAC,aAAa,eAAc,6BAA6B;QACvE;QAEA,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,aAAa,cACX,IAAU,EACV,YAAoB,EACpB,UAAuC,EACX;QAC5B,MAAM,UAAmC;YACvC,SAAS;YACT,cAAc;YACd,qBAAqB;YACrB,gBAAgB;QAClB;QAEA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,SAAS;IAC3C;IAEA,aAAa,iBAAiB,IAAU,EAA8C;YAA5C,gBAAA,iEAAwB;QAChE,IAAI;YACF,MAAM,IAAI,CAAC,UAAU;YAErB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,gBAAgB,AAAC,SAAmC,OAA3B,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACvD,MAAM,iBAAiB;YAEvB,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE;YAE3D,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACrB;gBAAM;gBACN;gBAAO,cAAc,QAAQ;gBAC7B;gBAAY;gBACZ;gBAAQ;gBACR;aACD;YAED,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACxC,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAK,EAAE;gBAAE,MAAM;YAAa;YAEnD,WAAW;YACX,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YAC7B,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YAE7B,OAAO,IAAI,eAAe,CAAC;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,aAAa,aAAa,IAAU,EAMjC;QACD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,gBAAgB,GAAG;gBACvB,QAAQ;oBACN,UAAU,MAAM,QAAQ;oBACxB,OAAO,MAAM,UAAU;oBACvB,QAAQ,MAAM,WAAW;oBACzB,KAAK;oBACL,SAAS,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,iBAAiB;gBACvE;YACF;YACA,MAAM,OAAO,GAAG;YAChB,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC;QAClC;IACF;AACF;AA7RE,yKADW,yBACI,UAAwB;AACvC,yKAFW,yBAEI,YAAW", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/services/gifCompression.ts"], "sourcesContent": ["import { CompressionResult, GifCompressionOptions } from '@/types'\n\nexport class GifCompressionService {\n  static async compressGif(\n    file: File,\n    options: GifCompressionOptions,\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    try {\n      // For now, we'll use a simplified approach with canvas\n      // In a production app, you might want to use a dedicated GIF library like gif.js\n      const result = await this.processGifWithCanvas(file, options, onProgress)\n      return result\n    } catch (error) {\n      console.error('GIF compression failed:', error)\n      return {\n        success: false,\n        originalSize: file.size,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      }\n    }\n  }\n\n  private static async processGifWithCanvas(\n    file: File,\n    options: GifCompressionOptions,\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    return new Promise((resolve) => {\n      // For GIF compression, we'll use a simplified approach\n      // Real GIF compression would require a specialized library like gif.js\n\n      const img = new Image()\n      img.onload = () => {\n        const canvas = document.createElement('canvas')\n        const ctx = canvas.getContext('2d')\n\n        if (!ctx) {\n          resolve({\n            success: false,\n            originalSize: file.size,\n            compressedSize: 0,\n            compressionRatio: 0,\n            error: 'Could not get canvas context'\n          })\n          return\n        }\n\n        // Calculate new dimensions\n        let { width, height } = this.calculateDimensions(\n          img.width,\n          img.height,\n          options.maxWidth,\n          options.maxHeight,\n          options.maintainAspectRatio\n        )\n\n        canvas.width = width\n        canvas.height = height\n\n        onProgress?.(50)\n\n        // Draw the image\n        ctx.drawImage(img, 0, 0, width, height)\n\n        // For now, convert to PNG to ensure compatibility\n        // In a real implementation, we'd preserve the GIF format with proper compression\n        canvas.toBlob((blob) => {\n          if (blob) {\n            const compressionRatio = ((file.size - blob.size) / file.size) * 100\n            resolve({\n              success: true,\n              originalSize: file.size,\n              compressedSize: blob.size,\n              compressionRatio,\n              blob\n            })\n          } else {\n            resolve({\n              success: false,\n              originalSize: file.size,\n              compressedSize: 0,\n              compressionRatio: 0,\n              error: 'Failed to compress GIF'\n            })\n          }\n          onProgress?.(100)\n        }, 'image/png', options.quality) // Use PNG for better compatibility\n      }\n\n      img.onerror = () => {\n        resolve({\n          success: false,\n          originalSize: file.size,\n          compressedSize: 0,\n          compressionRatio: 0,\n          error: 'Failed to load GIF'\n        })\n      }\n\n      img.src = URL.createObjectURL(file)\n      onProgress?.(25)\n    })\n  }\n\n  static async convertGifToVideo(\n    file: File,\n    outputFormat: 'mp4' | 'webm' = 'mp4',\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    try {\n      // This would require FFmpeg integration for proper GIF to video conversion\n      // For now, we'll return a placeholder implementation\n      onProgress?.(50)\n      \n      // Create a video element to get GIF dimensions and duration\n      const video = document.createElement('video')\n      const canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n\n      return new Promise((resolve) => {\n        const img = new Image()\n        img.onload = () => {\n          if (!ctx) {\n            resolve({\n              success: false,\n              originalSize: file.size,\n              compressedSize: 0,\n              compressionRatio: 0,\n              error: 'Could not get canvas context'\n            })\n            return\n          }\n\n          canvas.width = img.width\n          canvas.height = img.height\n          ctx.drawImage(img, 0, 0)\n\n          canvas.toBlob((blob) => {\n            if (blob) {\n              const compressionRatio = ((file.size - blob.size) / file.size) * 100\n              resolve({\n                success: true,\n                originalSize: file.size,\n                compressedSize: blob.size,\n                compressionRatio,\n                blob\n              })\n            } else {\n              resolve({\n                success: false,\n                originalSize: file.size,\n                compressedSize: 0,\n                compressionRatio: 0,\n                error: 'Failed to convert GIF'\n              })\n            }\n            onProgress?.(100)\n          }, `video/${outputFormat}`)\n        }\n\n        img.onerror = () => {\n          resolve({\n            success: false,\n            originalSize: file.size,\n            compressedSize: 0,\n            compressionRatio: 0,\n            error: 'Failed to load GIF'\n          })\n        }\n\n        img.src = URL.createObjectURL(file)\n      })\n    } catch (error) {\n      return {\n        success: false,\n        originalSize: file.size,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      }\n    }\n  }\n\n  static async optimizeGif(\n    file: File,\n    options: {\n      colors?: number\n      frameRate?: number\n      quality: number\n    },\n    onProgress?: (progress: number) => void\n  ): Promise<CompressionResult> {\n    try {\n      // Simplified optimization - in production, use a proper GIF library\n      const canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      const img = new Image()\n\n      return new Promise((resolve) => {\n        img.onload = () => {\n          if (!ctx) {\n            resolve({\n              success: false,\n              originalSize: file.size,\n              compressedSize: 0,\n              compressionRatio: 0,\n              error: 'Could not get canvas context'\n            })\n            return\n          }\n\n          canvas.width = img.width\n          canvas.height = img.height\n\n          // Apply some basic optimization\n          ctx.imageSmoothingEnabled = true\n          ctx.imageSmoothingQuality = 'high'\n          ctx.drawImage(img, 0, 0)\n\n          // Reduce colors by applying a simple quantization effect\n          if (options.colors && options.colors < 256) {\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\n            const data = imageData.data\n            const factor = Math.floor(256 / options.colors)\n\n            for (let i = 0; i < data.length; i += 4) {\n              data[i] = Math.floor(data[i] / factor) * factor     // Red\n              data[i + 1] = Math.floor(data[i + 1] / factor) * factor // Green\n              data[i + 2] = Math.floor(data[i + 2] / factor) * factor // Blue\n            }\n\n            ctx.putImageData(imageData, 0, 0)\n          }\n\n          onProgress?.(80)\n\n          canvas.toBlob((blob) => {\n            if (blob) {\n              const compressionRatio = ((file.size - blob.size) / file.size) * 100\n              resolve({\n                success: true,\n                originalSize: file.size,\n                compressedSize: blob.size,\n                compressionRatio,\n                blob\n              })\n            } else {\n              resolve({\n                success: false,\n                originalSize: file.size,\n                compressedSize: 0,\n                compressionRatio: 0,\n                error: 'Failed to optimize GIF'\n              })\n            }\n            onProgress?.(100)\n          }, 'image/gif', options.quality)\n        }\n\n        img.onerror = () => {\n          resolve({\n            success: false,\n            originalSize: file.size,\n            compressedSize: 0,\n            compressionRatio: 0,\n            error: 'Failed to load GIF'\n          })\n        }\n\n        img.src = URL.createObjectURL(file)\n      })\n    } catch (error) {\n      return {\n        success: false,\n        originalSize: file.size,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      }\n    }\n  }\n\n  private static calculateDimensions(\n    originalWidth: number,\n    originalHeight: number,\n    maxWidth?: number,\n    maxHeight?: number,\n    maintainAspectRatio: boolean = true\n  ): { width: number; height: number } {\n    let width = originalWidth\n    let height = originalHeight\n\n    if (maxWidth && maxHeight) {\n      if (maintainAspectRatio) {\n        const aspectRatio = originalWidth / originalHeight\n        if (width > maxWidth) {\n          width = maxWidth\n          height = width / aspectRatio\n        }\n        if (height > maxHeight) {\n          height = maxHeight\n          width = height * aspectRatio\n        }\n      } else {\n        width = Math.min(width, maxWidth)\n        height = Math.min(height, maxHeight)\n      }\n    } else if (maxWidth) {\n      if (width > maxWidth) {\n        const aspectRatio = originalWidth / originalHeight\n        width = maxWidth\n        if (maintainAspectRatio) {\n          height = width / aspectRatio\n        }\n      }\n    } else if (maxHeight) {\n      if (height > maxHeight) {\n        const aspectRatio = originalWidth / originalHeight\n        height = maxHeight\n        if (maintainAspectRatio) {\n          width = height * aspectRatio\n        }\n      }\n    }\n\n    return { width: Math.round(width), height: Math.round(height) }\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,aAAa,YACX,IAAU,EACV,OAA8B,EAC9B,UAAuC,EACX;QAC5B,IAAI;YACF,uDAAuD;YACvD,iFAAiF;YACjF,MAAM,SAAS,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,SAAS;YAC9D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAqB,qBACnB,IAAU,EACV,OAA8B,EAC9B,UAAuC,EACX;QAC5B,OAAO,IAAI,QAAQ,CAAC;YAClB,uDAAuD;YACvD,uEAAuE;YAEvE,MAAM,MAAM,IAAI;YAChB,IAAI,MAAM,GAAG;gBACX,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;gBAE9B,IAAI,CAAC,KAAK;oBACR,QAAQ;wBACN,SAAS;wBACT,cAAc,KAAK,IAAI;wBACvB,gBAAgB;wBAChB,kBAAkB;wBAClB,OAAO;oBACT;oBACA;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAC9C,IAAI,KAAK,EACT,IAAI,MAAM,EACV,QAAQ,QAAQ,EAChB,QAAQ,SAAS,EACjB,QAAQ,mBAAmB;gBAG7B,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,uBAAA,iCAAA,WAAa;gBAEb,iBAAiB;gBACjB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;gBAEhC,kDAAkD;gBAClD,iFAAiF;gBACjF,OAAO,MAAM,CAAC,CAAC;oBACb,IAAI,MAAM;wBACR,MAAM,mBAAmB,AAAC,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,GAAI;wBACjE,QAAQ;4BACN,SAAS;4BACT,cAAc,KAAK,IAAI;4BACvB,gBAAgB,KAAK,IAAI;4BACzB;4BACA;wBACF;oBACF,OAAO;wBACL,QAAQ;4BACN,SAAS;4BACT,cAAc,KAAK,IAAI;4BACvB,gBAAgB;4BAChB,kBAAkB;4BAClB,OAAO;wBACT;oBACF;oBACA,uBAAA,iCAAA,WAAa;gBACf,GAAG,aAAa,QAAQ,OAAO,GAAE,mCAAmC;YACtE;YAEA,IAAI,OAAO,GAAG;gBACZ,QAAQ;oBACN,SAAS;oBACT,cAAc,KAAK,IAAI;oBACvB,gBAAgB;oBAChB,kBAAkB;oBAClB,OAAO;gBACT;YACF;YAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;YAC9B,uBAAA,iCAAA,WAAa;QACf;IACF;IAEA,aAAa,kBACX,IAAU,EAGkB;YAF5B,eAAA,iEAA+B,OAC/B;QAEA,IAAI;YACF,2EAA2E;YAC3E,qDAAqD;YACrD,uBAAA,iCAAA,WAAa;YAEb,4DAA4D;YAC5D,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAE9B,OAAO,IAAI,QAAQ,CAAC;gBAClB,MAAM,MAAM,IAAI;gBAChB,IAAI,MAAM,GAAG;oBACX,IAAI,CAAC,KAAK;wBACR,QAAQ;4BACN,SAAS;4BACT,cAAc,KAAK,IAAI;4BACvB,gBAAgB;4BAChB,kBAAkB;4BAClB,OAAO;wBACT;wBACA;oBACF;oBAEA,OAAO,KAAK,GAAG,IAAI,KAAK;oBACxB,OAAO,MAAM,GAAG,IAAI,MAAM;oBAC1B,IAAI,SAAS,CAAC,KAAK,GAAG;oBAEtB,OAAO,MAAM,CAAC,CAAC;wBACb,IAAI,MAAM;4BACR,MAAM,mBAAmB,AAAC,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,GAAI;4BACjE,QAAQ;gCACN,SAAS;gCACT,cAAc,KAAK,IAAI;gCACvB,gBAAgB,KAAK,IAAI;gCACzB;gCACA;4BACF;wBACF,OAAO;4BACL,QAAQ;gCACN,SAAS;gCACT,cAAc,KAAK,IAAI;gCACvB,gBAAgB;gCAChB,kBAAkB;gCAClB,OAAO;4BACT;wBACF;wBACA,uBAAA,iCAAA,WAAa;oBACf,GAAG,AAAC,SAAqB,OAAb;gBACd;gBAEA,IAAI,OAAO,GAAG;oBACZ,QAAQ;wBACN,SAAS;wBACT,cAAc,KAAK,IAAI;wBACvB,gBAAgB;wBAChB,kBAAkB;wBAClB,OAAO;oBACT;gBACF;gBAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;YAChC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,aAAa,YACX,IAAU,EACV,OAIC,EACD,UAAuC,EACX;QAC5B,IAAI;YACF,oEAAoE;YACpE,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,MAAM,IAAI;YAEhB,OAAO,IAAI,QAAQ,CAAC;gBAClB,IAAI,MAAM,GAAG;oBACX,IAAI,CAAC,KAAK;wBACR,QAAQ;4BACN,SAAS;4BACT,cAAc,KAAK,IAAI;4BACvB,gBAAgB;4BAChB,kBAAkB;4BAClB,OAAO;wBACT;wBACA;oBACF;oBAEA,OAAO,KAAK,GAAG,IAAI,KAAK;oBACxB,OAAO,MAAM,GAAG,IAAI,MAAM;oBAE1B,gCAAgC;oBAChC,IAAI,qBAAqB,GAAG;oBAC5B,IAAI,qBAAqB,GAAG;oBAC5B,IAAI,SAAS,CAAC,KAAK,GAAG;oBAEtB,yDAAyD;oBACzD,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,GAAG,KAAK;wBAC1C,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;wBACpE,MAAM,OAAO,UAAU,IAAI;wBAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,QAAQ,MAAM;wBAE9C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;4BACvC,IAAI,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,UAAU,QAAW,MAAM;4BAC1D,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,QAAO,QAAQ;4BAChE,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,QAAO,OAAO;wBACjE;wBAEA,IAAI,YAAY,CAAC,WAAW,GAAG;oBACjC;oBAEA,uBAAA,iCAAA,WAAa;oBAEb,OAAO,MAAM,CAAC,CAAC;wBACb,IAAI,MAAM;4BACR,MAAM,mBAAmB,AAAC,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,GAAI;4BACjE,QAAQ;gCACN,SAAS;gCACT,cAAc,KAAK,IAAI;gCACvB,gBAAgB,KAAK,IAAI;gCACzB;gCACA;4BACF;wBACF,OAAO;4BACL,QAAQ;gCACN,SAAS;gCACT,cAAc,KAAK,IAAI;gCACvB,gBAAgB;gCAChB,kBAAkB;gCAClB,OAAO;4BACT;wBACF;wBACA,uBAAA,iCAAA,WAAa;oBACf,GAAG,aAAa,QAAQ,OAAO;gBACjC;gBAEA,IAAI,OAAO,GAAG;oBACZ,QAAQ;wBACN,SAAS;wBACT,cAAc,KAAK,IAAI;wBACvB,gBAAgB;wBAChB,kBAAkB;wBAClB,OAAO;oBACT;gBACF;gBAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;YAChC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,cAAc,KAAK,IAAI;gBACvB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,OAAe,oBACb,aAAqB,EACrB,cAAsB,EACtB,QAAiB,EACjB,SAAkB,EAEiB;YADnC,sBAAA,iEAA+B;QAE/B,IAAI,QAAQ;QACZ,IAAI,SAAS;QAEb,IAAI,YAAY,WAAW;YACzB,IAAI,qBAAqB;gBACvB,MAAM,cAAc,gBAAgB;gBACpC,IAAI,QAAQ,UAAU;oBACpB,QAAQ;oBACR,SAAS,QAAQ;gBACnB;gBACA,IAAI,SAAS,WAAW;oBACtB,SAAS;oBACT,QAAQ,SAAS;gBACnB;YACF,OAAO;gBACL,QAAQ,KAAK,GAAG,CAAC,OAAO;gBACxB,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC5B;QACF,OAAO,IAAI,UAAU;YACnB,IAAI,QAAQ,UAAU;gBACpB,MAAM,cAAc,gBAAgB;gBACpC,QAAQ;gBACR,IAAI,qBAAqB;oBACvB,SAAS,QAAQ;gBACnB;YACF;QACF,OAAO,IAAI,WAAW;YACpB,IAAI,SAAS,WAAW;gBACtB,MAAM,cAAc,gBAAgB;gBACpC,SAAS;gBACT,IAAI,qBAAqB;oBACvB,QAAQ,SAAS;gBACnB;YACF;QACF;QAEA,OAAO;YAAE,OAAO,KAAK,KAAK,CAAC;YAAQ,QAAQ,KAAK,KAAK,CAAC;QAAQ;IAChE;AACF", "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/hooks/useCompressionManager.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { FileItem, CompressionOptions, ImageCompressionOptions, VideoCompressionOptions, GifCompressionOptions } from '@/types'\nimport { ImageCompressionService } from '@/services/imageCompression'\nimport { VideoCompressionService } from '@/services/videoCompression'\nimport { GifCompressionService } from '@/services/gifCompression'\nimport { downloadFile } from '@/lib/utils'\n\nexport function useCompressionManager() {\n  const [files, setFiles] = useState<FileItem[]>([])\n  const [isProcessing, setIsProcessing] = useState(false)\n  const [currentProcessingIndex, setCurrentProcessingIndex] = useState(-1)\n\n  const addFiles = useCallback((newFiles: FileItem[]) => {\n    setFiles(prev => [...prev, ...newFiles])\n  }, [])\n\n  const removeFile = useCallback((id: string) => {\n    setFiles(prev => prev.filter(file => file.id !== id))\n  }, [])\n\n  const updateFileStatus = useCallback((id: string, updates: Partial<FileItem>) => {\n    setFiles(prev => prev.map(file => \n      file.id === id ? { ...file, ...updates } : file\n    ))\n  }, [])\n\n  const compressFile = useCallback(async (\n    file: FileItem, \n    options: CompressionOptions,\n    onProgress?: (progress: number) => void\n  ) => {\n    try {\n      updateFileStatus(file.id, { status: 'processing', progress: 0 })\n\n      let result\n      \n      if (file.type === 'image') {\n        result = await ImageCompressionService.compressImage(\n          file.file,\n          options as ImageCompressionOptions,\n          (progress) => {\n            updateFileStatus(file.id, { progress })\n            onProgress?.(progress)\n          }\n        )\n      } else if (file.type === 'video') {\n        result = await VideoCompressionService.compressVideo(\n          file.file,\n          options as VideoCompressionOptions,\n          (progress) => {\n            updateFileStatus(file.id, { progress })\n            onProgress?.(progress)\n          }\n        )\n      } else if (file.type === 'gif') {\n        result = await GifCompressionService.compressGif(\n          file.file,\n          options as GifCompressionOptions,\n          (progress) => {\n            updateFileStatus(file.id, { progress })\n            onProgress?.(progress)\n          }\n        )\n      } else {\n        throw new Error(`Unsupported file type: ${file.type}`)\n      }\n\n      if (result.success && result.blob) {\n        updateFileStatus(file.id, {\n          status: 'completed',\n          progress: 100,\n          compressedSize: result.compressedSize,\n          compressionRatio: result.compressionRatio,\n          compressedFile: result.blob,\n          downloadUrl: URL.createObjectURL(result.blob)\n        })\n      } else {\n        updateFileStatus(file.id, {\n          status: 'error',\n          progress: 0,\n          error: result.error || 'Compression failed'\n        })\n      }\n\n      return result\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'\n      updateFileStatus(file.id, {\n        status: 'error',\n        progress: 0,\n        error: errorMessage\n      })\n      return {\n        success: false,\n        originalSize: file.originalSize,\n        compressedSize: 0,\n        compressionRatio: 0,\n        error: errorMessage\n      }\n    }\n  }, [updateFileStatus])\n\n  const compressAllFiles = useCallback(async (options: CompressionOptions) => {\n    if (isProcessing) return\n\n    setIsProcessing(true)\n    const pendingFiles = files.filter(file => file.status === 'pending')\n    \n    for (let i = 0; i < pendingFiles.length; i++) {\n      setCurrentProcessingIndex(i)\n      await compressFile(pendingFiles[i], options)\n    }\n\n    setIsProcessing(false)\n    setCurrentProcessingIndex(-1)\n  }, [files, isProcessing, compressFile])\n\n  const retryFile = useCallback(async (id: string, options: CompressionOptions) => {\n    const file = files.find(f => f.id === id)\n    if (!file) return\n\n    updateFileStatus(id, { status: 'pending', error: undefined })\n    await compressFile(file, options)\n  }, [files, compressFile, updateFileStatus])\n\n  const downloadSingleFile = useCallback((id: string) => {\n    const file = files.find(f => f.id === id)\n    if (!file || !file.compressedFile) return\n\n    const extension = file.format\n    const baseName = file.name.replace(/\\.[^/.]+$/, '')\n    const fileName = `${baseName}_compressed.${extension}`\n\n    downloadFile(file.compressedFile, fileName)\n  }, [files])\n\n  const downloadAllFiles = useCallback(() => {\n    const completedFiles = files.filter(f => f.status === 'completed' && f.compressedFile)\n    \n    if (completedFiles.length === 0) return\n\n    if (completedFiles.length === 1) {\n      downloadSingleFile(completedFiles[0].id)\n      return\n    }\n\n    // For multiple files, we would need to create a ZIP file\n    // For now, download them individually\n    completedFiles.forEach(file => {\n      setTimeout(() => downloadSingleFile(file.id), 100)\n    })\n  }, [files, downloadFile])\n\n  const clearAllFiles = useCallback(() => {\n    // Clean up object URLs to prevent memory leaks\n    files.forEach(file => {\n      if (file.downloadUrl) {\n        URL.revokeObjectURL(file.downloadUrl)\n      }\n    })\n    setFiles([])\n  }, [files])\n\n  const getStats = useCallback(() => {\n    const total = files.length\n    const pending = files.filter(f => f.status === 'pending').length\n    const processing = files.filter(f => f.status === 'processing').length\n    const completed = files.filter(f => f.status === 'completed').length\n    const failed = files.filter(f => f.status === 'error').length\n    \n    const totalOriginalSize = files.reduce((sum, f) => sum + f.originalSize, 0)\n    const totalCompressedSize = files\n      .filter(f => f.compressedSize)\n      .reduce((sum, f) => sum + (f.compressedSize || 0), 0)\n    \n    const overallCompressionRatio = totalOriginalSize > 0 \n      ? ((totalOriginalSize - totalCompressedSize) / totalOriginalSize) * 100 \n      : 0\n\n    const overallProgress = total > 0 \n      ? (completed / total) * 100 \n      : 0\n\n    return {\n      total,\n      pending,\n      processing,\n      completed,\n      failed,\n      totalOriginalSize,\n      totalCompressedSize,\n      overallCompressionRatio,\n      overallProgress,\n      canDownload: completed > 0,\n      canProcess: pending > 0 && !isProcessing\n    }\n  }, [files, isProcessing])\n\n  return {\n    files,\n    isProcessing,\n    currentProcessingIndex,\n    addFiles,\n    removeFile,\n    compressFile,\n    compressAllFiles,\n    retryFile,\n    downloadFile: downloadSingleFile,\n    downloadAllFiles,\n    clearAllFiles,\n    getStats\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AACA;AACA;AACA;;AAPA;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEtE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YAC5B;+DAAS,CAAA,OAAQ;2BAAI;2BAAS;qBAAS;;QACzC;sDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YAC9B;iEAAS,CAAA,OAAQ,KAAK,MAAM;yEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;QACnD;wDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC,IAAY;YAChD;uEAAS,CAAA,OAAQ,KAAK,GAAG;+EAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;4BAAC,IAAI;;;QAE/C;8DAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAC/B,MACA,SACA;YAEA,IAAI;gBACF,iBAAiB,KAAK,EAAE,EAAE;oBAAE,QAAQ;oBAAc,UAAU;gBAAE;gBAE9D,IAAI;gBAEJ,IAAI,KAAK,IAAI,KAAK,SAAS;oBACzB,SAAS,MAAM,sIAAA,CAAA,0BAAuB,CAAC,aAAa,CAClD,KAAK,IAAI,EACT;2EACA,CAAC;4BACC,iBAAiB,KAAK,EAAE,EAAE;gCAAE;4BAAS;4BACrC,uBAAA,iCAAA,WAAa;wBACf;;gBAEJ,OAAO,IAAI,KAAK,IAAI,KAAK,SAAS;oBAChC,SAAS,MAAM,sIAAA,CAAA,0BAAuB,CAAC,aAAa,CAClD,KAAK,IAAI,EACT;2EACA,CAAC;4BACC,iBAAiB,KAAK,EAAE,EAAE;gCAAE;4BAAS;4BACrC,uBAAA,iCAAA,WAAa;wBACf;;gBAEJ,OAAO,IAAI,KAAK,IAAI,KAAK,OAAO;oBAC9B,SAAS,MAAM,oIAAA,CAAA,wBAAqB,CAAC,WAAW,CAC9C,KAAK,IAAI,EACT;2EACA,CAAC;4BACC,iBAAiB,KAAK,EAAE,EAAE;gCAAE;4BAAS;4BACrC,uBAAA,iCAAA,WAAa;wBACf;;gBAEJ,OAAO;oBACL,MAAM,IAAI,MAAM,AAAC,0BAAmC,OAAV,KAAK,IAAI;gBACrD;gBAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,iBAAiB,KAAK,EAAE,EAAE;wBACxB,QAAQ;wBACR,UAAU;wBACV,gBAAgB,OAAO,cAAc;wBACrC,kBAAkB,OAAO,gBAAgB;wBACzC,gBAAgB,OAAO,IAAI;wBAC3B,aAAa,IAAI,eAAe,CAAC,OAAO,IAAI;oBAC9C;gBACF,OAAO;oBACL,iBAAiB,KAAK,EAAE,EAAE;wBACxB,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,KAAK,IAAI;oBACzB;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,iBAAiB,KAAK,EAAE,EAAE;oBACxB,QAAQ;oBACR,UAAU;oBACV,OAAO;gBACT;gBACA,OAAO;oBACL,SAAS;oBACT,cAAc,KAAK,YAAY;oBAC/B,gBAAgB;oBAChB,kBAAkB;oBAClB,OAAO;gBACT;YACF;QACF;0DAAG;QAAC;KAAiB;IAErB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,OAAO;YAC1C,IAAI,cAAc;YAElB,gBAAgB;YAChB,MAAM,eAAe,MAAM,MAAM;oFAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;;YAE1D,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,0BAA0B;gBAC1B,MAAM,aAAa,YAAY,CAAC,EAAE,EAAE;YACtC;YAEA,gBAAgB;YAChB,0BAA0B,CAAC;QAC7B;8DAAG;QAAC;QAAO;QAAc;KAAa;IAEtC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO,IAAY;YAC/C,MAAM,OAAO,MAAM,IAAI;qEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACtC,IAAI,CAAC,MAAM;YAEX,iBAAiB,IAAI;gBAAE,QAAQ;gBAAW,OAAO;YAAU;YAC3D,MAAM,aAAa,MAAM;QAC3B;uDAAG;QAAC;QAAO;QAAc;KAAiB;IAE1C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YACtC,MAAM,OAAO,MAAM,IAAI;8EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACtC,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;YAEnC,MAAM,YAAY,KAAK,MAAM;YAC7B,MAAM,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;YAChD,MAAM,WAAW,AAAC,GAAyB,OAAvB,UAAS,gBAAwB,OAAV;YAE3C,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,cAAc,EAAE;QACpC;gEAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YACnC,MAAM,iBAAiB,MAAM,MAAM;sFAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,cAAc;;YAErF,IAAI,eAAe,MAAM,KAAK,GAAG;YAEjC,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,mBAAmB,cAAc,CAAC,EAAE,CAAC,EAAE;gBACvC;YACF;YAEA,yDAAyD;YACzD,sCAAsC;YACtC,eAAe,OAAO;uEAAC,CAAA;oBACrB;+EAAW,IAAM,mBAAmB,KAAK,EAAE;8EAAG;gBAChD;;QACF;8DAAG;QAAC;QAAO,sHAAA,CAAA,eAAY;KAAC;IAExB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAChC,+CAA+C;YAC/C,MAAM,OAAO;oEAAC,CAAA;oBACZ,IAAI,KAAK,WAAW,EAAE;wBACpB,IAAI,eAAe,CAAC,KAAK,WAAW;oBACtC;gBACF;;YACA,SAAS,EAAE;QACb;2DAAG;QAAC;KAAM;IAEV,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC3B,MAAM,QAAQ,MAAM,MAAM;YAC1B,MAAM,UAAU,MAAM,MAAM;+DAAC,CAAA,IAAK,EAAE,MAAM,KAAK;8DAAW,MAAM;YAChE,MAAM,aAAa,MAAM,MAAM;+DAAC,CAAA,IAAK,EAAE,MAAM,KAAK;8DAAc,MAAM;YACtE,MAAM,YAAY,MAAM,MAAM;+DAAC,CAAA,IAAK,EAAE,MAAM,KAAK;8DAAa,MAAM;YACpE,MAAM,SAAS,MAAM,MAAM;+DAAC,CAAA,IAAK,EAAE,MAAM,KAAK;8DAAS,MAAM;YAE7D,MAAM,oBAAoB,MAAM,MAAM;iFAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY;gFAAE;YACzE,MAAM,sBAAsB,MACzB,MAAM;mFAAC,CAAA,IAAK,EAAE,cAAc;kFAC5B,MAAM;mFAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,cAAc,IAAI,CAAC;kFAAG;YAErD,MAAM,0BAA0B,oBAAoB,IAChD,AAAC,CAAC,oBAAoB,mBAAmB,IAAI,oBAAqB,MAClE;YAEJ,MAAM,kBAAkB,QAAQ,IAC5B,AAAC,YAAY,QAAS,MACtB;YAEJ,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,aAAa,YAAY;gBACzB,YAAY,UAAU,KAAK,CAAC;YAC9B;QACF;sDAAG;QAAC;QAAO;KAAa;IAExB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,cAAc;QACd;QACA;QACA;IACF;AACF;GA7MgB", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/FileCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { FileCardProps } from '@/types'\nimport { formatFileSize, createImagePreview, createVideoPreview } from '@/lib/utils'\nimport { \n  X, \n  Download, \n  RotateCcw, \n  Eye, \n  Image as ImageIcon, \n  Video, \n  FileImage,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Loader2\n} from 'lucide-react'\n\nexport function FileCard({ item, onRemove, onRetry, onDownload, onPreview }: FileCardProps) {\n  const [preview, setPreview] = useState<string>('')\n  const [showPreview, setShowPreview] = useState(false)\n\n  useEffect(() => {\n    const generatePreview = async () => {\n      try {\n        if (item.type === 'image' || item.type === 'gif') {\n          const previewUrl = await createImagePreview(item.file)\n          setPreview(previewUrl)\n        } else if (item.type === 'video') {\n          const previewUrl = await createVideoPreview(item.file)\n          setPreview(previewUrl)\n        }\n      } catch (error) {\n        console.error('Failed to generate preview:', error)\n      }\n    }\n\n    if (!item.preview) {\n      generatePreview()\n    } else {\n      setPreview(item.preview)\n    }\n  }, [item])\n\n  const getFileIcon = () => {\n    switch (item.type) {\n      case 'image':\n      case 'gif':\n        return <ImageIcon className=\"w-6 h-6 text-blue-500\" />\n      case 'video':\n        return <Video className=\"w-6 h-6 text-purple-500\" />\n      default:\n        return <FileImage className=\"w-6 h-6 text-gray-500\" />\n    }\n  }\n\n  const getStatusIcon = () => {\n    switch (item.status) {\n      case 'pending':\n        return <Clock className=\"w-4 h-4 text-yellow-500\" />\n      case 'processing':\n        return <Loader2 className=\"w-4 h-4 text-blue-500 animate-spin\" />\n      case 'completed':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n      case 'cancelled':\n        return <X className=\"w-4 h-4 text-gray-500\" />\n      default:\n        return null\n    }\n  }\n\n  const getStatusColor = () => {\n    switch (item.status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n      case 'processing':\n        return 'bg-blue-100 text-blue-800 border-blue-200'\n      case 'completed':\n        return 'bg-green-100 text-green-800 border-green-200'\n      case 'error':\n        return 'bg-red-100 text-red-800 border-red-200'\n      case 'cancelled':\n        return 'bg-gray-100 text-gray-800 border-gray-200'\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200'\n    }\n  }\n\n  const compressionSavings = item.compressedSize \n    ? ((item.originalSize - item.compressedSize) / item.originalSize * 100).toFixed(1)\n    : null\n\n  return (\n    <>\n      <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-shadow duration-200\">\n        <div className=\"flex items-start space-x-4\">\n          {/* Preview/Icon */}\n          <div className=\"flex-shrink-0\">\n            {preview ? (\n              <div \n                className=\"w-16 h-16 rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity\"\n                onClick={() => setShowPreview(true)}\n              >\n                <img \n                  src={preview} \n                  alt={item.name}\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n            ) : (\n              <div className=\"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center\">\n                {getFileIcon()}\n              </div>\n            )}\n          </div>\n\n          {/* File Info */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-semibold text-gray-900 truncate\" title={item.name}>\n                  {item.name}\n                </h3>\n                <div className=\"flex items-center space-x-3 mt-1 text-sm text-gray-500\">\n                  <span>{formatFileSize(item.originalSize)}</span>\n                  <span className=\"capitalize\">{item.type}</span>\n                  <span className=\"uppercase\">{item.format}</span>\n                </div>\n                \n                {/* Compression Info */}\n                {item.status === 'completed' && item.compressedSize && (\n                  <div className=\"mt-2 text-sm\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-gray-600\">\n                        Compressed: {formatFileSize(item.compressedSize)}\n                      </span>\n                      {compressionSavings && (\n                        <span className=\"text-green-600 font-medium\">\n                          (-{compressionSavings}%)\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {/* Error Message */}\n                {item.status === 'error' && item.error && (\n                  <div className=\"mt-2 text-sm text-red-600\">\n                    {item.error}\n                  </div>\n                )}\n              </div>\n\n              {/* Actions */}\n              <div className=\"flex items-center space-x-2 ml-4\">\n                {preview && (\n                  <button\n                    onClick={() => setShowPreview(true)}\n                    className=\"p-2 text-gray-400 hover:text-blue-500 transition-colors\"\n                    title=\"Preview\"\n                  >\n                    <Eye className=\"w-4 h-4\" />\n                  </button>\n                )}\n                \n                {item.status === 'completed' && (\n                  <button\n                    onClick={() => onDownload(item.id)}\n                    className=\"p-2 text-gray-400 hover:text-green-500 transition-colors\"\n                    title=\"Download\"\n                  >\n                    <Download className=\"w-4 h-4\" />\n                  </button>\n                )}\n                \n                {item.status === 'error' && (\n                  <button\n                    onClick={() => onRetry(item.id)}\n                    className=\"p-2 text-gray-400 hover:text-blue-500 transition-colors\"\n                    title=\"Retry\"\n                  >\n                    <RotateCcw className=\"w-4 h-4\" />\n                  </button>\n                )}\n                \n                <button\n                  onClick={() => onRemove(item.id)}\n                  className=\"p-2 text-gray-400 hover:text-red-500 transition-colors\"\n                  title=\"Remove\"\n                >\n                  <X className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Progress Bar */}\n            {item.status === 'processing' && (\n              <div className=\"mt-3\">\n                <div className=\"flex items-center justify-between text-sm text-gray-600 mb-1\">\n                  <span>Processing...</span>\n                  <span>{Math.round(item.progress)}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div \n                    className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${item.progress}%` }}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Status Badge */}\n            <div className=\"mt-3 flex items-center\">\n              <div className={`\n                inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border\n                ${getStatusColor()}\n              `}>\n                {getStatusIcon()}\n                <span className=\"capitalize\">{item.status}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Preview Modal */}\n      {showPreview && preview && (\n        <div \n          className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\"\n          onClick={() => setShowPreview(false)}\n        >\n          <div className=\"max-w-4xl max-h-full\">\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowPreview(false)}\n                className=\"absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-colors z-10\"\n              >\n                <X className=\"w-6 h-6\" />\n              </button>\n              {item.type === 'video' ? (\n                <video \n                  src={URL.createObjectURL(item.file)}\n                  controls\n                  className=\"max-w-full max-h-full rounded-lg\"\n                />\n              ) : (\n                <img \n                  src={preview}\n                  alt={item.name}\n                  className=\"max-w-full max-h-full rounded-lg\"\n                />\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;AAmBO,SAAS,SAAS,KAAiE;QAAjE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAiB,GAAjE;;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;sDAAkB;oBACtB,IAAI;wBACF,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,OAAO;4BAChD,MAAM,aAAa,MAAM,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI;4BACrD,WAAW;wBACb,OAAO,IAAI,KAAK,IAAI,KAAK,SAAS;4BAChC,MAAM,aAAa,MAAM,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI;4BACrD,WAAW;wBACb;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C;gBACF;;YAEA,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB;YACF,OAAO;gBACL,WAAW,KAAK,OAAO;YACzB;QACF;6BAAG;QAAC;KAAK;IAET,MAAM,cAAc;QAClB,OAAQ,KAAK,IAAI;YACf,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;YACtB;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,KAAK,cAAc,GAC1C,CAAC,CAAC,KAAK,YAAY,GAAG,KAAK,cAAc,IAAI,KAAK,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC,KAC9E;IAEJ,qBACE;;0BACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,wBACC,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC;oCACC,KAAK;oCACL,KAAK,KAAK,IAAI;oCACd,WAAU;;;;;;;;;;qDAId,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;sCAMP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;oDAAuC,OAAO,KAAK,IAAI;8DAClE,KAAK,IAAI;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY;;;;;;sEACvC,6LAAC;4DAAK,WAAU;sEAAc,KAAK,IAAI;;;;;;sEACvC,6LAAC;4DAAK,WAAU;sEAAa,KAAK,MAAM;;;;;;;;;;;;gDAIzC,KAAK,MAAM,KAAK,eAAe,KAAK,cAAc,kBACjD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAgB;oEACjB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,cAAc;;;;;;;4DAEhD,oCACC,6LAAC;gEAAK,WAAU;;oEAA6B;oEACxC;oEAAmB;;;;;;;;;;;;;;;;;;gDAQ/B,KAAK,MAAM,KAAK,WAAW,KAAK,KAAK,kBACpC,6LAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK;;;;;;;;;;;;sDAMjB,6LAAC;4CAAI,WAAU;;gDACZ,yBACC,6LAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;gDAIlB,KAAK,MAAM,KAAK,6BACf,6LAAC;oDACC,SAAS,IAAM,WAAW,KAAK,EAAE;oDACjC,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;gDAIvB,KAAK,MAAM,KAAK,yBACf,6LAAC;oDACC,SAAS,IAAM,QAAQ,KAAK,EAAE;oDAC9B,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAIzB,6LAAC;oDACC,SAAS,IAAM,SAAS,KAAK,EAAE;oDAC/B,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gCAMlB,KAAK,MAAM,KAAK,8BACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,KAAK,KAAK,CAAC,KAAK,QAAQ;wDAAE;;;;;;;;;;;;;sDAEnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,AAAC,GAAgB,OAAd,KAAK,QAAQ,EAAC;gDAAG;;;;;;;;;;;;;;;;;8CAO5C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,2HAEI,OAAjB,kBAAiB;;4CAElB;0DACD,6LAAC;gDAAK,WAAU;0DAAc,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,eAAe,yBACd,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;0BAE9B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;4BAEd,KAAK,IAAI,KAAK,wBACb,6LAAC;gCACC,KAAK,IAAI,eAAe,CAAC,KAAK,IAAI;gCAClC,QAAQ;gCACR,WAAU;;;;;qDAGZ,6LAAC;gCACC,KAAK;gCACL,KAAK,KAAK,IAAI;gCACd,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAS5B;GAlPgB;KAAA", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/CompressionSettings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { CompressionSettingsProps, CompressionOptions } from '@/types'\nimport { Settings, Image as ImageIcon, Video, Sparkles } from 'lucide-react'\n\nexport function CompressionSettings({ type, options, onChange }: CompressionSettingsProps) {\n  const [isExpanded, setIsExpanded] = useState(false)\n\n  const handleQualityChange = (quality: number) => {\n    onChange({ ...options, quality })\n  }\n\n  const handleFormatChange = (format: string) => {\n    onChange({ ...options, outputFormat: format === 'auto' ? undefined : format })\n  }\n\n  const handleDimensionChange = (dimension: 'width' | 'height', value: string) => {\n    const numValue = value ? parseInt(value) : undefined\n    if (dimension === 'width') {\n      onChange({ ...options, maxWidth: numValue })\n    } else {\n      onChange({ ...options, maxHeight: numValue })\n    }\n  }\n\n  const handleToggleChange = (key: keyof CompressionOptions, value: boolean) => {\n    onChange({ ...options, [key]: value })\n  }\n\n  const getQualityLabel = (quality: number) => {\n    if (quality >= 0.8) return 'High Quality'\n    if (quality >= 0.6) return 'Medium Quality'\n    if (quality >= 0.4) return 'Low Quality'\n    return 'Maximum Compression'\n  }\n\n  const getFormatOptions = () => {\n    switch (type) {\n      case 'image':\n        return [\n          { value: 'auto', label: 'Keep Original' },\n          { value: 'jpeg', label: 'JPEG' },\n          { value: 'png', label: 'PNG' },\n          { value: 'webp', label: 'WebP' }\n        ]\n      case 'video':\n        return [\n          { value: 'auto', label: 'Keep Original' },\n          { value: 'mp4', label: 'MP4' },\n          { value: 'webm', label: 'WebM' },\n          { value: 'avi', label: 'AVI' }\n        ]\n      case 'gif':\n        return [\n          { value: 'auto', label: 'Keep as GIF' },\n          { value: 'gif', label: 'Optimized GIF' },\n          { value: 'mp4', label: 'Convert to MP4' },\n          { value: 'webm', label: 'Convert to WebM' }\n        ]\n      default:\n        return [{ value: 'auto', label: 'Keep Original' }]\n    }\n  }\n\n  const getTypeIcon = () => {\n    switch (type) {\n      case 'image':\n        return <ImageIcon className=\"w-5 h-5 text-blue-500\" />\n      case 'video':\n        return <Video className=\"w-5 h-5 text-purple-500\" />\n      case 'gif':\n        return <Sparkles className=\"w-5 h-5 text-pink-500\" />\n      default:\n        return <Settings className=\"w-5 h-5 text-gray-500\" />\n    }\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-200\">\n      <div \n        className=\"p-6 cursor-pointer\"\n        onClick={() => setIsExpanded(!isExpanded)}\n      >\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            {getTypeIcon()}\n            <h3 className=\"text-lg font-semibold text-gray-900 capitalize\">\n              {type} Compression Settings\n            </h3>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-500\">\n              Quality: {getQualityLabel(options.quality)}\n            </span>\n            <Settings className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />\n          </div>\n        </div>\n      </div>\n\n      {isExpanded && (\n        <div className=\"px-6 pb-6 border-t border-gray-100\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n            {/* Quality Slider */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Quality Level\n              </label>\n              <div className=\"space-y-3\">\n                <input\n                  type=\"range\"\n                  min=\"0.1\"\n                  max=\"1\"\n                  step=\"0.1\"\n                  value={options.quality}\n                  onChange={(e) => handleQualityChange(parseFloat(e.target.value))}\n                  className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n                />\n                <div className=\"flex justify-between text-xs text-gray-500\">\n                  <span>Max Compression</span>\n                  <span className=\"font-medium text-gray-700\">\n                    {getQualityLabel(options.quality)}\n                  </span>\n                  <span>Best Quality</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Output Format */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Output Format\n              </label>\n              <select\n                value={options.outputFormat || 'auto'}\n                onChange={(e) => handleFormatChange(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {getFormatOptions().map(option => (\n                  <option key={option.value} value={option.value}>\n                    {option.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Dimensions */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Maximum Dimensions (optional)\n              </label>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"number\"\n                  placeholder=\"Width\"\n                  value={options.maxWidth || ''}\n                  onChange={(e) => handleDimensionChange('width', e.target.value)}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <span className=\"flex items-center text-gray-500\">×</span>\n                <input\n                  type=\"number\"\n                  placeholder=\"Height\"\n                  value={options.maxHeight || ''}\n                  onChange={(e) => handleDimensionChange('height', e.target.value)}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Leave empty to keep original dimensions\n              </p>\n            </div>\n\n            {/* Additional Options */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Additional Options\n              </label>\n              <div className=\"space-y-3\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={options.maintainAspectRatio}\n                    onChange={(e) => handleToggleChange('maintainAspectRatio', e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">Maintain aspect ratio</span>\n                </label>\n                \n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={options.removeMetadata}\n                    onChange={(e) => handleToggleChange('removeMetadata', e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">Remove metadata</span>\n                </label>\n\n                {type === 'image' && (\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={(options as any).preserveTransparency !== false}\n                      onChange={(e) => handleToggleChange('preserveTransparency' as any, e.target.checked)}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                    <span className=\"ml-2 text-sm text-gray-700\">Preserve transparency</span>\n                  </label>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Presets */}\n          <div className=\"mt-6 pt-6 border-t border-gray-100\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n              Quick Presets\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => onChange({\n                  ...options,\n                  quality: 0.9,\n                  maxWidth: undefined,\n                  maxHeight: undefined,\n                  maintainAspectRatio: true,\n                  removeMetadata: false\n                })}\n                className=\"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors\"\n              >\n                High Quality\n              </button>\n              <button\n                onClick={() => onChange({\n                  ...options,\n                  quality: 0.7,\n                  maxWidth: 1920,\n                  maxHeight: 1080,\n                  maintainAspectRatio: true,\n                  removeMetadata: true\n                })}\n                className=\"px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors\"\n              >\n                Balanced\n              </button>\n              <button\n                onClick={() => onChange({\n                  ...options,\n                  quality: 0.5,\n                  maxWidth: 1280,\n                  maxHeight: 720,\n                  maintainAspectRatio: true,\n                  removeMetadata: true\n                })}\n                className=\"px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors\"\n              >\n                Small Size\n              </button>\n              <button\n                onClick={() => onChange({\n                  ...options,\n                  quality: 0.3,\n                  maxWidth: 800,\n                  maxHeight: 600,\n                  maintainAspectRatio: true,\n                  removeMetadata: true\n                })}\n                className=\"px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors\"\n              >\n                Maximum Compression\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;;;AAJA;;;AAMO,SAAS,oBAAoB,KAAqD;QAArD,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAA4B,GAArD;;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,sBAAsB,CAAC;QAC3B,SAAS;YAAE,GAAG,OAAO;YAAE;QAAQ;IACjC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,SAAS;YAAE,GAAG,OAAO;YAAE,cAAc,WAAW,SAAS,YAAY;QAAO;IAC9E;IAEA,MAAM,wBAAwB,CAAC,WAA+B;QAC5D,MAAM,WAAW,QAAQ,SAAS,SAAS;QAC3C,IAAI,cAAc,SAAS;YACzB,SAAS;gBAAE,GAAG,OAAO;gBAAE,UAAU;YAAS;QAC5C,OAAO;YACL,SAAS;gBAAE,GAAG,OAAO;gBAAE,WAAW;YAAS;QAC7C;IACF;IAEA,MAAM,qBAAqB,CAAC,KAA+B;QACzD,SAAS;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;IACtC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,WAAW,KAAK,OAAO;QAC3B,IAAI,WAAW,KAAK,OAAO;QAC3B,IAAI,WAAW,KAAK,OAAO;QAC3B,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAQ,OAAO;oBAAgB;oBACxC;wBAAE,OAAO;wBAAQ,OAAO;oBAAO;oBAC/B;wBAAE,OAAO;wBAAO,OAAO;oBAAM;oBAC7B;wBAAE,OAAO;wBAAQ,OAAO;oBAAO;iBAChC;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAQ,OAAO;oBAAgB;oBACxC;wBAAE,OAAO;wBAAO,OAAO;oBAAM;oBAC7B;wBAAE,OAAO;wBAAQ,OAAO;oBAAO;oBAC/B;wBAAE,OAAO;wBAAO,OAAO;oBAAM;iBAC9B;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO;wBAAQ,OAAO;oBAAc;oBACtC;wBAAE,OAAO;wBAAO,OAAO;oBAAgB;oBACvC;wBAAE,OAAO;wBAAO,OAAO;oBAAiB;oBACxC;wBAAE,OAAO;wBAAQ,OAAO;oBAAkB;iBAC3C;YACH;gBACE,OAAO;oBAAC;wBAAE,OAAO;wBAAQ,OAAO;oBAAgB;iBAAE;QACtD;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,cAAc,CAAC;0BAE9B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ;8CACD,6LAAC;oCAAG,WAAU;;wCACX;wCAAK;;;;;;;;;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;wCAAwB;wCAC5B,gBAAgB,QAAQ,OAAO;;;;;;;8CAE3C,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAW,AAAC,8CAA2E,OAA9B,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;YAKnG,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,MAAK;gDACL,OAAO,QAAQ,OAAO;gDACtB,UAAU,CAAC,IAAM,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC9D,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEACb,gBAAgB,QAAQ,OAAO;;;;;;kEAElC,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,QAAQ,YAAY,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;kDAET,mBAAmB,GAAG,CAAC,CAAA,uBACtB,6LAAC;gDAA0B,OAAO,OAAO,KAAK;0DAC3C,OAAO,KAAK;+CADF,OAAO,KAAK;;;;;;;;;;;;;;;;0CAQ/B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,QAAQ,IAAI;gDAC3B,UAAU,CAAC,IAAM,sBAAsB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC9D,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;0DAClD,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,SAAS,IAAI;gDAC5B,UAAU,CAAC,IAAM,sBAAsB,UAAU,EAAE,MAAM,CAAC,KAAK;gDAC/D,WAAU;;;;;;;;;;;;kDAGd,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,QAAQ,mBAAmB;wDACpC,UAAU,CAAC,IAAM,mBAAmB,uBAAuB,EAAE,MAAM,CAAC,OAAO;wDAC3E,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAG/C,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,QAAQ,cAAc;wDAC/B,UAAU,CAAC,IAAM,mBAAmB,kBAAkB,EAAE,MAAM,CAAC,OAAO;wDACtE,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;4CAG9C,SAAS,yBACR,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,AAAC,QAAgB,oBAAoB,KAAK;wDACnD,UAAU,CAAC,IAAM,mBAAmB,wBAA+B,EAAE,MAAM,CAAC,OAAO;wDACnF,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,SAAS;gDACtB,GAAG,OAAO;gDACV,SAAS;gDACT,UAAU;gDACV,WAAW;gDACX,qBAAqB;gDACrB,gBAAgB;4CAClB;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,SAAS;gDACtB,GAAG,OAAO;gDACV,SAAS;gDACT,UAAU;gDACV,WAAW;gDACX,qBAAqB;gDACrB,gBAAgB;4CAClB;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,SAAS;gDACtB,GAAG,OAAO;gDACV,SAAS;gDACT,UAAU;gDACV,WAAW;gDACX,qBAAqB;gDACrB,gBAAgB;4CAClB;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,SAAS;gDACtB,GAAG,OAAO;gDACV,SAAS;gDACT,UAAU;gDACV,WAAW;gDACX,qBAAqB;gDACrB,gBAAgB;4CAClB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhRgB;KAAA", "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/QueueManager.tsx"], "sourcesContent": ["'use client'\n\nimport { QueueManagerProps } from '@/types'\nimport { Play, Pause, Square, RotateCcw, Download, Trash2, Clock, CheckCircle, AlertCircle } from 'lucide-react'\nimport { formatFileSize } from '@/lib/utils'\n\nexport function QueueManager({\n  queue,\n  onPause,\n  onResume,\n  onCancel,\n  onCancelItem,\n  onRetryItem\n}: QueueManagerProps) {\n  const { items, isProcessing, currentIndex, totalProgress } = queue\n\n  const stats = {\n    total: items.length,\n    pending: items.filter(item => item.status === 'pending').length,\n    processing: items.filter(item => item.status === 'processing').length,\n    completed: items.filter(item => item.status === 'completed').length,\n    failed: items.filter(item => item.status === 'error').length,\n    cancelled: items.filter(item => item.status === 'cancelled').length\n  }\n\n  const totalOriginalSize = items.reduce((sum, item) => sum + item.originalSize, 0)\n  const totalCompressedSize = items\n    .filter(item => item.compressedSize)\n    .reduce((sum, item) => sum + (item.compressedSize || 0), 0)\n  \n  const overallSavings = totalOriginalSize > 0 \n    ? ((totalOriginalSize - totalCompressedSize) / totalOriginalSize * 100).toFixed(1)\n    : '0'\n\n  if (items.length === 0) {\n    return null\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Processing Queue</h3>\n          <p className=\"text-sm text-gray-600\">\n            {stats.completed} of {stats.total} files completed\n          </p>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          {isProcessing ? (\n            <button\n              onClick={onPause}\n              className=\"inline-flex items-center px-3 py-2 bg-yellow-600 text-white font-medium rounded-lg hover:bg-yellow-700 transition-colors\"\n            >\n              <Pause className=\"w-4 h-4 mr-2\" />\n              Pause\n            </button>\n          ) : stats.pending > 0 ? (\n            <button\n              onClick={onResume}\n              className=\"inline-flex items-center px-3 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <Play className=\"w-4 h-4 mr-2\" />\n              Resume\n            </button>\n          ) : null}\n          \n          {(isProcessing || stats.pending > 0) && (\n            <button\n              onClick={onCancel}\n              className=\"inline-flex items-center px-3 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors\"\n            >\n              <Square className=\"w-4 h-4 mr-2\" />\n              Cancel All\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Overall Progress */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">Overall Progress</span>\n          <span className=\"text-sm text-gray-600\">{Math.round(totalProgress)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-3\">\n          <div \n            className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300\"\n            style={{ width: `${totalProgress}%` }}\n          />\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mb-6\">\n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center mb-1\">\n            <Clock className=\"w-4 h-4 text-yellow-500 mr-1\" />\n            <span className=\"text-lg font-bold text-yellow-600\">{stats.pending}</span>\n          </div>\n          <div className=\"text-xs text-gray-600\">Pending</div>\n        </div>\n        \n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center mb-1\">\n            <div className=\"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-1\" />\n            <span className=\"text-lg font-bold text-blue-600\">{stats.processing}</span>\n          </div>\n          <div className=\"text-xs text-gray-600\">Processing</div>\n        </div>\n        \n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center mb-1\">\n            <CheckCircle className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-lg font-bold text-green-600\">{stats.completed}</span>\n          </div>\n          <div className=\"text-xs text-gray-600\">Completed</div>\n        </div>\n        \n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center mb-1\">\n            <AlertCircle className=\"w-4 h-4 text-red-500 mr-1\" />\n            <span className=\"text-lg font-bold text-red-600\">{stats.failed}</span>\n          </div>\n          <div className=\"text-xs text-gray-600\">Failed</div>\n        </div>\n        \n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center mb-1\">\n            <Download className=\"w-4 h-4 text-purple-500 mr-1\" />\n            <span className=\"text-lg font-bold text-purple-600\">{overallSavings}%</span>\n          </div>\n          <div className=\"text-xs text-gray-600\">Saved</div>\n        </div>\n      </div>\n\n      {/* Size Summary */}\n      {stats.completed > 0 && (\n        <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\">\n            <div>\n              <div className=\"text-sm text-gray-600\">Original Size</div>\n              <div className=\"text-lg font-semibold text-gray-900\">\n                {formatFileSize(totalOriginalSize)}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-sm text-gray-600\">Compressed Size</div>\n              <div className=\"text-lg font-semibold text-gray-900\">\n                {formatFileSize(totalCompressedSize)}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-sm text-gray-600\">Space Saved</div>\n              <div className=\"text-lg font-semibold text-green-600\">\n                {formatFileSize(totalOriginalSize - totalCompressedSize)} ({overallSavings}%)\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Current Processing Item */}\n      {isProcessing && currentIndex >= 0 && currentIndex < items.length && (\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <div className=\"text-sm font-medium text-blue-900\">Currently Processing</div>\n              <div className=\"text-blue-700\">{items[currentIndex].name}</div>\n            </div>\n            <div className=\"text-right\">\n              <div className=\"text-sm text-blue-600\">\n                {Math.round(items[currentIndex].progress)}%\n              </div>\n              <div className=\"w-24 bg-blue-200 rounded-full h-2 mt-1\">\n                <div \n                  className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${items[currentIndex].progress}%` }}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Quick Actions */}\n      <div className=\"flex flex-wrap gap-2\">\n        {stats.failed > 0 && (\n          <button\n            onClick={() => {\n              items\n                .filter(item => item.status === 'error')\n                .forEach(item => onRetryItem(item.id))\n            }}\n            className=\"inline-flex items-center px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors\"\n          >\n            <RotateCcw className=\"w-3 h-3 mr-1\" />\n            Retry Failed ({stats.failed})\n          </button>\n        )}\n        \n        {stats.completed > 0 && (\n          <button\n            className=\"inline-flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors\"\n          >\n            <Download className=\"w-3 h-3 mr-1\" />\n            Download All ({stats.completed})\n          </button>\n        )}\n        \n        <button\n          onClick={() => {\n            items.forEach(item => {\n              if (item.status === 'pending' || item.status === 'processing') {\n                onCancelItem(item.id)\n              }\n            })\n          }}\n          className=\"inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors\"\n        >\n          <Trash2 className=\"w-3 h-3 mr-1\" />\n          Clear Queue\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAMO,SAAS,aAAa,KAOT;QAPS,EAC3B,KAAK,EACL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,WAAW,EACO,GAPS;IAQ3B,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAE7D,MAAM,QAAQ;QACZ,OAAO,MAAM,MAAM;QACnB,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;QAC/D,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,cAAc,MAAM;QACrE,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;QACnE,QAAQ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,SAAS,MAAM;QAC5D,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;IACrE;IAEA,MAAM,oBAAoB,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;IAC/E,MAAM,sBAAsB,MACzB,MAAM,CAAC,CAAA,OAAQ,KAAK,cAAc,EAClC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,cAAc,IAAI,CAAC,GAAG;IAE3D,MAAM,iBAAiB,oBAAoB,IACvC,CAAC,CAAC,oBAAoB,mBAAmB,IAAI,oBAAoB,GAAG,EAAE,OAAO,CAAC,KAC9E;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;;oCACV,MAAM,SAAS;oCAAC;oCAAK,MAAM,KAAK;oCAAC;;;;;;;;;;;;;kCAItC,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;uCAGlC,MAAM,OAAO,GAAG,kBAClB,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;uCAGjC;4BAEH,CAAC,gBAAgB,MAAM,OAAO,GAAG,CAAC,mBACjC,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;0CACpD,6LAAC;gCAAK,WAAU;;oCAAyB,KAAK,KAAK,CAAC;oCAAe;;;;;;;;;;;;;kCAErE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,AAAC,GAAgB,OAAd,eAAc;4BAAG;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAqC,MAAM,OAAO;;;;;;;;;;;;0CAEpE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAmC,MAAM,UAAU;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAoC,MAAM,SAAS;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAkC,MAAM,MAAM;;;;;;;;;;;;0CAEhE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;;4CAAqC;4CAAe;;;;;;;;;;;;;0CAEtE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAK1C,MAAM,SAAS,GAAG,mBACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sCAGpB,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sCAGpB,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;wCACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB;wCAAqB;wCAAG;wCAAe;;;;;;;;;;;;;;;;;;;;;;;;YAQpF,gBAAgB,gBAAgB,KAAK,eAAe,MAAM,MAAM,kBAC/D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;8CAAiB,KAAK,CAAC,aAAa,CAAC,IAAI;;;;;;;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ;wCAAE;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,AAAC,GAA+B,OAA7B,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/D,6LAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,GAAG,mBACd,6LAAC;wBACC,SAAS;4BACP,MACG,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,SAC/B,OAAO,CAAC,CAAA,OAAQ,YAAY,KAAK,EAAE;wBACxC;wBACA,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;4BACvB,MAAM,MAAM;4BAAC;;;;;;;oBAI/B,MAAM,SAAS,GAAG,mBACjB,6LAAC;wBACC,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;4BACtB,MAAM,SAAS;4BAAC;;;;;;;kCAInC,6LAAC;wBACC,SAAS;4BACP,MAAM,OAAO,CAAC,CAAA;gCACZ,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK,cAAc;oCAC7D,aAAa,KAAK,EAAE;gCACtB;4BACF;wBACF;wBACA,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM7C;KA7NgB", "debugId": null}}, {"offset": {"line": 3000, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Zap, Menu, X, Home, Info, DollarSign } from 'lucide-react'\n\ninterface HeaderProps {\n  currentPath?: string\n}\n\nexport function Header({ currentPath = '/' }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: Home },\n    { name: 'Compress', href: '/compress', icon: Zap },\n    { name: 'About', href: '/about', icon: Info },\n    { name: 'Pricing', href: '/pricing', icon: DollarSign },\n  ]\n\n  const isActive = (href: string) => {\n    if (href === '/' && currentPath === '/') return true\n    if (href !== '/' && currentPath.startsWith(href)) return true\n    return false\n  }\n\n  return (\n    <header className=\"bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n              <Zap className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`\n                  flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                  ${isActive(item.href)\n                    ? 'text-blue-600 bg-blue-50'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  }\n                `}\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"w-6 h-6\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`\n                    flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-colors\n                    ${isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                    }\n                  `}\n                >\n                  <item.icon className=\"w-5 h-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n\n// Breadcrumb component for better navigation\ninterface BreadcrumbProps {\n  items: Array<{\n    label: string\n    href?: string\n  }>\n}\n\nexport function Breadcrumb({ items }: BreadcrumbProps) {\n  return (\n    <nav className=\"flex items-center space-x-2 text-sm text-gray-600 mb-6\">\n      {items.map((item, index) => (\n        <div key={index} className=\"flex items-center space-x-2\">\n          {index > 0 && (\n            <span className=\"text-gray-400\">/</span>\n          )}\n          {item.href ? (\n            <Link \n              href={item.href}\n              className=\"hover:text-gray-900 transition-colors\"\n            >\n              {item.label}\n            </Link>\n          ) : (\n            <span className=\"text-gray-900 font-medium\">{item.label}</span>\n          )}\n        </div>\n      ))}\n    </nav>\n  )\n}\n\n// Footer component\nexport function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerSections = [\n    {\n      title: 'Tools',\n      links: [\n        { name: 'Image Compressor', href: '/compress?type=image' },\n        { name: 'Video Compressor', href: '/compress?type=video' },\n        { name: 'GIF Optimizer', href: '/compress?type=gif' },\n        { name: 'Batch Processor', href: '/compress?mode=batch' },\n      ]\n    },\n    {\n      title: 'Support',\n      links: [\n        { name: 'Help Center', href: '/help' },\n        { name: 'Contact Us', href: '/contact' },\n        { name: 'FAQ', href: '/faq' },\n        { name: 'API Documentation', href: '/docs' },\n      ]\n    },\n    {\n      title: 'Company',\n      links: [\n        { name: 'About Us', href: '/about' },\n        { name: 'Privacy Policy', href: '/privacy' },\n        { name: 'Terms of Service', href: '/terms' },\n        { name: 'Blog', href: '/blog' },\n      ]\n    }\n  ]\n\n  return (\n    <footer className=\"bg-gray-50 border-t border-gray-200\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div>\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n            </div>\n            <p className=\"text-gray-600 text-sm leading-relaxed\">\n              The fastest and most secure way to compress your images and videos. \n              All processing happens locally in your browser.\n            </p>\n          </div>\n\n          {/* Footer sections */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"font-semibold text-gray-900 mb-4\">{section.title}</h4>\n              <ul className=\"space-y-2\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <Link \n                      href={link.href}\n                      className=\"text-gray-600 hover:text-gray-900 text-sm transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"border-t border-gray-200 mt-8 pt-8 text-center text-gray-600 text-sm\">\n          <p>&copy; {currentYear} CompressHub. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAkC;QAAlC,EAAE,cAAc,GAAG,EAAe,GAAlC;;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,sMAAA,CAAA,OAAI;QAAC;QACtC;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,mMAAA,CAAA,MAAG;QAAC;QACjD;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC5C;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,qNAAA,CAAA,aAAU;QAAC;KACvD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,OAAO,gBAAgB,KAAK,OAAO;QAChD,IAAI,SAAS,OAAO,YAAY,UAAU,CAAC,OAAO,OAAO;QACzD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,iIAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;sDAGH,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCAXX,KAAK,IAAI;;;;;;;;;;sCAiBpB,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,oBAAoB;gCACnC,WAAW,AAAC,qIAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;kDAGH,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;+BAZX,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBhC;GAxFgB;KAAA;AAkGT,SAAS,WAAW,KAA0B;QAA1B,EAAE,KAAK,EAAmB,GAA1B;IACzB,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAAgB,WAAU;;oBACxB,QAAQ,mBACP,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;oBAEjC,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,KAAK,IAAI;wBACf,WAAU;kCAET,KAAK,KAAK;;;;;6CAGb,6LAAC;wBAAK,WAAU;kCAA6B,KAAK,KAAK;;;;;;;eAZjD;;;;;;;;;;AAkBlB;MAtBgB;AAyBT,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAAqB;gBACpD;oBAAE,MAAM;oBAAmB,MAAM;gBAAuB;aACzD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAe,MAAM;gBAAQ;gBACrC;oBAAE,MAAM;oBAAc,MAAM;gBAAW;gBACvC;oBAAE,MAAM;oBAAO,MAAM;gBAAO;gBAC5B;oBAAE,MAAM;oBAAqB,MAAM;gBAAQ;aAC5C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAS;gBACnC;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAoB,MAAM;gBAAS;gBAC3C;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAC/B;QACH;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;wBAOtD,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,QAAQ,KAAK;;;;;;kDAC/D,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAkB3B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;4BAAE;4BAAQ;4BAAY;;;;;;;;;;;;;;;;;;;;;;;AAKjC;MA7EgB", "debugId": null}}, {"offset": {"line": 3456, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/UploadArea.tsx"], "sourcesContent": ["'use client'\n\nimport { useCallback, useState } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport { Upload, Image as ImageIcon, Video, FileImage, AlertCircle, CheckCircle } from 'lucide-react'\nimport { UploadAreaProps } from '@/types'\nimport { formatFileSize } from '@/lib/utils'\n\nexport function UploadArea({\n  onFilesSelected,\n  acceptedTypes,\n  maxFiles = 10,\n  maxFileSize = 100 * 1024 * 1024, // 100MB\n  disabled = false\n}: UploadAreaProps) {\n  const [dragActive, setDragActive] = useState(false)\n  const [uploadErrors, setUploadErrors] = useState<string[]>([])\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setUploadErrors([])\n    \n    // Handle rejected files\n    if (rejectedFiles.length > 0) {\n      const errors: string[] = []\n      rejectedFiles.forEach(({ file, errors: fileErrors }) => {\n        fileErrors.forEach((error: any) => {\n          if (error.code === 'file-too-large') {\n            errors.push(`${file.name}: File is too large (max ${formatFileSize(maxFileSize)})`)\n          } else if (error.code === 'file-invalid-type') {\n            errors.push(`${file.name}: File type not supported`)\n          } else if (error.code === 'too-many-files') {\n            errors.push(`Too many files selected (max ${maxFiles})`)\n          } else {\n            errors.push(`${file.name}: ${error.message}`)\n          }\n        })\n      })\n      setUploadErrors(errors)\n    }\n\n    // Handle accepted files\n    if (acceptedFiles.length > 0) {\n      onFilesSelected(acceptedFiles)\n    }\n  }, [onFilesSelected, maxFiles, maxFileSize])\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: acceptedTypes.reduce((acc, type) => {\n      acc[type] = []\n      return acc\n    }, {} as Record<string, string[]>),\n    maxFiles,\n    maxSize: maxFileSize,\n    disabled,\n    onDragEnter: () => setDragActive(true),\n    onDragLeave: () => setDragActive(false),\n    onDropAccepted: () => setDragActive(false),\n    onDropRejected: () => setDragActive(false)\n  })\n\n  const getAcceptedFormats = () => {\n    const formats = acceptedTypes.map(type => {\n      if (type.startsWith('image/')) return 'Images'\n      if (type.startsWith('video/')) return 'Videos'\n      return type\n    })\n    return [...new Set(formats)].join(', ')\n  }\n\n  return (\n    <div className=\"w-full\">\n      {/* Upload Area */}\n      <div\n        {...getRootProps()}\n        className={`\n          relative border-2 border-dashed rounded-2xl p-8 md:p-12 text-center cursor-pointer transition-all duration-200\n          ${disabled \n            ? 'border-gray-200 bg-gray-50 cursor-not-allowed' \n            : dragActive || isDragActive\n              ? 'border-blue-500 bg-blue-50 scale-[1.02]' \n              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n          }\n        `}\n      >\n        <input {...getInputProps()} />\n        \n        <div className=\"flex flex-col items-center space-y-4\">\n          {/* Icon */}\n          <div className={`\n            w-16 h-16 md:w-20 md:h-20 rounded-full flex items-center justify-center transition-all duration-200\n            ${disabled \n              ? 'bg-gray-200' \n              : dragActive || isDragActive \n                ? 'bg-blue-100 scale-110' \n                : 'bg-gray-100'\n            }\n          `}>\n            <Upload className={`\n              w-8 h-8 md:w-10 md:h-10 transition-colors\n              ${disabled \n                ? 'text-gray-400' \n                : dragActive || isDragActive \n                  ? 'text-blue-600' \n                  : 'text-gray-600'\n              }\n            `} />\n          </div>\n\n          {/* Text */}\n          <div className=\"space-y-2\">\n            <h3 className={`\n              text-xl md:text-2xl font-semibold transition-colors\n              ${disabled \n                ? 'text-gray-400' \n                : dragActive || isDragActive \n                  ? 'text-blue-900' \n                  : 'text-gray-900'\n              }\n            `}>\n              {disabled \n                ? 'Upload disabled' \n                : dragActive || isDragActive \n                  ? 'Drop files here' \n                  : 'Drag & drop files here'\n              }\n            </h3>\n            \n            {!disabled && (\n              <>\n                <p className=\"text-gray-600 text-base md:text-lg\">\n                  or click to select files from your device\n                </p>\n                \n                <div className=\"text-sm text-gray-500 space-y-1\">\n                  <p>Supports: {getAcceptedFormats()}</p>\n                  <p>Maximum {maxFiles} files, {formatFileSize(maxFileSize)} each</p>\n                </div>\n              </>\n            )}\n          </div>\n\n          {/* Visual indicators */}\n          {!disabled && (\n            <div className=\"flex items-center justify-center space-x-6 pt-4\">\n              <div className=\"flex items-center space-x-2 text-blue-600\">\n                <ImageIcon className=\"w-5 h-5\" />\n                <span className=\"text-sm font-medium\">Images</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-purple-600\">\n                <Video className=\"w-5 h-5\" />\n                <span className=\"text-sm font-medium\">Videos</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-pink-600\">\n                <FileImage className=\"w-5 h-5\" />\n                <span className=\"text-sm font-medium\">GIFs</span>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Drag overlay */}\n        {(dragActive || isDragActive) && !disabled && (\n          <div className=\"absolute inset-0 bg-blue-500/10 rounded-2xl flex items-center justify-center\">\n            <div className=\"bg-white rounded-lg px-6 py-3 shadow-lg\">\n              <div className=\"flex items-center space-x-2 text-blue-600\">\n                <CheckCircle className=\"w-5 h-5\" />\n                <span className=\"font-medium\">Drop to upload</span>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Error Messages */}\n      {uploadErrors.length > 0 && (\n        <div className=\"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-start space-x-2\">\n            <AlertCircle className=\"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5\" />\n            <div className=\"flex-1\">\n              <h4 className=\"text-sm font-medium text-red-800 mb-1\">\n                Upload Errors\n              </h4>\n              <ul className=\"text-sm text-red-700 space-y-1\">\n                {uploadErrors.map((error, index) => (\n                  <li key={index}>• {error}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Help Text */}\n      <div className=\"mt-4 text-center\">\n        <p className=\"text-xs text-gray-500\">\n          All files are processed locally in your browser. Nothing is uploaded to our servers.\n        </p>\n      </div>\n    </div>\n  )\n}\n\n// Compact version for smaller spaces\nexport function CompactUploadArea({ onFilesSelected, acceptedTypes, disabled = false }: Omit<UploadAreaProps, 'maxFiles' | 'maxFileSize'>) {\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop: onFilesSelected,\n    accept: acceptedTypes.reduce((acc, type) => {\n      acc[type] = []\n      return acc\n    }, {} as Record<string, string[]>),\n    disabled\n  })\n\n  return (\n    <div\n      {...getRootProps()}\n      className={`\n        border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200\n        ${disabled \n          ? 'border-gray-200 bg-gray-50 cursor-not-allowed' \n          : isDragActive\n            ? 'border-blue-500 bg-blue-50' \n            : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n        }\n      `}\n    >\n      <input {...getInputProps()} />\n      <div className=\"flex flex-col items-center space-y-2\">\n        <Upload className={`w-6 h-6 ${isDragActive ? 'text-blue-600' : 'text-gray-600'}`} />\n        <p className={`text-sm font-medium ${isDragActive ? 'text-blue-900' : 'text-gray-900'}`}>\n          {isDragActive ? 'Drop files here' : 'Click or drag files here'}\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AANA;;;;;AAQO,SAAS,WAAW,KAMT;QANS,EACzB,eAAe,EACf,aAAa,EACb,WAAW,EAAE,EACb,cAAc,MAAM,OAAO,IAAI,EAC/B,WAAW,KAAK,EACA,GANS;;IAOzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC,eAAuB;YACjD,gBAAgB,EAAE;YAElB,wBAAwB;YACxB,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,SAAmB,EAAE;gBAC3B,cAAc,OAAO;sDAAC;4BAAC,EAAE,IAAI,EAAE,QAAQ,UAAU,EAAE;wBACjD,WAAW,OAAO;8DAAC,CAAC;gCAClB,IAAI,MAAM,IAAI,KAAK,kBAAkB;oCACnC,OAAO,IAAI,CAAC,AAAC,GAAuC,OAArC,KAAK,IAAI,EAAC,6BAAuD,OAA5B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,cAAa;gCAClF,OAAO,IAAI,MAAM,IAAI,KAAK,qBAAqB;oCAC7C,OAAO,IAAI,CAAC,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC;gCAC3B,OAAO,IAAI,MAAM,IAAI,KAAK,kBAAkB;oCAC1C,OAAO,IAAI,CAAC,AAAC,gCAAwC,OAAT,UAAS;gCACvD,OAAO;oCACL,OAAO,IAAI,CAAC,AAAC,GAAgB,OAAd,KAAK,IAAI,EAAC,MAAkB,OAAd,MAAM,OAAO;gCAC5C;4BACF;;oBACF;;gBACA,gBAAgB;YAClB;YAEA,wBAAwB;YACxB,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,gBAAgB;YAClB;QACF;yCAAG;QAAC;QAAiB;QAAU;KAAY;IAE3C,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ,cAAc,MAAM;sCAAC,CAAC,KAAK;gBACjC,GAAG,CAAC,KAAK,GAAG,EAAE;gBACd,OAAO;YACT;qCAAG,CAAC;QACJ;QACA,SAAS;QACT;QACA,WAAW;sCAAE,IAAM,cAAc;;QACjC,WAAW;sCAAE,IAAM,cAAc;;QACjC,cAAc;sCAAE,IAAM,cAAc;;QACpC,cAAc;sCAAE,IAAM,cAAc;;IACtC;IAEA,MAAM,qBAAqB;QACzB,MAAM,UAAU,cAAc,GAAG,CAAC,CAAA;YAChC,IAAI,KAAK,UAAU,CAAC,WAAW,OAAO;YACtC,IAAI,KAAK,UAAU,CAAC,WAAW,OAAO;YACtC,OAAO;QACT;QACA,OAAO;eAAI,IAAI,IAAI;SAAS,CAAC,IAAI,CAAC;IACpC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,AAAC,yIAOT,OALC,WACE,kDACA,cAAc,eACZ,4CACA,0DACL;;kCAGH,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAW,AAAC,kIAOd,OALC,WACE,gBACA,cAAc,eACZ,0BACA,eACL;0CAED,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAW,AAAC,4EAOjB,OALC,WACE,kBACA,cAAc,eACZ,kBACA,iBACL;;;;;;;;;;;0CAKL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAW,AAAC,sFAOb,OALC,WACE,kBACA,cAAc,eACZ,kBACA,iBACL;kDAEA,WACG,oBACA,cAAc,eACZ,oBACA;;;;;;oCAIP,CAAC,0BACA;;0DACE,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAE;4DAAW;;;;;;;kEACd,6LAAC;;4DAAE;4DAAS;4DAAS;4DAAS,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;4DAAa;;;;;;;;;;;;;;;;;;;;;4BAOjE,CAAC,0BACA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;;oBAO7C,CAAC,cAAc,YAAY,KAAK,CAAC,0BAChC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQvC,aAAa,MAAM,GAAG,mBACrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;;gDAAe;gDAAG;;2CAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAM7C;GAjMgB;;QAsCwC,2KAAA,CAAA,cAAW;;;KAtCnD;AAoMT,SAAS,kBAAkB,KAAuG;QAAvG,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,KAAK,EAAqD,GAAvG;;IAChC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE,QAAQ;QACR,QAAQ,cAAc,MAAM;6CAAC,CAAC,KAAK;gBACjC,GAAG,CAAC,KAAK,GAAG,EAAE;gBACd,OAAO;YACT;4CAAG,CAAC;QACJ;IACF;IAEA,qBACE,6LAAC;QACE,GAAG,cAAc;QAClB,WAAW,AAAC,mHAOT,OALC,WACE,kDACA,eACE,+BACA,0DACL;;0BAGH,6LAAC;gBAAO,GAAG,eAAe;;;;;;0BAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAW,AAAC,WAA2D,OAAjD,eAAe,kBAAkB;;;;;;kCAC/D,6LAAC;wBAAE,WAAW,AAAC,uBAAuE,OAAjD,eAAe,kBAAkB;kCACnE,eAAe,oBAAoB;;;;;;;;;;;;;;;;;;AAK9C;IAhCgB;;QACwC,2KAAA,CAAA,cAAW;;;MADnD", "debugId": null}}, {"offset": {"line": 3934, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/compress/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport { Upload, Download, Trash2, Play, Pause, Zap } from 'lucide-react'\nimport Link from 'next/link'\nimport { FileItem, CompressionOptions } from '@/types'\nimport { generateUniqueId, isImageFile, isVideoFile, isGifFile, formatFileSize } from '@/lib/utils'\nimport { useCompressionManager } from '@/hooks/useCompressionManager'\nimport { FileCard } from '@/components/FileCard'\nimport { CompressionSettings } from '@/components/CompressionSettings'\nimport { BatchProgressBar } from '@/components/ProgressBar'\nimport { QueueManager } from '@/components/QueueManager'\nimport { Header, Breadcrumb } from '@/components/Header'\nimport { UploadArea } from '@/components/UploadArea'\nimport {\n  CompressionAnimation,\n  FloatingIcons,\n  ProgressRing,\n  PulseButton,\n  SlideInCard,\n  AnimatedProcessing\n} from '@/components/AnimatedIcons'\n\nexport default function CompressPage() {\n  const [isDragActive, setIsDragActive] = useState(false)\n  const [compressionOptions, setCompressionOptions] = useState<CompressionOptions>({\n    quality: 0.7,\n    maintainAspectRatio: true,\n    removeMetadata: true\n  })\n\n  const {\n    files,\n    isProcessing,\n    currentProcessingIndex,\n    addFiles,\n    removeFile,\n    compressAllFiles,\n    retryFile,\n    downloadFile: downloadSingleFile,\n    downloadAllFiles,\n    clearAllFiles,\n    getStats\n  } = useCompressionManager()\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const newFiles: FileItem[] = acceptedFiles.map(file => ({\n      id: generateUniqueId(),\n      file,\n      name: file.name,\n      size: file.size,\n      type: isGifFile(file) ? 'gif' : isImageFile(file) ? 'image' : 'video',\n      format: file.name.split('.').pop()?.toLowerCase() || '',\n      status: 'pending',\n      progress: 0,\n      originalSize: file.size\n    }))\n\n    addFiles(newFiles)\n  }, [addFiles])\n\n  const acceptedTypes = [\n    'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/bmp', 'image/tiff',\n    'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm', 'video/mkv', 'video/3gp'\n  ]\n\n  const stats = getStats()\n\n  const handleStartCompression = () => {\n    compressAllFiles(compressionOptions)\n  }\n\n  const handleRetry = (id: string) => {\n    retryFile(id, compressionOptions)\n  }\n\n  const handlePreview = (id: string) => {\n    // Preview functionality is handled within FileCard component\n    console.log('Preview file:', id)\n  }\n\n  const handlePauseQueue = () => {\n    // Pause functionality would be implemented in the compression manager\n    console.log('Pause queue')\n  }\n\n  const handleResumeQueue = () => {\n    handleStartCompression()\n  }\n\n  const handleCancelQueue = () => {\n    // Cancel all processing files\n    files.forEach(file => {\n      if (file.status === 'processing' || file.status === 'pending') {\n        // This would need to be implemented in the compression manager\n        console.log('Cancel file:', file.id)\n      }\n    })\n  }\n\n  const handleCancelItem = (id: string) => {\n    // Cancel specific item\n    console.log('Cancel item:', id)\n  }\n\n  const handleRetryItem = (id: string) => {\n    handleRetry(id)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <Header currentPath=\"/compress\" />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          <Breadcrumb\n            items={[\n              { label: 'Home', href: '/' },\n              { label: 'Compress Files' }\n            ]}\n          />\n\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              Compress Your Files\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              Upload images and videos to compress them while maintaining quality\n            </p>\n          </div>\n\n          {/* Upload Area */}\n          <UploadArea\n            onFilesSelected={onDrop}\n            acceptedTypes={acceptedTypes}\n            maxFiles={20}\n            maxFileSize={100 * 1024 * 1024} // 100MB\n            disabled={isProcessing}\n          />\n\n          {/* Stats and Progress */}\n          {files.length > 0 && (\n            <div className=\"mt-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-2xl font-bold text-gray-900\">\n                  Files ({stats.total})\n                </h2>\n                <div className=\"flex items-center space-x-4\">\n                  {stats.completed > 0 && (\n                    <button\n                      onClick={downloadAllFiles}\n                      className=\"inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors\"\n                    >\n                      <Download className=\"w-4 h-4 mr-2\" />\n                      Download All ({stats.completed})\n                    </button>\n                  )}\n                  <button\n                    onClick={clearAllFiles}\n                    className=\"inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors\"\n                  >\n                    <Trash2 className=\"w-4 h-4 mr-2\" />\n                    Clear All\n                  </button>\n                </div>\n              </div>\n\n              {/* Queue Manager */}\n              <div className=\"mb-6\">\n                <QueueManager\n                  queue={{\n                    items: files,\n                    isProcessing,\n                    currentIndex: currentProcessingIndex,\n                    totalProgress: stats.overallProgress,\n                    canPause: isProcessing,\n                    canCancel: isProcessing || stats.pending > 0\n                  }}\n                  onPause={handlePauseQueue}\n                  onResume={handleResumeQueue}\n                  onCancel={handleCancelQueue}\n                  onCancelItem={handleCancelItem}\n                  onRetryItem={handleRetryItem}\n                />\n              </div>\n\n              {/* File Cards */}\n              <div className=\"space-y-4\">\n                {files.map((file) => (\n                  <FileCard\n                    key={file.id}\n                    item={file}\n                    onRemove={removeFile}\n                    onRetry={handleRetry}\n                    onDownload={downloadSingleFile}\n                    onPreview={handlePreview}\n                  />\n                ))}\n              </div>\n\n              {/* Compression Settings */}\n              <div className=\"mt-8\">\n                {/* Determine the primary file type */}\n                {(() => {\n                  const imageFiles = files.filter(f => f.type === 'image').length\n                  const videoFiles = files.filter(f => f.type === 'video').length\n                  const gifFiles = files.filter(f => f.type === 'gif').length\n\n                  let primaryType: 'image' | 'video' | 'gif' = 'image'\n                  if (videoFiles > imageFiles && videoFiles > gifFiles) {\n                    primaryType = 'video'\n                  } else if (gifFiles > imageFiles && gifFiles > videoFiles) {\n                    primaryType = 'gif'\n                  }\n\n                  return (\n                    <CompressionSettings\n                      type={primaryType}\n                      options={compressionOptions}\n                      onChange={setCompressionOptions}\n                    />\n                  )\n                })()}\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"mt-8 flex justify-center space-x-4\">\n                <button\n                  onClick={handleStartCompression}\n                  disabled={!stats.canProcess}\n                  className={`\n                    inline-flex items-center px-8 py-3 font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl\n                    ${stats.canProcess\n                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'\n                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                    }\n                  `}\n                >\n                  {isProcessing ? (\n                    <>\n                      <Pause className=\"w-5 h-5 mr-2\" />\n                      Processing...\n                    </>\n                  ) : (\n                    <>\n                      <Play className=\"w-5 h-5 mr-2\" />\n                      Start Compression\n                    </>\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAdA;;;;;;;;;;AAwBe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QAC/E,SAAS;QACT,qBAAqB;QACrB,gBAAgB;IAClB;IAEA,MAAM,EACJ,KAAK,EACL,YAAY,EACZ,sBAAsB,EACtB,QAAQ,EACR,UAAU,EACV,gBAAgB,EAChB,SAAS,EACT,cAAc,kBAAkB,EAChC,gBAAgB,EAChB,aAAa,EACb,QAAQ,EACT,GAAG,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IAExB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YAC1B,MAAM,WAAuB,cAAc,GAAG;6DAAC,CAAA;wBAMrC;2BAN8C;wBACtD,IAAI,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD;wBACnB;wBACA,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,MAAM,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,UAAU;wBAC9D,QAAQ,EAAA,uBAAA,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,gBAAxB,2CAAA,qBAA4B,WAAW,OAAM;wBACrD,QAAQ;wBACR,UAAU;wBACV,cAAc,KAAK,IAAI;oBACzB;;;YAEA,SAAS;QACX;2CAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB;QACpB;QAAc;QAAa;QAAa;QAAc;QAAa;QAAa;QAChF;QAAa;QAAa;QAAa;QAAa;QAAa;QAAc;QAAa;KAC7F;IAED,MAAM,QAAQ;IAEd,MAAM,yBAAyB;QAC7B,iBAAiB;IACnB;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,IAAI;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,6DAA6D;QAC7D,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,mBAAmB;QACvB,sEAAsE;QACtE,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,oBAAoB;QACxB;IACF;IAEA,MAAM,oBAAoB;QACxB,8BAA8B;QAC9B,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,KAAK,MAAM,KAAK,gBAAgB,KAAK,MAAM,KAAK,WAAW;gBAC7D,+DAA+D;gBAC/D,QAAQ,GAAG,CAAC,gBAAgB,KAAK,EAAE;YACrC;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,uBAAuB;QACvB,QAAQ,GAAG,CAAC,gBAAgB;IAC9B;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,SAAM;gBAAC,aAAY;;;;;;0BAEpB,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+HAAA,CAAA,aAAU;4BACT,OAAO;gCACL;oCAAE,OAAO;oCAAQ,MAAM;gCAAI;gCAC3B;oCAAE,OAAO;gCAAiB;6BAC3B;;;;;;sCAGH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,6LAAC,mIAAA,CAAA,aAAU;4BACT,iBAAiB;4BACjB,eAAe;4BACf,UAAU;4BACV,aAAa,MAAM,OAAO;4BAC1B,UAAU;;;;;;wBAIX,MAAM,MAAM,GAAG,mBACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAmC;gDACvC,MAAM,KAAK;gDAAC;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;gDACZ,MAAM,SAAS,GAAG,mBACjB,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;wDACtB,MAAM,SAAS;wDAAC;;;;;;;8DAGnC,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAOzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,eAAY;wCACX,OAAO;4CACL,OAAO;4CACP;4CACA,cAAc;4CACd,eAAe,MAAM,eAAe;4CACpC,UAAU;4CACV,WAAW,gBAAgB,MAAM,OAAO,GAAG;wCAC7C;wCACA,SAAS;wCACT,UAAU;wCACV,UAAU;wCACV,cAAc;wCACd,aAAa;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,iIAAA,CAAA,WAAQ;4CAEP,MAAM;4CACN,UAAU;4CACV,SAAS;4CACT,YAAY;4CACZ,WAAW;2CALN,KAAK,EAAE;;;;;;;;;;8CAWlB,6LAAC;oCAAI,WAAU;8CAEZ,CAAC;wCACA,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;wCAC/D,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;wCAC/D,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,MAAM;wCAE3D,IAAI,cAAyC;wCAC7C,IAAI,aAAa,cAAc,aAAa,UAAU;4CACpD,cAAc;wCAChB,OAAO,IAAI,WAAW,cAAc,WAAW,YAAY;4CACzD,cAAc;wCAChB;wCAEA,qBACE,6LAAC,4IAAA,CAAA,sBAAmB;4CAClB,MAAM;4CACN,SAAS;4CACT,UAAU;;;;;;oCAGhB,CAAC;;;;;;8CAIH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,MAAM,UAAU;wCAC3B,WAAW,AAAC,gKAKT,OAHC,MAAM,UAAU,GACd,oGACA,gDACH;kDAGF,6BACC;;8DACE,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;yEAIpC;;8DACE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD;GA1OwB;;QAoBlB,wIAAA,CAAA,wBAAqB;;;KApBH", "debugId": null}}]}