{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Zap, Menu, X, Home, Info, DollarSign, ImageIcon, FileImage, Video } from 'lucide-react'\n\ninterface HeaderProps {\n  currentPath?: string\n}\n\nexport function Header({ currentPath = '/' }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: Home },\n    {\n      name: 'Compress',\n      href: '/compress',\n      icon: Zap,\n      submenu: [\n        { name: 'Images', href: '/image-compress', icon: ImageIcon, description: 'PNG, JPEG, WebP' },\n        { name: 'GIF Animation', href: '/gif-compress', icon: FileImage, description: 'Animated GIFs' },\n        { name: 'Videos', href: '/video-compress', icon: Video, description: 'MP4, AVI, MOV' },\n      ]\n    },\n    { name: 'About', href: '/about', icon: Info },\n    { name: 'Pricing', href: '/pricing', icon: DollarSign },\n  ]\n\n  const isActive = (href: string) => {\n    if (href === '/' && currentPath === '/') return true\n    if (href !== '/' && currentPath.startsWith(href)) return true\n    // Special handling for compress pages\n    if (href === '/compress' && (\n      currentPath.startsWith('/image-compress') ||\n      currentPath.startsWith('/gif-compress') ||\n      currentPath.startsWith('/video-compress')\n    )) return true\n    return false\n  }\n\n  return (\n    <header className=\"bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n              <Zap className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                {item.submenu ? (\n                  <>\n                    <Link\n                      href={item.href}\n                      className={`\n                        flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                        ${isActive(item.href)\n                          ? 'text-blue-600 bg-blue-50'\n                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                        }\n                      `}\n                    >\n                      <item.icon className=\"w-4 h-4\" />\n                      <span>{item.name}</span>\n                    </Link>\n\n                    {/* Dropdown Menu */}\n                    <div className=\"absolute top-full left-0 pt-2 w-64 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200\">\n                        <div className=\"p-2\">\n                          {item.submenu.map((subItem) => (\n                            <Link\n                              key={subItem.name}\n                              href={subItem.href}\n                              className={`\n                                flex items-center space-x-3 px-3 py-3 rounded-lg text-sm transition-colors\n                                ${currentPath === subItem.href\n                                  ? 'text-blue-600 bg-blue-50'\n                                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'\n                                }\n                              `}\n                            >\n                              <subItem.icon className=\"w-5 h-5 flex-shrink-0\" />\n                              <div>\n                                <div className=\"font-medium\">{subItem.name}</div>\n                                <div className=\"text-xs text-gray-500\">{subItem.description}</div>\n                              </div>\n                            </Link>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className={`\n                      flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                      ${isActive(item.href)\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    <item.icon className=\"w-4 h-4\" />\n                    <span>{item.name}</span>\n                  </Link>\n                )}\n              </div>\n            ))}\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"w-6 h-6\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-2\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  <Link\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`\n                      flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-colors\n                      ${isActive(item.href)\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    <item.icon className=\"w-5 h-5\" />\n                    <span>{item.name}</span>\n                  </Link>\n\n                  {/* Mobile Submenu */}\n                  {item.submenu && (\n                    <div className=\"ml-8 mt-2 space-y-1\">\n                      {item.submenu.map((subItem) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          onClick={() => setIsMobileMenuOpen(false)}\n                          className={`\n                            flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors\n                            ${currentPath === subItem.href\n                              ? 'text-blue-600 bg-blue-50'\n                              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                            }\n                          `}\n                        >\n                          <subItem.icon className=\"w-4 h-4 flex-shrink-0\" />\n                          <div>\n                            <div className=\"font-medium\">{subItem.name}</div>\n                            <div className=\"text-xs text-gray-400\">{subItem.description}</div>\n                          </div>\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n\n// Breadcrumb component for better navigation\ninterface BreadcrumbProps {\n  items: Array<{\n    label: string\n    href?: string\n  }>\n}\n\nexport function Breadcrumb({ items }: BreadcrumbProps) {\n  return (\n    <nav className=\"flex items-center space-x-2 text-sm text-gray-600 mb-6\">\n      {items.map((item, index) => (\n        <div key={index} className=\"flex items-center space-x-2\">\n          {index > 0 && (\n            <span className=\"text-gray-400\">/</span>\n          )}\n          {item.href ? (\n            <Link \n              href={item.href}\n              className=\"hover:text-gray-900 transition-colors\"\n            >\n              {item.label}\n            </Link>\n          ) : (\n            <span className=\"text-gray-900 font-medium\">{item.label}</span>\n          )}\n        </div>\n      ))}\n    </nav>\n  )\n}\n\n// Footer component\nexport function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerSections = [\n    {\n      title: 'Tools',\n      links: [\n        { name: 'Image Compressor', href: '/compress?type=image' },\n        { name: 'Video Compressor', href: '/compress?type=video' },\n        { name: 'GIF Optimizer', href: '/compress?type=gif' },\n        { name: 'Batch Processor', href: '/compress?mode=batch' },\n      ]\n    },\n    {\n      title: 'Support',\n      links: [\n        { name: 'Help Center', href: '/help' },\n        { name: 'Contact Us', href: '/contact' },\n        { name: 'FAQ', href: '/faq' },\n        { name: 'API Documentation', href: '/docs' },\n      ]\n    },\n    {\n      title: 'Company',\n      links: [\n        { name: 'About Us', href: '/about' },\n        { name: 'Privacy Policy', href: '/privacy' },\n        { name: 'Terms of Service', href: '/terms' },\n        { name: 'Blog', href: '/blog' },\n      ]\n    }\n  ]\n\n  return (\n    <footer className=\"bg-gray-50 border-t border-gray-200\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div>\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">CompressHub</span>\n            </div>\n            <p className=\"text-gray-600 text-sm leading-relaxed\">\n              The fastest and most secure way to compress your images and videos. \n              All processing happens locally in your browser.\n            </p>\n          </div>\n\n          {/* Footer sections */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"font-semibold text-gray-900 mb-4\">{section.title}</h4>\n              <ul className=\"space-y-2\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <Link \n                      href={link.href}\n                      className=\"text-gray-600 hover:text-gray-900 text-sm transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"border-t border-gray-200 mt-8 pt-8 text-center text-gray-600 text-sm\">\n          <p className=\"mb-2\">&copy; {currentYear} CompressHub. All rights reserved.</p>\n          <p>\n            By using this site you accept the{' '}\n            <Link href=\"/terms\" className=\"text-blue-600 hover:text-blue-800 underline\">\n              terms of use\n            </Link>\n            {' '}and our{' '}\n            <Link href=\"/privacy\" className=\"text-blue-600 hover:text-blue-800 underline\">\n              privacy policy\n            </Link>\n            .\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAkC;QAAlC,EAAE,cAAc,GAAG,EAAe,GAAlC;;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,sMAAA,CAAA,OAAI;QAAC;QACtC;YACE,MAAM;YACN,MAAM;YACN,MAAM,mMAAA,CAAA,MAAG;YACT,SAAS;gBACP;oBAAE,MAAM;oBAAU,MAAM;oBAAmB,MAAM,2MAAA,CAAA,YAAS;oBAAE,aAAa;gBAAkB;gBAC3F;oBAAE,MAAM;oBAAiB,MAAM;oBAAiB,MAAM,mNAAA,CAAA,YAAS;oBAAE,aAAa;gBAAgB;gBAC9F;oBAAE,MAAM;oBAAU,MAAM;oBAAmB,MAAM,uMAAA,CAAA,QAAK;oBAAE,aAAa;gBAAgB;aACtF;QACH;QACA;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC5C;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,qNAAA,CAAA,aAAU;QAAC;KACvD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,OAAO,gBAAgB,KAAK,OAAO;QAChD,IAAI,SAAS,OAAO,YAAY,UAAU,CAAC,OAAO,OAAO;QACzD,sCAAsC;QACtC,IAAI,SAAS,eAAe,CAC1B,YAAY,UAAU,CAAC,sBACvB,YAAY,UAAU,CAAC,oBACvB,YAAY,UAAU,CAAC,kBACzB,GAAG,OAAO;QACV,OAAO;IACT;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAAoB,WAAU;8CAC5B,KAAK,OAAO,iBACX;;0DACE,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,AAAC,6IAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;kEAGH,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,6LAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;0DAIlB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC,+JAAA,CAAA,UAAI;gEAEH,MAAM,QAAQ,IAAI;gEAClB,WAAW,AAAC,iJAKT,OAHC,gBAAgB,QAAQ,IAAI,GAC1B,6BACA,sDACH;;kFAGH,6LAAC,QAAQ,IAAI;wEAAC,WAAU;;;;;;kFACxB,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAe,QAAQ,IAAI;;;;;;0FAC1C,6LAAC;gFAAI,WAAU;0FAAyB,QAAQ,WAAW;;;;;;;;;;;;;+DAbxD,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;qEAsB7B,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,AAAC,yIAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;0DAGH,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;mCAxDZ,KAAK,IAAI;;;;;;;;;;sCAgEvB,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;;kDACC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,AAAC,yIAKT,OAHC,SAAS,KAAK,IAAI,IAChB,6BACA,sDACH;;0DAGH,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;oCAIjB,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS,IAAM,oBAAoB;gDACnC,WAAW,AAAC,yIAKT,OAHC,gBAAgB,QAAQ,IAAI,GAC1B,6BACA,sDACH;;kEAGH,6LAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;kEACxB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,QAAQ,IAAI;;;;;;0EAC1C,6LAAC;gEAAI,WAAU;0EAAyB,QAAQ,WAAW;;;;;;;;;;;;;+CAdxD,QAAQ,IAAI;;;;;;;;;;;+BArBjB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDnC;GAhLgB;KAAA;AA0LT,SAAS,WAAW,KAA0B;QAA1B,EAAE,KAAK,EAAmB,GAA1B;IACzB,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAAgB,WAAU;;oBACxB,QAAQ,mBACP,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;oBAEjC,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,KAAK,IAAI;wBACf,WAAU;kCAET,KAAK,KAAK;;;;;6CAGb,6LAAC;wBAAK,WAAU;kCAA6B,KAAK,KAAK;;;;;;;eAZjD;;;;;;;;;;AAkBlB;MAtBgB;AAyBT,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAoB,MAAM;gBAAuB;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAAqB;gBACpD;oBAAE,MAAM;oBAAmB,MAAM;gBAAuB;aACzD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAe,MAAM;gBAAQ;gBACrC;oBAAE,MAAM;oBAAc,MAAM;gBAAW;gBACvC;oBAAE,MAAM;oBAAO,MAAM;gBAAO;gBAC5B;oBAAE,MAAM;oBAAqB,MAAM;gBAAQ;aAC5C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAS;gBACnC;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAoB,MAAM;gBAAS;gBAC3C;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAC/B;QACH;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;wBAOtD,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,QAAQ,KAAK;;;;;;kDAC/D,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAkB3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAO;gCAAQ;gCAAY;;;;;;;sCACxC,6LAAC;;gCAAE;gCACiC;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA8C;;;;;;gCAG3E;gCAAI;gCAAQ;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8C;;;;;;gCAEvE;;;;;;;;;;;;;;;;;;;;;;;;AAOnB;MAxFgB", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/components/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { Zap, Github, Twitter, Mail, Heart } from 'lucide-react'\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerLinks = {\n    product: [\n      { name: 'Image Compression', href: '/image-compress' },\n      { name: 'GIF Compression', href: '/gif-compress' },\n      { name: 'Video Compression', href: '/video-compress' },\n      { name: 'Batch Processing', href: '/compress' },\n    ],\n    company: [\n      { name: 'About', href: '/about' },\n      { name: 'Pricing', href: '/pricing' },\n      { name: 'Contact', href: '/contact' },\n      { name: 'Blog', href: '/blog' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'API Documentation', href: '/docs' },\n      { name: 'Status', href: '/status' },\n      { name: 'Feedback', href: '/feedback' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n    ]\n  }\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold\">CompressHub</span>\n            </Link>\n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Professional file compression tools for images, GIFs, and videos. \n              Reduce file sizes while maintaining quality with our advanced algorithms.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://github.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\"\n              >\n                <Github className=\"w-5 h-5\" />\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\"\n              >\n                <Twitter className=\"w-5 h-5\" />\n              </a>\n              <a\n                href=\"mailto:<EMAIL>\"\n                className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors\"\n              >\n                <Mail className=\"w-5 h-5\" />\n              </a>\n            </div>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 mt-12 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-4 mb-4 md:mb-0\">\n              <p className=\"text-gray-400 text-sm\">\n                © {currentYear} CompressHub. All rights reserved.\n              </p>\n              <div className=\"hidden md:flex items-center space-x-4\">\n                {footerLinks.legal.map((link) => (\n                  <Link\n                    key={link.name}\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n            \n            <div className=\"flex items-center text-gray-400 text-sm\">\n              <span>Made with</span>\n              <Heart className=\"w-4 h-4 mx-1 text-red-500\" />\n              <span>for better web performance</span>\n            </div>\n          </div>\n\n          {/* Mobile Legal Links */}\n          <div className=\"md:hidden mt-4 flex flex-wrap justify-center gap-4\">\n            {footerLinks.legal.map((link) => (\n              <Link\n                key={link.name}\n                href={link.href}\n                className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n              >\n                {link.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAqB,MAAM;YAAkB;YACrD;gBAAE,MAAM;gBAAmB,MAAM;YAAgB;YACjD;gBAAE,MAAM;gBAAqB,MAAM;YAAkB;YACrD;gBAAE,MAAM;gBAAoB,MAAM;YAAY;SAC/C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAqB,MAAM;YAAQ;YAC3C;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDAAwB;gDAChC;gDAAY;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;mDAJL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAUtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;sCACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9B;KAxKgB", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/image-video-compress/src/app/pricing/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { Head<PERSON> } from '@/components/Header'\nimport { Footer } from '@/components/Footer'\nimport { \n  Check, \n  Crown, \n  Zap, \n  Shield, \n  Sparkles,\n  Users,\n  Clock,\n  HeadphonesIcon,\n  Code,\n  Download\n} from 'lucide-react'\n\nexport default function PricingPage() {\n  const [isAnnual, setIsAnnual] = useState(false)\n\n  const features = {\n    free: [\n      { icon: Zap, text: \"50 compressions per month\", highlight: true },\n      { icon: Download, text: \"10MB max file size\" },\n      { icon: Shield, text: \"PNG, JPEG, WebP support\" },\n      { icon: Clock, text: \"Basic compression speed\" },\n    ],\n    pro: [\n      { icon: Sparkles, text: \"Unlimited compressions\", highlight: true },\n      { icon: Download, text: \"200MB max file size\", highlight: true },\n      { icon: Shield, text: \"All formats (PNG, JPEG, WebP, GIF, Video)\" },\n      { icon: Zap, text: \"Advanced compression algorithms\" },\n      { icon: Users, text: \"Batch processing up to 100 files\" },\n      { icon: Code, text: \"API access with 10,000 calls/month\" },\n      { icon: HeadphonesIcon, text: \"Priority email support\" },\n      { icon: Crown, text: \"Format conversion (PNG↔JPEG↔WebP)\" },\n    ]\n  }\n\n  const proPrice = isAnnual ? 7 : 9\n  const proPriceOriginal = isAnnual ? 10 : 12\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <Header currentPath=\"/pricing\" />\n      \n      <main className=\"container mx-auto px-4 py-16\">\n        {/* Hero Section */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <h1 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Simple, <span className=\"text-blue-600\">Affordable</span> Pricing\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-8\">\n            Get more for less. Our Pro plan offers better value than competitors with \n            more free compressions and lower prices.\n          </p>\n          \n          {/* Billing Toggle */}\n          <div className=\"flex items-center justify-center mb-8\">\n            <span className={`mr-3 ${!isAnnual ? 'text-gray-900 font-semibold' : 'text-gray-500'}`}>\n              Monthly\n            </span>\n            <button\n              onClick={() => setIsAnnual(!isAnnual)}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                isAnnual ? 'bg-blue-600' : 'bg-gray-300'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  isAnnual ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n            <span className={`ml-3 ${isAnnual ? 'text-gray-900 font-semibold' : 'text-gray-500'}`}>\n              Annual\n            </span>\n            {isAnnual && (\n              <span className=\"ml-2 bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full\">\n                Save 22%\n              </span>\n            )}\n          </div>\n        </motion.div>\n\n        {/* Pricing Cards */}\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12\">\n            \n            {/* Free Plan */}\n            <motion.div \n              className=\"bg-white rounded-3xl shadow-lg p-8 border border-gray-200 relative\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n            >\n              <div className=\"text-center mb-8\">\n                <div className=\"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <Zap className=\"w-8 h-8 text-gray-600\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Free</h3>\n                <div className=\"text-5xl font-bold text-gray-900 mb-2\">\n                  $0\n                  <span className=\"text-lg font-normal text-gray-600\">/month</span>\n                </div>\n                <p className=\"text-gray-600\">Perfect for trying out our service</p>\n              </div>\n\n              <ul className=\"space-y-4 mb-8\">\n                {features.free.map((feature, index) => (\n                  <li key={index} className=\"flex items-start\">\n                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-0.5 ${\n                      feature.highlight ? 'bg-blue-100' : 'bg-gray-100'\n                    }`}>\n                      <Check className={`w-4 h-4 ${feature.highlight ? 'text-blue-600' : 'text-gray-600'}`} />\n                    </div>\n                    <span className={`text-gray-700 ${feature.highlight ? 'font-semibold' : ''}`}>\n                      {feature.text}\n                    </span>\n                  </li>\n                ))}\n              </ul>\n\n              <button className=\"w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105\">\n                Get Started Free\n              </button>\n              \n              <p className=\"text-center text-sm text-gray-500 mt-4\">\n                No credit card required\n              </p>\n            </motion.div>\n\n            {/* Pro Plan */}\n            <motion.div \n              className=\"bg-white rounded-3xl shadow-2xl p-8 border-2 border-blue-500 relative\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n            >\n              {/* Popular Badge */}\n              <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center\">\n                  <Crown className=\"w-4 h-4 mr-2\" />\n                  Most Popular\n                </div>\n              </div>\n\n              <div className=\"text-center mb-8\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <Crown className=\"w-8 h-8 text-white\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Pro</h3>\n                <div className=\"flex items-center justify-center mb-2\">\n                  <span className=\"text-2xl text-gray-400 line-through mr-2\">\n                    ${proPriceOriginal}\n                  </span>\n                  <span className=\"text-5xl font-bold text-gray-900\">\n                    ${proPrice}\n                  </span>\n                  <span className=\"text-lg font-normal text-gray-600 ml-1\">\n                    /{isAnnual ? 'month' : 'month'}\n                  </span>\n                </div>\n                {isAnnual && (\n                  <p className=\"text-sm text-green-600 font-semibold mb-2\">\n                    Billed annually - Save $36/year\n                  </p>\n                )}\n                <p className=\"text-gray-600\">For professionals and businesses</p>\n              </div>\n\n              <ul className=\"space-y-4 mb-8\">\n                {features.pro.map((feature, index) => (\n                  <li key={index} className=\"flex items-start\">\n                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-0.5 ${\n                      feature.highlight ? 'bg-blue-100' : 'bg-gray-100'\n                    }`}>\n                      <Check className={`w-4 h-4 ${feature.highlight ? 'text-blue-600' : 'text-gray-600'}`} />\n                    </div>\n                    <span className={`text-gray-700 ${feature.highlight ? 'font-semibold' : ''}`}>\n                      {feature.text}\n                    </span>\n                  </li>\n                ))}\n              </ul>\n\n              <button className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg\">\n                Start 14-Day Free Trial\n              </button>\n              \n              <p className=\"text-center text-sm text-gray-500 mt-4\">\n                No credit card required • Cancel anytime\n              </p>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Comparison with Competitors */}\n        <motion.div\n          className=\"max-w-4xl mx-auto mt-20\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n        >\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Why Choose Us Over Competitors?\n            </h2>\n            <p className=\"text-gray-600\">\n              Compare our Pro plan with leading competitors\n            </p>\n          </div>\n\n          <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-900\">Feature</th>\n                    <th className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">Our Pro</th>\n                    <th className=\"px-6 py-4 text-center text-sm font-semibold text-gray-600\">Tinify Pro</th>\n                    <th className=\"px-6 py-4 text-center text-sm font-semibold text-gray-600\">Others</th>\n                  </tr>\n                </thead>\n                <tbody className=\"divide-y divide-gray-200\">\n                  <tr>\n                    <td className=\"px-6 py-4 text-sm text-gray-900\">Monthly Price</td>\n                    <td className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">$7-9</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">$25</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">$15-30</td>\n                  </tr>\n                  <tr className=\"bg-gray-50\">\n                    <td className=\"px-6 py-4 text-sm text-gray-900\">Free Monthly Compressions</td>\n                    <td className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">50</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">20</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">10-20</td>\n                  </tr>\n                  <tr>\n                    <td className=\"px-6 py-4 text-sm text-gray-900\">Max File Size</td>\n                    <td className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">200MB</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">75MB</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">50-100MB</td>\n                  </tr>\n                  <tr className=\"bg-gray-50\">\n                    <td className=\"px-6 py-4 text-sm text-gray-900\">Video Support</td>\n                    <td className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">✓</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">✗</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">Limited</td>\n                  </tr>\n                  <tr>\n                    <td className=\"px-6 py-4 text-sm text-gray-900\">API Calls/Month</td>\n                    <td className=\"px-6 py-4 text-center text-sm font-semibold text-blue-600\">10,000</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">500</td>\n                    <td className=\"px-6 py-4 text-center text-sm text-gray-600\">1,000-5,000</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* FAQ Section */}\n        <motion.div\n          className=\"max-w-4xl mx-auto mt-20\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n        >\n          <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n            Frequently Asked Questions\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                What payment methods do you accept?\n              </h3>\n              <p className=\"text-gray-600\">\n                We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                Can I cancel my subscription anytime?\n              </h3>\n              <p className=\"text-gray-600\">\n                Yes, you can cancel anytime with no questions asked. You'll keep access to Pro features until your current billing period ends.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                Is there really a free trial?\n              </h3>\n              <p className=\"text-gray-600\">\n                Yes! Get 14 days of full Pro access with no credit card required. Experience unlimited compressions and all premium features.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                How does the API work?\n              </h3>\n              <p className=\"text-gray-600\">\n                Our RESTful API lets you integrate compression into your apps. Pro users get 10,000 API calls per month with detailed documentation.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                Do you offer team discounts?\n              </h3>\n              <p className=\"text-gray-600\">\n                Yes! Teams of 5+ users get 15% off, and enterprise customers get custom pricing. Contact us for volume discounts.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                What's your refund policy?\n              </h3>\n              <p className=\"text-gray-600\">\n                We offer a 30-day money-back guarantee. If you're not satisfied, we'll refund your payment in full, no questions asked.\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          className=\"text-center mt-20\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n        >\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white\">\n            <h2 className=\"text-3xl font-bold mb-4\">\n              Ready to compress like a pro?\n            </h2>\n            <p className=\"text-xl mb-8 opacity-90\">\n              Join thousands of users who trust us with their compression needs\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-white text-blue-600 font-semibold py-3 px-8 rounded-xl hover:bg-gray-100 transition-colors\">\n                Start Free Trial\n              </button>\n              <button className=\"border-2 border-white text-white font-semibold py-3 px-8 rounded-xl hover:bg-white hover:text-blue-600 transition-colors\">\n                View Demo\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </main>\n\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,WAAW;QACf,MAAM;YACJ;gBAAE,MAAM,mMAAA,CAAA,MAAG;gBAAE,MAAM;gBAA6B,WAAW;YAAK;YAChE;gBAAE,MAAM,6MAAA,CAAA,WAAQ;gBAAE,MAAM;YAAqB;YAC7C;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,MAAM;YAA0B;YAChD;gBAAE,MAAM,uMAAA,CAAA,QAAK;gBAAE,MAAM;YAA0B;SAChD;QACD,KAAK;YACH;gBAAE,MAAM,6MAAA,CAAA,WAAQ;gBAAE,MAAM;gBAA0B,WAAW;YAAK;YAClE;gBAAE,MAAM,6MAAA,CAAA,WAAQ;gBAAE,MAAM;gBAAuB,WAAW;YAAK;YAC/D;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,MAAM;YAA4C;YAClE;gBAAE,MAAM,mMAAA,CAAA,MAAG;gBAAE,MAAM;YAAkC;YACrD;gBAAE,MAAM,uMAAA,CAAA,QAAK;gBAAE,MAAM;YAAmC;YACxD;gBAAE,MAAM,qMAAA,CAAA,OAAI;gBAAE,MAAM;YAAqC;YACzD;gBAAE,MAAM,qNAAA,CAAA,iBAAc;gBAAE,MAAM;YAAyB;YACvD;gBAAE,MAAM,uMAAA,CAAA,QAAK;gBAAE,MAAM;YAAoC;SAC1D;IACH;IAEA,MAAM,WAAW,WAAW,IAAI;IAChC,MAAM,mBAAmB,WAAW,KAAK;IAEzC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,SAAM;gBAAC,aAAY;;;;;;0BAEpB,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAG,WAAU;;oCAAoD;kDACxD,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAiB;;;;;;;0CAE3D,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAM5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,AAAC,QAAmE,OAA5D,CAAC,WAAW,gCAAgC;kDAAmB;;;;;;kDAGxF,6LAAC;wCACC,SAAS,IAAM,YAAY,CAAC;wCAC5B,WAAW,AAAC,6EAEX,OADC,WAAW,gBAAgB;kDAG7B,cAAA,6LAAC;4CACC,WAAW,AAAC,6EAEX,OADC,WAAW,kBAAkB;;;;;;;;;;;kDAInC,6LAAC;wCAAK,WAAW,AAAC,QAAkE,OAA3D,WAAW,gCAAgC;kDAAmB;;;;;;oCAGtF,0BACC,6LAAC;wCAAK,WAAU;kDAAgF;;;;;;;;;;;;;;;;;;kCAQtG,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAGb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;;wDAAwC;sEAErD,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAEtD,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAG/B,6LAAC;4CAAG,WAAU;sDACX,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC;4DAAI,WAAW,AAAC,mFAEhB,OADC,QAAQ,SAAS,GAAG,gBAAgB;sEAEpC,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAW,AAAC,WAAgE,OAAtD,QAAQ,SAAS,GAAG,kBAAkB;;;;;;;;;;;sEAErE,6LAAC;4DAAK,WAAW,AAAC,iBAAyD,OAAzC,QAAQ,SAAS,GAAG,kBAAkB;sEACrE,QAAQ,IAAI;;;;;;;mDAPR;;;;;;;;;;sDAab,6LAAC;4CAAO,WAAU;sDAA2I;;;;;;sDAI7J,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;8CAMxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAGxC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAKtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAA2C;gEACvD;;;;;;;sEAEJ,6LAAC;4DAAK,WAAU;;gEAAmC;gEAC/C;;;;;;;sEAEJ,6LAAC;4DAAK,WAAU;;gEAAyC;gEACrD,WAAW,UAAU;;;;;;;;;;;;;gDAG1B,0BACC,6LAAC;oDAAE,WAAU;8DAA4C;;;;;;8DAI3D,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAG/B,6LAAC;4CAAG,WAAU;sDACX,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC;4DAAI,WAAW,AAAC,mFAEhB,OADC,QAAQ,SAAS,GAAG,gBAAgB;sEAEpC,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAW,AAAC,WAAgE,OAAtD,QAAQ,SAAS,GAAG,kBAAkB;;;;;;;;;;;sEAErE,6LAAC;4DAAK,WAAW,AAAC,iBAAyD,OAAzC,QAAQ,SAAS,GAAG,kBAAkB;sEACrE,QAAQ,IAAI;;;;;;;mDAPR;;;;;;;;;;sDAab,6LAAC;4CAAO,WAAU;sDAA4M;;;;;;sDAI9N,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;;;;;;kCAQ5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA0D;;;;;;sEACxE,6LAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAC1E,6LAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAC1E,6LAAC;4DAAG,WAAU;sEAA4D;;;;;;;;;;;;;;;;;0DAG9E,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAC5D,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;;;;;;;kEAE9D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAC5D,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;;;;;;;kEAE9D,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAC5D,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;;;;;;;kEAE9D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAC5D,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;;;;;;;kEAE9D,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAC5D,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAInE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAQnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0B;;;;;;8CAGxC,6LAAC;oCAAE,WAAU;8CAA0B;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAAgG;;;;;;sDAGlH,6LAAC;4CAAO,WAAU;sDAA2H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrJ,6LAAC,+HAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GA1VwB;KAAA", "debugId": null}}]}