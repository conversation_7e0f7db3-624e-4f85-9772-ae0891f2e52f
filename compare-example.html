<div class="chie1vm">
  <div class="header">
    <h3>IMAGE COMPRESSION AND CONVERSION WITHOUT VISIBLE QUALITY LOSS</h3>
    <h2>Can you tell the difference?</h2>
    <p>
      Move the slider to compare the compressed and converted image with the
      original.  <br />The file size is reduced by more than 85%!
    </p>
  </div>
  <div class="body">
    <div class="slider">
      <div
        data-testid="container"
        style="
          box-sizing: border-box;
          position: relative;
          width: 100%;
          height: 657.657px;
          overflow: hidden;
          display: block;
        "
      >
        <img
          alt=""
          data-testid="right-image"
          src="/static/images/boat-compressed.jpg"
          style="
            clip-path: inset(0px 0px 0px 710.5px);
            display: block;
            height: 100%;
            object-fit: cover;
            position: absolute;
            width: 100%;
          "
        /><img
          alt=""
          data-testid="left-image"
          src="/static/images/boat.png"
          style="
            clip-path: inset(0px 417.5px 0px 0px);
            display: block;
            height: 100%;
            object-fit: cover;
            position: absolute;
            width: 100%;
          "
        />
        <div
          style="
            align-items: center;
            display: flex;
            justify-content: center;
            position: absolute;
            cursor: ew-resize;
            flex-direction: column;
            height: 100%;
            left: 690.5px;
            top: 0px;
            width: 40px;
          "
        >
          <div
            style="
              background: rgb(255, 255, 255);
              box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 1px -2px,
                rgba(0, 0, 0, 0.14) 0px 2px 2px 0px,
                rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;
              flex: 0 1 auto;
              height: 100%;
              width: 5px;
            "
          ></div>
          <div
            style="
              align-items: center;
              box-sizing: border-box;
              display: flex;
              flex: 1 0 auto;
              height: auto;
              justify-content: center;
              width: auto;
            "
          >
            <div
              style="
                align-items: center;
                box-sizing: border-box;
                display: flex;
                flex: 1 0 auto;
                height: 40px;
                justify-content: center;
                width: 5px;
                background: white;
                box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 1px -2px,
                  rgba(0, 0, 0, 0.14) 0px 2px 2px 0px,
                  rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;
                transform: none;
              "
            >
              <div
                style="
                  border-width: 11px;
                  border-style: inset solid inset inset;
                  border-color: rgba(0, 0, 0, 0) rgb(255, 255, 255)
                    rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
                  border-image: initial;
                  height: 0px;
                  margin-left: -40px;
                  margin-right: 18px;
                  width: 0px;
                "
              ></div>
              <div
                style="
                  border-width: 11px;
                  border-style: inset inset inset solid;
                  border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0)
                    rgba(0, 0, 0, 0) rgb(255, 255, 255);
                  border-image: initial;
                  height: 0px;
                  margin-right: -40px;
                  width: 0px;
                "
              ></div>
            </div>
          </div>
          <div
            style="
              background: rgb(255, 255, 255);
              box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 1px -2px,
                rgba(0, 0, 0, 0.14) 0px 2px 2px 0px,
                rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;
              flex: 0 1 auto;
              height: 100%;
              width: 5px;
            "
          ></div>
        </div>
      </div>
    </div>
    <div class="arrow left">
      <img
        width="20"
        height="57"
        alt="arrow"
        src="/static/images/arrow_left.svg"
      />
      <div class="arrow-label">Darker places stay intact</div>
    </div>
    <div class="arrow right">
      <img
        width="22"
        height="58"
        alt="arrow"
        src="/static/images/arrow_right.svg"
      />
      <div class="arrow-label">Tiniest details are still there</div>
    </div>
    <div class="label original">
      <span class="origin">ORIGINAL</span><span class="filesize">1.1 MB</span>
    </div>
    <div class="label tinify">
      <span class="origin">TINIFY</span><span class="filesize">188 KB</span>
    </div>
  </div>
</div>
