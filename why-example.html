<div class="page-width cdsb0n1 sdrxscy">
  <section>
    <div class="text">
      <h2>Why should you convert your images?</h2>
      <span
        >Why converting images to the smallest size possible is important for
        your website?</span
      >
    </div>
    <div class="c1h0s4l3">
      <article class="b151rtft bg-grey text-smaller">
        <div class="horizontal">
          <svg
            aria-hidden="true"
            focusable="false"
            data-prefix="fas"
            data-icon="rocket-launch"
            class="svg-inline--fa fa-rocket-launch"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
            color="#624BD8"
          >
            <path
              fill="currentColor"
              d="M117.8 128l89.3 0C286.9-3.7 409.5-8.5 483.9 5.3c11.6 2.2 20.7 11.2 22.8 22.8c13.8 74.4 9 197-122.7 276.9l0 89.3c0 25.4-13.4 49-35.3 61.9l-88.5 52.5c-7.4 4.4-16.6 4.5-24.1 .2s-12.1-12.2-12.1-20.9l0-114.7c0-22.6-9-44.3-25-60.3s-37.7-25-60.3-25L24 288c-8.6 0-16.6-4.6-20.9-12.1s-4.2-16.7 .2-24.1l52.5-88.5c13-21.9 36.5-35.3 61.9-35.3zM424 128a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zM166.5 470C132.3 504.3 66 511 28.3 511.9c-16 .4-28.6-12.2-28.2-28.2C1 446 7.7 379.7 42 345.5c34.4-34.4 90.1-34.4 124.5 0s34.4 90.1 0 124.5zm-46.7-36.4c11.4-11.4 11.4-30 0-41.4s-30-11.4-41.4 0c-10.1 10.1-13 28.5-13.7 41.3c-.5 8 5.9 14.3 13.9 13.9c12.8-.7 31.2-3.7 41.3-13.7z"
            ></path>
          </svg>
          <section>
            <h3>Fast loading times</h3>
            <span
              >Smaller images load faster, improving user experience, reducing
              bounce rates, and increasing visitor engagement.</span
            >
          </section>
        </div>
      </article>
      <article class="b151rtft bg-grey text-smaller">
        <div class="horizontal">
          <svg
            aria-hidden="true"
            focusable="false"
            data-prefix="fas"
            data-icon="mobile-notch"
            class="svg-inline--fa fa-mobile-notch"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 384 512"
            color="#624BD8"
          >
            <path
              fill="currentColor"
              d="M128 64l0 16c0 8.8 7.2 16 16 16l96 0c8.8 0 16-7.2 16-16l0-16 32 0c8.8 0 16 7.2 16 16l0 352c0 8.8-7.2 16-16 16L96 448c-8.8 0-16-7.2-16-16L80 80c0-8.8 7.2-16 16-16l32 0zM96 0C51.8 0 16 35.8 16 80l0 352c0 44.2 35.8 80 80 80l192 0c44.2 0 80-35.8 80-80l0-352c0-44.2-35.8-80-80-80L96 0zm32 400c0 8.8 7.2 16 16 16l96 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-96 0c-8.8 0-16 7.2-16 16z"
            ></path>
          </svg>
          <section>
            <h3>Enhanced performance on mobile</h3>
            <span
              >Small images cater to mobile users with limited bandwidth and
              slower connections.</span
            >
          </section>
        </div>
      </article>
      <article class="b151rtft bg-grey text-smaller">
        <div class="horizontal">
          <svg
            aria-hidden="true"
            focusable="false"
            data-prefix="fas"
            data-icon="crosshairs"
            class="svg-inline--fa fa-crosshairs"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
            color="#624BD8"
          >
            <path
              fill="currentColor"
              d="M256 0c17.7 0 32 14.3 32 32l0 10.4c93.7 13.9 167.7 88 181.6 181.6l10.4 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-10.4 0c-13.9 93.7-88 167.7-181.6 181.6l0 10.4c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-10.4C130.3 455.7 56.3 381.7 42.4 288L32 288c-17.7 0-32-14.3-32-32s14.3-32 32-32l10.4 0C56.3 130.3 130.3 56.3 224 42.4L224 32c0-17.7 14.3-32 32-32zM107.4 288c12.5 58.3 58.4 104.1 116.6 116.6l0-20.6c0-17.7 14.3-32 32-32s32 14.3 32 32l0 20.6c58.3-12.5 104.1-58.4 116.6-116.6L384 288c-17.7 0-32-14.3-32-32s14.3-32 32-32l20.6 0C392.1 165.7 346.3 119.9 288 107.4l0 20.6c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-20.6C165.7 119.9 119.9 165.7 107.4 224l20.6 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-20.6 0zM256 224a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"
            ></path>
          </svg>
          <section>
            <h3>SEO boost</h3>
            <span
              >Fast-loading websites rank higher on search engines like Google
              and Bing.</span
            >
          </section>
        </div>
      </article>
      <article class="b151rtft bg-grey text-smaller">
        <div class="horizontal">
          <svg
            aria-hidden="true"
            focusable="false"
            data-prefix="fas"
            data-icon="signal-bars"
            class="svg-inline--fa fa-signal-bars"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 640 512"
            color="#624BD8"
          >
            <path
              fill="currentColor"
              d="M560 0c-26.5 0-48 21.5-48 48l0 416c0 26.5 21.5 48 48 48s48-21.5 48-48l0-416c0-26.5-21.5-48-48-48zM352 176l0 288c0 26.5 21.5 48 48 48s48-21.5 48-48l0-288c0-26.5-21.5-48-48-48s-48 21.5-48 48zM240 256c-26.5 0-48 21.5-48 48l0 160c0 26.5 21.5 48 48 48s48-21.5 48-48l0-160c0-26.5-21.5-48-48-48zM80 384c-26.5 0-48 21.5-48 48l0 32c0 26.5 21.5 48 48 48s48-21.5 48-48l0-32c0-26.5-21.5-48-48-48z"
            ></path>
          </svg>
          <section>
            <h3>Enhanced performance on mobile</h3>
            <span
              >A speedy site engages potential customers, leading to higher
              conversion rates.</span
            >
          </section>
        </div>
      </article>
      <article class="b151rtft bg-grey text-smaller">
        <div class="horizontal">
          <svg
            aria-hidden="true"
            focusable="false"
            data-prefix="fas"
            data-icon="wifi"
            class="svg-inline--fa fa-wifi"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 640 512"
            color="#624BD8"
          >
            <path
              fill="currentColor"
              d="M54.2 202.9C123.2 136.7 216.8 96 320 96s196.8 40.7 265.8 106.9c12.8 12.2 33 11.8 45.2-.9s11.8-33-.9-45.2C549.7 79.5 440.4 32 320 32S90.3 79.5 9.8 156.7C-2.9 169-3.3 189.2 8.9 202s32.5 13.2 45.2 .9zM320 256c56.8 0 108.6 21.1 148.2 56c13.3 11.7 33.5 10.4 45.2-2.8s10.4-33.5-2.8-45.2C459.8 219.2 393 192 320 192s-139.8 27.2-190.5 72c-13.3 11.7-14.5 31.9-2.8 45.2s31.9 14.5 45.2 2.8c39.5-34.9 91.3-56 148.2-56zm64 160a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z"
            ></path>
          </svg>
          <section>
            <h3>Bandwidth savings</h3>
            <span
              >Smaller images reduce bandwidth, cutting hosting costs and
              benefiting users with data limits or cloud storage.</span
            >
          </section>
        </div>
      </article>
      <article class="b151rtft bg-grey text-smaller">
        <div class="horizontal">
          <svg
            aria-hidden="true"
            focusable="false"
            data-prefix="fas"
            data-icon="layer-group"
            class="svg-inline--fa fa-layer-group"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 576 512"
            color="#624BD8"
          >
            <path
              fill="currentColor"
              d="M264.5 5.2c14.9-6.9 32.1-6.9 47 0l218.6 101c8.5 3.9 13.9 12.4 13.9 21.8s-5.4 17.9-13.9 21.8l-218.6 101c-14.9 6.9-32.1 6.9-47 0L45.9 149.8C37.4 145.8 32 137.3 32 128s5.4-17.9 13.9-21.8L264.5 5.2zM476.9 209.6l53.2 24.6c8.5 3.9 13.9 12.4 13.9 21.8s-5.4 17.9-13.9 21.8l-218.6 101c-14.9 6.9-32.1 6.9-47 0L45.9 277.8C37.4 273.8 32 265.3 32 256s5.4-17.9 13.9-21.8l53.2-24.6 152 70.2c23.4 10.8 50.4 10.8 73.8 0l152-70.2zm-152 198.2l152-70.2 53.2 24.6c8.5 3.9 13.9 12.4 13.9 21.8s-5.4 17.9-13.9 21.8l-218.6 101c-14.9 6.9-32.1 6.9-47 0L45.9 405.8C37.4 401.8 32 393.3 32 384s5.4-17.9 13.9-21.8l53.2-24.6 152 70.2c23.4 10.8 50.4 10.8 73.8 0z"
            ></path>
          </svg>
          <section>
            <h3>Disk space savings</h3>
            <span
              >Smaller image files reduce server and cloud resource usage,
              cutting costs and IOPS while improving performance.</span
            >
          </section>
        </div>
      </article>
    </div>
  </section>
</div>
