'use client'

import { useState, useCallback } from 'react'
import { FileItem, CompressionOptions, ImageCompressionOptions, VideoCompressionOptions, GifCompressionOptions } from '@/types'
import { ImageCompressionService } from '@/services/imageCompression'
import { VideoCompressionService } from '@/services/videoCompression'
import { GifCompressionService } from '@/services/gifCompression'
import { downloadFile } from '@/lib/utils'

export function useCompressionManager() {
  const [files, setFiles] = useState<FileItem[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentProcessingIndex, setCurrentProcessingIndex] = useState(-1)

  const addFiles = useCallback((newFiles: FileItem[]) => {
    setFiles(prev => [...prev, ...newFiles])
  }, [])

  const removeFile = useCallback((id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id))
  }, [])

  const updateFileStatus = useCallback((id: string, updates: Partial<FileItem>) => {
    setFiles(prev => prev.map(file => 
      file.id === id ? { ...file, ...updates } : file
    ))
  }, [])

  const compressFile = useCallback(async (
    file: FileItem, 
    options: CompressionOptions,
    onProgress?: (progress: number) => void
  ) => {
    try {
      updateFileStatus(file.id, { status: 'processing', progress: 0 })

      let result
      
      if (file.type === 'image') {
        result = await ImageCompressionService.compressImage(
          file.file,
          options as ImageCompressionOptions,
          (progress) => {
            updateFileStatus(file.id, { progress })
            onProgress?.(progress)
          }
        )
      } else if (file.type === 'video') {
        result = await VideoCompressionService.compressVideo(
          file.file,
          options as VideoCompressionOptions,
          (progress) => {
            updateFileStatus(file.id, { progress })
            onProgress?.(progress)
          }
        )
      } else if (file.type === 'gif') {
        result = await GifCompressionService.compressGif(
          file.file,
          options as GifCompressionOptions,
          (progress) => {
            updateFileStatus(file.id, { progress })
            onProgress?.(progress)
          }
        )
      } else {
        throw new Error(`Unsupported file type: ${file.type}`)
      }

      if (result.success && result.blob) {
        updateFileStatus(file.id, {
          status: 'completed',
          progress: 100,
          compressedSize: result.compressedSize,
          compressionRatio: result.compressionRatio,
          compressedFile: result.blob,
          downloadUrl: URL.createObjectURL(result.blob)
        })
      } else {
        updateFileStatus(file.id, {
          status: 'error',
          progress: 0,
          error: result.error || 'Compression failed'
        })
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      updateFileStatus(file.id, {
        status: 'error',
        progress: 0,
        error: errorMessage
      })
      return {
        success: false,
        originalSize: file.originalSize,
        compressedSize: 0,
        compressionRatio: 0,
        error: errorMessage
      }
    }
  }, [updateFileStatus])

  const compressAllFiles = useCallback(async (options: CompressionOptions) => {
    if (isProcessing) return

    setIsProcessing(true)
    const pendingFiles = files.filter(file => file.status === 'pending')
    
    for (let i = 0; i < pendingFiles.length; i++) {
      setCurrentProcessingIndex(i)
      await compressFile(pendingFiles[i], options)
    }

    setIsProcessing(false)
    setCurrentProcessingIndex(-1)
  }, [files, isProcessing, compressFile])

  const retryFile = useCallback(async (id: string, options: CompressionOptions) => {
    const file = files.find(f => f.id === id)
    if (!file) return

    updateFileStatus(id, { status: 'pending', error: undefined })
    await compressFile(file, options)
  }, [files, compressFile, updateFileStatus])

  const downloadSingleFile = useCallback((id: string) => {
    const file = files.find(f => f.id === id)
    if (!file || !file.compressedFile) return

    // Determine the correct file extension based on the compressed file type
    let extension = file.format
    if (file.compressedFile.type) {
      const mimeType = file.compressedFile.type
      if (mimeType.includes('jpeg')) extension = 'jpg'
      else if (mimeType.includes('png')) extension = 'png'
      else if (mimeType.includes('webp')) extension = 'webp'
      else if (mimeType.includes('gif')) extension = 'gif'
      else if (mimeType.includes('mp4')) extension = 'mp4'
      else if (mimeType.includes('webm')) extension = 'webm'
    }

    const baseName = file.name.replace(/\.[^/.]+$/, '')
    const fileName = `${baseName}_compressed.${extension}`

    downloadFile(file.compressedFile, fileName)
  }, [files])

  const downloadAllFiles = useCallback(() => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.compressedFile)
    
    if (completedFiles.length === 0) return

    if (completedFiles.length === 1) {
      downloadSingleFile(completedFiles[0].id)
      return
    }

    // For multiple files, we would need to create a ZIP file
    // For now, download them individually
    completedFiles.forEach(file => {
      setTimeout(() => downloadSingleFile(file.id), 100)
    })
  }, [files, downloadFile])

  const clearAllFiles = useCallback(() => {
    // Clean up object URLs to prevent memory leaks
    files.forEach(file => {
      if (file.downloadUrl) {
        URL.revokeObjectURL(file.downloadUrl)
      }
    })
    setFiles([])
  }, [files])

  const getStats = useCallback(() => {
    const total = files.length
    const pending = files.filter(f => f.status === 'pending').length
    const processing = files.filter(f => f.status === 'processing').length
    const completed = files.filter(f => f.status === 'completed').length
    const failed = files.filter(f => f.status === 'error').length
    
    const totalOriginalSize = files.reduce((sum, f) => sum + f.originalSize, 0)
    const totalCompressedSize = files
      .filter(f => f.compressedSize)
      .reduce((sum, f) => sum + (f.compressedSize || 0), 0)
    
    const overallCompressionRatio = totalOriginalSize > 0 
      ? ((totalOriginalSize - totalCompressedSize) / totalOriginalSize) * 100 
      : 0

    const overallProgress = total > 0 
      ? (completed / total) * 100 
      : 0

    return {
      total,
      pending,
      processing,
      completed,
      failed,
      totalOriginalSize,
      totalCompressedSize,
      overallCompressionRatio,
      overallProgress,
      canDownload: completed > 0,
      canProcess: pending > 0 && !isProcessing
    }
  }, [files, isProcessing])

  return {
    files,
    isProcessing,
    currentProcessingIndex,
    addFiles,
    removeFile,
    compressFile,
    compressAllFiles,
    retryFile,
    downloadFile: downloadSingleFile,
    downloadAllFiles,
    clearAllFiles,
    getStats
  }
}
