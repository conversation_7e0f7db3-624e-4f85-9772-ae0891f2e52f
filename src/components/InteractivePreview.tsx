'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { X, Eye, RotateCcw } from 'lucide-react'
import { FileItem } from '@/types'

interface InteractivePreviewProps {
  item: FileItem
  originalUrl: string
  compressedUrl: string
  onClose: () => void
}

export function InteractivePreview({ 
  item, 
  originalUrl, 
  compressedUrl, 
  onClose 
}: InteractivePreviewProps) {
  const [isComparing, setIsComparing] = useState(false)
  const [scanPosition, setScanPosition] = useState(0)
  const [isScanning, setIsScanning] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>()

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return
    
    setIsComparing(true)
    setIsScanning(true)
    
    const rect = containerRef.current.getBoundingClientRect()
    const startX = (e.clientX - rect.left) / rect.width * 100
    setScanPosition(startX)

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return
      const rect = containerRef.current.getBoundingClientRect()
      const x = Math.max(0, Math.min(100, (e.clientX - rect.left) / rect.width * 100))
      setScanPosition(x)
    }

    const handleMouseUp = () => {
      setIsScanning(false)
      // Animate scan line back to start
      animateToStart()
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [])

  const animateToStart = useCallback(() => {
    const startPosition = scanPosition
    const startTime = Date.now()
    const duration = 800 // 800ms animation

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Easing function for smooth animation
      const easeOut = 1 - Math.pow(1 - progress, 3)
      const currentPosition = startPosition * (1 - easeOut)
      
      setScanPosition(currentPosition)
      
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate)
      } else {
        setIsComparing(false)
      }
    }
    
    animationRef.current = requestAnimationFrame(animate)
  }, [scanPosition])

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  const handleReset = () => {
    setIsComparing(false)
    setScanPosition(0)
    setIsScanning(false)
  }

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div className="max-w-6xl max-h-full w-full">
        <div className="relative bg-white rounded-lg overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
              <p className="text-sm text-gray-600">
                Original: {(item.originalSize / 1024).toFixed(1)}KB → 
                Compressed: {item.compressedSize ? (item.compressedSize / 1024).toFixed(1) : '0'}KB
                {item.compressionRatio && (
                  <span className="ml-2 text-green-600 font-medium">
                    ({item.compressionRatio.toFixed(1)}% reduction)
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleReset}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
                title="Reset comparison"
              >
                <RotateCcw className="w-5 h-5" />
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Image Container */}
          <div className="relative bg-gray-100">
            <div 
              ref={containerRef}
              className="relative overflow-hidden cursor-crosshair select-none"
              onMouseDown={handleMouseDown}
              style={{ aspectRatio: '16/9', minHeight: '400px' }}
            >
              {/* Compressed Image (Background) */}
              <img
                src={compressedUrl}
                alt={`${item.name} - Compressed`}
                className="absolute inset-0 w-full h-full object-contain"
                draggable={false}
              />
              
              {/* Original Image (Clipped) */}
              <div
                className="absolute inset-0 overflow-hidden"
                style={{
                  clipPath: isComparing 
                    ? `polygon(0 0, ${scanPosition}% 0, ${scanPosition}% 100%, 0 100%)`
                    : 'polygon(0 0, 0 0, 0 100%, 0 100%)'
                }}
              >
                <img
                  src={originalUrl}
                  alt={`${item.name} - Original`}
                  className="w-full h-full object-contain"
                  draggable={false}
                />
              </div>

              {/* Scan Line */}
              {isComparing && (
                <div
                  className="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg z-10 transition-all duration-75"
                  style={{ left: `${scanPosition}%` }}
                >
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
                    <Eye className="w-4 h-4 text-gray-700" />
                  </div>
                </div>
              )}

              {/* Instructions Overlay */}
              {!isComparing && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20">
                  <div className="bg-white rounded-lg p-6 text-center shadow-xl">
                    <Eye className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      Interactive Comparison
                    </h4>
                    <p className="text-gray-600 mb-4">
                      Click and drag to compare original vs compressed quality
                    </p>
                    <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                        Left: Original
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
                        Right: Compressed
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 bg-gray-50 border-t">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div>
                Drag across the image to see the quality difference
              </div>
              <div className="flex items-center space-x-4">
                <span>Original Quality</span>
                <div className="w-16 h-2 bg-gradient-to-r from-blue-500 to-green-500 rounded"></div>
                <span>Compressed</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
