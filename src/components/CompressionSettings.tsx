'use client'

import { useState } from 'react'
import { CompressionSettingsProps, CompressionOptions } from '@/types'
import { Settings, Image as ImageIcon, Video, Sparkles } from 'lucide-react'

export function CompressionSettings({ type, options, onChange }: CompressionSettingsProps) {
  const [isExpanded, setIsExpanded] = useState(true) // Default to expanded for better visibility

  const handleQualityChange = (quality: number) => {
    onChange({ ...options, quality })
  }

  const handleFormatChange = (format: string) => {
    console.log('Format changed to:', format)
    onChange({ ...options, outputFormat: format === 'auto' ? undefined : format })
  }

  const handleDimensionChange = (dimension: 'width' | 'height', value: string) => {
    const numValue = value ? parseInt(value) : undefined
    if (dimension === 'width') {
      onChange({ ...options, maxWidth: numValue })
    } else {
      onChange({ ...options, maxHeight: numValue })
    }
  }

  const handleToggleChange = (key: keyof CompressionOptions, value: boolean) => {
    onChange({ ...options, [key]: value })
  }

  const getQualityLabel = (quality: number) => {
    if (quality >= 0.8) return 'High Quality'
    if (quality >= 0.6) return 'Medium Quality'
    if (quality >= 0.4) return 'Low Quality'
    return 'Maximum Compression'
  }

  const getFormatOptions = () => {
    switch (type) {
      case 'image':
        return [
          { value: 'auto', label: 'Keep Original' },
          { value: 'jpeg', label: 'JPEG' },
          { value: 'png', label: 'PNG' },
          { value: 'webp', label: 'WebP' }
        ]
      case 'video':
        return [
          { value: 'auto', label: 'Keep Original' },
          { value: 'mp4', label: 'MP4' },
          { value: 'webm', label: 'WebM' },
          { value: 'avi', label: 'AVI' }
        ]
      case 'gif':
        return [
          { value: 'auto', label: 'Keep as GIF' },
          { value: 'gif', label: 'Optimized GIF' },
          { value: 'mp4', label: 'Convert to MP4' },
          { value: 'webm', label: 'Convert to WebM' }
        ]
      default:
        return [{ value: 'auto', label: 'Keep Original' }]
    }
  }

  const getTypeIcon = () => {
    switch (type) {
      case 'image':
        return <ImageIcon className="w-5 h-5 text-blue-500" />
      case 'video':
        return <Video className="w-5 h-5 text-purple-500" />
      case 'gif':
        return <Sparkles className="w-5 h-5 text-pink-500" />
      default:
        return <Settings className="w-5 h-5 text-gray-500" />
    }
  }

  return (
    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl shadow-lg border-2 border-blue-200">
      {/* Header - Always visible and prominent */}
      <div className="p-6 bg-white rounded-t-xl border-b-2 border-blue-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getTypeIcon()}
            <div>
              <h3 className="text-xl font-bold text-gray-900 capitalize">
                {type} Compression Settings
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Current: {getQualityLabel(options.quality)}
              </p>
            </div>
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 rounded-lg transition-colors"
          >
            <span className="text-sm font-medium text-blue-700">
              {isExpanded ? 'Hide' : 'Show'} Settings
            </span>
            <Settings className={`w-4 h-4 text-blue-600 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
          </button>
        </div>
      </div>

      {/* Quick Quality Presets - Always visible */}
      <div className="p-4 bg-white border-b border-gray-100">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium text-gray-700">Quick Presets:</span>
          <span className="text-xs text-gray-500">Click to apply</span>
        </div>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => handleQualityChange(0.1)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality <= 0.2
                ? 'bg-red-100 text-red-700 border-2 border-red-300'
                : 'bg-gray-100 text-gray-700 hover:bg-red-50'
            }`}
          >
            🔥 Maximum Compression
          </button>
          <button
            onClick={() => handleQualityChange(0.4)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality > 0.2 && options.quality <= 0.5
                ? 'bg-orange-100 text-orange-700 border-2 border-orange-300'
                : 'bg-gray-100 text-gray-700 hover:bg-orange-50'
            }`}
          >
            ⚡ Balanced
          </button>
          <button
            onClick={() => handleQualityChange(0.7)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality > 0.5 && options.quality <= 0.8
                ? 'bg-blue-100 text-blue-700 border-2 border-blue-300'
                : 'bg-gray-100 text-gray-700 hover:bg-blue-50'
            }`}
          >
            💎 High Quality
          </button>
          <button
            onClick={() => handleQualityChange(0.9)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality > 0.8
                ? 'bg-green-100 text-green-700 border-2 border-green-300'
                : 'bg-gray-100 text-gray-700 hover:bg-green-50'
            }`}
          >
            🎯 Best Quality
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-6 bg-white rounded-b-xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Quality Slider */}
            <div className="bg-gray-50 rounded-lg p-4">
              <label className="flex items-center text-sm font-bold text-gray-800 mb-4">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Fine-tune Quality Level
              </label>
              <div className="space-y-4">
                <div className="relative">
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.05"
                    value={options.quality}
                    onChange={(e) => handleQualityChange(parseFloat(e.target.value))}
                    className="w-full h-3 bg-gradient-to-r from-red-200 via-yellow-200 to-green-200 rounded-lg appearance-none cursor-pointer slider"
                    style={{
                      background: `linear-gradient(to right, #fecaca 0%, #fef3c7 50%, #d1fae5 100%)`
                    }}
                  />
                  <div
                    className="absolute top-0 w-6 h-3 bg-blue-600 rounded-lg shadow-lg transform -translate-x-1/2"
                    style={{ left: `${options.quality * 100}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-600">
                  <span className="font-medium">Max Compression</span>
                  <span className="font-bold text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {Math.round(options.quality * 100)}% Quality
                  </span>
                  <span className="font-medium">Best Quality</span>
                </div>
                <div className="text-center">
                  <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {getQualityLabel(options.quality)}
                  </span>
                </div>
              </div>
            </div>

            {/* Output Format */}
            <div className="bg-gray-50 rounded-lg p-4">
              <label className="flex items-center text-sm font-bold text-gray-800 mb-4">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                Output Format
              </label>
              <select
                value={options.outputFormat || 'auto'}
                onChange={(e) => handleFormatChange(e.target.value)}
                className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white font-medium"
              >
                {getFormatOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-600 mt-2">
                Choose the output format for your compressed files
              </p>
            </div>

            {/* Dimensions */}
            <div className="bg-gray-50 rounded-lg p-4">
              <label className="flex items-center text-sm font-bold text-gray-800 mb-4">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Maximum Dimensions
              </label>
              <div className="flex space-x-3 items-center">
                <input
                  type="number"
                  placeholder="Width"
                  value={options.maxWidth || ''}
                  onChange={(e) => handleDimensionChange('width', e.target.value)}
                  className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white font-medium text-center"
                />
                <span className="flex items-center text-gray-400 font-bold text-lg">×</span>
                <input
                  type="number"
                  placeholder="Height"
                  value={options.maxHeight || ''}
                  onChange={(e) => handleDimensionChange('height', e.target.value)}
                  className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white font-medium text-center"
                />
              </div>
              <p className="text-xs text-gray-600 mt-2">
                💡 Leave empty to keep original dimensions
              </p>
            </div>

            {/* Additional Options */}
            <div className="bg-gray-50 rounded-lg p-4 lg:col-span-2">
              <label className="flex items-center text-sm font-bold text-gray-800 mb-4">
                <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                Additional Options
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label className="flex items-center p-3 bg-white rounded-lg border-2 border-gray-200 hover:border-blue-300 cursor-pointer transition-colors">
                  <input
                    type="checkbox"
                    checked={options.maintainAspectRatio}
                    onChange={(e) => handleToggleChange('maintainAspectRatio', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-700">📐 Maintain aspect ratio</span>
                </label>

                <label className="flex items-center p-3 bg-white rounded-lg border-2 border-gray-200 hover:border-blue-300 cursor-pointer transition-colors">
                  <input
                    type="checkbox"
                    checked={options.removeMetadata}
                    onChange={(e) => handleToggleChange('removeMetadata', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-700">🗑️ Remove metadata</span>
                </label>

                {type === 'image' && (
                  <label className="flex items-center p-3 bg-white rounded-lg border-2 border-gray-200 hover:border-blue-300 cursor-pointer transition-colors">
                    <input
                      type="checkbox"
                      checked={(options as any).preserveTransparency !== false}
                      onChange={(e) => handleToggleChange('preserveTransparency' as any, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                    />
                    <span className="ml-3 text-sm font-medium text-gray-700">✨ Preserve transparency</span>
                  </label>
                )}
              </div>
            </div>
          </div>

          {/* Presets */}
          <div className="mt-6 pt-6 border-t border-gray-100">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Quick Presets
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.9,
                  maxWidth: undefined,
                  maxHeight: undefined,
                  maintainAspectRatio: true,
                  removeMetadata: false
                })}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
              >
                High Quality
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.7,
                  maxWidth: 1920,
                  maxHeight: 1080,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors"
              >
                Balanced
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.5,
                  maxWidth: 1280,
                  maxHeight: 720,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
              >
                Small Size
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.3,
                  maxWidth: 800,
                  maxHeight: 600,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors"
              >
                Maximum Compression
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
