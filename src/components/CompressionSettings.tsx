'use client'

import { useState } from 'react'
import { CompressionSettingsProps, CompressionOptions } from '@/types'
import { Settings, Image as ImageIcon, Video, Sparkles } from 'lucide-react'

export function CompressionSettings({ type, options, onChange }: CompressionSettingsProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleQualityChange = (quality: number) => {
    onChange({ ...options, quality })
  }

  const handleFormatChange = (format: string) => {
    console.log('Format changed to:', format)
    onChange({ ...options, outputFormat: format === 'auto' ? undefined : format })
  }

  const handleDimensionChange = (dimension: 'width' | 'height', value: string) => {
    const numValue = value ? parseInt(value) : undefined
    if (dimension === 'width') {
      onChange({ ...options, maxWidth: numValue })
    } else {
      onChange({ ...options, maxHeight: numValue })
    }
  }

  const handleToggleChange = (key: keyof CompressionOptions, value: boolean) => {
    onChange({ ...options, [key]: value })
  }

  const getQualityLabel = (quality: number) => {
    if (quality >= 0.8) return 'High Quality'
    if (quality >= 0.6) return 'Medium Quality'
    if (quality >= 0.4) return 'Low Quality'
    return 'Maximum Compression'
  }

  const getFormatOptions = () => {
    switch (type) {
      case 'image':
        return [
          { value: 'auto', label: 'Keep Original' },
          { value: 'jpeg', label: 'JPEG' },
          { value: 'png', label: 'PNG' },
          { value: 'webp', label: 'WebP' }
        ]
      case 'video':
        return [
          { value: 'auto', label: 'Keep Original' },
          { value: 'mp4', label: 'MP4' },
          { value: 'webm', label: 'WebM' },
          { value: 'avi', label: 'AVI' }
        ]
      case 'gif':
        return [
          { value: 'auto', label: 'Keep as GIF' },
          { value: 'gif', label: 'Optimized GIF' },
          { value: 'mp4', label: 'Convert to MP4' },
          { value: 'webm', label: 'Convert to WebM' }
        ]
      default:
        return [{ value: 'auto', label: 'Keep Original' }]
    }
  }

  const getTypeIcon = () => {
    switch (type) {
      case 'image':
        return <ImageIcon className="w-5 h-5 text-blue-500" />
      case 'video':
        return <Video className="w-5 h-5 text-purple-500" />
      case 'gif':
        return <Sparkles className="w-5 h-5 text-pink-500" />
      default:
        return <Settings className="w-5 h-5 text-gray-500" />
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200">
      <div 
        className="p-6 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getTypeIcon()}
            <h3 className="text-lg font-semibold text-gray-900 capitalize">
              {type} Compression Settings
            </h3>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              Quality: {getQualityLabel(options.quality)}
            </span>
            <Settings className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="px-6 pb-6 border-t border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            {/* Quality Slider */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Quality Level
              </label>
              <div className="space-y-3">
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={options.quality}
                  onChange={(e) => handleQualityChange(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Max Compression</span>
                  <span className="font-medium text-gray-700">
                    {getQualityLabel(options.quality)}
                  </span>
                  <span>Best Quality</span>
                </div>
              </div>
            </div>

            {/* Output Format */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Output Format
              </label>
              <select
                value={options.outputFormat || 'auto'}
                onChange={(e) => handleFormatChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {getFormatOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Dimensions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Maximum Dimensions (optional)
              </label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="Width"
                  value={options.maxWidth || ''}
                  onChange={(e) => handleDimensionChange('width', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="flex items-center text-gray-500">×</span>
                <input
                  type="number"
                  placeholder="Height"
                  value={options.maxHeight || ''}
                  onChange={(e) => handleDimensionChange('height', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to keep original dimensions
              </p>
            </div>

            {/* Additional Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Additional Options
              </label>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={options.maintainAspectRatio}
                    onChange={(e) => handleToggleChange('maintainAspectRatio', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Maintain aspect ratio</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={options.removeMetadata}
                    onChange={(e) => handleToggleChange('removeMetadata', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Remove metadata</span>
                </label>

                {type === 'image' && (
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={(options as any).preserveTransparency !== false}
                      onChange={(e) => handleToggleChange('preserveTransparency' as any, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Preserve transparency</span>
                  </label>
                )}
              </div>
            </div>
          </div>

          {/* Presets */}
          <div className="mt-6 pt-6 border-t border-gray-100">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Quick Presets
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.9,
                  maxWidth: undefined,
                  maxHeight: undefined,
                  maintainAspectRatio: true,
                  removeMetadata: false
                })}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
              >
                High Quality
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.7,
                  maxWidth: 1920,
                  maxHeight: 1080,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors"
              >
                Balanced
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.5,
                  maxWidth: 1280,
                  maxHeight: 720,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
              >
                Small Size
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.3,
                  maxWidth: 800,
                  maxHeight: 600,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors"
              >
                Maximum Compression
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
