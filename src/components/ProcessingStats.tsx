'use client'

import { motion } from 'framer-motion'
import { 
  Play, 
  Download, 
  Trash2, 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Zap
} from 'lucide-react'

interface ProcessingStatsProps {
  stats: {
    total: number
    completed: number
    failed: number
    processing: number
    pending: number
    totalOriginalSize: number
    totalCompressedSize: number
    averageCompressionRatio: number
  }
  isProcessing: boolean
  currentProcessingIndex: number
  onCompress: () => void
  onDownloadAll: () => void
  onClearAll: () => void
}

export function ProcessingStats({
  stats,
  isProcessing,
  currentProcessingIndex,
  onCompress,
  onDownloadAll,
  onClearAll
}: ProcessingStatsProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const canCompress = stats.total > 0 && !isProcessing && (stats.pending > 0 || stats.failed > 0)
  const canDownload = stats.completed > 0
  const canClear = stats.total > 0 && !isProcessing

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-2xl shadow-lg p-6 space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Zap className="w-5 h-5 mr-2 text-blue-600" />
          Processing Stats
        </h3>
        {isProcessing && (
          <div className="flex items-center text-sm text-blue-600">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse mr-2"></div>
            Processing...
          </div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <FileText className="w-4 h-4 text-gray-600" />
            <span className="text-2xl font-bold text-gray-900">{stats.total}</span>
          </div>
          <div className="text-sm text-gray-600">Total Files</div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <span className="text-2xl font-bold text-green-700">{stats.completed}</span>
          </div>
          <div className="text-sm text-green-600">Completed</div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <Clock className="w-4 h-4 text-blue-600" />
            <span className="text-2xl font-bold text-blue-700">{stats.pending + stats.processing}</span>
          </div>
          <div className="text-sm text-blue-600">Remaining</div>
        </div>

        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <span className="text-2xl font-bold text-red-700">{stats.failed}</span>
          </div>
          <div className="text-sm text-red-600">Failed</div>
        </div>
      </div>

      {/* Compression Stats */}
      {stats.completed > 0 && (
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Compression Results</h4>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Original Size:</span>
              <span className="font-medium">{formatFileSize(stats.totalOriginalSize)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Compressed Size:</span>
              <span className="font-medium">{formatFileSize(stats.totalCompressedSize)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Space Saved:</span>
              <span className="font-medium text-green-600">
                {formatFileSize(stats.totalOriginalSize - stats.totalCompressedSize)}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Average Compression:</span>
              <span className="font-medium text-blue-600">
                {stats.averageCompressionRatio.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Progress Bar */}
      {isProcessing && stats.total > 0 && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progress</span>
            <span className="text-gray-900">
              {currentProcessingIndex + 1} of {stats.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ 
                width: `${((currentProcessingIndex + 1) / stats.total) * 100}%` 
              }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <button
          onClick={onCompress}
          disabled={!canCompress}
          className={`
            w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors
            ${canCompress
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }
          `}
        >
          <Play className="w-4 h-4 mr-2" />
          {isProcessing ? 'Processing...' : 'Start Compression'}
        </button>

        {canDownload && (
          <button
            onClick={onDownloadAll}
            className="w-full flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
          >
            <Download className="w-4 h-4 mr-2" />
            Download All ({stats.completed})
          </button>
        )}

        {canClear && (
          <button
            onClick={onClearAll}
            className="w-full flex items-center justify-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Clear All
          </button>
        )}
      </div>
    </motion.div>
  )
}
