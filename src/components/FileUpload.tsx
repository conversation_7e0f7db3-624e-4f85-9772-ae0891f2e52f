'use client'

import { useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { motion } from 'framer-motion'
import { Upload, FileImage, AlertCircle } from 'lucide-react'

interface FileUploadProps {
  onFileUpload: (files: File[]) => void
  acceptedTypes?: Record<string, string[]>
  maxFileSize?: number
  title?: string
  subtitle?: string
  multiple?: boolean
}

export function FileUpload({
  onFileUpload,
  acceptedTypes = {
    'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
    'video/*': ['.mp4', '.avi', '.mov', '.webm', '.mkv']
  },
  maxFileSize = 100 * 1024 * 1024, // 100MB default
  title = "Drop your files here",
  subtitle = "or click to browse",
  multiple = true
}: FileUploadProps) {
  
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) => 
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      ).join('\n')
      alert(`Some files were rejected:\n${errors}`)
    }
    
    if (acceptedFiles.length > 0) {
      onFileUpload(acceptedFiles)
    }
  }, [onFileUpload])

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: acceptedTypes,
    maxSize: maxFileSize,
    multiple
  })

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer
          transition-all duration-300 ease-in-out
          ${isDragActive && !isDragReject
            ? 'border-blue-400 bg-blue-50 scale-105'
            : isDragReject
            ? 'border-red-400 bg-red-50'
            : 'border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100'
          }
        `}
      >
        <input {...getInputProps()} />
        
        {/* Upload Icon */}
        <motion.div
          animate={isDragActive ? { scale: 1.1, rotate: 5 } : { scale: 1, rotate: 0 }}
          transition={{ duration: 0.2 }}
          className="flex justify-center mb-6"
        >
          {isDragReject ? (
            <AlertCircle className="w-16 h-16 text-red-500" />
          ) : (
            <div className={`
              w-20 h-20 rounded-full flex items-center justify-center
              ${isDragActive 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-gray-200 text-gray-600'
              }
            `}>
              {isDragActive ? (
                <FileImage className="w-10 h-10" />
              ) : (
                <Upload className="w-10 h-10" />
              )}
            </div>
          )}
        </motion.div>

        {/* Text */}
        <div className="space-y-2">
          <h3 className={`
            text-xl font-semibold
            ${isDragReject 
              ? 'text-red-600' 
              : isDragActive 
              ? 'text-blue-600' 
              : 'text-gray-900'
            }
          `}>
            {isDragReject 
              ? 'Invalid file type' 
              : isDragActive 
              ? 'Drop files here' 
              : title
            }
          </h3>
          <p className={`
            text-sm
            ${isDragReject 
              ? 'text-red-500' 
              : isDragActive 
              ? 'text-blue-500' 
              : 'text-gray-500'
            }
          `}>
            {isDragReject 
              ? 'Please check file type and size requirements' 
              : subtitle
            }
          </p>
        </div>

        {/* File Requirements */}
        <div className="mt-6 text-xs text-gray-400 space-y-1">
          <div>
            Supported formats: {Object.values(acceptedTypes).flat().join(', ')}
          </div>
          <div>
            Maximum file size: {Math.round(maxFileSize / (1024 * 1024))}MB
          </div>
        </div>

        {/* Animated Background */}
        {isDragActive && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-2xl"
          />
        )}
      </div>
    </motion.div>
  )
}
