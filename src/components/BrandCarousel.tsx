'use client'

const brands = [
  { name: 'Google', logo: '/brands/google.svg' },
  { name: 'Microsoft', logo: '/brands/microsoft.svg' },
  { name: 'Apple', logo: '/brands/apple.svg' },
  { name: 'Amazon', logo: '/brands/amazon.svg' },
  { name: 'Netflix', logo: '/brands/netflix.svg' },
  { name: 'Spotify', logo: '/brands/spotify.svg' },
  { name: 'Adobe', logo: '/brands/adobe.svg' },
  { name: 'Tesla', logo: '/brands/tesla.svg' },
  { name: 'Uber', logo: '/brands/uber.svg' },
  { name: 'Airbnb', logo: '/brands/airbnb.svg' },
  { name: 'Dropbox', logo: '/brands/dropbox.svg' },
  { name: 'Slack', logo: '/brands/slack.svg' },
  { name: 'Samsung', logo: '/brands/samsung.svg' },
  { name: 'Sony', logo: '/brands/sony.svg' },
  { name: 'Walmart', logo: '/brands/walmart.svg' },
  { name: 'Bank of America', logo: '/brands/boa.svg' }
]

export function BrandCarousel() {
  return (

    <section className="py-16 bg-gray-50 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Trusted by Leading Companies Worldwide
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Join thousands of businesses that rely on our compression technology to optimize their digital assets and improve performance.
          </p>
        </div>

        <div className="relative overflow-hidden">
          <div
            className="flex animate-marquee space-x-16"
            style={{
              maskImage: 'linear-gradient(to right, transparent, black 10%, black 90%, transparent)',
              WebkitMaskImage: 'linear-gradient(to right, transparent, black 10%, black 90%, transparent)'
            }}
          >
            {/* First set of brands */}
            {brands.concat(brands).map((brand, index) => (
              <div
                key={index}
                className="flex items-center justify-center min-w-[120px] h-16 grayscale hover:grayscale-0 transition-all duration-300 opacity-60 hover:opacity-100 flex-shrink-0"
              >
                <div className="text-lg font-semibold text-gray-400 hover:text-gray-600 transition-colors whitespace-nowrap">
                  {brand.name}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
