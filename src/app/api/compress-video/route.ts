import { NextRequest, NextResponse } from 'next/server'
import ffmpeg from 'fluent-ffmpeg'
import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

// 设置FFmpeg路径（如果需要）
// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const options = JSON.parse(formData.get('options') as string || '{}')

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // 验证文件类型
    if (!file.type.includes('video')) {
      return NextResponse.json({ error: 'File must be a video' }, { status: 400 })
    }

    // 创建临时目录
    const tempDir = path.join(process.cwd(), 'temp')
    await fs.mkdir(tempDir, { recursive: true })

    // 生成唯一文件名
    const fileId = uuidv4()
    const inputExt = file.name.split('.').pop() || 'mp4'
    const outputExt = options.outputFormat || 'mp4'
    const inputPath = path.join(tempDir, `${fileId}_input.${inputExt}`)
    const outputPath = path.join(tempDir, `${fileId}_output.${outputExt}`)

    // 保存上传的文件
    const buffer = Buffer.from(await file.arrayBuffer())
    await fs.writeFile(inputPath, buffer)

    // 获取原始文件大小
    const originalSize = buffer.length

    try {
      // 执行FFmpeg压缩 - 使用简化的方法避免编解码器问题
      await new Promise<void>((resolve, reject) => {
        let command = ffmpeg(inputPath)

        // 视频编解码器设置 - 使用更兼容的编解码器
        // 直接使用最兼容的编解码器，避免复杂的检测逻辑
        let codec = 'libx264'

        // 如果用户指定了编解码器，先尝试用户选择
        if (options.codec && options.codec !== 'h264') {
          codec = options.codec
        }

        console.log(`Attempting to use video codec: ${codec}`)
        command = command.videoCodec(codec)

        // 音频编解码器 - 使用最兼容的设置
        command = command.audioCodec('aac').audioBitrate('128k')

        // 质量控制 - 使用CRF (Constant Rate Factor)
        let crf = 23 // 默认值，平衡质量和大小
        if (options.quality) {
          // quality: 0.1-1.0 -> CRF: 51-18 (数值越小质量越好)
          crf = Math.round(51 - (options.quality * 33))
          crf = Math.max(18, Math.min(51, crf)) // 限制在18-51范围内
        }
        command = command.outputOptions(['-crf', crf.toString()])

        // 预设 - 平衡编码速度和压缩效率
        command = command.outputOptions(['-preset', 'medium'])

        // 尺寸控制
        if (options.maxWidth || options.maxHeight) {
          const width = options.maxWidth || -1
          const height = options.maxHeight || -1
          command = command.size(`${width}x${height}`)
        }

        // 帧率控制
        if (options.fps) {
          command = command.fps(options.fps)
        }

        // 比特率控制（可选，CRF优先）
        if (options.bitrate) {
          command = command.videoBitrate(options.bitrate)
        }

        // 移除元数据
        if (options.removeMetadata) {
          command = command.outputOptions(['-map_metadata', '-1'])
        }

        // 针对不同格式的优化
        if (outputExt === 'mp4') {
          // MP4优化：快速启动
          command = command.outputOptions(['-movflags', '+faststart'])
        } else if (outputExt === 'webm') {
          // WebM优化
          command = command.videoCodec('libvpx-vp9').audioCodec('libopus')
        }

        // 两遍编码（可选，用于更好的质量控制）
        if (options.quality && options.quality > 0.8) {
          command = command.outputOptions(['-pass', '1', '-f', 'null'])
        }

        command
          .output(outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg command:', commandLine)
          })
          .on('progress', (progress) => {
            console.log('Processing: ' + (progress.percent || 0) + '% done')
          })
          .on('end', () => {
            console.log('Video compression finished')
            resolve()
          })
          .on('error', (err) => {
            console.error('FFmpeg error:', err)
            reject(err)
          })
          .run()
      })

      // 读取压缩后的文件
      const compressedBuffer = await fs.readFile(outputPath)
      const compressedSize = compressedBuffer.length
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100

      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files:', cleanupError)
      }

      // 返回压缩结果
      return new NextResponse(compressedBuffer, {
        status: 200,
        headers: {
          'Content-Type': `video/${outputExt}`,
          'Content-Length': compressedSize.toString(),
          'X-Original-Size': originalSize.toString(),
          'X-Compressed-Size': compressedSize.toString(),
          'X-Compression-Ratio': compressionRatio.toFixed(2),
        },
      })

    } catch (ffmpegError) {
      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files after error:', cleanupError)
      }

      console.error('FFmpeg processing failed:', ffmpegError)

      // 提供更详细的错误信息
      let errorMessage = 'Video processing failed'
      let errorDetails = ffmpegError

      if (ffmpegError instanceof Error) {
        if (ffmpegError.message.includes('codec') || ffmpegError.message.includes('h264') || ffmpegError.message.includes('libx264')) {
          errorMessage = 'FFmpeg codec not available'
          errorDetails = 'The server does not have the required video codecs installed. This is a server configuration issue. Please try using the client-side compression instead.'
        } else if (ffmpegError.message.includes('Invalid data found')) {
          errorMessage = 'Invalid video file'
          errorDetails = 'The uploaded file appears to be corrupted or in an unsupported format.'
        } else if (ffmpegError.message.includes('Permission denied')) {
          errorMessage = 'Server permission error'
          errorDetails = 'Server does not have permission to process the file. Please try again later.'
        } else if (ffmpegError.message.includes('No such file')) {
          errorMessage = 'File processing error'
          errorDetails = 'Temporary file handling failed. Please try again.'
        } else {
          errorDetails = ffmpegError.message
        }
      }

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          details: errorDetails,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
