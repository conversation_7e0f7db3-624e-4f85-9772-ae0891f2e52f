import { NextRequest, NextResponse } from 'next/server'
import ffmpeg from 'fluent-ffmpeg'
import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

// 设置FFmpeg路径（如果需要）
// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const options = JSON.parse(formData.get('options') as string || '{}')

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // 验证文件类型
    if (!file.type.includes('gif')) {
      return NextResponse.json({ error: 'File must be a GIF' }, { status: 400 })
    }

    // 创建临时目录
    const tempDir = path.join(process.cwd(), 'temp')
    await fs.mkdir(tempDir, { recursive: true })

    // 生成唯一文件名
    const fileId = uuidv4()
    const inputPath = path.join(tempDir, `${fileId}_input.gif`)
    const outputPath = path.join(tempDir, `${fileId}_output.gif`)

    // 保存上传的文件
    const buffer = Buffer.from(await file.arrayBuffer())
    await fs.writeFile(inputPath, buffer)

    // 获取原始文件大小
    const originalSize = buffer.length

    try {
      // 执行FFmpeg压缩 - 专门针对GIF优化
      await new Promise<void>((resolve, reject) => {
        // 第一步：生成调色板
        const paletteFile = path.join(tempDir, `${fileId}_palette.png`)

        // 构建调色板生成命令
        let paletteCommand = ffmpeg(inputPath)

        // 帧率控制
        const frameRate = options.frameRate || 10
        let paletteFilter = `fps=${frameRate}`

        // 尺寸控制
        if (options.maxWidth || options.maxHeight) {
          const width = options.maxWidth || -1
          const height = options.maxHeight || -1
          paletteFilter += `,scale=${width}:${height}:flags=lanczos`
        }

        // 调色板生成 - 根据质量调整颜色数量
        let maxColors = 256
        if (options.quality < 0.3) {
          maxColors = 64  // 低质量，少颜色
        } else if (options.quality < 0.7) {
          maxColors = 128 // 中等质量
        } else {
          maxColors = 256 // 高质量，全颜色
        }

        paletteFilter += `,palettegen=max_colors=${maxColors}:reserve_transparent=1`

        paletteCommand
          .complexFilter(paletteFilter)
          .output(paletteFile)
          .on('end', () => {
            console.log('Palette generation finished')

            // 第二步：使用调色板压缩GIF
            let finalCommand = ffmpeg()
              .input(inputPath)
              .input(paletteFile)

            // 构建最终压缩过滤器
            let finalFilter = `fps=${frameRate}`

            if (options.maxWidth || options.maxHeight) {
              const width = options.maxWidth || -1
              const height = options.maxHeight || -1
              finalFilter += `,scale=${width}:${height}:flags=lanczos`
            }

            finalFilter += '[x];[x][1:v]paletteuse'

            // 根据质量调整抖动
            if (options.quality < 0.5) {
              finalFilter += '=dither=bayer:bayer_scale=5:diff_mode=rectangle'
            } else {
              finalFilter += '=dither=bayer:bayer_scale=2'
            }

            const outputOptions = ['-y', '-loop', '0']

            // 移除元数据
            if (options.removeMetadata) {
              outputOptions.push('-map_metadata', '-1')
            }

            finalCommand
              .complexFilter(finalFilter)
              .outputOptions(outputOptions)
              .output(outputPath)
              .on('start', (commandLine) => {
                console.log('Final FFmpeg command:', commandLine)
              })
              .on('progress', (progress) => {
                console.log('Final processing: ' + (progress.percent || 0) + '% done')
              })
              .on('end', async () => {
                console.log('GIF compression finished')
                // 清理调色板文件
                try {
                  await fs.unlink(paletteFile)
                } catch (e) {
                  console.warn('Failed to cleanup palette file:', e)
                }
                resolve()
              })
              .on('error', async (err) => {
                console.error('Final FFmpeg error:', err)
                // 清理调色板文件
                try {
                  await fs.unlink(paletteFile)
                } catch (e) {
                  console.warn('Failed to cleanup palette file after error:', e)
                }
                reject(err)
              })
              .run()
          })
          .on('error', (err) => {
            console.error('Palette generation error:', err)
            reject(err)
          })
          .run()
      })

      // 读取压缩后的文件
      const compressedBuffer = await fs.readFile(outputPath)
      const compressedSize = compressedBuffer.length
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100

      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files:', cleanupError)
      }

      // 返回压缩结果
      return new NextResponse(compressedBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'image/gif',
          'Content-Length': compressedSize.toString(),
          'X-Original-Size': originalSize.toString(),
          'X-Compressed-Size': compressedSize.toString(),
          'X-Compression-Ratio': compressionRatio.toFixed(2),
        },
      })

    } catch (ffmpegError) {
      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files after error:', cleanupError)
      }

      console.error('FFmpeg processing failed:', ffmpegError)
      return NextResponse.json(
        { error: 'Video processing failed', details: ffmpegError },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
