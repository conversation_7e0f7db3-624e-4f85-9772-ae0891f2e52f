import { NextRequest, NextResponse } from 'next/server'
import ffmpeg from 'fluent-ffmpeg'
import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

// 设置FFmpeg路径（如果需要）
// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const options = JSON.parse(formData.get('options') as string || '{}')

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // 验证文件类型
    if (!file.type.includes('gif')) {
      return NextResponse.json({ error: 'File must be a GIF' }, { status: 400 })
    }

    // 创建临时目录
    const tempDir = path.join(process.cwd(), 'temp')
    await fs.mkdir(tempDir, { recursive: true })

    // 生成唯一文件名
    const fileId = uuidv4()
    const inputPath = path.join(tempDir, `${fileId}_input.gif`)
    const outputPath = path.join(tempDir, `${fileId}_output.gif`)

    // 保存上传的文件
    const buffer = Buffer.from(await file.arrayBuffer())
    await fs.writeFile(inputPath, buffer)

    // 获取原始文件大小
    const originalSize = buffer.length

    try {
      // 执行FFmpeg压缩 - 使用简化的方法
      await new Promise<void>((resolve, reject) => {
        let command = ffmpeg(inputPath)

        // 基本的GIF优化参数
        const outputOptions = ['-y'] // 覆盖输出文件

        // 帧率控制
        const frameRate = options.frameRate || 10
        command = command.fps(frameRate)

        // 尺寸控制
        if (options.maxWidth || options.maxHeight) {
          const width = options.maxWidth || -1
          const height = options.maxHeight || -1
          command = command.size(`${width}x${height}`)
        }

        // 质量控制 - 通过比特率
        if (options.quality && options.quality < 0.5) {
          // 低质量 - 更激进的压缩
          outputOptions.push('-b:v', '200k')
        } else if (options.quality && options.quality < 0.8) {
          // 中等质量
          outputOptions.push('-b:v', '500k')
        } else {
          // 高质量
          outputOptions.push('-b:v', '1000k')
        }

        // 循环设置
        outputOptions.push('-loop', '0')

        // 移除元数据
        if (options.removeMetadata) {
          outputOptions.push('-map_metadata', '-1')
        }

        command
          .outputOptions(outputOptions)
          .output(outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg command:', commandLine)
          })
          .on('progress', (progress) => {
            console.log('Processing: ' + (progress.percent || 0) + '% done')
          })
          .on('end', () => {
            console.log('FFmpeg processing finished')
            resolve()
          })
          .on('error', (err) => {
            console.error('FFmpeg error:', err)
            reject(err)
          })
          .run()
      })

      // 读取压缩后的文件
      const compressedBuffer = await fs.readFile(outputPath)
      const compressedSize = compressedBuffer.length
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100

      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files:', cleanupError)
      }

      // 返回压缩结果
      return new NextResponse(compressedBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'image/gif',
          'Content-Length': compressedSize.toString(),
          'X-Original-Size': originalSize.toString(),
          'X-Compressed-Size': compressedSize.toString(),
          'X-Compression-Ratio': compressionRatio.toFixed(2),
        },
      })

    } catch (ffmpegError) {
      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files after error:', cleanupError)
      }

      console.error('FFmpeg processing failed:', ffmpegError)
      return NextResponse.json(
        { error: 'Video processing failed', details: ffmpegError },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
