import { NextRequest, NextResponse } from 'next/server'
import ffmpeg from 'fluent-ffmpeg'
import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

// 设置FFmpeg路径（如果需要）
// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const options = JSON.parse(formData.get('options') as string || '{}')

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // 验证文件类型
    if (!file.type.includes('gif')) {
      return NextResponse.json({ error: 'File must be a GIF' }, { status: 400 })
    }

    // 创建临时目录
    const tempDir = path.join(process.cwd(), 'temp')
    await fs.mkdir(tempDir, { recursive: true })

    // 生成唯一文件名
    const fileId = uuidv4()
    const inputPath = path.join(tempDir, `${fileId}_input.gif`)
    const outputPath = path.join(tempDir, `${fileId}_output.gif`)

    // 保存上传的文件
    const buffer = Buffer.from(await file.arrayBuffer())
    await fs.writeFile(inputPath, buffer)

    // 获取原始文件大小
    const originalSize = buffer.length

    try {
      // 执行FFmpeg压缩 - 专门针对GIF优化
      await new Promise<void>((resolve, reject) => {
        // 第一步：生成调色板
        const paletteFile = path.join(tempDir, `${fileId}_palette.png`)

        // 构建调色板生成命令
        let paletteCommand = ffmpeg(inputPath)

        // 帧率控制
        const frameRate = options.frameRate || 10
        let paletteFilter = `fps=${frameRate}`

        // 尺寸控制
        if (options.maxWidth || options.maxHeight) {
          const width = options.maxWidth || -1
          const height = options.maxHeight || -1
          paletteFilter += `,scale=${width}:${height}:flags=lanczos`
        }

        // 调色板生成 - 根据质量调整颜色数量，更激进的压缩
        let maxColors = 256
        if (options.quality < 0.2) {
          maxColors = 32   // 极低质量，极少颜色
        } else if (options.quality < 0.4) {
          maxColors = 64   // 低质量，少颜色
        } else if (options.quality < 0.6) {
          maxColors = 96   // 中低质量
        } else if (options.quality < 0.8) {
          maxColors = 128  // 中等质量
        } else {
          maxColors = 192  // 高质量，减少一些颜色以获得压缩
        }

        paletteFilter += `,palettegen=max_colors=${maxColors}:reserve_transparent=1`

        paletteCommand
          .complexFilter(paletteFilter)
          .output(paletteFile)
          .on('end', () => {
            console.log('Palette generation finished')

            // 第二步：使用调色板压缩GIF
            let finalCommand = ffmpeg()
              .input(inputPath)
              .input(paletteFile)

            // 构建最终压缩过滤器
            let finalFilter = `fps=${frameRate}`

            if (options.maxWidth || options.maxHeight) {
              const width = options.maxWidth || -1
              const height = options.maxHeight || -1
              finalFilter += `,scale=${width}:${height}:flags=lanczos`
            }

            finalFilter += '[x];[x][1:v]paletteuse'

            // 根据质量调整抖动和优化参数
            if (options.quality < 0.3) {
              // 极低质量：强抖动 + 差异模式 + 新帧检测
              finalFilter += '=dither=bayer:bayer_scale=5:diff_mode=rectangle:new=1'
            } else if (options.quality < 0.6) {
              // 中低质量：中等抖动 + 差异模式
              finalFilter += '=dither=bayer:bayer_scale=3:diff_mode=rectangle'
            } else {
              // 高质量：轻微抖动
              finalFilter += '=dither=bayer:bayer_scale=1'
            }

            const outputOptions = ['-y', '-loop', '0']

            // 移除元数据
            if (options.removeMetadata) {
              outputOptions.push('-map_metadata', '-1')
            }

            // GIF特定的优化选项（移除不支持的选项）
            // 注意：GIF格式不支持compression_level, preset, optimize等视频选项

            finalCommand
              .complexFilter(finalFilter)
              .outputOptions(outputOptions)
              .output(outputPath)
              .on('start', (commandLine) => {
                console.log('Final FFmpeg command:', commandLine)
              })
              .on('progress', (progress) => {
                console.log('Final processing: ' + (progress.percent || 0) + '% done')
              })
              .on('end', async () => {
                console.log('GIF compression finished')
                // 清理调色板文件
                try {
                  await fs.unlink(paletteFile)
                } catch (e) {
                  console.warn('Failed to cleanup palette file:', e)
                }
                resolve()
              })
              .on('error', async (err) => {
                console.error('Final FFmpeg error:', err)
                // 清理调色板文件
                try {
                  await fs.unlink(paletteFile)
                } catch (e) {
                  console.warn('Failed to cleanup palette file after error:', e)
                }
                reject(err)
              })
              .run()
          })
          .on('error', (err) => {
            console.error('Palette generation error:', err)
            reject(err)
          })
          .run()
      })

      // 读取压缩后的文件
      const compressedBuffer = await fs.readFile(outputPath)
      const compressedSize = compressedBuffer.length
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100

      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files:', cleanupError)
      }

      // 返回压缩结果
      return new NextResponse(compressedBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'image/gif',
          'Content-Length': compressedSize.toString(),
          'X-Original-Size': originalSize.toString(),
          'X-Compressed-Size': compressedSize.toString(),
          'X-Compression-Ratio': compressionRatio.toFixed(2),
        },
      })

    } catch (ffmpegError) {
      // 清理临时文件
      try {
        await fs.unlink(inputPath)
        await fs.unlink(outputPath)
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp files after error:', cleanupError)
      }

      console.error('FFmpeg GIF processing failed:', ffmpegError)

      // 提供更详细的错误信息
      let errorMessage = 'GIF processing failed'
      let errorDetails = ffmpegError

      if (ffmpegError instanceof Error) {
        if (ffmpegError.message.includes('Invalid data found')) {
          errorMessage = 'Invalid GIF file'
          errorDetails = 'The uploaded file appears to be corrupted or not a valid GIF.'
        } else if (ffmpegError.message.includes('Permission denied')) {
          errorMessage = 'Server permission error'
          errorDetails = 'Server does not have permission to process the file. Please try again later.'
        } else if (ffmpegError.message.includes('No such file')) {
          errorMessage = 'File processing error'
          errorDetails = 'Temporary file was not created properly. Please try again.'
        } else {
          errorDetails = ffmpegError.message
        }
      }

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          details: errorDetails,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
