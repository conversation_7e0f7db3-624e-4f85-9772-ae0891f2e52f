'use client'

import { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { FileUpload } from '@/components/FileUpload'
import { FileCard } from '@/components/FileCard'
import { CompressionSettings } from '@/components/CompressionSettings'
import { ProcessingStats } from '@/components/ProcessingStats'
import { useCompressionManager } from '@/hooks/useCompressionManager'
import { CompressionOptions } from '@/types'
import { 
  Sparkles, 
  Zap, 
  Download,
  Settings,
  FileImage,
  Play,
  Palette,
  Clock,
  Layers
} from 'lucide-react'

export default function GifCompressPage() {
  const [compressionOptions, setCompressionOptions] = useState<CompressionOptions>({
    quality: 0.3, // Balanced compression for GIFs
    maintainAspectRatio: true,
    removeMetadata: true,
    frameRate: 10,
    colors: 128,
    dithering: true
  })

  const {
    files,
    isProcessing,
    currentProcessingIndex,
    addFiles,
    removeFile,
    compressAllFiles,
    retryFile,
    reprocessFile,
    downloadFile: downloadSingleFile,
    downloadAllFiles,
    clearAllFiles,
    getStats
  } = useCompressionManager()

  const stats = getStats()

  const handleFileUpload = useCallback((uploadedFiles: File[]) => {
    // Filter only GIF files
    const gifFiles = uploadedFiles.filter(file => 
      file.type === 'image/gif'
    )
    
    if (gifFiles.length !== uploadedFiles.length) {
      alert('Only GIF files are allowed on this page. Other image formats should be processed on the Image compression page.')
    }
    
    if (gifFiles.length > 0) {
      addFiles(gifFiles)
    }
  }, [addFiles])

  const handleCompress = useCallback(() => {
    compressAllFiles(compressionOptions)
  }, [compressAllFiles, compressionOptions])

  const handleRetry = (id: string) => {
    retryFile(id, compressionOptions)
  }

  const handleReprocess = (id: string) => {
    reprocessFile(id, compressionOptions)
  }

  const handlePreview = (id: string) => {
    console.log('Preview GIF:', id)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <Header currentPath="/gif-compress" />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                <FileImage className="w-10 h-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-400 rounded-full flex items-center justify-center animate-pulse">
                <Play className="w-4 h-4 text-green-800" />
              </div>
            </div>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            GIF <span className="text-purple-600">Animation</span> Compression
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Compress animated GIF files while preserving smooth animations and transparency. 
            Advanced palette optimization reduces file sizes by up to 80% without losing quality.
          </p>
          
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-500">
            <div className="flex items-center">
              <Play className="w-4 h-4 mr-2 text-purple-500" />
              Animation Preserved
            </div>
            <div className="flex items-center">
              <Palette className="w-4 h-4 mr-2 text-pink-500" />
              Smart Palette Optimization
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2 text-green-500" />
              Frame Rate Control
            </div>
            <div className="flex items-center">
              <Layers className="w-4 h-4 mr-2 text-blue-500" />
              Transparency Support
            </div>
          </div>
        </motion.div>

        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <FileUpload
            onFileUpload={handleFileUpload}
            acceptedTypes={{
              'image/gif': ['.gif']
            }}
            maxFileSize={100 * 1024 * 1024} // 100MB for GIFs
            title="Drop your animated GIFs here"
            subtitle="Support for animated GIF files up to 100MB"
          />
        </motion.div>

        {/* Settings and Stats */}
        {files.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
            <div className="lg:col-span-2">
              <CompressionSettings
                type="gif"
                options={compressionOptions}
                onChange={setCompressionOptions}
                files={files}
              />
            </div>
            <div>
              <ProcessingStats
                stats={stats}
                isProcessing={isProcessing}
                currentProcessingIndex={currentProcessingIndex}
                onCompress={handleCompress}
                onDownloadAll={downloadAllFiles}
                onClearAll={clearAllFiles}
              />
            </div>
          </div>
        )}

        {/* Files List */}
        {files.length > 0 && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <Settings className="w-6 h-6 mr-3 text-purple-600" />
                GIF Files ({files.length})
              </h2>
              {stats.completed > 0 && (
                <button
                  onClick={downloadAllFiles}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download All ({stats.completed})
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {files.map((file, index) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <FileCard
                    item={file}
                    onRemove={removeFile}
                    onRetry={handleRetry}
                    onReprocess={handleReprocess}
                    onDownload={downloadSingleFile}
                    onPreview={handlePreview}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Features Section */}
        {files.length === 0 && (
          <motion.div
            className="mt-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Advanced GIF Optimization
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Professional-grade GIF compression with animation preservation and smart palette optimization
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Play className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Animation Intact</h3>
                <p className="text-gray-600 text-sm">
                  Preserve all animation frames and timing information
                </p>
              </div>

              <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-pink-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Palette className="w-6 h-6 text-pink-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Smart Palette</h3>
                <p className="text-gray-600 text-sm">
                  Intelligent color palette optimization for smaller files
                </p>
              </div>

              <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Frame Control</h3>
                <p className="text-gray-600 text-sm">
                  Adjust frame rate and timing for optimal compression
                </p>
              </div>

              <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Server Power</h3>
                <p className="text-gray-600 text-sm">
                  Server-side FFmpeg processing for reliable results
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </main>

      <Footer />
    </div>
  )
}
