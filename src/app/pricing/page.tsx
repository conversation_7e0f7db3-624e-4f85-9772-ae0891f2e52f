'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Head<PERSON> } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { 
  Check, 
  Crown, 
  Zap, 
  Shield, 
  Sparkles,
  Users,
  Clock,
  HeadphonesIcon,
  Code,
  Download
} from 'lucide-react'

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false)

  const features = {
    free: [
      { icon: Zap, text: "50 compressions per month", highlight: true },
      { icon: Download, text: "10MB max file size" },
      { icon: Shield, text: "PNG, JPEG, WebP support" },
      { icon: Clock, text: "Basic compression speed" },
    ],
    pro: [
      { icon: Sparkles, text: "Unlimited compressions", highlight: true },
      { icon: Download, text: "200MB max file size", highlight: true },
      { icon: Shield, text: "All formats (PNG, JPEG, WebP, GIF, Video)" },
      { icon: Zap, text: "Advanced compression algorithms" },
      { icon: Users, text: "Batch processing up to 100 files" },
      { icon: Code, text: "API access with 10,000 calls/month" },
      { icon: HeadphonesIcon, text: "Priority email support" },
      { icon: Crown, text: "Format conversion (PNG↔JPEG↔WebP)" },
    ]
  }

  const proPrice = isAnnual ? 7 : 9
  const proPriceOriginal = isAnnual ? 10 : 12

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Header currentPath="/pricing" />
      
      <main className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Simple, <span className="text-blue-600">Affordable</span> Pricing
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Get more for less. Our Pro plan offers better value than competitors with 
            more free compressions and lower prices.
          </p>
          
          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-8">
            <span className={`mr-3 ${!isAnnual ? 'text-gray-900 font-semibold' : 'text-gray-500'}`}>
              Monthly
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isAnnual ? 'bg-blue-600' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 ${isAnnual ? 'text-gray-900 font-semibold' : 'text-gray-500'}`}>
              Annual
            </span>
            {isAnnual && (
              <span className="ml-2 bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full">
                Save 22%
              </span>
            )}
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            
            {/* Free Plan */}
            <motion.div 
              className="bg-white rounded-3xl shadow-lg p-8 border border-gray-200 relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-8 h-8 text-gray-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Free</h3>
                <div className="text-5xl font-bold text-gray-900 mb-2">
                  $0
                  <span className="text-lg font-normal text-gray-600">/month</span>
                </div>
                <p className="text-gray-600">Perfect for trying out our service</p>
              </div>

              <ul className="space-y-4 mb-8">
                {features.free.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-0.5 ${
                      feature.highlight ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <Check className={`w-4 h-4 ${feature.highlight ? 'text-blue-600' : 'text-gray-600'}`} />
                    </div>
                    <span className={`text-gray-700 ${feature.highlight ? 'font-semibold' : ''}`}>
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>

              <button className="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105">
                Get Started Free
              </button>
              
              <p className="text-center text-sm text-gray-500 mt-4">
                No credit card required
              </p>
            </motion.div>

            {/* Pro Plan */}
            <motion.div 
              className="bg-white rounded-3xl shadow-2xl p-8 border-2 border-blue-500 relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {/* Popular Badge */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center">
                  <Crown className="w-4 h-4 mr-2" />
                  Most Popular
                </div>
              </div>

              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Crown className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Pro</h3>
                <div className="flex items-center justify-center mb-2">
                  <span className="text-2xl text-gray-400 line-through mr-2">
                    ${proPriceOriginal}
                  </span>
                  <span className="text-5xl font-bold text-gray-900">
                    ${proPrice}
                  </span>
                  <span className="text-lg font-normal text-gray-600 ml-1">
                    /{isAnnual ? 'month' : 'month'}
                  </span>
                </div>
                {isAnnual && (
                  <p className="text-sm text-green-600 font-semibold mb-2">
                    Billed annually - Save $36/year
                  </p>
                )}
                <p className="text-gray-600">For professionals and businesses</p>
              </div>

              <ul className="space-y-4 mb-8">
                {features.pro.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-0.5 ${
                      feature.highlight ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <Check className={`w-4 h-4 ${feature.highlight ? 'text-blue-600' : 'text-gray-600'}`} />
                    </div>
                    <span className={`text-gray-700 ${feature.highlight ? 'font-semibold' : ''}`}>
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>

              <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg">
                Start 14-Day Free Trial
              </button>
              
              <p className="text-center text-sm text-gray-500 mt-4">
                No credit card required • Cancel anytime
              </p>
            </motion.div>
          </div>
        </div>

        {/* Comparison with Competitors */}
        <motion.div
          className="max-w-4xl mx-auto mt-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose Us Over Competitors?
            </h2>
            <p className="text-gray-600">
              Compare our Pro plan with leading competitors
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Feature</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-blue-600">Our Pro</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-600">Tinify Pro</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-600">Others</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">Monthly Price</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600">$7-9</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">$25</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">$15-30</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="px-6 py-4 text-sm text-gray-900">Free Monthly Compressions</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600">50</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">20</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">10-20</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">Max File Size</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600">200MB</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">75MB</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">50-100MB</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="px-6 py-4 text-sm text-gray-900">Video Support</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">✗</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">Limited</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">API Calls/Month</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600">10,000</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">500</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">1,000-5,000</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          className="max-w-4xl mx-auto mt-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently Asked Questions
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600">
                We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Can I cancel my subscription anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can cancel anytime with no questions asked. You'll keep access to Pro features until your current billing period ends.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Is there really a free trial?
              </h3>
              <p className="text-gray-600">
                Yes! Get 14 days of full Pro access with no credit card required. Experience unlimited compressions and all premium features.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                How does the API work?
              </h3>
              <p className="text-gray-600">
                Our RESTful API lets you integrate compression into your apps. Pro users get 10,000 API calls per month with detailed documentation.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Do you offer team discounts?
              </h3>
              <p className="text-gray-600">
                Yes! Teams of 5+ users get 15% off, and enterprise customers get custom pricing. Contact us for volume discounts.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                What's your refund policy?
              </h3>
              <p className="text-gray-600">
                We offer a 30-day money-back guarantee. If you're not satisfied, we'll refund your payment in full, no questions asked.
              </p>
            </div>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white">
            <h2 className="text-3xl font-bold mb-4">
              Ready to compress like a pro?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of users who trust us with their compression needs
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 font-semibold py-3 px-8 rounded-xl hover:bg-gray-100 transition-colors">
                Start Free Trial
              </button>
              <button className="border-2 border-white text-white font-semibold py-3 px-8 rounded-xl hover:bg-white hover:text-blue-600 transition-colors">
                View Demo
              </button>
            </div>
          </div>
        </motion.div>
      </main>

      <Footer />
    </div>
  )
}
