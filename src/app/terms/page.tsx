import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/Header"
import { FileText, Shield, AlertCircle, Scale, Globe, Clock } from "lucide-react"

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header currentPath="/terms" />

      <main className="py-20">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Terms of Service
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Please read these terms carefully before using our image and video compression services.
            </p>
            <div className="flex items-center justify-center space-x-2 mt-6 text-sm text-gray-500">
              <Clock className="w-4 h-4" />
              <span>Last updated: January 2025</span>
            </div>
          </div>

          {/* Content */}
          <div className="prose prose-lg max-w-none">
            
            {/* Section 1 */}
            <div className="mb-12">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Scale className="w-4 h-4 text-blue-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 m-0">1. Acceptance of Terms</h2>
              </div>
              <div className="bg-gray-50 rounded-xl p-6">
                <p className="text-gray-700 leading-relaxed mb-4">
                  By accessing and using CompressHub's image and video compression services, you accept and agree to be bound by the terms and provision of this agreement.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  If you do not agree to abide by the above, please do not use this service. We reserve the right to change these terms at any time without prior notice.
                </p>
              </div>
            </div>

            {/* Section 2 */}
            <div className="mb-12">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-4 h-4 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 m-0">2. Service Description</h2>
              </div>
              <div className="bg-gray-50 rounded-xl p-6">
                <p className="text-gray-700 leading-relaxed mb-4">
                  CompressHub provides online image and video compression services that allow users to reduce file sizes while maintaining quality. Our services include:
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                  <li>Image compression for JPEG, PNG, WebP, and other formats</li>
                  <li>Video compression for MP4, AVI, MOV, and other formats</li>
                  <li>Batch processing capabilities</li>
                  <li>Format conversion services</li>
                </ul>
                <p className="text-gray-700 leading-relaxed">
                  We strive to provide reliable service but do not guarantee uninterrupted availability or error-free operation.
                </p>
              </div>
            </div>

            {/* Section 3 */}
            <div className="mb-12">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Globe className="w-4 h-4 text-purple-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 m-0">3. User Responsibilities</h2>
              </div>
              <div className="bg-gray-50 rounded-xl p-6">
                <p className="text-gray-700 leading-relaxed mb-4">
                  Users are responsible for ensuring they have the right to compress and process the files they upload. You agree not to:
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                  <li>Upload copyrighted material without proper authorization</li>
                  <li>Upload illegal, harmful, or offensive content</li>
                  <li>Use the service for any unlawful purposes</li>
                  <li>Attempt to reverse engineer or compromise our systems</li>
                  <li>Upload files containing malware or viruses</li>
                </ul>
                <p className="text-gray-700 leading-relaxed">
                  We reserve the right to refuse service or terminate accounts that violate these terms.
                </p>
              </div>
            </div>

            {/* Section 4 */}
            <div className="mb-12">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <AlertCircle className="w-4 h-4 text-orange-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 m-0">4. Privacy and Data Protection</h2>
              </div>
              <div className="bg-gray-50 rounded-xl p-6">
                <p className="text-gray-700 leading-relaxed mb-4">
                  We take your privacy seriously. Files uploaded to our service are:
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                  <li>Processed securely using industry-standard encryption</li>
                  <li>Automatically deleted from our servers after processing</li>
                  <li>Never shared with third parties</li>
                  <li>Not used for any purpose other than compression</li>
                </ul>
                <p className="text-gray-700 leading-relaxed">
                  For more details, please review our Privacy Policy.
                </p>
              </div>
            </div>

            {/* Section 5 */}
            <div className="mb-12">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-4 h-4 text-red-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 m-0">5. Limitation of Liability</h2>
              </div>
              <div className="bg-gray-50 rounded-xl p-6">
                <p className="text-gray-700 leading-relaxed mb-4">
                  CompressHub provides this service "as is" without any warranties. We are not liable for:
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                  <li>Any loss of data or files during processing</li>
                  <li>Service interruptions or downtime</li>
                  <li>Quality issues with compressed files</li>
                  <li>Any indirect or consequential damages</li>
                </ul>
                <p className="text-gray-700 leading-relaxed">
                  Users should always keep backups of their original files before processing.
                </p>
              </div>
            </div>

            {/* Contact */}
            <div className="bg-blue-50 rounded-xl p-8 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Questions about our Terms?</h3>
              <p className="text-gray-600 mb-6">
                If you have any questions about these Terms of Service, please contact us.
              </p>
              <a 
                href="mailto:<EMAIL>" 
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
              >
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
