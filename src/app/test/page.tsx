'use client'

import { useState } from 'react'
import { ImageCompressionService } from '@/services/imageCompression'
import { formatFileSize } from '@/lib/utils'

export default function TestPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [compressing, setCompressing] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [progress, setProgress] = useState(0)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setResult(null)
      setProgress(0)
    }
  }

  const handleCompress = async () => {
    if (!selectedFile) return

    setCompressing(true)
    setProgress(0)

    try {
      const compressionResult = await ImageCompressionService.compressImage(
        selectedFile,
        {
          quality: 0.7,
          maintainAspectRatio: true,
          removeMetadata: true,
          preserveTransparency: true
        },
        (progress) => {
          setProgress(progress)
        }
      )

      setResult(compressionResult)
    } catch (error) {
      console.error('Compression failed:', error)
      setResult({ success: false, error: 'Compression failed' })
    } finally {
      setCompressing(false)
    }
  }

  const downloadResult = () => {
    if (result?.blob) {
      const url = URL.createObjectURL(result.blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `compressed_${selectedFile?.name}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Image Compression Test
          </h1>

          <div className="bg-white rounded-lg shadow-lg p-6">
            {/* File Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select an image file
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>

            {/* Selected File Info */}
            {selectedFile && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">Selected File:</h3>
                <p className="text-sm text-gray-600">Name: {selectedFile.name}</p>
                <p className="text-sm text-gray-600">Size: {formatFileSize(selectedFile.size)}</p>
                <p className="text-sm text-gray-600">Type: {selectedFile.type}</p>
              </div>
            )}

            {/* Compress Button */}
            <div className="mb-6">
              <button
                onClick={handleCompress}
                disabled={!selectedFile || compressing}
                className={`
                  w-full py-3 px-4 rounded-lg font-medium transition-colors
                  ${!selectedFile || compressing
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                  }
                `}
              >
                {compressing ? `Compressing... ${Math.round(progress)}%` : 'Compress Image'}
              </button>
            </div>

            {/* Progress Bar */}
            {compressing && (
              <div className="mb-6">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            )}

            {/* Results */}
            {result && (
              <div className="p-4 rounded-lg border">
                {result.success ? (
                  <div className="text-green-800 bg-green-50 border-green-200">
                    <h3 className="font-medium mb-2">Compression Successful!</h3>
                    <div className="text-sm space-y-1">
                      <p>Original Size: {formatFileSize(result.originalSize)}</p>
                      <p>Compressed Size: {formatFileSize(result.compressedSize)}</p>
                      <p>Compression Ratio: {result.compressionRatio.toFixed(1)}%</p>
                      <p>Space Saved: {formatFileSize(result.originalSize - result.compressedSize)}</p>
                    </div>
                    <button
                      onClick={downloadResult}
                      className="mt-3 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      Download Compressed Image
                    </button>
                  </div>
                ) : (
                  <div className="text-red-800 bg-red-50 border-red-200">
                    <h3 className="font-medium mb-2">Compression Failed</h3>
                    <p className="text-sm">{result.error}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-8 text-center text-sm text-gray-600">
            <p>This is a test page for the image compression functionality.</p>
            <p>Select an image file and click compress to test the compression service.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
