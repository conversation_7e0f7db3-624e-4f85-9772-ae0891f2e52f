import Link from "next/link"
import {
  ArrowRight,
  Image as ImageIcon,
  Video,
  Zap,
  Shield,
  Download,
  Star,
  Upload,
  Settings,
  CheckCircle,
  Users,
  Globe,
  Smartphone,
  Monitor,
  Camera,
  Play,
  FileImage,
  Layers,
  Award
} from "lucide-react"
import { <PERSON><PERSON>, Footer } from "@/components/Header"
import { BrandCarousel } from "@/components/BrandCarousel"
import { WhyCompress } from "@/components/WhyCompress"
import { ImageComparison } from "@/components/ImageComparison"

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header currentPath="/" />

      <main>
      {/* Hero Section - 模块一 */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>
        <div className="container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Free Online Image & Video Compressor
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mt-2">
                Reduce File Size Instantly
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Experience the best image compressor and video compressor available. Compress images and compress videos with our simple, efficient, and secure tool, ensuring your privacy is always protected.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link
                href="/compress"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                Compress Files Now
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
              <button className="inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200">
                <Play className="mr-2 w-5 h-5" />
                Watch Demo
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200">
                <div className="text-4xl font-bold text-blue-600 mb-2">10M+</div>
                <div className="text-gray-600 font-medium">Files Compressed</div>
              </div>
              <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200">
                <div className="text-4xl font-bold text-purple-600 mb-2">90%</div>
                <div className="text-gray-600 font-medium">Average Size Reduction</div>
              </div>
              <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200">
                <div className="text-4xl font-bold text-green-600 mb-2">100%</div>
                <div className="text-gray-600 font-medium">Privacy Protected</div>
              </div>
            </div>

            {/* Hero Image Placeholder */}
            <div className="relative max-w-4xl mx-auto">
              <div className="bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl p-8 border-2 border-dashed border-blue-300">
                <div className="flex items-center justify-center space-x-8">
                  <div className="text-center">
                    <div className="w-24 h-24 bg-red-100 rounded-xl flex items-center justify-center mb-4 mx-auto">
                      <FileImage className="w-12 h-12 text-red-500" />
                    </div>
                    <p className="text-sm text-gray-600">Large File<br/>5.2 MB</p>
                  </div>
                  <ArrowRight className="w-8 h-8 text-blue-600 animate-pulse" />
                  <div className="text-center">
                    <div className="w-24 h-24 bg-green-100 rounded-xl flex items-center justify-center mb-4 mx-auto">
                      <FileImage className="w-12 h-12 text-green-500" />
                    </div>
                    <p className="text-sm text-gray-600">Compressed<br/>520 KB</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Brand Carousel Section */}
      <BrandCarousel />

      {/* Why Compress Section */}
      <WhyCompress />

      {/* Image Comparison Section */}
      <ImageComparison
        beforeImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=800&fit=crop&q=100"
        afterImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=800&fit=crop&q=75"
        beforeLabel="ORIGINAL"
        afterLabel="TINIFY"
        beforeSize="1.1 MB"
        afterSize="188 KB"
      />

      {/* Product Advantages Section - 模块二 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Why Our Image & Video Compressor Stands Out
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the power of our image compressor and video compressor. We offer batch compress images capabilities, saving you hours of manual work. Efficiency and simplicity are built into our core.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            {/* First Advantage */}
            <div className="order-2 lg:order-1">
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8">
                <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
                  <Shield className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  High-Efficiency Image Compressor with Free, Private Processing
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Unlike competitors that limit you, we offer free, secure local processing for your compressed images. Our system is designed to compress JPEG and compress PNG files with unmatched speed, and all processing happens in your browser for maximum privacy.
                </p>
                <div className="mt-6 flex items-center space-x-4">
                  <div className="flex items-center text-sm text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    100% Private
                  </div>
                  <div className="flex items-center text-sm text-blue-600">
                    <Zap className="w-4 h-4 mr-2" />
                    Lightning Fast
                  </div>
                </div>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-12 flex items-center justify-center">
                <div className="text-center">
                  <Monitor className="w-24 h-24 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-600">Browser-based Processing</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Second Advantage */}
            <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-12 flex items-center justify-center">
              <div className="text-center">
                <Layers className="w-24 h-24 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-600">Advanced Compression</p>
              </div>
            </div>
            <div>
              <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8">
                <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mb-6">
                  <Settings className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  More Than Compression: Advanced Format Conversion and Batch Processing
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Our image compressor goes beyond simple compression. Convert image formats, compress videos to smaller sizes, or use our advanced algorithms to reduce image file size by up to 90%. It's a comprehensive tool for perfect results.
                </p>
                <div className="mt-6">
                  <Link
                    href="/compress"
                    className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    Try for Free Now
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section - 模块三 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Real-World Applications of Our Compression Tools
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See how our image compressor and video compressor help creators and businesses. It's the ultimate tool to reduce file size and optimize your visual content for web and mobile.
            </p>
          </div>

          <div className="space-y-20">
            {/* Use Case 1 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="bg-gradient-to-br from-blue-100 to-cyan-100 rounded-2xl p-12 flex items-center justify-center">
                <div className="text-center">
                  <Globe className="w-24 h-24 text-blue-600 mx-auto mb-4" />
                  <div className="text-sm text-blue-600 font-medium">Web Optimization</div>
                </div>
              </div>
              <div>
                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                  Perfect for Web Developers: Optimize Images for Faster Loading
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  With over 10 million images compressed, our image compressor is the go-to choice for web optimization. This powerful tool ensures faster page loading by reducing image file size without quality loss, helping you improve SEO rankings effortlessly.
                </p>
                <div className="flex items-center space-x-6 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">60%</div>
                    <div className="text-sm text-gray-600">Faster Loading</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">90%</div>
                    <div className="text-sm text-gray-600">Size Reduction</div>
                  </div>
                </div>
                <Link
                  href="/compress"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Compress Images Now
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </div>
            </div>

            {/* Use Case 2 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                  Social Media Ready: Compress Videos for Easy Sharing
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  90% of social media posts perform better with optimized media. Our video compressor provides perfect compression for Instagram, TikTok, and YouTube, reducing video file size by up to 80% while maintaining visual quality.
                </p>
                <div className="flex items-center space-x-6 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">80%</div>
                    <div className="text-sm text-gray-600">Size Reduction</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-pink-600">3x</div>
                    <div className="text-sm text-gray-600">Faster Upload</div>
                  </div>
                </div>
                <Link
                  href="/compress"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Compress Your Video
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </div>
              <div className="order-1 lg:order-2">
                <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-12 flex items-center justify-center">
                  <div className="text-center">
                    <Smartphone className="w-24 h-24 text-purple-600 mx-auto mb-4" />
                    <div className="text-sm text-purple-600 font-medium">Social Media</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Use Case 3 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl p-12 flex items-center justify-center">
                <div className="text-center">
                  <Layers className="w-24 h-24 text-green-600 mx-auto mb-4" />
                  <div className="text-sm text-green-600 font-medium">Batch Processing</div>
                </div>
              </div>
              <div>
                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                  Storage Saver: Batch Compress Images for Cloud Storage
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  Don't let large files eat up your storage space. Our batch image compressor can process hundreds of photos at once, helping you compress images efficiently and save up to 90% storage space.
                </p>
                <div className="flex items-center space-x-6 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">100+</div>
                    <div className="text-sm text-gray-600">Files at Once</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">90%</div>
                    <div className="text-sm text-gray-600">Storage Saved</div>
                  </div>
                </div>
                <Link
                  href="/compress"
                  className="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors"
                >
                  Start Batch Compression
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </div>
            </div>

            {/* Use Case 4 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                  Professional Quality: Lossless Compression for Photography
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  Achieve professional results with our advanced compression algorithms. Whether you need to compress JPEG for web use or compress PNG with transparency, our tool maintains image quality while significantly reducing file size.
                </p>
                <div className="flex items-center space-x-6 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">0%</div>
                    <div className="text-sm text-gray-600">Quality Loss</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">85%</div>
                    <div className="text-sm text-gray-600">Size Reduction</div>
                  </div>
                </div>
                <Link
                  href="/compress"
                  className="inline-flex items-center px-6 py-3 bg-orange-600 text-white font-semibold rounded-lg hover:bg-orange-700 transition-colors"
                >
                  Compress Photos
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </div>
              <div className="order-1 lg:order-2">
                <div className="bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl p-12 flex items-center justify-center">
                  <div className="text-center">
                    <Camera className="w-24 h-24 text-orange-600 mx-auto mb-4" />
                    <div className="text-sm text-orange-600 font-medium">Photography</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How-To Guide Section - 模块四 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              How to Use Our Image & Video Compressor
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              In just three simple steps, use our image compressor to get perfectly optimized files. It's the most efficient image compression tool free of charge you'll find online.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Step 1 */}
              <div className="text-center group">
                <div className="relative mb-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Upload className="w-12 h-12 text-blue-600" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    1
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Step 1: Upload Your Files
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Simply drag and drop your images or videos, or select them from your device. Our tool supports various formats including JPEG, PNG, MP4, GIF and can compress images in batch for maximum efficiency.
                </p>
              </div>

              {/* Step 2 */}
              <div className="text-center group">
                <div className="relative mb-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Settings className="w-12 h-12 text-purple-600" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    2
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Step 2: Choose Compression Settings
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Select your desired compression level and output format. Our image compressor offers customizable quality settings to balance file size reduction with visual quality according to your needs.
                </p>
              </div>

              {/* Step 3 */}
              <div className="text-center group">
                <div className="relative mb-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Download className="w-12 h-12 text-green-600" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    3
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Step 3: Download Your Compressed Files
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Our compression engine processes your files in seconds. Your optimized images and videos are ready for download, with significant file size reduction achieved. It's that simple and fast.
                </p>
              </div>
            </div>

            {/* CTA */}
            <div className="text-center mt-12">
              <Link
                href="/compress"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                Start Compressing Now for Free
                <ArrowRight className="ml-2 w-6 h-6" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - 模块五 */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              What Our Users Are Saying
            </h2>
            <div className="flex items-center justify-center space-x-1 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
              <span className="ml-2 text-gray-600 font-medium">4.9/5 from 10,000+ users</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center space-x-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4 leading-relaxed">
                "This is the best image compressor I've ever used. It reduced my photo file sizes by 85% without any visible quality loss. My website loads 60% faster now!"
              </p>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Sarah Chen</div>
                  <div className="text-sm text-gray-500">Web Developer</div>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center space-x-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4 leading-relaxed">
                "I needed a reliable video compressor for my YouTube channel, and CompressHub delivered perfectly. The batch processing feature saved me hours of work."
              </p>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <Video className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Mike Rodriguez</div>
                  <div className="text-sm text-gray-500">Content Creator</div>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center space-x-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4 leading-relaxed">
                "The ability to compress images while maintaining transparency saved my design projects! This tool is incredibly efficient and user-friendly."
              </p>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <ImageIcon className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Emma Thompson</div>
                  <div className="text-sm text-gray-500">Graphic Designer</div>
                </div>
              </div>
            </div>

            {/* Testimonial 4 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center space-x-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4 leading-relaxed">
                "As an e-commerce business owner, this image compressor is essential for my product photos. Professional results with lightning-fast processing."
              </p>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <Award className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">David Kim</div>
                  <div className="text-sm text-gray-500">Business Owner</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section - 模块六 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get answers to common questions about our image and video compression tools.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {/* FAQ 1 */}
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  How does the free image compressor work while ensuring privacy?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Our image compressor processes all files locally in your browser, ensuring complete privacy. No files are uploaded to servers, and all compression happens on your device for maximum security. Your images never leave your computer.
                </p>
              </div>

              {/* FAQ 2 */}
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Can I compress images without losing quality?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Yes, our advanced image compressor algorithm uses smart compression techniques to reduce file size while maintaining visual quality. You can achieve up to 90% size reduction with minimal quality impact, and you have full control over the quality settings.
                </p>
              </div>

              {/* FAQ 3 */}
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  What file formats does your compressor support?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Our tool supports all major formats: compress JPEG, compress PNG, compress GIF, MP4 video compression, and many more including WebP, BMP, TIFF, AVI, MOV, and WebM. You can also convert between formats during compression.
                </p>
              </div>

              {/* FAQ 4 */}
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  How much can I reduce my file sizes?
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Typically, you can reduce image file size by 70-90% and video file size by 60-80% depending on the original format and quality settings. Our compressor is optimized for maximum efficiency while preserving visual quality.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Compress Your Files?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join millions of users who trust our compression tools. Start optimizing your images and videos today.
          </p>
          <Link
            href="/compress"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-bold text-lg rounded-xl hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Get Started for Free
            <ArrowRight className="ml-2 w-6 h-6" />
          </Link>
        </div>
      </section>
      </main>

      <Footer />
    </div>
  )
}
