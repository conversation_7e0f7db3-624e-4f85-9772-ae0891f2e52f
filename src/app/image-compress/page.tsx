'use client'

import { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { FileUpload } from '@/components/FileUpload'
import { FileCard } from '@/components/FileCard'
import { CompressionSettings } from '@/components/CompressionSettings'
import { ProcessingStats } from '@/components/ProcessingStats'
import { useCompressionManager } from '@/hooks/useCompressionManager'
import { CompressionOptions } from '@/types'
import { 
  ImageIcon, 
  Sparkles, 
  Zap, 
  Download,
  Settings,
  FileImage,
  Layers
} from 'lucide-react'

export default function ImageCompressPage() {
  const [compressionOptions, setCompressionOptions] = useState<CompressionOptions>({
    quality: 0.1, // Maximum Compression for images
    maintainAspectRatio: true,
    removeMetadata: true,
    preserveTransparency: true
  })

  const {
    files,
    isProcessing,
    currentProcessingIndex,
    addFiles,
    removeFile,
    compressAllFiles,
    retryFile,
    reprocessFile,
    downloadFile: downloadSingleFile,
    downloadAllFiles,
    clearAllFiles,
    getStats
  } = useCompressionManager()

  const stats = getStats()

  const handleFileUpload = useCallback((uploadedFiles: File[]) => {
    // Filter only image files
    const imageFiles = uploadedFiles.filter(file => 
      file.type.startsWith('image/') && !file.type.includes('gif')
    )
    
    if (imageFiles.length !== uploadedFiles.length) {
      alert('Only image files (PNG, JPEG, WebP) are allowed on this page. GIF files should be processed on the GIF compression page.')
    }
    
    if (imageFiles.length > 0) {
      addFiles(imageFiles)
    }
  }, [addFiles])

  const handleCompress = useCallback(() => {
    compressAllFiles(compressionOptions)
  }, [compressAllFiles, compressionOptions])

  const handleRetry = (id: string) => {
    retryFile(id, compressionOptions)
  }

  const handleReprocess = (id: string) => {
    reprocessFile(id, compressionOptions)
  }

  const handlePreview = (id: string) => {
    console.log('Preview image:', id)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Header currentPath="/image-compress" />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                <ImageIcon className="w-10 h-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-yellow-800" />
              </div>
            </div>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Image <span className="text-blue-600">Compression</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Compress PNG, JPEG, and WebP images while preserving quality and transparency. 
            Reduce file sizes by up to 90% with our advanced compression algorithms.
          </p>
          
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-500">
            <div className="flex items-center">
              <FileImage className="w-4 h-4 mr-2 text-blue-500" />
              PNG, JPEG, WebP Support
            </div>
            <div className="flex items-center">
              <Layers className="w-4 h-4 mr-2 text-green-500" />
              Transparency Preserved
            </div>
            <div className="flex items-center">
              <Zap className="w-4 h-4 mr-2 text-yellow-500" />
              Batch Processing
            </div>
          </div>
        </motion.div>

        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <FileUpload
            onFileUpload={handleFileUpload}
            acceptedTypes={{
              'image/png': ['.png'],
              'image/jpeg': ['.jpg', '.jpeg'],
              'image/webp': ['.webp']
            }}
            maxFileSize={50 * 1024 * 1024} // 50MB for images
            title="Drop your images here"
            subtitle="Support for PNG, JPEG, and WebP formats"
          />
        </motion.div>

        {/* Settings and Stats */}
        {files.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
            <div className="lg:col-span-2">
              <CompressionSettings
                type="image"
                options={compressionOptions}
                onChange={setCompressionOptions}
                files={files}
              />
            </div>
            <div>
              <ProcessingStats
                stats={stats}
                isProcessing={isProcessing}
                currentProcessingIndex={currentProcessingIndex}
                onCompress={handleCompress}
                onDownloadAll={downloadAllFiles}
                onClearAll={clearAllFiles}
              />
            </div>
          </div>
        )}

        {/* Files List */}
        {files.length > 0 && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <Settings className="w-6 h-6 mr-3 text-blue-600" />
                Image Files ({files.length})
              </h2>
              {stats.completed > 0 && (
                <button
                  onClick={downloadAllFiles}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download All ({stats.completed})
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {files.map((file, index) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <FileCard
                    item={file}
                    onRemove={removeFile}
                    onRetry={handleRetry}
                    onReprocess={handleReprocess}
                    onDownload={downloadSingleFile}
                    onPreview={handlePreview}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Features Section */}
        {files.length === 0 && (
          <motion.div
            className="mt-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose Our Image Compression?
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Advanced algorithms ensure maximum compression while maintaining visual quality
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Lightning Fast</h3>
                <p className="text-gray-600">
                  Process multiple images simultaneously with our optimized compression engine
                </p>
              </div>

              <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Layers className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Quality Preserved</h3>
                <p className="text-gray-600">
                  Smart algorithms maintain image quality while significantly reducing file size
                </p>
              </div>

              <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <FileImage className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Format Support</h3>
                <p className="text-gray-600">
                  Support for PNG, JPEG, WebP with transparency preservation
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </main>

      <Footer />
    </div>
  )
}
