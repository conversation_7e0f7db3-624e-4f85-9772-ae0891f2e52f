'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, Download, Trash2, Play, Pause, Zap } from 'lucide-react'
import Link from 'next/link'
import { FileItem, CompressionOptions } from '@/types'
import { generateUniqueId, isImageFile, isVideoFile, isGifFile, formatFileSize } from '@/lib/utils'
import { useCompressionManager } from '@/hooks/useCompressionManager'
import { FileCard } from '@/components/FileCard'
import { CompressionSettings } from '@/components/CompressionSettings'
import { BatchProgressBar } from '@/components/ProgressBar'
import { QueueManager } from '@/components/QueueManager'
import { Header, Breadcrumb } from '@/components/Header'
import { UploadArea } from '@/components/UploadArea'
import {
  CompressionAnimation,
  FloatingIcons,
  ProgressRing,
  PulseButton,
  SlideInCard,
  AnimatedProcessing
} from '@/components/AnimatedIcons'

export default function CompressPage() {
  const [isDragActive, setIsDragActive] = useState(false)
  const [compressionOptions, setCompressionOptions] = useState<CompressionOptions>({
    quality: 0.7,
    maintainAspectRatio: true,
    removeMetadata: true
  })

  const {
    files,
    isProcessing,
    currentProcessingIndex,
    addFiles,
    removeFile,
    compressAllFiles,
    retryFile,
    downloadFile: downloadSingleFile,
    downloadAllFiles,
    clearAllFiles,
    getStats
  } = useCompressionManager()

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: FileItem[] = acceptedFiles.map(file => ({
      id: generateUniqueId(),
      file,
      name: file.name,
      size: file.size,
      type: isGifFile(file) ? 'gif' : isImageFile(file) ? 'image' : 'video',
      format: file.name.split('.').pop()?.toLowerCase() || '',
      status: 'pending',
      progress: 0,
      originalSize: file.size
    }))

    addFiles(newFiles)
  }, [addFiles])

  const acceptedTypes = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/bmp', 'image/tiff',
    'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm', 'video/mkv', 'video/3gp'
  ]

  const stats = getStats()

  const handleStartCompression = () => {
    compressAllFiles(compressionOptions)
  }

  const handleRetry = (id: string) => {
    retryFile(id, compressionOptions)
  }

  const handlePreview = (id: string) => {
    // Preview functionality is handled within FileCard component
    console.log('Preview file:', id)
  }

  const handlePauseQueue = () => {
    // Pause functionality would be implemented in the compression manager
    console.log('Pause queue')
  }

  const handleResumeQueue = () => {
    handleStartCompression()
  }

  const handleCancelQueue = () => {
    // Cancel all processing files
    files.forEach(file => {
      if (file.status === 'processing' || file.status === 'pending') {
        // This would need to be implemented in the compression manager
        console.log('Cancel file:', file.id)
      }
    })
  }

  const handleCancelItem = (id: string) => {
    // Cancel specific item
    console.log('Cancel item:', id)
  }

  const handleRetryItem = (id: string) => {
    handleRetry(id)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      <FloatingIcons />
      <Header currentPath="/compress" />

      <main className="container mx-auto px-4 py-8 relative z-10">
        <div className="max-w-4xl mx-auto">
          <Breadcrumb
            items={[
              { label: 'Home', href: '/' },
              { label: 'Compress Files' }
            ]}
          />

          <SlideInCard className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Compress Your Files
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Upload images and videos to compress them while maintaining quality
            </p>

            {/* Animated Compression Demo */}
            <div className="flex justify-center mb-8">
              <CompressionAnimation isActive={files.length === 0} />
            </div>
          </SlideInCard>

          {/* Upload Area */}
          <SlideInCard delay={0.2}>
            <UploadArea
              onFilesSelected={onDrop}
              acceptedTypes={acceptedTypes}
              maxFiles={20}
              maxFileSize={100 * 1024 * 1024} // 100MB
              disabled={isProcessing}
            />
          </SlideInCard>

          {/* Stats and Progress */}
          {files.length > 0 && (
            <div className="mt-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  Files ({stats.total})
                </h2>
                <div className="flex items-center space-x-4">
                  {stats.completed > 0 && (
                    <button
                      onClick={downloadAllFiles}
                      className="inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download All ({stats.completed})
                    </button>
                  )}
                  <button
                    onClick={clearAllFiles}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear All
                  </button>
                </div>
              </div>

              {/* Queue Manager */}
              <SlideInCard delay={0.4} className="mb-6">
                <QueueManager
                  queue={{
                    items: files,
                    isProcessing,
                    currentIndex: currentProcessingIndex,
                    totalProgress: stats.overallProgress,
                    canPause: isProcessing,
                    canCancel: isProcessing || stats.pending > 0
                  }}
                  onPause={handlePauseQueue}
                  onResume={handleResumeQueue}
                  onCancel={handleCancelQueue}
                  onCancelItem={handleCancelItem}
                  onRetryItem={handleRetryItem}
                />
              </SlideInCard>

              {/* File Cards */}
              <div className="space-y-4">
                {files.map((file, index) => (
                  <SlideInCard key={file.id} delay={0.1 * index}>
                    <FileCard
                      item={file}
                      onRemove={removeFile}
                      onRetry={handleRetry}
                      onDownload={downloadSingleFile}
                      onPreview={handlePreview}
                    />
                  </SlideInCard>
                ))}
              </div>

              {/* Compression Settings */}
              <SlideInCard delay={0.6} className="mt-8">
                {/* Determine the primary file type */}
                {(() => {
                  const imageFiles = files.filter(f => f.type === 'image').length
                  const videoFiles = files.filter(f => f.type === 'video').length
                  const gifFiles = files.filter(f => f.type === 'gif').length

                  let primaryType: 'image' | 'video' | 'gif' = 'image'
                  if (videoFiles > imageFiles && videoFiles > gifFiles) {
                    primaryType = 'video'
                  } else if (gifFiles > imageFiles && gifFiles > videoFiles) {
                    primaryType = 'gif'
                  }

                  return (
                    <CompressionSettings
                      type={primaryType}
                      options={compressionOptions}
                      onChange={setCompressionOptions}
                    />
                  )
                })()}
              </SlideInCard>

              {/* Action Buttons */}
              <SlideInCard delay={0.8} className="mt-8 flex justify-center space-x-4">
                <PulseButton
                  onClick={handleStartCompression}
                  disabled={!stats.canProcess}
                  isActive={stats.canProcess && !isProcessing}
                  className={`
                    inline-flex items-center px-8 py-3 font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl
                    ${stats.canProcess
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }
                  `}
                >
                  {isProcessing ? (
                    <>
                      <AnimatedProcessing isActive={true} />
                      <span className="ml-2">Processing...</span>
                    </>
                  ) : (
                    <>
                      <Play className="w-5 h-5 mr-2" />
                      Start Compression
                    </>
                  )}
                </PulseButton>

                {/* Progress Ring for overall progress */}
                {isProcessing && (
                  <div className="flex items-center justify-center ml-8">
                    <ProgressRing progress={stats.overallProgress} size={80} />
                  </div>
                )}
              </SlideInCard>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
