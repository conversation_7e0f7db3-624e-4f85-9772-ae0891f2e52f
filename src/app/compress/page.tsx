'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { FloatingIcons } from '@/components/AnimatedIcons'
import { 
  ImageIcon, 
  FileImage, 
  Video, 
  ArrowRight,
  Zap,
  Sparkles,
  Play,
  Layers,
  Clock,
  Palette,
  Film,
  Monitor
} from 'lucide-react'

export default function CompressPage() {
  const compressionTypes = [
    {
      title: 'Image Compression',
      description: 'Compress PNG, JPEG, and WebP images while preserving quality and transparency',
      href: '/image-compress',
      icon: ImageIcon,
      gradient: 'from-blue-500 to-indigo-600',
      bgGradient: 'from-blue-50 to-indigo-50',
      features: ['PNG, JPEG, WebP', 'Transparency Preserved', 'Batch Processing', 'Up to 90% Reduction'],
      stats: 'Up to 90% size reduction',
      color: 'blue'
    },
    {
      title: 'GIF Animation',
      description: 'Compress animated GIF files while maintaining smooth animations and frame timing',
      href: '/gif-compress',
      icon: FileImage,
      gradient: 'from-purple-500 to-pink-600',
      bgGradient: 'from-purple-50 to-pink-50',
      features: ['Animation Preserved', 'Smart Palette', 'Frame Rate Control', 'Up to 80% Reduction'],
      stats: 'Up to 80% size reduction',
      color: 'purple'
    },
    {
      title: 'Video Compression',
      description: 'Professional video encoding with advanced codec support and quality optimization',
      href: '/video-compress',
      icon: Video,
      gradient: 'from-green-500 to-emerald-600',
      bgGradient: 'from-green-50 to-emerald-50',
      features: ['Multiple Formats', 'Quality Control', 'Mobile Ready', 'Fast Processing'],
      stats: 'Professional encoding',
      color: 'green'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      <FloatingIcons />
      <Header currentPath="/compress" />

      <main className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-8">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
                <Zap className="w-12 h-12 text-white" />
              </div>
              <div className="absolute -top-3 -right-3 w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse">
                <Sparkles className="w-5 h-5 text-yellow-800" />
              </div>
            </div>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Choose Your <span className="text-blue-600">Compression</span> Type
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Select the perfect compression tool for your needs. Each specialized page offers 
            optimized settings and processing for different file types.
          </p>
          
          <div className="flex flex-wrap items-center justify-center gap-8 text-sm text-gray-500">
            <div className="flex items-center">
              <Zap className="w-4 h-4 mr-2 text-blue-500" />
              Server-Side Processing
            </div>
            <div className="flex items-center">
              <Layers className="w-4 h-4 mr-2 text-green-500" />
              Batch Operations
            </div>
            <div className="flex items-center">
              <Play className="w-4 h-4 mr-2 text-purple-500" />
              Animation Support
            </div>
          </div>
        </motion.div>

        {/* Compression Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {compressionTypes.map((type, index) => (
            <motion.div
              key={type.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Link href={type.href}>
                <div className={`
                  relative group bg-gradient-to-br ${type.bgGradient} p-8 rounded-2xl 
                  border border-gray-200 hover:border-gray-300 transition-all duration-300
                  hover:shadow-xl hover:scale-105 cursor-pointer overflow-hidden
                `}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white to-transparent"></div>
                  </div>
                  
                  {/* Icon */}
                  <div className={`
                    w-16 h-16 bg-gradient-to-br ${type.gradient} rounded-2xl 
                    flex items-center justify-center mb-6 shadow-lg
                    group-hover:scale-110 transition-transform duration-300
                  `}>
                    <type.icon className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">
                    {type.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {type.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-2 mb-6">
                    {type.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-600">
                        <div className={`w-2 h-2 bg-${type.color}-500 rounded-full mr-3`}></div>
                        {feature}
                      </div>
                    ))}
                  </div>

                  {/* Stats */}
                  <div className={`
                    inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    bg-${type.color}-100 text-${type.color}-700 mb-4
                  `}>
                    {type.stats}
                  </div>

                  {/* Action */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">
                      Get Started
                    </span>
                    <ArrowRight className={`
                      w-5 h-5 text-${type.color}-500 
                      group-hover:translate-x-1 transition-transform duration-300
                    `} />
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Features Comparison */}
        <motion.div
          className="bg-white rounded-2xl shadow-lg p-8 mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            Why Choose Specialized Compression?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <ImageIcon className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Optimized Algorithms</h3>
              <p className="text-gray-600">
                Each page uses specialized compression algorithms tailored for specific file types
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Palette className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Smart Settings</h3>
              <p className="text-gray-600">
                Customized compression settings and options for maximum efficiency
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Monitor className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Better Results</h3>
              <p className="text-gray-600">
                Achieve better compression ratios while maintaining quality
              </p>
            </div>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white text-center">
            <div className="text-3xl font-bold mb-2">90%</div>
            <div className="text-blue-100">Average Image Compression</div>
          </div>
          
          <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white text-center">
            <div className="text-3xl font-bold mb-2">80%</div>
            <div className="text-purple-100">GIF Size Reduction</div>
          </div>
          
          <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white text-center">
            <div className="text-3xl font-bold mb-2">75%</div>
            <div className="text-green-100">Video Compression Ratio</div>
          </div>
        </motion.div>
      </main>

      <Footer />
    </div>
  )
}
