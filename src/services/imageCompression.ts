import imageCompression from 'browser-image-compression'
import { CompressionResult, ImageCompressionOptions } from '@/types'

export class ImageCompressionService {
  static async compressImage(
    file: File,
    options: ImageCompressionOptions,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    try {
      // Determine output format
      let outputFormat = options.outputFormat || file.type.replace('image/', '')

      // Handle special cases
      if (outputFormat === 'jpg') outputFormat = 'jpeg'

      const compressionOptions = {
        maxSizeMB: 10, // Maximum file size in MB
        maxWidthOrHeight: Math.max(options.maxWidth || 1920, options.maxHeight || 1080),
        useWebWorker: true,
        fileType: `image/${outputFormat}`,
        initialQuality: options.quality,
        alwaysKeepResolution: !options.maintainAspectRatio,
        preserveExif: !options.removeMetadata,
        onProgress: (progress: number) => {
          onProgress?.(progress)
        }
      }

      // Handle transparency preservation for PNG
      if (options.preserveTransparency && (file.type === 'image/png' || outputFormat === 'png')) {
        compressionOptions.fileType = 'image/png'
        outputFormat = 'png'
      }

      // For WebP, ensure proper handling
      if (outputFormat === 'webp') {
        compressionOptions.fileType = 'image/webp'
      }

      console.log('Compression options:', compressionOptions)

      const compressedFile = await imageCompression(file, compressionOptions)

      const compressionRatio = ((file.size - compressedFile.size) / file.size) * 100

      return {
        success: true,
        originalSize: file.size,
        compressedSize: compressedFile.size,
        compressionRatio,
        blob: compressedFile
      }
    } catch (error) {
      console.error('Image compression failed:', error)
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  static async convertFormat(
    file: File,
    targetFormat: string,
    quality: number = 0.8
  ): Promise<CompressionResult> {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          canvas.width = img.width
          canvas.height = img.height
          
          if (ctx) {
            // Handle transparency for PNG
            if (targetFormat === 'png') {
              ctx.clearRect(0, 0, canvas.width, canvas.height)
            } else {
              // Fill with white background for JPEG/WebP
              ctx.fillStyle = '#FFFFFF'
              ctx.fillRect(0, 0, canvas.width, canvas.height)
            }
            
            ctx.drawImage(img, 0, 0)
            
            canvas.toBlob((blob) => {
              if (blob) {
                const compressionRatio = ((file.size - blob.size) / file.size) * 100
                resolve({
                  success: true,
                  originalSize: file.size,
                  compressedSize: blob.size,
                  compressionRatio,
                  blob
                })
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to convert image format'
                })
              }
            }, `image/${targetFormat}`, quality)
          }
        }

        img.onerror = () => {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: 'Failed to load image'
          })
        }

        img.src = URL.createObjectURL(file)
      })
    } catch (error) {
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  static async resizeImage(
    file: File,
    maxWidth: number,
    maxHeight: number,
    maintainAspectRatio: boolean = true
  ): Promise<CompressionResult> {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          let { width, height } = img

          if (maintainAspectRatio) {
            const aspectRatio = width / height
            if (width > maxWidth) {
              width = maxWidth
              height = width / aspectRatio
            }
            if (height > maxHeight) {
              height = maxHeight
              width = height * aspectRatio
            }
          } else {
            width = Math.min(width, maxWidth)
            height = Math.min(height, maxHeight)
          }

          canvas.width = width
          canvas.height = height

          if (ctx) {
            ctx.drawImage(img, 0, 0, width, height)
            
            canvas.toBlob((blob) => {
              if (blob) {
                const compressionRatio = ((file.size - blob.size) / file.size) * 100
                resolve({
                  success: true,
                  originalSize: file.size,
                  compressedSize: blob.size,
                  compressionRatio,
                  blob
                })
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to resize image'
                })
              }
            }, file.type, 0.9)
          }
        }

        img.onerror = () => {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: 'Failed to load image'
          })
        }

        img.src = URL.createObjectURL(file)
      })
    } catch (error) {
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }
}
