import imageCompression from 'browser-image-compression'
import { CompressionResult, ImageCompressionOptions } from '@/types'

export class ImageCompressionService {
  static async compressImage(
    file: File,
    options: ImageCompressionOptions,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    try {
      console.log('Starting image compression with options:', options)
      onProgress?.(10)

      // Determine output format
      let outputFormat = options.outputFormat || file.type.replace('image/', '')

      // Handle special cases
      if (outputFormat === 'jpg') outputFormat = 'jpeg'

      console.log('Target output format:', outputFormat)

      // If format conversion is needed or WebP is requested, use Canvas API
      const needsFormatConversion = outputFormat !== file.type.replace('image/', '') || outputFormat === 'webp'

      if (needsFormatConversion) {
        console.log('Using Canvas API for format conversion')
        return await this.compressWithCanvas(file, options, outputFormat, onProgress)
      }

      // Use browser-image-compression for same-format compression
      const compressionOptions = {
        maxSizeMB: 10,
        maxWidthOrHeight: Math.max(options.maxWidth || 1920, options.maxHeight || 1080),
        useWebWorker: true,
        fileType: file.type,
        initialQuality: options.quality,
        alwaysKeepResolution: !options.maintainAspectRatio,
        preserveExif: !options.removeMetadata,
        onProgress: (progress: number) => {
          onProgress?.(50 + progress * 0.5) // 50-100% range
        }
      }

      console.log('Using browser-image-compression with options:', compressionOptions)
      onProgress?.(50)

      const compressedFile = await imageCompression(file, compressionOptions)
      const compressionRatio = ((file.size - compressedFile.size) / file.size) * 100

      onProgress?.(100)

      return {
        success: true,
        originalSize: file.size,
        compressedSize: compressedFile.size,
        compressionRatio,
        blob: compressedFile
      }
    } catch (error) {
      console.error('Image compression failed:', error)
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  private static async compressWithCanvas(
    file: File,
    options: ImageCompressionOptions,
    outputFormat: string,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    return new Promise((resolve) => {
      const img = new Image()

      img.onload = () => {
        try {
          onProgress?.(60)

          // Calculate dimensions
          let { width, height } = img

          if (options.maxWidth && width > options.maxWidth) {
            height = (height * options.maxWidth) / width
            width = options.maxWidth
          }

          if (options.maxHeight && height > options.maxHeight) {
            width = (width * options.maxHeight) / height
            height = options.maxHeight
          }

          onProgress?.(70)

          // Create canvas
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          if (!ctx) {
            resolve({
              success: false,
              originalSize: file.size,
              compressedSize: 0,
              compressionRatio: 0,
              error: 'Could not get canvas context'
            })
            return
          }

          canvas.width = width
          canvas.height = height

          // Handle transparency for PNG/WebP
          if (outputFormat === 'png' || outputFormat === 'webp') {
            ctx.clearRect(0, 0, canvas.width, canvas.height)
          } else {
            // Fill with white background for JPEG
            ctx.fillStyle = '#FFFFFF'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
          }

          onProgress?.(80)

          // Draw image
          ctx.drawImage(img, 0, 0, width, height)

          onProgress?.(90)

          // Convert to blob with specified format and quality
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const compressionRatio = ((file.size - blob.size) / file.size) * 100

                onProgress?.(100)

                resolve({
                  success: true,
                  originalSize: file.size,
                  compressedSize: blob.size,
                  compressionRatio,
                  blob
                })
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to create blob'
                })
              }
            },
            `image/${outputFormat}`,
            options.quality
          )
        } catch (error) {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: error instanceof Error ? error.message : 'Canvas compression failed'
          })
        }
      }

      img.onerror = () => {
        resolve({
          success: false,
          originalSize: file.size,
          compressedSize: 0,
          compressionRatio: 0,
          error: 'Failed to load image'
        })
      }

      img.src = URL.createObjectURL(file)
    })
  }

  static async convertFormat(
    file: File,
    targetFormat: string,
    quality: number = 0.8
  ): Promise<CompressionResult> {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          canvas.width = img.width
          canvas.height = img.height
          
          if (ctx) {
            // Handle transparency for PNG
            if (targetFormat === 'png') {
              ctx.clearRect(0, 0, canvas.width, canvas.height)
            } else {
              // Fill with white background for JPEG/WebP
              ctx.fillStyle = '#FFFFFF'
              ctx.fillRect(0, 0, canvas.width, canvas.height)
            }
            
            ctx.drawImage(img, 0, 0)
            
            canvas.toBlob((blob) => {
              if (blob) {
                const compressionRatio = ((file.size - blob.size) / file.size) * 100
                resolve({
                  success: true,
                  originalSize: file.size,
                  compressedSize: blob.size,
                  compressionRatio,
                  blob
                })
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to convert image format'
                })
              }
            }, `image/${targetFormat}`, quality)
          }
        }

        img.onerror = () => {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: 'Failed to load image'
          })
        }

        img.src = URL.createObjectURL(file)
      })
    } catch (error) {
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  static async resizeImage(
    file: File,
    maxWidth: number,
    maxHeight: number,
    maintainAspectRatio: boolean = true
  ): Promise<CompressionResult> {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          let { width, height } = img

          if (maintainAspectRatio) {
            const aspectRatio = width / height
            if (width > maxWidth) {
              width = maxWidth
              height = width / aspectRatio
            }
            if (height > maxHeight) {
              height = maxHeight
              width = height * aspectRatio
            }
          } else {
            width = Math.min(width, maxWidth)
            height = Math.min(height, maxHeight)
          }

          canvas.width = width
          canvas.height = height

          if (ctx) {
            ctx.drawImage(img, 0, 0, width, height)
            
            canvas.toBlob((blob) => {
              if (blob) {
                const compressionRatio = ((file.size - blob.size) / file.size) * 100
                resolve({
                  success: true,
                  originalSize: file.size,
                  compressedSize: blob.size,
                  compressionRatio,
                  blob
                })
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to resize image'
                })
              }
            }, file.type, 0.9)
          }
        }

        img.onerror = () => {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: 'Failed to load image'
          })
        }

        img.src = URL.createObjectURL(file)
      })
    } catch (error) {
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }
}
