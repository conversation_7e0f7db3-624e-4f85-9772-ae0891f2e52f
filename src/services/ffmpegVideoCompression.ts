import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { CompressionResult, VideoCompressionOptions } from '@/types'

class FFmpegVideoCompressionService {
  private static ffmpeg: FFmpeg | null = null
  private static isLoaded = false
  private static isLoading = false

  static async initFFmpeg(onProgress?: (progress: number) => void): Promise<boolean> {
    if (this.isLoaded) return true
    if (this.isLoading) {
      // Wait for loading to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return this.isLoaded
    }

    try {
      this.isLoading = true
      onProgress?.(10)

      this.ffmpeg = new FFmpeg()
      
      // Load FFmpeg with progress tracking
      this.ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg log:', message)
      })

      this.ffmpeg.on('progress', ({ progress }) => {
        // FFmpeg progress is 0-1, convert to percentage for loading
        onProgress?.(10 + progress * 40) // 10-50% for FFmpeg operations
      })

      onProgress?.(30)

      // Load FFmpeg core
      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd'
      await this.ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      })

      this.isLoaded = true
      onProgress?.(50)
      return true
    } catch (error) {
      console.error('Failed to load FFmpeg:', error)
      this.isLoaded = false
      return false
    } finally {
      this.isLoading = false
    }
  }

  static async compressVideo(
    file: File,
    options: VideoCompressionOptions,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    try {
      console.log('Starting FFmpeg video compression for:', file.name)
      
      // Initialize FFmpeg
      const initialized = await this.initFFmpeg((progress) => {
        onProgress?.(progress * 0.5) // First 50% for initialization
      })

      if (!initialized || !this.ffmpeg) {
        throw new Error('Failed to initialize FFmpeg')
      }

      onProgress?.(50)

      // Write input file
      const inputFileName = `input.${file.name.split('.').pop()}`
      const outputFileName = `output.${options.outputFormat || 'mp4'}`
      
      await this.ffmpeg.writeFile(inputFileName, await fetchFile(file))
      onProgress?.(60)

      // Build FFmpeg command
      const command = this.buildFFmpegCommand(inputFileName, outputFileName, options)
      console.log('FFmpeg command:', command)

      // Execute compression
      await this.ffmpeg.exec(command)
      onProgress?.(90)

      // Read output file
      const outputData = await this.ffmpeg.readFile(outputFileName)
      const outputBlob = new Blob([outputData], { 
        type: `video/${options.outputFormat || 'mp4'}` 
      })

      // Clean up
      await this.ffmpeg.deleteFile(inputFileName)
      await this.ffmpeg.deleteFile(outputFileName)

      const compressionRatio = ((file.size - outputBlob.size) / file.size) * 100

      onProgress?.(100)

      return {
        success: true,
        originalSize: file.size,
        compressedSize: outputBlob.size,
        compressionRatio,
        blob: outputBlob
      }

    } catch (error) {
      console.error('FFmpeg video compression failed:', error)
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'FFmpeg compression failed'
      }
    }
  }

  private static buildFFmpegCommand(
    inputFile: string,
    outputFile: string,
    options: VideoCompressionOptions
  ): string[] {
    const command = ['-i', inputFile]

    // Video codec
    if (options.codec) {
      switch (options.codec) {
        case 'h264':
          command.push('-c:v', 'libx264')
          break
        case 'h265':
          command.push('-c:v', 'libx265')
          break
        case 'vp8':
          command.push('-c:v', 'libvpx')
          break
        case 'vp9':
          command.push('-c:v', 'libvpx-vp9')
          break
        default:
          command.push('-c:v', 'libx264') // Default to H.264
      }
    } else {
      command.push('-c:v', 'libx264')
    }

    // Quality settings (CRF - lower is better quality)
    const crf = Math.round((1 - options.quality) * 40 + 18) // 18-58 range
    command.push('-crf', crf.toString())

    // Resolution
    if (options.maxWidth || options.maxHeight) {
      const width = options.maxWidth || -1
      const height = options.maxHeight || -1
      command.push('-vf', `scale=${width}:${height}`)
    }

    // Bitrate
    if (options.bitrate) {
      command.push('-b:v', options.bitrate)
    }

    // Frame rate
    if (options.fps) {
      command.push('-r', options.fps.toString())
    }

    // Audio codec
    command.push('-c:a', 'aac')
    command.push('-b:a', '128k')

    // Remove metadata if requested
    if (options.removeMetadata) {
      command.push('-map_metadata', '-1')
    }

    // Output format specific options
    if (options.outputFormat === 'webm') {
      command.push('-f', 'webm')
    } else if (options.outputFormat === 'mp4') {
      command.push('-movflags', '+faststart')
    }

    // Preset for encoding speed vs compression efficiency
    command.push('-preset', 'medium')

    command.push(outputFile)
    return command
  }

  static async convertFormat(
    file: File,
    targetFormat: string,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    const options: VideoCompressionOptions = {
      quality: 0.7,
      outputFormat: targetFormat,
      maintainAspectRatio: true,
      removeMetadata: false
    }

    return this.compressVideo(file, options, onProgress)
  }

  static isFFmpegLoaded(): boolean {
    return this.isLoaded
  }

  static async unloadFFmpeg(): Promise<void> {
    if (this.ffmpeg) {
      this.ffmpeg.terminate()
      this.ffmpeg = null
      this.isLoaded = false
    }
  }
}

export { FFmpegVideoCompressionService }
